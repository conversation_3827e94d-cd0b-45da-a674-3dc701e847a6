#!/bin/bash
# 开发环境构建脚本，不创建通用应用

set -e

# 清理钥匙串中的证书
security find-certificate -a -c "Developer ID Application: Beijing Haiyi Tongzhan Information Technology Co., Ltd" -Z ~/Library/Keychains/login.keychain-db 2>/dev/null | grep SHA-1 | awk '{print $3}' | xargs -I {} security delete-certificate -Z {} ~/Library/Keychains/login.keychain-db 2>/dev/null || true
security find-certificate -a -c "Developer ID Certification Authority" -Z ~/Library/Keychains/login.keychain-db 2>/dev/null | grep SHA-1 | awk '{print $3}' | xargs -I {} security delete-certificate -Z {} ~/Library/Keychains/login.keychain-db 2>/dev/null || true
echo "已清理钥匙串中的证书"

./import_certificates.sh 20241218
# To fix /Volumes/JoyCode errors, DO NOT RUN IN VOID!!!
# To fix permission errors, sudo chmod -r +rwx ~/Desktop/void
# Run in sudo if have errors
# Build, sign and package arm64
./mac-sign.sh build arm64 dev
./cp-vsix.sh joycoder-editor joycode
# ./cp-vsix.sh clouddev- jdcom
./move-joycoder-extension.sh VSCode-darwin-arm64 joycode.joycoder-editor
# ./move-joycoder-extension.sh VSCode-darwin-arm64 jdcom.clouddev
# ./move-joycoder-extension.sh VSCode-darwin-arm64 joy.resource

# ./mac-sign.sh sign arm64
# ./mac-sign.sh notarize arm64

echo "-------------------- build dev app --------------------"
