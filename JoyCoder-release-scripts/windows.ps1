# Windows PowerShell脚本，执行与windows.sh相同的操作

Write-Host "开始构建JoyCode Windows版本..." -ForegroundColor Green

# 构建React部分
Write-Host "构建React部分..." -ForegroundColor Cyan
npm run buildreact
if ($LASTEXITCODE -ne 0) {
    Write-Host "React构建失败！" -ForegroundColor Red
    exit $LASTEXITCODE
}

# 构建主应用
Write-Host "构建主应用..." -ForegroundColor Cyan
npm run gulp vscode-win32-x64-min
if ($LASTEXITCODE -ne 0) {
    Write-Host "主应用构建失败！" -ForegroundColor Red
    exit $LASTEXITCODE
}

# 更改图标和构建更新器
Write-Host "构建更新器..." -ForegroundColor Cyan
npm run gulp vscode-win32-x64-inno-updater
if ($LASTEXITCODE -ne 0) {
    Write-Host "更新器构建失败！" -ForegroundColor Red
    exit $LASTEXITCODE
}

# 移动扩展
Write-Host "移动joycoder-editor扩展..." -ForegroundColor Cyan
npm run move:extension -- joycoder-editor joycode VSCode-win32-x64 joycode.joycoder-editor
if ($LASTEXITCODE -ne 0) {
    Write-Host "移动joycoder-editor扩展失败！" -ForegroundColor Red
    exit $LASTEXITCODE
}

# Write-Host "移动clouddev扩展..." -ForegroundColor Cyan
# npm run move:extension -- clouddev- jdcom VSCode-win32-x64 jdcom.clouddev
# if ($LASTEXITCODE -ne 0) {
#     Write-Host "移动clouddev扩展失败！" -ForegroundColor Red
#     exit $LASTEXITCODE
# }

# 构建系统安装程序（运行两次）
Write-Host "构建系统安装程序（第一次）..." -ForegroundColor Cyan
npm run gulp vscode-win32-x64-system-setup
if ($LASTEXITCODE -ne 0) {
    Write-Host "系统安装程序构建失败（第一次）！" -ForegroundColor Red
    exit $LASTEXITCODE
}

Write-Host "构建系统安装程序（第二次）..." -ForegroundColor Cyan
npm run gulp vscode-win32-x64-system-setup
if ($LASTEXITCODE -ne 0) {
    Write-Host "系统安装程序构建失败（第二次）！" -ForegroundColor Red
    exit $LASTEXITCODE
}

Write-Host "构建完成！输出文件位于.build/目录中。" -ForegroundColor Green
