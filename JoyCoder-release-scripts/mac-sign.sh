#!/usr/bin/env bash

set -e # Exit on error



echo "-------------------- Running $1 on $2 --------------------"
ARCH=$2
DEV_PACKAGE=$3
if [ -n "$DEV_PACKAGE" ] && [ "$DEV_PACKAGE" = "dev" ]; then
    DEV_PACKAGE='--no-mangle'
    echo "-------------------- package dev --------------------"
fi


USER_HOME="$(dirname $(pwd))"
USER_HOME_UP="$(dirname $(dirname $(pwd)))"

# Required variables (store these values in mac-env.sh and copy them over to run this script):
ORIGINAL_DOTAPP_DIR="${USER_HOME_UP}/VSCode-darwin-${ARCH}" # location of original (nothing is modified in this dir, just copied away from it)
ORIGINAL_REH_DIR="${USER_HOME}/vscode-reh-darwin-${ARCH}"
VOID_DIR="${USER_HOME}"
WORKING_DIR="${USER_HOME}/JoyCodeSign/JoyCodeSign-${ARCH}" # temp dir for all the work here
P12_FILE="${USER_HOME}/JoyCoder-release-scripts/developerID_application.p12"
P12_PASSWORD="********"
APPLE_ID="<EMAIL>" # see https://appleid.apple.com
APP_PASSWORD="nbrx-yoyx-tkto-dapl" # see https://appleid.apple.com
TEAM_ID="F6Q9F9N36U" # see https://developer.apple.com/account/resources/identifiers/list
CODESIGN_IDENTITY="Developer ID Application: Beijing Haiyi Tongzhan Information Technology Co., Ltd (F6Q9F9N36U)" #Developer ID Application: ... try `security find-identity -v -p codesigning`
KEYCHAIN_PROFILE_NAME="JoyCode" # this doesnt seem to do anything but is required


# Check if all required variables are set
if [ -z "$ORIGINAL_DOTAPP_DIR" ] || [ -z "$WORKING_DIR" ] || [ -z "$P12_FILE" ] || [ -z "$P12_PASSWORD" ] || [ -z "$APPLE_ID" ] || [ -z "$TEAM_ID" ] || [ -z "$APP_PASSWORD" ] || [ -z "$CODESIGN_IDENTITY" ]; then
    echo "Error: Make sure to set all variables."
    exit 1
fi


## computedx
KEYCHAIN_DIR="${WORKING_DIR}/1_Keychain"
KEYCHAIN="${KEYCHAIN_DIR}/buildagent.keychain"

SIGN_DIR="${WORKING_DIR}/2_Signed"
SIGNED_DOTAPP_DIR="${SIGN_DIR}/VSCode-darwin-${ARCH}"
SIGNED_DOTAPP="${SIGN_DIR}/VSCode-darwin-${ARCH}/JoyCode.app"

SIGNED_DMG_DIR="${SIGN_DIR}/VSCode-darwin-${ARCH}"
SIGNED_DMG="${SIGN_DIR}/VSCode-darwin-${ARCH}/JoyCode-Installer-darwin-${ARCH}.dmg"





sign() {

    echo "-------------------- 0. cleanup + copy --------------------"
    rm -rf "${USER_HOME}/JoyCodeSign"
    echo "-------------------- rm. ${USER_HOME}/JoyCodeSign --------------------"
    security delete-generic-password -l "JoyCode Safe Storage" 2>/dev/null || true
    mkdir "${USER_HOME}/JoyCodeSign"
        echo "-------------------- mkdir. ${USER_HOME}/JoyCodeSign --------------------"

    mkdir "${WORKING_DIR}"
    mkdir "${KEYCHAIN_DIR}"
    mkdir "${SIGN_DIR}"

    cp -Rp "${ORIGINAL_DOTAPP_DIR}" "${SIGN_DIR}"


    echo "-------------------- 1. Make temp keychain --------------------"
    # Create a new keychain
    security create-keychain -p pwd "${KEYCHAIN}"
    security set-keychain-settings -lut 21600 "${KEYCHAIN}"

    security unlock-keychain -p pwd "${KEYCHAIN}"

    # Import your p12 certificate
    security import "${P12_FILE}" -k "${KEYCHAIN}" -P "${P12_PASSWORD}" -T /usr/bin/codesign -A
    security set-key-partition-list -S apple-tool:,apple:,codesign: -s -k pwd "${KEYCHAIN}" > /dev/null

    echo "-------------------- 1a. Setup custom entitlements --------------------"
    # Create directory for custom entitlements if it doesn't exist
    mkdir -p "${VOID_DIR}/build/azure-pipelines/darwin"

    # Copy our custom entitlements files to the location expected by sign.js
    cp "${USER_HOME}/JoyCoder-release-scripts/custom-app-entitlements.plist" "${VOID_DIR}/build/azure-pipelines/darwin/app-entitlements.plist"
    cp "${USER_HOME}/JoyCoder-release-scripts/custom-helper-gpu-entitlements.plist" "${VOID_DIR}/build/azure-pipelines/darwin/helper-gpu-entitlements.plist"
    cp "${USER_HOME}/JoyCoder-release-scripts/custom-helper-renderer-entitlements.plist" "${VOID_DIR}/build/azure-pipelines/darwin/helper-renderer-entitlements.plist"
    cp "${USER_HOME}/JoyCoder-release-scripts/custom-helper-plugin-entitlements.plist" "${VOID_DIR}/build/azure-pipelines/darwin/helper-plugin-entitlements.plist"


    echo "-------------------- 2a. Sign --------------------"
    cd "${VOID_DIR}/build/darwin"

    # used in sign.js
    export AGENT_TEMPDIRECTORY=$KEYCHAIN_DIR
    export CODESIGN_IDENTITY="${CODESIGN_IDENTITY}"
    export VSCODE_ARCH=$ARCH
    echo "------$AGENT_TEMPDIRECTORY----$SIGN_DIR---------- $CODESIGN_IDENTITY ------------$VSCODE_ARCH--------"
    node sign.js "${SIGN_DIR}"

    # Add additional codesign with explicit entitlements to ensure keychain access
    echo "-------------------- 2b. Additional signing with explicit entitlements --------------------"

    # Get the helper app paths
    APP_FRAMEWORKS_PATH="${SIGNED_DOTAPP}/Contents/Frameworks"
    GPU_HELPER_APP="${APP_FRAMEWORKS_PATH}/JoyCode Helper (GPU).app"
    RENDERER_HELPER_APP="${APP_FRAMEWORKS_PATH}/JoyCode Helper (Renderer).app"
    PLUGIN_HELPER_APP="${APP_FRAMEWORKS_PATH}/JoyCode Helper (Plugin).app"

    # Sign helper apps with explicit entitlements
    codesign --force --options runtime --entitlements "${USER_HOME}/JoyCoder-release-scripts/custom-helper-gpu-entitlements.plist" --sign "${CODESIGN_IDENTITY}" "${GPU_HELPER_APP}"
    codesign --force --options runtime --entitlements "${USER_HOME}/JoyCoder-release-scripts/custom-helper-renderer-entitlements.plist" --sign "${CODESIGN_IDENTITY}" "${RENDERER_HELPER_APP}"
    codesign --force --options runtime --entitlements "${USER_HOME}/JoyCoder-release-scripts/custom-helper-plugin-entitlements.plist" --sign "${CODESIGN_IDENTITY}" "${PLUGIN_HELPER_APP}"

    # 修改Info.plist，添加环境变量设置，避免使用钥匙串
    INFOPLIST_PATH="${SIGNED_DOTAPP}/Contents/Info.plist"
    /usr/libexec/PlistBuddy -c "Merge ${USER_HOME}/JoyCoder-release-scripts/no-keychain-info.plist" "${INFOPLIST_PATH}"
    echo "Info.plist已修改，添加了VSCODE_USE_INMEMORY_SECRETSTORAGE=1环境变量"

    # Sign main app with explicit entitlements
    codesign --force --options runtime --entitlements "${USER_HOME}/JoyCoder-release-scripts/custom-app-entitlements.plist" --sign "${CODESIGN_IDENTITY}" "${SIGNED_DOTAPP}"

    # Verify signatures
    codesign --verify --verbose=4 "${GPU_HELPER_APP}"
    codesign --verify --verbose=4 "${RENDERER_HELPER_APP}"
    codesign --verify --verbose=4 "${PLUGIN_HELPER_APP}"
    codesign --verify --verbose=4 "${SIGNED_DOTAPP}"

    # 设置钥匙串访问权限，避免弹出提示框
    # echo "设置钥匙串访问权限..."
    # security set-generic-password-partition-list -S apple-tool:,apple: -s "JoyCode Safe Storage" -a "${SIGNED_DOTAPP}" -k pwd
    # echo "钥匙串访问权限已设置"


    echo "-------------------- 2b. Make into dmg --------------------"
    npx create-dmg --volname "JoyCode Installer" --identity="${CODESIGN_IDENTITY}" "${SIGNED_DOTAPP}" "${SIGNED_DMG_DIR}"
    # there are two create-dmgs https://github.com/create-dmg/create-dmg https://github.com/sindresorhus/create-dmg the latter one is on npm and works better
    GENERATED_DMG=$(ls "${SIGNED_DMG_DIR}"/*.dmg) # figure out the full path of the generated file because create-dmg is stupid
    if [[ -z "$GENERATED_DMG" ]]; then
        echo "Error: No .dmg file was created."
        exit 1
    fi
    mv "${GENERATED_DMG}" "${SIGNED_DMG}" # rename

    # We don't even have to codesign - apparently create-dmg does it! codesign --deep --options runtime --sign "${CODESIGN_IDENTITY}" "${SIGNED_DMG}" create
    codesign --verify --verbose=4 "${SIGNED_DMG}"

}


# notarize DMG
notarize(){

    # echo "-------------------- 4. Notarize --------------------"
    # echo "Past history:"
    # xcrun notarytool history --keychain-profile "${KEYCHAIN_PROFILE_NAME}" --keychain "${KEYCHAIN}"
    echo "JoyCode: Setting credentials..."
    security unlock-keychain -p pwd ${KEYCHAIN}
    xcrun notarytool store-credentials "${KEYCHAIN_PROFILE_NAME}" \
    --apple-id "${APPLE_ID}" \
    --team-id "${TEAM_ID}" \
    --password "${APP_PASSWORD}" \
    --keychain "${KEYCHAIN}"

    echo "JoyCode: Submitting..."
    xcrun notarytool submit "${SIGNED_DMG}" \
    --keychain-profile "${KEYCHAIN_PROFILE_NAME}" \
    --keychain "${KEYCHAIN}" \
    --wait

    echo "Done! Stapling..."
    # finds notarized ticket that was made and staples it to JoyCode.app
    xcrun stapler staple "${SIGNED_DMG}"

    # echo "-------------------- 6. Verify --------------------"
    # spctl --assess --verbose=4 "${SIGNED_DMG}"

    # 清理临时钥匙串
    echo "-------------------- 清理临时钥匙串 --------------------"
    security delete-keychain "${KEYCHAIN}"
    echo "临时钥匙串已删除"

}


rawapp() {
  cd "${SIGNED_DOTAPP_DIR}"
  echo "Zipping rawapp here..."

  VOIDAPP=$(basename $SIGNED_DOTAPP)
    ZIPNAME="JoyCode-RawApp-darwin-${ARCH}.zip"
    # ZIPPEDAPP="${SIGNED_DOTAPP_DIR}/${ZIPNAME}"
    ditto -c -k --sequesterRsrc --keepParent "${VOIDAPP}" "${ZIPNAME}"

  echo "Done!"
}


hashrawapp() {
    cd "${SIGNED_DOTAPP_DIR}"

    SHA1=$(shasum -a 1 "${SIGNED_DOTAPP_DIR}/JoyCode-RawApp-darwin-${ARCH}.zip" | cut -d' ' -f1)
    SHA256=$(shasum -a 256 "${SIGNED_DOTAPP_DIR}/JoyCode-RawApp-darwin-${ARCH}.zip" | cut -d' ' -f1)
    TIMESTAMP=$(date +%s)

    cat > "JoyCode-UpdJSON-darwin-${ARCH}.json" << EOF
{
    "sha256hash": "${SHA256}",
    "hash": "${SHA1}",
    "timestamp": ${TIMESTAMP}
}
EOF

  echo "Done!"
}


USAGE="Usage: $0 {sign|notarize|rawapp|hashrawapp} {arm64|x64}"

# check to make sure arm64 or x64 is specified
case "$2" in
    arm64)
        ;;
    x64)
        ;;
    *)
        echo $USAGE
        exit 1
        ;;
esac

# Check the first argument
case "$1" in
    build)
        cd "${VOID_DIR}"
        npm run buildreact
        npm run gulp "vscode-darwin-${ARCH}-min"
        ;;
    buildDev)

        cd "${VOID_DIR}"
        npm run buildreact
        # 添加--verbose参数以获取更详细的日志输出
        # 添加--continue参数以在遇到错误时继续执行
        npm run gulp-dev "vscode-darwin-${ARCH}-min" $DEV_PACKAGE --verbose --continue

        ;;
    sign)
        sign
        ;;
    notarize)
        notarize
        ;;
  rawapp)
    rawapp
    ;;
  hashrawapp)
    hashrawapp
    ;;

 buildreh)
        cd "${VOID_DIR}"
        npm run gulp "vscode-reh-darwin-${ARCH}"
        ;;
   packagereh)
        tar -czf "${SIGNED_DOTAPP_DIR}/void-server-darwin-${ARCH}.tar.gz" -C "$(dirname "$ORIGINAL_REH_DIR")" "$(basename "$ORIGINAL_REH_DIR")"
        ;;
    *)
        echo $USAGE
        exit 1
        ;;
esac
