#!/bin/bash

# 设置默认值和检查参数
DEFAULT_TARGET="VSCode-darwin-arm64"
DEFAULT_VSIXNAME="joycode.joycoder-editor"
TARGET_DIR_NAME="${1:-$DEFAULT_TARGET}"
VSIXNAME="${2:-$DEFAULT_VSIXNAME}"

echo "目标目录: $TARGET_DIR_NAME"
if [ $# -eq 0 ]; then
    echo "使用默认目标目录: $DEFAULT_TARGET"
fi

# 设置工作目录为项目根目录
cd "$(dirname "$0")/.." || exit 1

# 源目录和目标目录
SOURCE_DIR="jd-inner-extensions"
DESKTOP_DIR="$(dirname $PWD)"
TARGET_BASE_DIR="${DESKTOP_DIR}/${TARGET_DIR_NAME}"
TARGET_DIR="${TARGET_BASE_DIR}/JoyCode.app/Contents/Resources/app/extensions"

TARGET_DIR_YML="${TARGET_BASE_DIR}/JoyCode.app/Contents/Resources/app"


echo "路径信息:"
echo "当前项目目录: $PWD"
echo "目标父目录: $DESKTOP_DIR"
echo "目标应用目录: $TARGET_BASE_DIR"

# 确保目标目录不在当前项目内
if [[ "$TARGET_BASE_DIR" == "$PWD"* ]]; then
    echo "错误: 目标目录不能在当前项目内"
    exit 1
fi

# 检查源目录是否存在
if [ ! -d "$SOURCE_DIR" ]; then
    echo "错误: 源目录 $SOURCE_DIR 不存在"
    echo "当前工作目录: $PWD"
    exit 1
fi

# 创建目标目录（如果不存在）
mkdir -p "$TARGET_DIR"

# 删除目标目录中特定前缀的内容
echo "正在删除目标目录中的特定前缀旧内容..."
prefixes_to_delete=("$VSIXNAME")
for prefix in "${prefixes_to_delete[@]}"; do
    matching_dirs=$(find "$TARGET_DIR" -maxdepth 1 -type d -name "${prefix}*")
    if [ -n "$matching_dirs" ]; then
        for dir in $matching_dirs; do
            rm -rf "$dir"
            echo "已删除: $dir"
        done
    else
        echo "没有找到匹配前缀 ${prefix} 的目录"
    fi
done


# 复制扩展文件夹内的内容
echo "正在复制扩展文件夹内容..."
echo "源路径: $PWD/$SOURCE_DIR/"
echo "目标路径: $TARGET_DIR"
cp -r "$SOURCE_DIR"/* "$TARGET_DIR/"


# cp app-update.yml 和 dev-app-update.yml 到 TARGET_DIR_YML
echo $PWD
cp ./app-update.yml "$TARGET_DIR_YML"
cp ./dev-app-update.yml "$TARGET_DIR_YML"


if [ $? -eq 0 ]; then
    echo "扩展复制成功！"
else
    echo "错误: 复制扩展时发生错误"
    exit 1
fi
