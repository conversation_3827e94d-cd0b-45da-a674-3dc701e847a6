

npm run buildreact
npm run gulp vscode-win32-x64-min
# change icon
npm run gulp vscode-win32-x64-inno-updater
npm run move:extension -- joycoder-editor joycode VSCode-win32-x64 joycode.joycoder-editor
# npm run move:extension --  clouddev- jdcom VSCode-win32-x64 jdcom.clouddev
# run twice:
npm run gulp vscode-win32-x64-system-setup
npm run gulp vscode-win32-x64-system-setup

# output is in .build/...
