# Do not run this unless you know what you're doing.
# Don't run this when JoyCode is open, or <PERSON> will confuse the two versions (run in terminal or VS Code).

set -e
# 清理钥匙串中的证书
security find-certificate -a -c "Developer ID Application: Beijing Haiyi Tongzhan Information Technology Co., Ltd" -Z ~/Library/Keychains/login.keychain-db 2>/dev/null | grep SHA-1 | awk '{print $3}' | xargs -I {} security delete-certificate -Z {} ~/Library/Keychains/login.keychain-db 2>/dev/null || true
security find-certificate -a -c "Developer ID Certification Authority" -Z ~/Library/Keychains/login.keychain-db 2>/dev/null | grep SHA-1 | awk '{print $3}' | xargs -I {} security delete-certificate -Z {} ~/Library/Keychains/login.keychain-db 2>/dev/null || true
echo "已清理钥匙串中的证书"

./import_certificates.sh 20241218
# To fix /Volumes/JoyCode errors, DO NOT RUN IN VOID!!!
# To fix permission errors, sudo chmod -r +rwx ~/Desktop/void
# Run in sudo if have errors
# Build, sign and package arm64

# 清理 内置构建
rm -rf ../extensions/ai-resource/out && rm -rf ../extensions/clouddev/out

# 编译内置扩展
cd .. && npm run gulp compile-extension:ai-resource && cd JoyCoder-release-scripts
cd .. && npm run gulp compile-extension:clouddev && cd JoyCoder-release-scripts

 ./mac-sign.sh buildDev arm64 dev
# 2c1d27132f1f05b058f67bc8bb7cbe71e7bf2027
npm run update-commit bf58862d8d60ad8747b8588ad1b8c1ae956ddad6

./cp-vsix.sh joycoder-editor joycode
./move-joycoder-extension.sh VSCode-darwin-arm64 joycode.joycoder-editor

./mac-sign.sh sign arm64
./mac-sign.sh notarize arm64
./mac-sign.sh rawapp arm64
# ./mac-sign.sh hashrawapp arm64
# ./mac-sign.sh packagereh arm64

