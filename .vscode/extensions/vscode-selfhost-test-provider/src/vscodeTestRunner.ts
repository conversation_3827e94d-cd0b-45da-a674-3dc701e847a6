/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

import { spawn } from 'child_process';
import { promises as fs } from 'fs';
import { AddressInfo, createServer } from 'net';
import * as path from 'path';
import * as vscode from 'vscode';
import { TestOutputScanner } from './testOutputScanner';
import { TestCase, TestFile, TestSuite, itemData } from './testTree';

/**
 * From MDN
 * @see https://developer.mozilla.org/en-US/docs/Web/JavaScript/Guide/Regular_Expressions#Escaping
 */
const escapeRe = (s: string) => s.replace(/[.*+\-?^${}()|[\]\\]/g, '\\$&');

/**
 * 验证路径是否安全，不包含危险字符
 */
function isSecurePath(filePath: string): boolean {
	if (!filePath || typeof filePath !== 'string') {
		return false;
	}

	// 检查危险字符
	const dangerousChars = /[<>"'&;|`$(){}[\]]/;
	if (dangerousChars.test(filePath)) {
		return false;
	}

	// 检查路径遍历
	if (filePath.includes('..') || filePath.includes('~')) {
		return false;
	}

	// 检查空字节
	if (filePath.includes('\0')) {
		return false;
	}

	// 检查路径长度
	if (filePath.length > 1000) {
		return false;
	}

	return true;
}

/**
 * 验证命令参数是否安全
 */
function validateArgs(args: ReadonlyArray<string>): boolean {
	if (!Array.isArray(args)) {
		return false;
	}

	for (const arg of args) {
		if (!arg || typeof arg !== 'string') {
			return false;
		}

		// 检查危险字符
		if (arg.includes(';') || arg.includes('&') || arg.includes('|') ||
			arg.includes('`') || arg.includes('\n') || arg.includes('\r') ||
			arg.includes('\0')) {
			return false;
		}

		// 检查命令替换模式
		if (arg.includes('$(') || arg.includes('`')) {
			return false;
		}

		// 检查参数长度
		if (arg.length > 2000) {
			return false;
		}
	}

	return true;
}

/**
 * 安全的 spawn 包装函数
 */
function secureSpawn(command: string, args: ReadonlyArray<string>, options: any) {
	// 验证命令路径
	if (!isSecurePath(command)) {
		throw new Error('Command path contains unsafe characters');
	}

	// 验证参数
	if (!validateArgs(args)) {
		throw new Error('Command arguments contain unsafe characters');
	}

	// 验证工作目录
	if (options.cwd && !isSecurePath(options.cwd)) {
		throw new Error('Working directory path contains unsafe characters');
	}

	// 确保路径是绝对路径
	const resolvedCommand = path.resolve(command);
	const resolvedCwd = options.cwd ? path.resolve(options.cwd) : undefined;

	// 验证解析后的路径
	if (!isSecurePath(resolvedCommand) || (resolvedCwd && !isSecurePath(resolvedCwd))) {
		throw new Error('Resolved paths contain unsafe characters');
	}

	return spawn(resolvedCommand, args, {
		...options,
		cwd: resolvedCwd,
		stdio: options.stdio || 'pipe',
		timeout: options.timeout || 300000, // 5 minute timeout
	});
}

const TEST_ELECTRON_SCRIPT_PATH = 'test/unit/electron/index.js';
const TEST_BROWSER_SCRIPT_PATH = 'test/unit/browser/index.js';

const ATTACH_CONFIG_NAME = 'Attach to VS Code';
const DEBUG_TYPE = 'pwa-chrome';

export abstract class VSCodeTestRunner {
	constructor(protected readonly repoLocation: vscode.WorkspaceFolder) { }

	public async run(baseArgs: ReadonlyArray<string>, filter?: ReadonlyArray<vscode.TestItem>) {
		const args = this.prepareArguments(baseArgs, filter);
		const binaryPath = await this.binaryPath();

		// 使用安全的 spawn 包装函数
		const cp = secureSpawn(binaryPath, args, {
			cwd: this.repoLocation.uri.fsPath,
			stdio: 'pipe',
			env: this.getEnvironment(),
		});

		return new TestOutputScanner(cp, args);
	}

	public async debug(testRun: vscode.TestRun, baseArgs: ReadonlyArray<string>, filter?: ReadonlyArray<vscode.TestItem>) {
		const port = await this.findOpenPort();
		const baseConfiguration = vscode.workspace
			.getConfiguration('launch', this.repoLocation)
			.get<vscode.DebugConfiguration[]>('configurations', [])
			.find(c => c.name === ATTACH_CONFIG_NAME);

		if (!baseConfiguration) {
			throw new Error(`Could not find launch configuration ${ATTACH_CONFIG_NAME}`);
		}

		const server = this.createWaitServer();
		const args = [
			...this.prepareArguments(baseArgs, filter),
			`--remote-debugging-port=${port}`,
			// for breakpoint freeze: https://github.com/microsoft/vscode/issues/122225#issuecomment-885377304
			'--js-flags="--regexp_interpret_all"',
			// for general runtime freezes: https://github.com/microsoft/vscode/issues/127861#issuecomment-904144910
			'--disable-features=CalculateNativeWinOcclusion',
			'--timeout=0',
			`--waitServer=${server.port}`,
		];

		const binaryPath = await this.binaryPath();

		// 使用安全的 spawn 包装函数
		const cp = secureSpawn(binaryPath, args, {
			cwd: this.repoLocation.uri.fsPath,
			stdio: 'pipe',
			env: this.getEnvironment(port),
		});

		// Register a descriptor factory that signals the server when any
		// breakpoint set requests on the debugee have been completed.
		const factory = vscode.debug.registerDebugAdapterTrackerFactory(DEBUG_TYPE, {
			createDebugAdapterTracker(session) {
				if (!session.parentSession || session.parentSession !== rootSession) {
					return;
				}

				let initRequestId: number | undefined;

				return {
					onDidSendMessage(message) {
						if (message.type === 'response' && message.request_seq === initRequestId) {
							server.ready();
						}
					},
					onWillReceiveMessage(message) {
						if (initRequestId !== undefined) {
							return;
						}

						if (message.command === 'launch' || message.command === 'attach') {
							initRequestId = message.seq;
						}
					},
				};
			},
		});

		vscode.debug.startDebugging(this.repoLocation, { ...baseConfiguration, port }, { testRun });

		let exited = false;
		let rootSession: vscode.DebugSession | undefined;
		cp.once('exit', () => {
			exited = true;
			server.dispose();
			listener.dispose();
			factory.dispose();

			if (rootSession) {
				vscode.debug.stopDebugging(rootSession);
			}
		});

		const listener = vscode.debug.onDidStartDebugSession(s => {
			if (s.name === ATTACH_CONFIG_NAME && !rootSession) {
				if (exited) {
					vscode.debug.stopDebugging(rootSession);
				} else {
					rootSession = s;
				}
			}
		});

		return new TestOutputScanner(cp, args);
	}

	private findOpenPort(): Promise<number> {
		return new Promise((resolve, reject) => {
			const server = createServer();
			server.listen(0, () => {
				const address = server.address() as AddressInfo;
				const port = address.port;
				server.close(() => {
					resolve(port);
				});
			});
			server.on('error', (error: Error) => {
				reject(error);
			});
		});
	}

	protected getEnvironment(_remoteDebugPort?: number): NodeJS.ProcessEnv {
		return {
			...process.env,
			ELECTRON_RUN_AS_NODE: undefined,
			ELECTRON_ENABLE_LOGGING: '1',
		};
	}

	private prepareArguments(
		baseArgs: ReadonlyArray<string>,
		filter?: ReadonlyArray<vscode.TestItem>
	) {
		const args = [...this.getDefaultArgs(), ...baseArgs, '--reporter', 'full-json-stream'];
		if (!filter) {
			return args;
		}

		const grepRe: string[] = [];
		const runPaths = new Set<string>();
		const addTestFileRunPath = (data: TestFile) =>
			runPaths.add(
				path.relative(data.workspaceFolder.uri.fsPath, data.uri.fsPath).replace(/\\/g, '/')
			);

		const itemDatas = filter.map(f => itemData.get(f));
		/** If true, we have to be careful with greps, as a grep for one test file affects the run of the other test file. */
		const hasBothTestCaseOrTestSuiteAndTestFileFilters =
			itemDatas.some(d => (d instanceof TestCase) || (d instanceof TestSuite)) &&
			itemDatas.some(d => d instanceof TestFile);

		function addTestCaseOrSuite(data: TestCase | TestSuite, test: vscode.TestItem): void {
			grepRe.push(escapeRe(data.fullName) + (data instanceof TestCase ? '$' : ' '));
			for (let p = test.parent; p; p = p.parent) {
				const parentData = itemData.get(p);
				if (parentData instanceof TestFile) {
					addTestFileRunPath(parentData);
				}
			}
		}

		for (const test of filter) {
			const data = itemData.get(test);
			if (data instanceof TestCase || data instanceof TestSuite) {
				addTestCaseOrSuite(data, test);
			} else if (data instanceof TestFile) {
				if (!hasBothTestCaseOrTestSuiteAndTestFileFilters) {
					addTestFileRunPath(data);
				} else {
					// We add all the items individually so they get their own grep expressions.
					for (const [_id, nestedTest] of test.children) {
						const childData = itemData.get(nestedTest);
						if (childData instanceof TestCase || childData instanceof TestSuite) {
							addTestCaseOrSuite(childData, nestedTest);
						} else {
							console.error('Unexpected test item in test file', nestedTest.id, nestedTest.label);
						}
					}
				}
			}
		}

		if (grepRe.length) {
			args.push('--grep', `/^(${grepRe.join('|')})/`);
		}

		if (runPaths.size) {
			args.push(...[...runPaths].flatMap(p => ['--run', p]));
		}

		return args;
	}

	protected abstract getDefaultArgs(): string[];

	protected abstract binaryPath(): Promise<string>;

	protected async readProductJson() {
		const projectJson = await fs.readFile(
			path.join(this.repoLocation.uri.fsPath, 'product.json'),
			'utf-8'
		);
		try {
			return JSON.parse(projectJson);
		} catch (e) {
			throw new Error(`Error parsing product.json: ${(e as Error).message}`);
		}
	}

	private createWaitServer() {
		const onReady = new vscode.EventEmitter<void>();
		let ready = false;

		const server = createServer(socket => {
			if (ready) {
				socket.end();
			} else {
				onReady.event(() => socket.end());
			}
		});

		server.listen(0);

		return {
			port: (server.address() as AddressInfo).port,
			ready: () => {
				ready = true;
				onReady.fire();
			},
			dispose: () => {
				server.close();
			},
		};
	}
}

export class BrowserTestRunner extends VSCodeTestRunner {
	/** @override */
	protected binaryPath(): Promise<string> {
		const execPath = process.execPath;

		// 验证可执行文件路径的安全性
		if (!isSecurePath(execPath)) {
			throw new Error('Executable path contains unsafe characters');
		}

		return Promise.resolve(path.resolve(execPath));
	}

	/** @override */
	protected override getEnvironment(remoteDebugPort?: number) {
		return {
			...super.getEnvironment(remoteDebugPort),
			PLAYWRIGHT_CHROMIUM_DEBUG_PORT: remoteDebugPort ? String(remoteDebugPort) : undefined,
			ELECTRON_RUN_AS_NODE: '1',
		};
	}

	/** @override */
	protected getDefaultArgs() {
		return [TEST_BROWSER_SCRIPT_PATH];
	}
}

export class WindowsTestRunner extends VSCodeTestRunner {
	/** @override */
	protected async binaryPath() {
		const { nameShort } = await this.readProductJson();

		// 验证产品名称的安全性
		if (!nameShort || typeof nameShort !== 'string' || !/^[a-zA-Z0-9\-_]+$/.test(nameShort)) {
			throw new Error('Invalid product name in product.json');
		}

		const binaryPath = path.join(this.repoLocation.uri.fsPath, `.build/electron/${nameShort}.exe`);
		const resolvedPath = path.resolve(binaryPath);

		// 验证生成的路径安全性
		if (!isSecurePath(resolvedPath)) {
			throw new Error('Generated binary path contains unsafe characters');
		}

		// 确保路径在预期的目录范围内
		const repoPath = path.resolve(this.repoLocation.uri.fsPath);
		if (!resolvedPath.startsWith(repoPath)) {
			throw new Error('Binary path is outside repository directory');
		}

		return resolvedPath;
	}

	/** @override */
	protected getDefaultArgs() {
		return [TEST_ELECTRON_SCRIPT_PATH];
	}
}

export class PosixTestRunner extends VSCodeTestRunner {
	/** @override */
	protected async binaryPath() {
		const { applicationName } = await this.readProductJson();

		// 验证应用程序名称的安全性
		if (!applicationName || typeof applicationName !== 'string' || !/^[a-zA-Z0-9\-_]+$/.test(applicationName)) {
			throw new Error('Invalid application name in product.json');
		}

		const binaryPath = path.join(this.repoLocation.uri.fsPath, `.build/electron/${applicationName}`);
		const resolvedPath = path.resolve(binaryPath);

		// 验证生成的路径安全性
		if (!isSecurePath(resolvedPath)) {
			throw new Error('Generated binary path contains unsafe characters');
		}

		// 确保路径在预期的目录范围内
		const repoPath = path.resolve(this.repoLocation.uri.fsPath);
		if (!resolvedPath.startsWith(repoPath)) {
			throw new Error('Binary path is outside repository directory');
		}

		return resolvedPath;
	}

	/** @override */
	protected getDefaultArgs() {
		return [TEST_ELECTRON_SCRIPT_PATH];
	}
}

export class DarwinTestRunner extends PosixTestRunner {
	/** @override */
	protected override getDefaultArgs() {
		return [
			TEST_ELECTRON_SCRIPT_PATH,
			'--no-sandbox',
			'--disable-dev-shm-usage',
			'--use-gl=swiftshader',
		];
	}

	/** @override */
	protected override async binaryPath() {
		const { nameLong } = await this.readProductJson();

		// 验证产品长名称的安全性
		if (!nameLong || typeof nameLong !== 'string' || !/^[a-zA-Z0-9\-_\s]+$/.test(nameLong)) {
			throw new Error('Invalid product long name in product.json');
		}

		const binaryPath = path.join(
			this.repoLocation.uri.fsPath,
			`.build/electron/${nameLong}.app/Contents/MacOS/Electron`
		);
		const resolvedPath = path.resolve(binaryPath);

		// 验证生成的路径安全性
		if (!isSecurePath(resolvedPath)) {
			throw new Error('Generated binary path contains unsafe characters');
		}

		// 确保路径在预期的目录范围内
		const repoPath = path.resolve(this.repoLocation.uri.fsPath);
		if (!resolvedPath.startsWith(repoPath)) {
			throw new Error('Binary path is outside repository directory');
		}

		return resolvedPath;
	}
}

export const PlatformTestRunner =
	process.platform === 'win32'
		? WindowsTestRunner
		: process.platform === 'darwin'
			? DarwinTestRunner
			: PosixTestRunner;
