{"displayName": "GitHub", "description": "JoyCode 的 GitHub 功能", "command.copyVscodeDevLink": "复制 vscode.dev 链接", "command.publish": "发布到 GitHub", "command.openOnGitHub": "在 GitHub 上打开", "command.openOnVscodeDev": "在 vscode.dev 中打开", "config.branchProtection": "控制是否查询 GitHub 存储库的存储库规则", "config.gitAuthentication": "控制是否为 JoyCode 中的 git 命令启用自动 GitHub 身份验证。", "config.gitProtocol": "控制用于克隆 GitHub 存储库的协议", "config.showAvatar": "控制是否在各种悬停中显示提交作者的 GitHub 头像（例如：Git 责任、时间线、源代码管理图等）", "welcome.publishFolder": {"message": "您可以直接将此文件夹发布到 GitHub 存储库。发布后，您将可以访问由 Git 和 GitHub 提供支持的源代码管理功能。\n[$(github) 发布到 GitHub](command:github.publish)", "comment": ["{Locked='$(github)'}", "Do not translate '$(github)'. It will be rendered as an icon", "{Locked='](command:github.publish'}", "Do not translate the 'command:*' part inside of the '(..)'. It is an internal command syntax for JoyCode", "Please make sure there is no space between the right bracket and left parenthesis:  ]( this is an internal syntax for links"]}, "welcome.publishWorkspaceFolder": {"message": "您可以直接将工作区文件夹发布到 GitHub 存储库。发布后，您将可以访问由 Git 和 GitHub 提供支持的源代码管理功能。\n[$(github) 发布到 GitHub](command:github.publish)", "comment": ["{Locked='$(github)'}", "Do not translate '$(github)'. It will be rendered as an icon", "{Locked='](command:github.publish'}", "Do not translate the 'command:*' part inside of the '(..)'. It is an internal command syntax for JoyCode", "Please make sure there is no space between the right bracket and left parenthesis:  ]( this is an internal syntax for links"]}}