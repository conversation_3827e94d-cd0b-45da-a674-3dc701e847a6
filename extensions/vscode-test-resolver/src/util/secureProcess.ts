/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

import * as cp from 'child_process';
import * as path from 'path';
import * as fs from 'fs';

/**
 * Security utilities for safe process execution
 */

export interface SecureSpawnResult {
	success: boolean;
	result?: cp.SpawnSyncReturns<Buffer>;
	error?: Error;
}

export interface SecureExecResult {
	success: boolean;
	output?: string;
	error?: Error;
}

/**
 * Validates that a path is safe and doesn't contain path traversal attempts
 */
function validatePath(inputPath: string): boolean {
	if (!inputPath || typeof inputPath !== 'string') {
		return false;
	}

	// Normalize the path to resolve any relative components
	const normalizedPath = path.normalize(inputPath);

	// Check for path traversal attempts
	if (normalizedPath.includes('..') || normalizedPath.includes('~')) {
		return false;
	}

	// Check for null bytes (common in injection attacks)
	if (normalizedPath.includes('\0')) {
		return false;
	}

	return true;
}

/**
 * Validates that a PID is a valid positive integer
 */
function validatePid(pid: string): boolean {
	if (!pid || typeof pid !== 'string') {
		return false;
	}

	const pidNum = parseInt(pid, 10);
	return !isNaN(pidNum) && pidNum > 0 && pidNum.toString() === pid;
}

/**
 * Validates file extension against allowed extensions
 */
function validateFileExtension(filePath: string, allowedExtensions: string[]): boolean {
	if (!filePath || !allowedExtensions || allowedExtensions.length === 0) {
		return false;
	}

	const ext = path.extname(filePath).toLowerCase();
	return allowedExtensions.includes(ext);
}

/**
 * Safely executes a script file with validated arguments
 */
export function secureExecuteScript(scriptPath: string, args: string[], extensionPath: string): SecureSpawnResult {
	try {
		// Validate extension path
		if (!validatePath(extensionPath)) {
			return { success: false, error: new Error('Invalid extension path') };
		}

		// Ensure script path is within extension directory
		const resolvedScriptPath = path.resolve(extensionPath, scriptPath);
		const resolvedExtensionPath = path.resolve(extensionPath);

		if (!resolvedScriptPath.startsWith(resolvedExtensionPath)) {
			return { success: false, error: new Error('Script path outside extension directory') };
		}

		// Validate script file exists and has correct extension
		if (!fs.existsSync(resolvedScriptPath)) {
			return { success: false, error: new Error('Script file does not exist') };
		}

		if (!validateFileExtension(resolvedScriptPath, ['.sh', '.bat', '.cmd'])) {
			return { success: false, error: new Error('Invalid script file extension') };
		}

		// Validate all arguments
		for (const arg of args) {
			if (!validatePath(arg) && !validatePid(arg)) {
				return { success: false, error: new Error('Invalid argument detected') };
			}
		}

		const result = cp.spawnSync(resolvedScriptPath, args, {
			stdio: ['pipe', 'pipe', 'pipe'],
			timeout: 30000, // 30 second timeout
			killSignal: 'SIGTERM'
		});

		return { success: true, result };
	} catch (error) {
		return { success: false, error: error as Error };
	}
}

/**
 * Safely executes PowerShell commands with validated arguments
 */
export function secureExecutePowerShell(archivePath: string, destinationPath: string): SecureSpawnResult {
	try {
		// Validate paths
		if (!validatePath(archivePath) || !validatePath(destinationPath)) {
			return { success: false, error: new Error('Invalid file paths') };
		}

		// Validate archive file extension
		if (!validateFileExtension(archivePath, ['.zip'])) {
			return { success: false, error: new Error('Invalid archive file extension') };
		}

		// Ensure paths exist
		if (!fs.existsSync(archivePath)) {
			return { success: false, error: new Error('Archive file does not exist') };
		}

		// Resolve absolute paths to prevent injection
		const resolvedArchivePath = path.resolve(archivePath);
		const resolvedDestinationPath = path.resolve(destinationPath);

		// Construct safe PowerShell command
		const safeCommand = `Microsoft.PowerShell.Archive\\Expand-Archive -Path "${resolvedArchivePath}" -DestinationPath "${resolvedDestinationPath}"`;

		const result = cp.spawnSync('powershell.exe', [
			'-NoProfile',
			'-ExecutionPolicy', 'Bypass',
			'-NonInteractive',
			'-NoLogo',
			'-Command',
			safeCommand
		], {
			stdio: ['pipe', 'pipe', 'pipe'],
			timeout: 300000, // 5 minute timeout for extraction
			killSignal: 'SIGTERM'
		});

		return { success: true, result };
	} catch (error) {
		return { success: false, error: error as Error };
	}
}

/**
 * Safely executes unzip command with validated arguments
 */
export function secureExecuteUnzip(archivePath: string, destinationPath: string): SecureSpawnResult {
	try {
		// Validate paths
		if (!validatePath(archivePath) || !validatePath(destinationPath)) {
			return { success: false, error: new Error('Invalid file paths') };
		}

		// Validate archive file extension
		if (!validateFileExtension(archivePath, ['.zip'])) {
			return { success: false, error: new Error('Invalid archive file extension') };
		}

		// Ensure archive exists
		if (!fs.existsSync(archivePath)) {
			return { success: false, error: new Error('Archive file does not exist') };
		}

		// Resolve absolute paths
		const resolvedArchivePath = path.resolve(archivePath);
		const resolvedDestinationPath = path.resolve(destinationPath);

		const result = cp.spawnSync('unzip', [resolvedArchivePath, '-d', resolvedDestinationPath], {
			stdio: ['pipe', 'pipe', 'pipe'],
			timeout: 300000, // 5 minute timeout
			killSignal: 'SIGTERM'
		});

		return { success: true, result };
	} catch (error) {
		return { success: false, error: error as Error };
	}
}

/**
 * Safely executes tar command with validated arguments
 */
export function secureExecuteTar(archivePath: string, destinationPath: string): SecureSpawnResult {
	try {
		// Validate paths
		if (!validatePath(archivePath) || !validatePath(destinationPath)) {
			return { success: false, error: new Error('Invalid file paths') };
		}

		// Validate archive file extension
		if (!validateFileExtension(archivePath, ['.tgz', '.tar.gz', '.tar'])) {
			return { success: false, error: new Error('Invalid archive file extension') };
		}

		// Ensure archive exists
		if (!fs.existsSync(archivePath)) {
			return { success: false, error: new Error('Archive file does not exist') };
		}

		// Resolve absolute paths
		const resolvedArchivePath = path.resolve(archivePath);
		const resolvedDestinationPath = path.resolve(destinationPath);

		const result = cp.spawnSync('tar', ['-xzf', resolvedArchivePath, '-C', resolvedDestinationPath, '--strip-components', '1'], {
			stdio: ['pipe', 'pipe', 'pipe'],
			timeout: 300000, // 5 minute timeout
			killSignal: 'SIGTERM'
		});

		return { success: true, result };
	} catch (error) {
		return { success: false, error: error as Error };
	}
}

/**
 * Safely executes taskkill command with validated PID
 */
export function secureExecuteTaskkill(pid: string): SecureSpawnResult {
	try {
		// Validate PID
		if (!validatePid(pid)) {
			return { success: false, error: new Error('Invalid PID') };
		}

		const result = cp.spawnSync('taskkill', ['/T', '/F', '/PID', pid], {
			stdio: ['pipe', 'pipe', 'pipe'],
			timeout: 30000, // 30 second timeout
			killSignal: 'SIGTERM'
		});

		return { success: true, result };
	} catch (error) {
		return { success: false, error: error as Error };
	}
}

/**
 * Validates that a command is in the allowed list of safe commands
 */
function validateCommand(command: string, allowedCommands: string[]): boolean {
	if (!command || typeof command !== 'string') {
		return false;
	}

	// Check if command is in allowed list
	return allowedCommands.includes(command);
}

/**
 * Validates script arguments to prevent injection
 */
function validateScriptArgs(args: string[]): boolean {
	if (!Array.isArray(args)) {
		return false;
	}

	for (const arg of args) {
		if (!arg || typeof arg !== 'string') {
			return false;
		}

		// Check for dangerous characters that could be used for injection
		if (arg.includes(';') || arg.includes('&') || arg.includes('|') ||
			arg.includes('`') || arg.includes('$') || arg.includes('\n') ||
			arg.includes('\r') || arg.includes('\0')) {
			return false;
		}

		// Check for command substitution patterns
		if (arg.includes('$(') || arg.includes('`')) {
			return false;
		}
	}

	return true;
}

/**
 * Safely executes a file with validated arguments and working directory
 */
export function secureExecFileSync(
	command: string,
	args: string[],
	options: { cwd?: string; timeout?: number; encoding?: BufferEncoding } = {}
): SecureExecResult {
	try {
		// Validate command against allowed list
		const allowedCommands = ['node', 'npm', 'yarn', 'tsc', 'git'];
		if (!validateCommand(command, allowedCommands)) {
			return { success: false, error: new Error(`Command '${command}' is not allowed`) };
		}

		// Validate arguments
		if (!validateScriptArgs(args)) {
			return { success: false, error: new Error('Invalid or potentially dangerous arguments detected') };
		}

		// Validate working directory if provided
		if (options.cwd) {
			if (!validatePath(options.cwd)) {
				return { success: false, error: new Error('Invalid working directory path') };
			}

			// Ensure working directory exists
			if (!fs.existsSync(options.cwd)) {
				return { success: false, error: new Error('Working directory does not exist') };
			}

			// Ensure it's actually a directory
			if (!fs.lstatSync(options.cwd).isDirectory()) {
				return { success: false, error: new Error('Working directory path is not a directory') };
			}

			// Resolve to absolute path to prevent injection
			options.cwd = path.resolve(options.cwd);
		}

		// Set safe defaults
		const safeOptions: cp.ExecFileSyncOptions = {
			windowsHide: true,
			timeout: options.timeout || 10000, // Default 10 second timeout
			encoding: options.encoding || 'utf-8',
			cwd: options.cwd,
			stdio: ['pipe', 'pipe', 'pipe']
		};

		const output = cp.execFileSync(command, args, safeOptions);

		return {
			success: true,
			output: typeof output === 'string' ? output : output.toString()
		};
	} catch (error) {
		return { success: false, error: error as Error };
	}
}
