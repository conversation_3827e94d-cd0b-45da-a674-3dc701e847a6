/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/
import * as cp from 'child_process';
import * as path from 'path';
import { secureExecuteScript, secureExecuteTaskkill } from './secureProcess';

export interface TerminateResponse {
	success: boolean;
	error?: any;
}

export function terminateProcess(p: cp.ChildProcess, extensionPath: string): TerminateResponse {
	if (!p.pid) {
		return { success: false, error: new Error('Process PID is not available') };
	}

	if (process.platform === 'win32') {
		const result = secureExecuteTaskkill(p.pid.toString());
		if (!result.success) {
			return { success: false, error: result.error };
		}
	} else if (process.platform === 'darwin' || process.platform === 'linux') {
		const scriptPath = path.join('scripts', 'terminateProcess.sh');
		const result = secureExecuteScript(scriptPath, [p.pid.toString()], extensionPath);
		if (!result.success) {
			return { success: false, error: result.error };
		}
		if (result.result && result.result.error) {
			return { success: false, error: result.result.error };
		}
	} else {
		p.kill('SIGKILL');
	}
	return { success: true };
}
