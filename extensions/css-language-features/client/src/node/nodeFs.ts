/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

import * as fs from 'fs';
import * as path from 'path';
import { Uri } from 'vscode';
import { RequestService, FileType } from '../requests';

export function getNodeFSRequestService(): RequestService {
	/**
	 * 验证和清理 URI 输入
	 * @param location URI 字符串
	 * @returns 验证后的安全 URI 字符串
	 */
	function validateAndSanitizeUri(location: string): string {
		if (!location || typeof location !== 'string') {
			throw new Error('Invalid location parameter');
		}

		// 清理输入，移除危险字符
		const sanitized = location
			.replace(/[<>"'&;|`$(){}[\]]/g, '') // 移除危险字符
			.trim();

		if (!sanitized || sanitized.length === 0) {
			throw new Error('Invalid sanitized location');
		}

		// 检查长度限制
		if (sanitized.length > 2000) {
			throw new Error('Location URI too long');
		}

		return sanitized;
	}

	/**
	 * 安全地解析和验证文件 URI
	 * @param location URI 字符串
	 * @returns 验证后的安全文件路径
	 */
	function parseAndValidateFileUri(location: string): string {
		// 首先验证和清理输入
		const sanitizedLocation = validateAndSanitizeUri(location);

		// 确保是文件 URI
		if (!sanitizedLocation.startsWith('file://')) {
			throw new Error('fileRequestService can only handle file URLs');
		}

		let uri: Uri;
		try {
			uri = Uri.parse(sanitizedLocation);
		} catch (error) {
			throw new Error(`Invalid URI format: ${error}`);
		}

		// 验证 URI scheme
		if (uri.scheme !== 'file') {
			throw new Error('Only file:// URIs are allowed');
		}

		// 获取文件系统路径
		const fsPath = uri.fsPath;

		// 验证路径安全性
		if (!fsPath || typeof fsPath !== 'string') {
			throw new Error('Invalid file system path');
		}

		// 标准化路径
		const normalizedPath = path.resolve(fsPath);

		// 检查路径遍历
		if (normalizedPath.includes('..') || fsPath.includes('..')) {
			throw new Error('Path traversal detected');
		}

		// 检查危险路径模式
		const dangerousPatterns = [
			'/etc/passwd',
			'/etc/shadow',
			'\\windows\\system32',
			'\\windows\\system',
			'/proc/',
			'/sys/',
			'/dev/'
		];

		const lowerPath = normalizedPath.toLowerCase();
		for (const pattern of dangerousPatterns) {
			if (lowerPath.includes(pattern)) {
				throw new Error('Access to system files is not allowed');
			}
		}

		// 检查文件扩展名白名单（对于 CSS 语言功能）
		const allowedExtensions = [
			'.css', '.scss', '.sass', '.less', '.styl', '.stylus',
			'.json', '.map', '.ts', '.js', '.html', '.htm', '.vue',
			'.svelte', '.astro', '.md', '.txt'
		];

		const ext = path.extname(normalizedPath).toLowerCase();
		if (ext && !allowedExtensions.includes(ext)) {
			console.warn(`Warning: Accessing file with non-standard extension: ${ext}`);
		}

		return normalizedPath;
	}


	return {
		getContent(location: string, encoding?: BufferEncoding) {
			return new Promise((c, e) => {
				try {
					// 使用安全的路径解析和验证
					const safePath = parseAndValidateFileUri(location);

					fs.readFile(safePath, encoding, (err, buf) => {
						if (err) {
							return e(err);
						}
						c(buf.toString());
					});
				} catch (error) {
					e(error);
				}
			});
		},
		stat(location: string) {
			return new Promise((c, e) => {
				try {
					// 使用安全的路径解析和验证
					const safePath = parseAndValidateFileUri(location);

					fs.stat(safePath, (err, stats) => {
						if (err) {
							if (err.code === 'ENOENT') {
								return c({ type: FileType.Unknown, ctime: -1, mtime: -1, size: -1 });
							} else {
								return e(err);
							}
						}

						let type = FileType.Unknown;
						if (stats.isFile()) {
							type = FileType.File;
						} else if (stats.isDirectory()) {
							type = FileType.Directory;
						} else if (stats.isSymbolicLink()) {
							type = FileType.SymbolicLink;
						}

						c({
							type,
							ctime: stats.ctime.getTime(),
							mtime: stats.mtime.getTime(),
							size: stats.size
						});
					});
				} catch (error) {
					e(error);
				}
			});
		},
		readDirectory(location: string) {
			return new Promise((c, e) => {
				try {
					// 使用安全的路径解析和验证
					const safePath = parseAndValidateFileUri(location);

					fs.readdir(safePath, { withFileTypes: true }, (err, children) => {
						if (err) {
							return e(err);
						}
						c(children.map(stat => {
							if (stat.isSymbolicLink()) {
								return [stat.name, FileType.SymbolicLink];
							} else if (stat.isDirectory()) {
								return [stat.name, FileType.Directory];
							} else if (stat.isFile()) {
								return [stat.name, FileType.File];
							} else {
								return [stat.name, FileType.Unknown];
							}
						}));
					});
				} catch (error) {
					e(error);
				}
			});
		}
	};
}
