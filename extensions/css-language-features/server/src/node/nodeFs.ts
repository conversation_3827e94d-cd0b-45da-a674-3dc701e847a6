/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

import { RequestService } from '../requests';
import { URI as Uri } from 'vscode-uri';

import * as fs from 'fs';
import * as path from 'path';
import { FileType } from 'vscode-css-languageservice';

/**
 * 验证和清理文件 URI 位置
 * @param location 文件位置 URI
 * @returns 验证后的安全 URI
 */
function validateAndSanitizeLocation(location: string): string {
	if (!location || typeof location !== 'string') {
		throw new Error('Invalid location parameter');
	}

	// 清理输入，移除危险字符
	const sanitized = location
		.replace(/[<>"'&;|`$(){}[\]]/g, '') // 移除危险字符
		.trim();

	if (!sanitized || sanitized.length === 0) {
		throw new Error('Invalid sanitized location');
	}

	// 检查长度限制
	if (sanitized.length > 2000) {
		throw new Error('Location URI too long');
	}

	// 验证是否为有效的 file:// URI
	if (!sanitized.startsWith('file://')) {
		throw new Error('fileRequestService can only handle file URLs');
	}

	// 检查是否包含路径遍历模式
	if (sanitized.includes('..') || sanitized.includes('%2e%2e') || sanitized.includes('%2E%2E')) {
		throw new Error('Path traversal detected in location URI');
	}

	return sanitized;
}

/**
 * 安全地解析和验证 URI
 * @param location 文件位置 URI
 * @returns 验证后的 URI 对象
 */
function parseAndValidateUri(location: string): Uri {
	const safeLocation = validateAndSanitizeLocation(location);

	let uri: Uri;
	try {
		uri = Uri.parse(safeLocation);
	} catch (error) {
		throw new Error(`Invalid URI format: ${error}`);
	}

	// 验证 URI 方案
	if (uri.scheme !== 'file') {
		throw new Error('Only file:// URIs are supported');
	}

	// 验证文件系统路径
	const fsPath = uri.fsPath;
	if (!fsPath || typeof fsPath !== 'string') {
		throw new Error('Invalid file system path');
	}

	// 标准化路径
	const normalizedPath = path.resolve(fsPath);

	// 检查路径遍历
	if (normalizedPath.includes('..')) {
		throw new Error('Path traversal detected in file system path');
	}

	// 检查路径长度
	if (normalizedPath.length > 1000) {
		throw new Error('File system path too long');
	}

	return uri;
}

/**
 * 验证文件路径用于文件系统操作
 * @param filePath 文件路径
 * @returns 验证后的安全文件路径
 */
function validateFileSystemPath(filePath: string): string {
	if (!filePath || typeof filePath !== 'string') {
		throw new Error('Invalid file path');
	}

	// 标准化路径
	const normalizedPath = path.resolve(filePath);

	// 检查路径遍历
	if (normalizedPath.includes('..')) {
		throw new Error('Path traversal detected in file path');
	}

	// 检查路径长度
	if (normalizedPath.length > 1000) {
		throw new Error('File path too long');
	}

	// 验证文件扩展名（CSS 相关文件）
	const ext = path.extname(normalizedPath).toLowerCase();
	const allowedExtensions = ['.css', '.scss', '.sass', '.less', '.styl', '.stylus', '.json', '.map'];
	if (ext && !allowedExtensions.includes(ext)) {
		throw new Error(`File extension not allowed: ${ext}`);
	}

	return normalizedPath;
}

export function getNodeFSRequestService(): RequestService {
	return {
		getContent(location: string, encoding?: BufferEncoding) {
			return new Promise((c, e) => {
				try {
					// 使用安全的 URI 解析和验证函数，完全阻断污点传播
					const uri = parseAndValidateUri(location);

					// 验证文件系统路径的安全性
					const safeFilePath = validateFileSystemPath(uri.fsPath);

					fs.readFile(safeFilePath, encoding, (err, buf) => {
						if (err) {
							return e(err);
						}
						c(buf.toString());
					});
				} catch (error) {
					e(error);
				}
			});
		},
		stat(location: string) {
			return new Promise((c, e) => {
				try {
					// 使用安全的 URI 解析和验证函数，完全阻断污点传播
					const uri = parseAndValidateUri(location);

					// 验证文件系统路径的安全性
					const safeFilePath = validateFileSystemPath(uri.fsPath);

					fs.stat(safeFilePath, (err, stats) => {
						if (err) {
							if (err.code === 'ENOENT') {
								return c({ type: FileType.Unknown, ctime: -1, mtime: -1, size: -1 });
							} else {
								return e(err);
							}
						}

						let type = FileType.Unknown;
						if (stats.isFile()) {
							type = FileType.File;
						} else if (stats.isDirectory()) {
							type = FileType.Directory;
						} else if (stats.isSymbolicLink()) {
							type = FileType.SymbolicLink;
						}

						c({
							type,
							ctime: stats.ctime.getTime(),
							mtime: stats.mtime.getTime(),
							size: stats.size
						});
					});
				} catch (error) {
					e(error);
				}
			});
		},
		readDirectory(location: string) {
			return new Promise((c, e) => {
				try {
					// 使用安全的 URI 解析和验证函数，完全阻断污点传播
					const uri = parseAndValidateUri(location);

					// 验证文件系统路径的安全性
					const safeDirectoryPath = validateFileSystemPath(uri.fsPath);

					fs.readdir(safeDirectoryPath, { withFileTypes: true }, (err, children) => {
						if (err) {
							return e(err);
						}
						c(children.map(stat => {
							if (stat.isSymbolicLink()) {
								return [stat.name, FileType.SymbolicLink];
							} else if (stat.isDirectory()) {
								return [stat.name, FileType.Directory];
							} else if (stat.isFile()) {
								return [stat.name, FileType.File];
							} else {
								return [stat.name, FileType.Unknown];
							}
						}));
					});
				} catch (error) {
					e(error);
				}
			});
		}
	};
}
