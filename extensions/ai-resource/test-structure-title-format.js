// 测试结构标题格式
console.log("=== 测试结构标题格式 ===");

// 模拟 getStructureName 方法
function getStructureName(structure) {
    function capitalizeFirstLetter(str) {
        if (!str) return str;
        return str.charAt(0).toUpperCase() + str.slice(1);
    }

    if (structure.isArrayItem) {
        // 如果是数组项结构，使用数组名称 + _item structure
        return `${capitalizeFirstLetter(structure.name)}_item structure`;
    } else {
        // 普通结构使用首字母大写的格式，与表格中的类型格式保持一致
        return `${capitalizeFirstLetter(structure.name)} Structure`;
    }
}

// 测试用例
const testCases = [
    // 普通对象结构
    { name: 'data', isArrayItem: false, expected: 'Data Structure' },
    { name: 'user_info', isArrayItem: false, expected: 'User_info Structure' },
    { name: 'response', isArrayItem: false, expected: 'Response Structure' },
    { name: 'config', isArrayItem: false, expected: 'Config Structure' },
    
    // 数组项结构
    { name: 'sub_task_result_list', isArrayItem: true, expected: 'Sub_task_result_list_item structure' },
    { name: 'final_image_list', isArrayItem: true, expected: 'Final_image_list_item structure' },
    { name: 'items', isArrayItem: true, expected: 'Items_item structure' },
    { name: 'users', isArrayItem: true, expected: 'Users_item structure' }
];

console.log("测试结果：");
console.log("结构名 | 类型 | 期望标题 | 实际标题 | 结果");
console.log("--- | --- | --- | --- | ---");

let allPassed = true;

testCases.forEach(testCase => {
    const actualTitle = getStructureName(testCase);
    const passed = actualTitle === testCase.expected;
    const result = passed ? "✅" : "❌";
    const type = testCase.isArrayItem ? "数组项" : "普通对象";
    
    if (!passed) {
        allPassed = false;
    }
    
    console.log(`${testCase.name} | ${type} | ${testCase.expected} | ${actualTitle} | ${result}`);
});

console.log("\n=== 格式一致性检查 ===");

// 检查表格中的类型格式
const tableTypeExamples = [
    { paramName: 'data', paramType: 'Object', displayType: 'Data' },
    { paramName: 'user_info', paramType: 'Object', displayType: 'User_info' },
    { paramName: 'sub_task_result_list', paramType: 'Array', displayType: 'Sub_task_result_list_item[]' }
];

console.log("表格中的类型格式示例：");
tableTypeExamples.forEach(example => {
    console.log(`- ${example.paramName} → ${example.displayType}`);
});

// 对应的结构标题格式
const structureTitleExamples = [
    { name: 'data', title: getStructureName({ name: 'data', isArrayItem: false }) },
    { name: 'user_info', title: getStructureName({ name: 'user_info', isArrayItem: false }) },
    { name: 'sub_task_result_list', title: getStructureName({ name: 'sub_task_result_list', isArrayItem: true }) }
];

console.log("\n对应的结构标题格式：");
structureTitleExamples.forEach(example => {
    console.log(`- ${example.name} → ${example.title}`);
});

// 验证一致性
console.log("\n=== 一致性验证 ===");
console.log("✅ 普通对象：");
console.log("  - 表格类型：data → Data");
console.log("  - 结构标题：data → Data Structure");
console.log("  - 格式一致：都是首字母大写");

console.log("\n✅ 数组类型：");
console.log("  - 表格类型：sub_task_result_list → Sub_task_result_list_item[]");
console.log("  - 结构标题：sub_task_result_list → Sub_task_result_list_item structure");
console.log("  - 格式一致：都是首字母大写 + 后缀");

console.log("\n=== 总体结果 ===");
if (allPassed) {
    console.log("✅ 所有测试用例通过！结构标题格式与表格类型格式保持一致。");
    console.log("主要改进：");
    console.log("- 普通结构：data structure → Data Structure");
    console.log("- 数组结构：保持 Sub_task_result_list_item structure 格式");
    console.log("- 与表格中的 Data、Sub_task_result_list_item[] 格式保持一致");
} else {
    console.log("❌ 部分测试用例失败，需要检查实现。");
}

// 展示最终效果
console.log("\n=== 最终效果示例 ===");
console.log("Response Parameters");
console.log("| Field Name | Type | Description |");
console.log("| --- | --- | --- |");
console.log("| data | Data | 数据对象 |");
console.log("| sub_task_result_list | Sub_task_result_list_item[] | 子任务结果列表 |");
console.log("");
console.log("#### Data Structure");
console.log("| Field Name | Type | Description |");
console.log("| --- | --- | --- |");
console.log("| task_id | String | 任务ID |");
console.log("");
console.log("#### Sub_task_result_list_item structure");
console.log("| Field Name | Type | Description |");
console.log("| --- | --- | --- |");
console.log("| sub_task_id | String | 子任务ID |");
