// 最终测试：验证数组格式修复
console.log("=== 测试数组格式修复 ===");

// 模拟真实的数据结构
const testParams = [
    {
        "paramName": "log_id",
        "paramType": "String",
        "requiredFlag": false,
        "paramDesc": ""
    },
    {
        "paramName": "data",
        "paramType": "Object",
        "requiredFlag": false,
        "paramDesc": "",
        "children": [
            {
                "paramName": "task_id",
                "paramType": "Integer",
                "requiredFlag": false,
                "paramDesc": ""
            },
            {
                "paramName": "sub_task_result_list",
                "paramType": "Array",
                "requiredFlag": false,
                "paramDesc": "",
                "children": [
                    {
                        "paramName": "[Array Item]",
                        "paramType": "Object",
                        "requiredFlag": false,
                        "paramDesc": "",
                        "children": [
                            {
                                "paramName": "sub_task_error_code",
                                "paramType": "Integer",
                                "requiredFlag": false,
                                "paramDesc": ""
                            },
                            {
                                "paramName": "final_image_list",
                                "paramType": "Array",
                                "requiredFlag": false,
                                "paramDesc": "",
                                "children": [
                                    {
                                        "paramName": "[Array Item]",
                                        "paramType": "Object",
                                        "requiredFlag": false,
                                        "paramDesc": "",
                                        "children": [
                                            {
                                                "paramName": "img_url",
                                                "paramType": "String",
                                                "requiredFlag": false,
                                                "paramDesc": ""
                                            }
                                        ]
                                    }
                                ]
                            },
                            {
                                "paramName": "sub_task_id",
                                "paramType": "String",
                                "requiredFlag": false,
                                "paramDesc": ""
                            }
                        ]
                    }
                ]
            }
        ]
    }
];

// 简化的生成逻辑
function generateParametersWithComments(params, indentLevel = 4) {
    let content = "";
    const indent = " ".repeat(indentLevel);
    
    params.forEach((param, index) => {
        const isLast = index === params.length - 1;
        const required = param.requiredFlag ? "必填" : "不必填";
        const comment = `// ${param.paramType}, ${required}, ${param.paramDesc || ""}`;

        // 跳过 [Array Item] 这种特殊的参数名，直接处理其子元素
        if (param.paramName === "[Array Item]" && param.children && param.children.length > 0) {
            content += generateParametersWithComments(param.children, indentLevel);
            return;
        }

        if (param.paramType === "array" || param.paramType === "Array") {
            // 处理数组类型
            content += `${indent}${param.paramName}: [ ${comment}\n`;

            // 如果有子元素定义，生成对象数组
            if (param.children && param.children.length > 0) {
                // 检查子元素是否是 [Array Item]
                const hasArrayItem = param.children.some(child => child.paramName === "[Array Item]");
                if (hasArrayItem) {
                    // 如果有 [Array Item]，直接处理其子元素
                    const arrayItemChild = param.children.find(child => child.paramName === "[Array Item]");
                    if (arrayItemChild && arrayItemChild.children && arrayItemChild.children.length > 0) {
                        content += `${indent}  { // object, 数组元素\n`;
                        content += generateParametersWithComments(arrayItemChild.children, indentLevel + 4);
                        content += `${indent}  }\n`;
                    }
                } else {
                    content += `${indent}  { // object, 数组元素\n`;
                    content += generateParametersWithComments(param.children, indentLevel + 4);
                    content += `${indent}  }\n`;
                }
            } else {
                content += `${indent}  "" // string, 数组元素\n`;
            }

            content += `${indent}]${isLast ? "" : ","}\n`;
        } else if (param.children && param.children.length > 0) {
            // 处理嵌套对象
            content += `${indent}${param.paramName}: { ${comment}\n`;
            content += generateParametersWithComments(param.children, indentLevel + 2);
            content += `${indent}}${isLast ? "" : ","}\n`;
        } else {
            // 处理基本类型
            let value;
            switch (param.paramType.toLowerCase()) {
                case "string": value = '""'; break;
                case "integer": value = "0"; break;
                case "object": value = "{}"; break;
                default: value = '""';
            }
            content += `${indent}${param.paramName}: ${value}${isLast ? "" : ","} ${comment}\n`;
        }
    });
    
    return content;
}

// 生成结果
const result = `**Fetch 响应格式：**
\`\`\`javascript
{
  code: 200, // number, 响应状态码
  msg: "", // string, 响应消息
  data: {
${generateParametersWithComments(testParams, 4)}  }
}
\`\`\``;

console.log(result);

// 验证结果
const hasCorrectFormat = result.includes("sub_task_result_list: [") && 
                        result.includes("final_image_list: [") &&
                        !result.includes("[Array Item]");

console.log("\n=== 验证结果 ===");
if (hasCorrectFormat) {
    console.log("✅ 数组格式修复成功！");
    console.log("- sub_task_result_list 使用了正确的数组格式 []");
    console.log("- final_image_list 使用了正确的数组格式 []");
    console.log("- 不再包含 [Array Item] 这种错误格式");
} else {
    console.log("❌ 数组格式仍然有问题");
    console.log("检查点：");
    console.log("- sub_task_result_list: [", result.includes("sub_task_result_list: ["));
    console.log("- final_image_list: [", result.includes("final_image_list: ["));
    console.log("- 不包含 [Array Item]:", !result.includes("[Array Item]"));
}
