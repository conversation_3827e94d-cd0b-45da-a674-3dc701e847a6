// 测试响应部分的顺序
console.log("=== 测试响应部分的顺序 ===");

// 模拟生成的 Markdown 内容顺序
function generateMockMarkdown() {
    let mdContent = "";
    
    // 1. 响应参数表格
    mdContent += "### 接口出参\n\n";
    mdContent += "| 字段名 | 类型 | 说明 |\n";
    mdContent += "| --- | --- | --- |\n";
    mdContent += "| log_id | String | 日志ID |\n";
    mdContent += "| data | Data | 数据对象 |\n";
    mdContent += "| sub_task_result_list | Sub_task_result_list_item[] | 子任务结果列表 |\n";
    mdContent += "\n";
    
    // 2. 嵌套结构表格
    mdContent += "#### Data structure\n\n";
    mdContent += "| 字段名 | 类型 | 说明 |\n";
    mdContent += "| --- | --- | --- |\n";
    mdContent += "| task_id | Integer | 任务ID |\n";
    mdContent += "| sub_task_result_list | Sub_task_result_list_item[] | 子任务结果列表 |\n";
    mdContent += "\n";
    
    mdContent += "#### Sub_task_result_list_item structure\n\n";
    mdContent += "| 字段名 | 类型 | 说明 |\n";
    mdContent += "| --- | --- | --- |\n";
    mdContent += "| sub_task_error_code | Integer | 子任务错误码 |\n";
    mdContent += "| final_image_list | Final_image_list_item[] | 最终图片列表 |\n";
    mdContent += "| sub_task_id | String | 子任务ID |\n";
    mdContent += "\n";
    
    mdContent += "#### Final_image_list_item structure\n\n";
    mdContent += "| 字段名 | 类型 | 说明 |\n";
    mdContent += "| --- | --- | --- |\n";
    mdContent += "| img_url | String | 图片URL |\n";
    mdContent += "\n";
    
    // 3. Fetch 响应格式（最后）
    mdContent += "**Fetch 响应格式：**\n";
    mdContent += "```javascript\n";
    mdContent += "{\n";
    mdContent += "  code: 200, // number, 响应状态码\n";
    mdContent += "  msg: \"\", // string, 响应消息\n";
    mdContent += "  data: {\n";
    mdContent += "    log_id: \"\", // string, 不必填, 日志ID\n";
    mdContent += "    data: { // object, 不必填, 数据对象\n";
    mdContent += "      task_id: 0, // integer, 不必填, 任务ID\n";
    mdContent += "      sub_task_result_list: [ // array, 不必填, 子任务结果列表\n";
    mdContent += "        { // object, 数组元素\n";
    mdContent += "          sub_task_error_code: 0, // integer, 不必填, 子任务错误码\n";
    mdContent += "          final_image_list: [ // array, 不必填, 最终图片列表\n";
    mdContent += "            { // object, 数组元素\n";
    mdContent += "              img_url: \"\" // string, 不必填, 图片URL\n";
    mdContent += "            }\n";
    mdContent += "          ],\n";
    mdContent += "          sub_task_id: \"\" // string, 不必填, 子任务ID\n";
    mdContent += "        }\n";
    mdContent += "      ]\n";
    mdContent += "    }\n";
    mdContent += "  }\n";
    mdContent += "}\n";
    mdContent += "```\n\n";
    
    return mdContent;
}

// 验证顺序的函数
function validateOrder(content) {
    const sections = [];
    
    // 查找各个部分的位置
    const responseTablePos = content.indexOf("### 接口出参");
    const dataStructurePos = content.indexOf("#### Data structure");
    const subTaskStructurePos = content.indexOf("#### Sub_task_result_list_item structure");
    const finalImageStructurePos = content.indexOf("#### Final_image_list_item structure");
    const fetchResponsePos = content.indexOf("**Fetch 响应格式：**");
    
    if (responseTablePos !== -1) sections.push({ name: "响应参数表格", pos: responseTablePos });
    if (dataStructurePos !== -1) sections.push({ name: "Data 结构", pos: dataStructurePos });
    if (subTaskStructurePos !== -1) sections.push({ name: "Sub_task_result_list_item 结构", pos: subTaskStructurePos });
    if (finalImageStructurePos !== -1) sections.push({ name: "Final_image_list_item 结构", pos: finalImageStructurePos });
    if (fetchResponsePos !== -1) sections.push({ name: "Fetch 响应格式", pos: fetchResponsePos });
    
    // 按位置排序
    sections.sort((a, b) => a.pos - b.pos);
    
    return sections;
}

// 生成测试内容
const mockContent = generateMockMarkdown();
console.log("生成的 Markdown 内容：");
console.log("=".repeat(50));
console.log(mockContent);
console.log("=".repeat(50));

// 验证顺序
const sections = validateOrder(mockContent);
console.log("\n检测到的部分顺序：");
sections.forEach((section, index) => {
    console.log(`${index + 1}. ${section.name} (位置: ${section.pos})`);
});

// 期望的顺序
const expectedOrder = [
    "响应参数表格",
    "Data 结构", 
    "Sub_task_result_list_item 结构",
    "Final_image_list_item 结构",
    "Fetch 响应格式"
];

const actualOrder = sections.map(s => s.name);

console.log("\n=== 顺序验证 ===");
console.log("期望顺序:", expectedOrder);
console.log("实际顺序:", actualOrder);

const isCorrectOrder = JSON.stringify(expectedOrder) === JSON.stringify(actualOrder);

if (isCorrectOrder) {
    console.log("✅ 顺序正确！");
    console.log("- 响应参数表格在最前面");
    console.log("- 嵌套结构表格在中间");
    console.log("- Fetch 响应格式在最后");
} else {
    console.log("❌ 顺序不正确，需要调整");
}

// 检查 Fetch 响应格式是否在最后
const fetchIsLast = sections.length > 0 && sections[sections.length - 1].name === "Fetch 响应格式";
console.log(`\nFetch 响应格式是否在最后: ${fetchIsLast ? '✅ 是' : '❌ 否'}`);

console.log("\n=== 总结 ===");
if (isCorrectOrder && fetchIsLast) {
    console.log("✅ 响应部分的顺序优化成功！");
    console.log("现在的顺序更加合理：先看表格了解结构，最后看具体的 Fetch 使用方式。");
} else {
    console.log("❌ 响应部分的顺序需要进一步调整。");
}
