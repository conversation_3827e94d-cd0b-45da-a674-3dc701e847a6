// 测试引用模式（不包含表格）
console.log("=== 测试引用模式功能 ===");

// 模拟 I18n 类
class MockI18n {
    static t(key, ...args) {
        const translations = {
            "markdown.service.title": "{0} 服务接口API文档",
            "markdown.service.url": "接口地址",
            "markdown.request.params": "接口入参",
            "markdown.response.params": "接口出参",
            "markdown.structure.title": "{0} 结构",
            "markdown.table.paramName": "参数名",
            "markdown.table.fieldName": "字段名",
            "markdown.table.type": "类型",
            "markdown.table.required": "必填",
            "markdown.table.description": "说明",
            "markdown.table.yes": "是",
            "markdown.table.no": "否",
            "markdown.fetch.request.title": "Fetch 请求配置",
            "markdown.fetch.response.title": "Fetch 响应格式"
        };
        
        let message = translations[key] || key;
        if (args.length > 0) {
            args.forEach((arg, index) => {
                message = message.replace(new RegExp(`\\{${index}\\}`, 'g'), String(arg));
            });
        }
        return message;
    }
}

// 简化的 MarkdownContentGenerator 实现
class MarkdownContentGenerator {
    generatePluginMarkdown(data, options = {}) {
        const { includeTable = true } = options;
        let mdContent = `# ${MockI18n.t("markdown.service.title", data.name)}\n\n`;
        mdContent += `${data.pluginDesc || ""}\n\n`;

        if (data.tools && data.tools.length > 0) {
            data.tools.forEach((tool) => {
                const nestedStructures = [];
                const collectNestedStructures = (paramName, children, level = 0) => {
                    nestedStructures.push({
                        name: paramName,
                        children: children,
                        level: level,
                    });
                };

                mdContent += `## ${tool.name}\n\n`;
                mdContent += `${tool.toolDesc || ""}\n\n`;
                mdContent += `### ${MockI18n.t("markdown.service.url")}\n\n\`POST ${tool.toolUrl}\`\n\n`;

                if (tool.requestParams && tool.requestParams.length > 0) {
                    mdContent += this.generateRequestParamsMarkdown(tool.requestParams, includeTable);
                }

                if (tool.responseParams && tool.responseParams.length > 0) {
                    mdContent += this.generateResponseParamsMarkdown(tool.responseParams, collectNestedStructures, includeTable);
                }

                if (includeTable && nestedStructures.length > 0) {
                    mdContent += this.generateNestedStructuresMarkdown(nestedStructures);
                }
            });
        }

        return mdContent;
    }

    generateRequestParamsMarkdown(requestParams, includeTable = true) {
        let mdContent = "";
        
        if (includeTable) {
            mdContent += `### ${MockI18n.t("markdown.request.params")}\n\n`;
            mdContent += `| ${MockI18n.t("markdown.table.paramName")} | ${MockI18n.t("markdown.table.type")} | ${MockI18n.t("markdown.table.required")} | ${MockI18n.t("markdown.table.description")} |\n`;
            mdContent += `| --- | --- | --- | --- |\n`;

            requestParams.forEach((param) => {
                const required = param.requiredFlag ? MockI18n.t("markdown.table.yes") : MockI18n.t("markdown.table.no");
                mdContent += `| ${param.paramName} | ${param.paramType} | ${required} | ${param.paramDesc || ""} |\n`;
            });

            mdContent += "\n";
        }

        // 添加 Fetch 请求配置 JSON
        mdContent += this.generateFetchRequestJson(requestParams);

        return mdContent;
    }

    generateResponseParamsMarkdown(responseParams, collectNestedStructures, includeTable = true) {
        let mdContent = "";
        
        if (includeTable) {
            mdContent += `### ${MockI18n.t("markdown.response.params")}\n\n`;
            mdContent += `| ${MockI18n.t("markdown.table.fieldName")} | ${MockI18n.t("markdown.table.type")} | ${MockI18n.t("markdown.table.description")} |\n`;
            mdContent += `| --- | --- | --- |\n`;

            responseParams.forEach((param) => {
                mdContent += `| ${param.paramName} | ${param.paramType} | ${param.paramDesc || ""} |\n`;
                if (param.children && param.children.length > 0) {
                    collectNestedStructures(param.paramName, param.children);
                }
            });

            mdContent += "\n";
        }

        // 添加 Fetch 响应格式 JSON
        mdContent += this.generateFetchResponseJson(responseParams);

        return mdContent;
    }

    generateFetchRequestJson(requestParams) {
        return `**${MockI18n.t("markdown.fetch.request.title")}：**\n\`\`\`javascript\n// Fetch 请求配置示例\n\`\`\`\n\n`;
    }

    generateFetchResponseJson(responseParams) {
        return `**${MockI18n.t("markdown.fetch.response.title")}：**\n\`\`\`javascript\n// Fetch 响应格式示例\n\`\`\`\n\n`;
    }

    generateNestedStructuresMarkdown(nestedStructures) {
        return "#### 嵌套结构表格\n\n";
    }
}

// 测试数据
const testData = {
    name: "测试工具",
    pluginDesc: "这是一个测试工具",
    tools: [
        {
            name: "测试接口",
            toolDesc: "测试接口描述",
            toolUrl: "/api/test",
            requestParams: [
                {
                    paramName: "query",
                    paramType: "string",
                    requiredFlag: true,
                    paramDesc: "查询参数"
                }
            ],
            responseParams: [
                {
                    paramName: "result",
                    paramType: "string",
                    paramDesc: "结果",
                    children: [
                        {
                            paramName: "data",
                            paramType: "object",
                            paramDesc: "数据"
                        }
                    ]
                }
            ]
        }
    ]
};

const generator = new MarkdownContentGenerator();

console.log("=== 完整模式（包含表格）===");
const fullResult = generator.generatePluginMarkdown(testData, { includeTable: true });
console.log(fullResult);

console.log("\n=== 引用模式（仅 Fetch 部分）===");
const quoteResult = generator.generatePluginMarkdown(testData, { includeTable: false });
console.log(quoteResult);

// 验证结果
const fullHasTables = fullResult.includes("| 参数名 |") && fullResult.includes("| 字段名 |");
const quoteHasNoTables = !quoteResult.includes("| 参数名 |") && !quoteResult.includes("| 字段名 |");
const bothHaveFetch = fullResult.includes("**Fetch 请求配置：**") && quoteResult.includes("**Fetch 请求配置：**");

console.log("\n=== 验证结果 ===");
if (fullHasTables && quoteHasNoTables && bothHaveFetch) {
    console.log("✅ 引用模式功能实现成功！");
    console.log("- 完整模式包含表格");
    console.log("- 引用模式不包含表格");
    console.log("- 两种模式都包含 Fetch 配置");
} else {
    console.log("❌ 引用模式功能有问题");
    console.log("检查点：");
    console.log("- 完整模式包含表格:", fullHasTables);
    console.log("- 引用模式不包含表格:", quoteHasNoTables);
    console.log("- 两种模式都包含 Fetch:", bothHaveFetch);
}
