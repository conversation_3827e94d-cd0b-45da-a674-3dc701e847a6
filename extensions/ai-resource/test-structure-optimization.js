// 测试结构优化功能
console.log("=== 测试结构优化功能 ===");

// 模拟优化后的 collectNestedStructures 逻辑
function testCollectNestedStructures() {
    const nestedStructures = [];
    
    const collectNestedStructures = (paramName, children, level = 0) => {
        // 检查是否是数组类型且只有一个 [Array Item] 子元素
        const hasArrayItem = children.length === 1 && children[0].paramName === "[Array Item]";
        
        if (hasArrayItem) {
            // 如果是数组类型，直接收集 [Array Item] 的子元素，使用父级数组名称
            const arrayItemChild = children[0];
            if (arrayItemChild.children && arrayItemChild.children.length > 0) {
                nestedStructures.push({
                    name: paramName, // 使用数组名称而不是 [Array Item]
                    children: arrayItemChild.children,
                    level: level,
                    isArrayItem: true
                });
                
                // 递归处理 [Array Item] 的子元素
                arrayItemChild.children.forEach((grandChild) => {
                    if (grandChild.children && grandChild.children.length > 0) {
                        collectNestedStructures(
                            grandChild.paramName,
                            grandChild.children,
                            level + 1,
                        );
                    }
                });
            }
        } else {
            // 普通对象结构
            nestedStructures.push({
                name: paramName,
                children: children,
                level: level,
            });
            children.forEach((child) => {
                if (child.children && child.children.length > 0) {
                    collectNestedStructures(
                        child.paramName,
                        child.children,
                        level + 1,
                    );
                }
            });
        }
    };

    // 模拟真实的数据结构
    const testData = [
        {
            paramName: "data",
            paramType: "Object",
            children: [
                {
                    paramName: "task_id",
                    paramType: "Integer",
                    paramDesc: "任务ID"
                },
                {
                    paramName: "sub_task_result_list",
                    paramType: "Array",
                    paramDesc: "子任务结果列表",
                    children: [
                        {
                            paramName: "[Array Item]",
                            paramType: "Object",
                            children: [
                                {
                                    paramName: "sub_task_error_code",
                                    paramType: "Integer",
                                    paramDesc: "子任务错误码"
                                },
                                {
                                    paramName: "final_image_list",
                                    paramType: "Array",
                                    paramDesc: "最终图片列表",
                                    children: [
                                        {
                                            paramName: "[Array Item]",
                                            paramType: "Object",
                                            children: [
                                                {
                                                    paramName: "img_url",
                                                    paramType: "String",
                                                    paramDesc: "图片URL"
                                                }
                                            ]
                                        }
                                    ]
                                },
                                {
                                    paramName: "sub_task_id",
                                    paramType: "String",
                                    paramDesc: "子任务ID"
                                }
                            ]
                        }
                    ]
                }
            ]
        }
    ];

    // 收集嵌套结构
    testData.forEach(param => {
        if (param.children && param.children.length > 0) {
            collectNestedStructures(param.paramName, param.children);
        }
    });

    return nestedStructures;
}

// 模拟 getStructureName 方法
function getStructureName(structure) {
    function capitalizeFirstLetter(str) {
        if (!str) return str;
        return str.charAt(0).toUpperCase() + str.slice(1);
    }

    if (structure.isArrayItem) {
        // 如果是数组项结构，使用数组名称 + _item structure
        return `${capitalizeFirstLetter(structure.name)}_item structure`;
    } else {
        // 普通结构使用原有逻辑
        return `${structure.name} Structure`;
    }
}

// 执行测试
const structures = testCollectNestedStructures();

console.log("收集到的结构：");
structures.forEach((structure, index) => {
    const structureName = getStructureName(structure);
    console.log(`${index + 1}. ${structureName}`);
    console.log(`   - 级别: ${structure.level}`);
    console.log(`   - 是否为数组项: ${structure.isArrayItem || false}`);
    console.log(`   - 子字段数量: ${structure.children.length}`);
    console.log(`   - 子字段: ${structure.children.map(child => child.paramName).join(', ')}`);
    console.log("");
});

// 验证结果
const expectedStructures = [
    "data Structure",
    "Sub_task_result_list_item structure",
    "Final_image_list_item structure"
];

const actualStructures = structures.map(s => getStructureName(s));

console.log("=== 验证结果 ===");
console.log("期望的结构名称:");
expectedStructures.forEach((name, index) => {
    console.log(`${index + 1}. ${name}`);
});

console.log("\n实际的结构名称:");
actualStructures.forEach((name, index) => {
    console.log(`${index + 1}. ${name}`);
});

const isCorrect = JSON.stringify(expectedStructures) === JSON.stringify(actualStructures);

console.log(`\n结果: ${isCorrect ? '✅ 通过' : '❌ 失败'}`);

if (isCorrect) {
    console.log("✅ 结构优化功能正常工作！");
    console.log("- 跳过了中间层的数组结构");
    console.log("- 将 [Array Item] 结构重命名为有意义的名称");
    console.log("- 保持了正确的层级关系");
} else {
    console.log("❌ 结构优化功能有问题，需要检查实现");
}
