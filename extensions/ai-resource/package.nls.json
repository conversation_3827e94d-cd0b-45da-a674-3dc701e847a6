{"displayName": "Resource", "description": "Resource", "viewContainer.title": "Resources", "view.name": "Resources", "command.openSettings.title": "Settings", "command.showAiResources.title": "Show Resources", "config.joycodeDir.description": "The directory name for joycode configuration files", "config.apiKeyFile.description": "The filename for API key configuration", "login.prompt": "For a better experience, please {LOGIN_LINK} to your account", "login.linkText": "login", "success.quote": "Quote successful!", "list.loading": "Loading...", "list.noData": "No data available", "list.noMore": "No more data", "list.quote": "Quote", "search.placeholder": "Search", "environment.error.title": "Environment Error", "environment.error.message": "⚠️ Environment Error", "environment.error.description": "Please use this plugin in JoyCode software", "environment.error.useInJoyCode": "Please use this plugin in JoyCode software!", "login.page.title": "Login JoyCode", "plugin.detail.title": "Plug<PERSON>", "plugin.detail.noInfo": "No details available", "plugin.detail.fetchFailed": "Failed to fetch details, please try again later", "config.instructions": "\n# Configuration Instructions\n\n## API Credentials Configuration\n\n- Read user's api<PERSON>ey and baseUrl from `{0}/resources/{1}` file.\n- In example scenarios, read the apikey.json file and write apiKey and baseUrl directly into the code.\n- For each API access, write authorization information in header, e.g.: Authorization={apiKey}\n- Concatenate baseUrl when making requests\n- This interface has cross-origin access restrictions\n\n## Request Parameter Rules\n- Parameters should be wrapped with \"params\" at the outer layer, example: parameter is query\n```json\n{\n  params:{\n      query:\"test\"\n  }\n}\n```\n## Response Parameter Rules\n- Response data structure should be wrapped with \"data\", example: output is log_id, data\n```json\n{\n  code: 200,\n  msg: \"\",\n  data: {\n    log_id: 'xxx',\n    data: {}\n  }\n}\n```\n\n## Framework Integration\n\nBased on the technology framework used in your project, place apiKey and baseUrl into the project configuration files.\n\n### Naming Convention\n- Namespace: `joycode.resources`\n- Configuration items: \n  - `joycode.resources.api_key`\n  - `joycode.resources.base_url`\n\n### Configuration Examples\n\n#### Spring Boot Projects\n\nConfiguration in `application.yml`:\n\n```yaml\n# application.yml\njoycode:\n  resources:\n    api_key: your-api-key\n    base_url: your-base-url\n```\n\nOr configuration in `application.properties`:\n\n```properties\n# application.properties\njoycode.resources.api_key=your-api-key\njoycode.resources.base_url=your-base-url\n```\n\nFor other frameworks, please configure according to their respective configuration specifications.", "markdown.service.title": "{0} Service Interface API Documentation", "markdown.service.name": "Service Name", "markdown.service.description": "Service Description", "markdown.service.url": "Interface URL", "markdown.request.params": "Request Parameters", "markdown.request.type": "Request Type: `{0}`", "markdown.response.params": "Response Parameters", "markdown.structure.title": "{0} Structure", "markdown.table.paramName": "Parameter Name", "markdown.table.fieldName": "Field Name", "markdown.table.type": "Type", "markdown.table.required": "Required", "markdown.table.description": "Description", "markdown.table.yes": "Yes", "markdown.table.no": "No", "markdown.fetch.request.title": "Fetch Request Configuration", "markdown.fetch.response.title": "Fetch Response Format", "joycode.error.readOnlyFileSystem": "Current environment is read-only file system, cannot update configuration file.", "joycode.error.permissionDenied": "Permission denied, cannot update configuration file. Please check directory permissions.", "joycode.error.noWorkspace": "Cannot get workspace path", "joycode.error.workspaceNotFound": "Workspace not found, cannot save file", "joycode.guide.manualSetup": "Please manually create configuration directory and file:\n\n1. Create {1}/resources folder in project root\n2. Create {2} file in {1}/resources folder\n3. File content example:\n{\n  \"apikey\": \"your-api-key-here\",\n  \"baseUrl\": \"https://joyagent.jd.com\"\n}\n\nTarget path: {0}", "joycode.guide.title": "Cannot automatically create configuration file, please create manually", "joycode.guide.viewSteps": "View detailed steps", "joycode.guide.openDirectory": "Open directory", "settings.title": "Resources Settings", "settings.success.apiKeySaved": "Selected API Key has been saved", "settings.success.keysRefreshed": "API Key list has been refreshed", "settings.warning.noApiKeySelected": "No API Key selected, not saved", "settings.warning.notLoggedIn": "Not logged in, cannot refresh API Key list", "settings.info.gettingNewKey": "Getting new API key...", "settings.info.refreshingKeys": "Refreshing API Key list...", "settings.error.activationGetKeyListFailedMessage": "Failed to get key list during activation", "settings.error.getKeyListFailedMessage": "Failed to get key list", "settings.error.refreshKeyListFailedMessage": "Failed to refresh key list", "settings.loginRequired.title": "<PERSON><PERSON> Required", "settings.loginRequired.message": "Please login to JoyCode to access settings", "settings.loginRequired.loginButton": "<PERSON><PERSON>", "settings.button.refresh": "Refresh", "settings.button.save": "Save", "quote.warning.noWorkspace": "Please open a project folder first to save the quoted Markdown file.", "quote.action.openFolder": "Open Folder", "model.previewMd": "# {0}\n\n## Introduction\n\nThis model interface is compatible with OpenAI standards.\n\n## API URL\n\n`POST {baseUrl}/api/saas/openai-u/v1/chat/completions`\n\n## Request Parameters\n\n### Headers\n| Parameter | Type   | Required | Description |\n| --------- | ------ | -------- | ----------- |\n| Authorization | string | Yes      | {apiKey} |\n\n### Body\n| Parameter | Type   | Required | Description |\n| --------- | ------ | -------- | ----------- |\n| message   | object | Yes      |             |\n| - role    | string | No       |             |\n| - content | string | Yes      |             |\n| model     | string | Yes      |             |\n\n\n## Response Parameters\n\n| Parameter | Type | Description |\n|-----------|------|-------------|\n| created | int | Creation timestamp |\n| usage | object | Usage statistics |\n| - completion_tokens | int | Number of completion tokens |\n| - prompt_tokens | int | Number of prompt tokens |\n| - total_tokens | int | Total number of tokens |\n| model | string | Model name |\n| id | string | Response ID |\n| choices | array | Choice items |\n| - finish_reason | string | Finish reason |\n| - index | int | Choice index |\n| - message | object | Message content |\n| -- role | string | Role (assistant) |\n| -- content | string | Content |", "model.fileMd": "## Additional Interface Usage Instructions\n- Read user's apiKey and baseUrl from `{0}/resources/{1}` file.\n- In example scenarios, read {1} file and write apiKey and baseUrl directly into the code.\n- For each interface access, authorization information should be written in the header, e.g.: Authorization={apiKey}\n- Concatenate baseUrl when making requests"}