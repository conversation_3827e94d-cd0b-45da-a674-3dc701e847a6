// 测试数组格式修复
const fs = require('fs');
const path = require('path');

// 模拟 I18n 类
class MockI18n {
    static t(key, ...args) {
        const translations = {
            "markdown.service.title": "{0} 服务接口API文档",
            "markdown.service.url": "接口地址",
            "markdown.request.params": "接口入参",
            "markdown.response.params": "接口出参",
            "markdown.structure.title": "{0} 结构",
            "markdown.table.paramName": "参数名",
            "markdown.table.fieldName": "字段名",
            "markdown.table.type": "类型",
            "markdown.table.required": "必填",
            "markdown.table.description": "说明",
            "markdown.table.yes": "是",
            "markdown.table.no": "否",
            "markdown.fetch.request.title": "Fetch 请求配置",
            "markdown.fetch.response.title": "Fetch 响应格式"
        };
        
        let message = translations[key] || key;
        if (args.length > 0) {
            args.forEach((arg, index) => {
                message = message.replace(new RegExp(`\\{${index}\\}`, 'g'), String(arg));
            });
        }
        return message;
    }
}

// 读取并执行 TypeScript 编译后的代码
const tsCode = fs.readFileSync(path.join(__dirname, 'src/providers/handlers/markdownContentGenerator.ts'), 'utf8');

// 简化的 MarkdownContentGenerator 实现
class MarkdownContentGenerator {
    generatePluginMarkdown(data) {
        let mdContent = `# ${MockI18n.t("markdown.service.title", data.name)}\n\n`;
        mdContent += `${data.pluginDesc || ""}\n\n`;

        if (data.tools && data.tools.length > 0) {
            data.tools.forEach((tool) => {
                mdContent += `## ${tool.name}\n\n`;
                mdContent += `${tool.toolDesc || ""}\n\n`;
                
                if (tool.responseParams && tool.responseParams.length > 0) {
                    mdContent += this.generateFetchResponseJson(tool.responseParams);
                }
            });
        }

        return mdContent;
    }

    generateFetchResponseJson(responseParams) {
        let mdContent = `**${MockI18n.t("markdown.fetch.response.title")}：**\n`;
        mdContent += "```javascript\n";

        mdContent += "{\n";
        mdContent += "  code: 200, // number, 响应状态码\n";
        mdContent += '  msg: "", // string, 响应消息\n';
        mdContent += "  data: {\n";
        
        mdContent += this.generateParametersWithComments(responseParams, 4);
        
        mdContent += "  }\n";
        mdContent += "}\n";
        mdContent += "```\n\n";

        return mdContent;
    }

    generateParametersWithComments(params, indentLevel = 4) {
        let content = "";
        const indent = " ".repeat(indentLevel);
        
        params.forEach((param, index) => {
            const isLast = index === params.length - 1;
            const required = param.requiredFlag ? "必填" : "不必填";
            const comment = `// ${param.paramType}, ${required}, ${param.paramDesc || ""}`;
            
            if (param.paramType === "array") {
                content += `${indent}${param.paramName}: [ ${comment}\n`;
                
                if (param.children && param.children.length > 0) {
                    content += `${indent}  { // object, 数组元素\n`;
                    content += this.generateParametersWithComments(param.children, indentLevel + 4);
                    content += `${indent}  }\n`;
                } else {
                    const arrayElementType = this.getArrayElementType(param);
                    const arrayElementComment = `// ${arrayElementType}, 数组元素`;
                    content += `${indent}  ${this.getDefaultValueByType(arrayElementType)} ${arrayElementComment}\n`;
                }
                
                content += `${indent}]${isLast ? "" : ","}\n`;
            } else if (param.children && param.children.length > 0) {
                content += `${indent}${param.paramName}: { ${comment}\n`;
                content += this.generateParametersWithComments(param.children, indentLevel + 2);
                content += `${indent}}${isLast ? "" : ","}\n`;
            } else {
                const value = this.getDefaultValueByType(param.paramType);
                content += `${indent}${param.paramName}: ${value}${isLast ? "" : ","} ${comment}\n`;
            }
        });
        
        return content;
    }

    getDefaultValueByType(type) {
        switch (type.toLowerCase()) {
            case "string": return '""';
            case "number":
            case "integer": return "0";
            case "boolean": return "false";
            case "array": return "[]";
            case "object": return "{}";
            default: return '""';
        }
    }

    getArrayElementType(param) {
        if (param.children && param.children.length > 0) {
            return param.children[0].paramType || "string";
        }
        return "string";
    }
}

// 测试数据
const testData = {
    name: "任务管理工具",
    pluginDesc: "测试数组格式修复",
    tools: [
        {
            name: "获取任务详情",
            toolDesc: "测试复杂数组结构",
            responseParams: [
                {
                    paramName: "log_id",
                    paramType: "string",
                    requiredFlag: false,
                    paramDesc: "日志ID"
                },
                {
                    paramName: "data",
                    paramType: "object",
                    requiredFlag: false,
                    paramDesc: "数据对象",
                    children: [
                        {
                            paramName: "task_id",
                            paramType: "integer",
                            requiredFlag: false,
                            paramDesc: "任务ID"
                        },
                        {
                            paramName: "sub_task_result_list",
                            paramType: "array",
                            requiredFlag: false,
                            paramDesc: "子任务结果列表",
                            children: [
                                {
                                    paramName: "sub_task_error_code",
                                    paramType: "integer",
                                    requiredFlag: false,
                                    paramDesc: "子任务错误码"
                                },
                                {
                                    paramName: "final_image_list",
                                    paramType: "array",
                                    requiredFlag: false,
                                    paramDesc: "最终图片列表",
                                    children: [
                                        {
                                            paramName: "img_url",
                                            paramType: "string",
                                            requiredFlag: false,
                                            paramDesc: "图片URL"
                                        }
                                    ]
                                }
                            ]
                        }
                    ]
                }
            ]
        }
    ]
};

const generator = new MarkdownContentGenerator();
const result = generator.generatePluginMarkdown(testData);

console.log("=== 测试结果 ===");
console.log(result);

// 检查是否包含正确的数组格式
const hasCorrectArrayFormat = result.includes("sub_task_result_list: [") && 
                             result.includes("final_image_list: [") &&
                             !result.includes("[Array Item]");

console.log("\n=== 验证结果 ===");
if (hasCorrectArrayFormat) {
    console.log("✅ 数组格式修复成功！");
} else {
    console.log("❌ 数组格式仍然有问题");
}
