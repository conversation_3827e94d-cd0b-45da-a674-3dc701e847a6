// 测试真实数据结构的数组格式修复
const fs = require('fs');
const path = require('path');

// 模拟 I18n 类
class MockI18n {
    static t(key, ...args) {
        const translations = {
            "markdown.service.title": "{0} 服务接口API文档",
            "markdown.service.url": "接口地址",
            "markdown.request.params": "接口入参",
            "markdown.response.params": "接口出参",
            "markdown.structure.title": "{0} 结构",
            "markdown.table.paramName": "参数名",
            "markdown.table.fieldName": "字段名",
            "markdown.table.type": "类型",
            "markdown.table.required": "必填",
            "markdown.table.description": "说明",
            "markdown.table.yes": "是",
            "markdown.table.no": "否",
            "markdown.fetch.request.title": "Fetch 请求配置",
            "markdown.fetch.response.title": "Fetch 响应格式"
        };
        
        let message = translations[key] || key;
        if (args.length > 0) {
            args.forEach((arg, index) => {
                message = message.replace(new RegExp(`\\{${index}\\}`, 'g'), String(arg));
            });
        }
        return message;
    }
}

// 简化的 MarkdownContentGenerator 实现
class MarkdownContentGenerator {
    generateFetchResponseJson(responseParams) {
        let mdContent = `**${MockI18n.t("markdown.fetch.response.title")}：**\n`;
        mdContent += "```javascript\n";

        mdContent += "{\n";
        mdContent += "  code: 200, // number, 响应状态码\n";
        mdContent += '  msg: "", // string, 响应消息\n';
        mdContent += "  data: {\n";
        
        mdContent += this.generateParametersWithComments(responseParams, 4);
        
        mdContent += "  }\n";
        mdContent += "}\n";
        mdContent += "```\n\n";

        return mdContent;
    }

    generateParametersWithComments(params, indentLevel = 4) {
        let content = "";
        const indent = " ".repeat(indentLevel);
        
        params.forEach((param, index) => {
            const isLast = index === params.length - 1;
            const required = param.requiredFlag ? "必填" : "不必填";
            const comment = `// ${param.paramType}, ${required}, ${param.paramDesc || ""}`;

            // 跳过 [Array Item] 这种特殊的参数名，直接处理其子元素
            if (param.paramName === "[Array Item]" && param.children && param.children.length > 0) {
                content += this.generateParametersWithComments(param.children, indentLevel);
                return;
            }

            if (param.paramType === "array") {
                content += `${indent}${param.paramName}: [ ${comment}\n`;
                
                if (param.children && param.children.length > 0) {
                    content += `${indent}  { // object, 数组元素\n`;
                    content += this.generateParametersWithComments(param.children, indentLevel + 4);
                    content += `${indent}  }\n`;
                } else {
                    const arrayElementType = this.getArrayElementType(param);
                    const arrayElementComment = `// ${arrayElementType}, 数组元素`;
                    content += `${indent}  ${this.getDefaultValueByType(arrayElementType)} ${arrayElementComment}\n`;
                }
                
                content += `${indent}]${isLast ? "" : ","}\n`;
            } else if (param.children && param.children.length > 0) {
                content += `${indent}${param.paramName}: { ${comment}\n`;
                content += this.generateParametersWithComments(param.children, indentLevel + 2);
                content += `${indent}}${isLast ? "" : ","}\n`;
            } else {
                const value = this.getDefaultValueByType(param.paramType);
                content += `${indent}${param.paramName}: ${value}${isLast ? "" : ","} ${comment}\n`;
            }
        });
        
        return content;
    }

    getDefaultValueByType(type) {
        switch (type.toLowerCase()) {
            case "string": return '""';
            case "number":
            case "integer": return "0";
            case "boolean": return "false";
            case "array": return "[]";
            case "object": return "{}";
            default: return '""';
        }
    }

    getArrayElementType(param) {
        if (param.children && param.children.length > 0) {
            return param.children[0].paramType || "string";
        }
        return "string";
    }
}

// 使用真实的数据结构进行测试
const realResponseParams = [
    {
        "paramName": "log_id",
        "paramType": "String",
        "requiredFlag": false,
        "paramDesc": null
    },
    {
        "paramName": "data",
        "paramType": "Object",
        "requiredFlag": false,
        "paramDesc": null,
        "children": [
            {
                "paramName": "task_progress_detail",
                "paramType": "Integer",
                "requiredFlag": false,
                "paramDesc": null
            },
            {
                "paramName": "task_progress",
                "paramType": "Integer",
                "requiredFlag": false,
                "paramDesc": null
            },
            {
                "paramName": "task_status",
                "paramType": "String",
                "requiredFlag": false,
                "paramDesc": null
            },
            {
                "paramName": "task_id",
                "paramType": "Integer",
                "requiredFlag": false,
                "paramDesc": null
            },
            {
                "paramName": "sub_task_result_list",
                "paramType": "Array",
                "requiredFlag": false,
                "paramDesc": null,
                "children": [
                    {
                        "paramName": "[Array Item]",
                        "paramType": "Object",
                        "requiredFlag": false,
                        "paramDesc": null,
                        "children": [
                            {
                                "paramName": "sub_task_error_code",
                                "paramType": "Integer",
                                "requiredFlag": false,
                                "paramDesc": null
                            },
                            {
                                "paramName": "sub_task_progress_detail",
                                "paramType": "Integer",
                                "requiredFlag": false,
                                "paramDesc": null
                            },
                            {
                                "paramName": "sub_task_progress",
                                "paramType": "Integer",
                                "requiredFlag": false,
                                "paramDesc": null
                            },
                            {
                                "paramName": "final_image_list",
                                "paramType": "Array",
                                "requiredFlag": false,
                                "paramDesc": null,
                                "children": [
                                    {
                                        "paramName": "[Array Item]",
                                        "paramType": "Object",
                                        "requiredFlag": false,
                                        "paramDesc": "",
                                        "children": [
                                            {
                                                "paramName": "img_url",
                                                "paramType": "String",
                                                "requiredFlag": false,
                                                "paramDesc": ""
                                            }
                                        ]
                                    }
                                ]
                            },
                            {
                                "paramName": "sub_task_id",
                                "paramType": "String",
                                "requiredFlag": false,
                                "paramDesc": null
                            },
                            {
                                "paramName": "sub_task_status",
                                "paramType": "String",
                                "requiredFlag": false,
                                "paramDesc": null
                            }
                        ]
                    }
                ]
            }
        ]
    }
];

const generator = new MarkdownContentGenerator();
const result = generator.generateFetchResponseJson(realResponseParams);

console.log("=== 真实数据测试结果 ===");
console.log(result);

// 检查是否包含正确的数组格式
const hasCorrectArrayFormat = result.includes("sub_task_result_list: [") && 
                             result.includes("final_image_list: [") &&
                             !result.includes("[Array Item]");

console.log("\n=== 验证结果 ===");
if (hasCorrectArrayFormat) {
    console.log("✅ 数组格式修复成功！");
} else {
    console.log("❌ 数组格式仍然有问题");
    console.log("检查点：");
    console.log("- sub_task_result_list: [", result.includes("sub_task_result_list: ["));
    console.log("- final_image_list: [", result.includes("final_image_list: ["));
    console.log("- 不包含 [Array Item]:", !result.includes("[Array Item]"));
}
