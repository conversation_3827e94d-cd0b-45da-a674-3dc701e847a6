
import request from "./request";
import {
	ResponseData,
	getListPageParams,
	pluginDetail,
	pluginResponse,
	PageResponse,
	userKey,
} from "../types";
import { getJoyCodeConfig, INTERNAL_USER_PLUGIN_BASE_URL } from "../utils/globalContext";

/**
 * 获取插件列表
 * @param data 查询列表参数
 */
export const getListPage = async (data: getListPageParams) => {
	const config = getJoyCodeConfig();

	// // 判断是否为内部用户
	// const isInternalUser = config.env === 'inner';

	// let apiUrl: string;
	// if (isInternalUser) {
	// 	// 内部用户使用 joyagent.jd.com 域名和 autobots 接口
	// 	apiUrl = INTERNAL_USER_PLUGIN_BASE_URL + "/autobots/external/api/v1/tool/pluginPage";
	// } else {
	// 	// 外部用户使用原有的接口
	// 	apiUrl = config.baseUrl + "/api/saas/tool/v1/pluginPage";
	// }
	const apiUrl = config.baseUrl + "/api/saas/tool/v1/pluginPage"

	return request.post<ResponseData<PageResponse<pluginResponse>>>(
		apiUrl,
		data,
	);
};

/**
 * 获取插件详情
 * @param pluginId 插件ID
 */
export const getPluginInfo = async (pluginId: number) => {
	const config = getJoyCodeConfig();

	// // 判断是否为内部用户
	// const isInternalUser = config.env === 'inner';

	// let apiUrl: string;
	// if (isInternalUser) {
	// 	// 内部用户使用 joyagent.jd.com 域名和 autobots 接口
	// 	apiUrl = `${INTERNAL_USER_PLUGIN_BASE_URL}/autobots/external/api/v1/tool/plugin/${pluginId}`;
	// } else {
	// 	// 外部用户使用原有的接口
	// 	apiUrl = config.baseUrl + `/api/saas/tool/v1/plugin/${pluginId}`;
	// }
	const apiUrl = config.baseUrl + `/api/saas/tool/v1/plugin/${pluginId}`;

	return request.get<ResponseData<pluginDetail>>(apiUrl);
};

/**
 * 获取分类列表
 * @param catalog_type 分类类型，默认为 "plugin"
 */
export const getCatalogList = async (catalog_type = "plugin") => {
	const config = getJoyCodeConfig();
	return request.post<ResponseData<any>>(
		config.baseUrl + `/api/saas/tool/v1/plugin/catalogList?catalog_type=${catalog_type}`,
		{},
	);
};

/**
 * 获取大模型列表
 * @param data 查询列表参数
 */
export const getModelList = async (data: getListPageParams) => {
	const config = getJoyCodeConfig();
	return request.post<ResponseData<PageResponse<pluginResponse>>>(
		config.baseUrl + "/api/saas/model-u/v1/modelList",
		data,
	);
};

/**
 * 获取用户可使用key的列表
 */
export const getUserKeyList = async () => {
	const config = getJoyCodeConfig();
	// 内部用户的 baseUrl 已在 globalContext 中固定为 https://joycode-api.jd.com
	// 外部用户使用配置的 baseUrl，因此可以直接使用 config.baseUrl
	return request.get<ResponseData<userKey>>(
		config.baseUrl + "/api/saas/authkey/v1/list",
		{},
	);
};
