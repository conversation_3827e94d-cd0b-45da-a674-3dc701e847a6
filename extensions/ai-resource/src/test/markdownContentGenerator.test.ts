import * as assert from 'assert';
import { MarkdownContentGenerator, FetchRequestConfig, FetchResponseData } from '../providers/handlers/markdownContentGenerator';

suite('MarkdownContentGenerator Tests', () => {
    let generator: MarkdownContentGenerator;

    setup(() => {
        generator = new MarkdownContentGenerator();
    });

    test('应该生成包含 Fetch 配置的完整 Markdown 文档', () => {
        const testData = {
            name: "测试工具",
            pluginDesc: "这是一个测试工具的描述",
            tools: [
                {
                    name: "测试接口",
                    toolDesc: "测试接口描述",
                    pluginToolId: "test-tool-001",
                    requestParams: [
                        {
                            paramName: "query",
                            paramType: "string",
                            requiredFlag: true,
                            paramDesc: "查询参数"
                        },
                        {
                            paramName: "limit",
                            paramType: "integer",
                            requiredFlag: false,
                            paramDesc: "限制数量"
                        }
                    ],
                    responseParams: [
                        {
                            paramName: "data",
                            paramType: "array",
                            paramDesc: "返回数据"
                        },
                        {
                            paramName: "total",
                            paramType: "integer",
                            paramDesc: "总数量"
                        }
                    ]
                }
            ]
        };

        const result = generator.generatePluginMarkdown(testData);

        // 验证基本结构
        assert.ok(result.includes('# 测试工具 服务接口API文档'));
        assert.ok(result.includes('## 测试接口'));
        assert.ok(result.includes('### 接口地址'));
        assert.ok(result.includes('### 接口入参'));
        assert.ok(result.includes('### 接口出参'));

        // 验证 Fetch 配置部分
        assert.ok(result.includes('**Fetch 请求配置：**'));
        assert.ok(result.includes('**Fetch 响应格式：**'));

        // 验证 JavaScript 对象结构
        assert.ok(result.includes('method: "POST"'));
        assert.ok(result.includes('"Content-Type": "application/json"'));
        assert.ok(result.includes('"Authorization": "${apiKey}"'));
        assert.ok(result.includes('params: {'));
        assert.ok(result.includes('code: 200'));
        assert.ok(result.includes('msg: ""'));
        assert.ok(result.includes('data: {'));

        // 验证注释格式
        assert.ok(result.includes('// string, 必填, 查询参数'));
        assert.ok(result.includes('// integer, 不必填, 限制数量'));
        assert.ok(result.includes('// number, 响应状态码'));
        assert.ok(result.includes('// string, 响应消息'));
    });

    test('应该正确处理嵌套参数结构', () => {
        const testData = {
            name: "嵌套测试",
            tools: [
                {
                    name: "嵌套接口",
                    pluginToolId: "nested-test",
                    requestParams: [
                        {
                            paramName: "user",
                            paramType: "object",
                            requiredFlag: true,
                            paramDesc: "用户信息"
                        }
                    ],
                    responseParams: [
                        {
                            paramName: "profile",
                            paramType: "object",
                            paramDesc: "用户档案",
                            children: [
                                {
                                    paramName: "name",
                                    paramType: "string",
                                    paramDesc: "用户名"
                                },
                                {
                                    paramName: "age",
                                    paramType: "integer",
                                    paramDesc: "年龄"
                                }
                            ]
                        }
                    ]
                }
            ]
        };

        const result = generator.generatePluginMarkdown(testData);

        // 验证嵌套结构处理
        assert.ok(result.includes('profile 结构'));
        assert.ok(result.includes('"profile": {'));
        assert.ok(result.includes('"name": ""'));
        assert.ok(result.includes('"age": 0'));
    });

    test('应该正确生成不同数据类型的默认值', () => {
        const testData = {
            name: "类型测试",
            tools: [
                {
                    name: "类型接口",
                    pluginToolId: "type-test",
                    requestParams: [
                        {
                            paramName: "stringParam",
                            paramType: "string",
                            requiredFlag: true,
                            paramDesc: "字符串参数"
                        },
                        {
                            paramName: "numberParam",
                            paramType: "number",
                            requiredFlag: true,
                            paramDesc: "数字参数"
                        },
                        {
                            paramName: "booleanParam",
                            paramType: "boolean",
                            requiredFlag: true,
                            paramDesc: "布尔参数"
                        },
                        {
                            paramName: "arrayParam",
                            paramType: "array",
                            requiredFlag: true,
                            paramDesc: "数组参数"
                        },
                        {
                            paramName: "objectParam",
                            paramType: "object",
                            requiredFlag: true,
                            paramDesc: "对象参数"
                        }
                    ],
                    responseParams: []
                }
            ]
        };

        const result = generator.generatePluginMarkdown(testData);

        // 验证不同类型的默认值
        assert.ok(result.includes('"stringParam": ""'));
        assert.ok(result.includes('"numberParam": 0'));
        assert.ok(result.includes('"booleanParam": false'));
        assert.ok(result.includes('"arrayParam": []'));
        assert.ok(result.includes('"objectParam": {}'));
    });

    test('应该处理空参数列表', () => {
        const testData = {
            name: "空参数测试",
            tools: [
                {
                    name: "空参数接口",
                    pluginToolId: "empty-params",
                    requestParams: [],
                    responseParams: []
                }
            ]
        };

        const result = generator.generatePluginMarkdown(testData);

        // 验证空参数处理
        assert.ok(result.includes('# 空参数测试 服务接口API文档'));
        assert.ok(result.includes('## 空参数接口'));

        // 验证 Fetch 配置仍然生成
        assert.ok(result.includes('**Fetch 请求配置：**'));
        assert.ok(result.includes('**Fetch 响应格式：**'));
        assert.ok(result.includes('params: {'));
        assert.ok(result.includes('data: {'));
    });

    test('应该正确生成带注释的复杂嵌套结构', () => {
        const testData = {
            name: "复杂结构测试",
            tools: [
                {
                    name: "复杂接口",
                    pluginToolId: "complex-test",
                    requestParams: [
                        {
                            paramName: "user",
                            paramType: "object",
                            requiredFlag: true,
                            paramDesc: "用户信息对象",
                            children: [
                                {
                                    paramName: "name",
                                    paramType: "string",
                                    requiredFlag: true,
                                    paramDesc: "用户姓名"
                                },
                                {
                                    paramName: "tags",
                                    paramType: "array",
                                    requiredFlag: false,
                                    paramDesc: "用户标签列表"
                                }
                            ]
                        },
                        {
                            paramName: "options",
                            paramType: "array",
                            requiredFlag: false,
                            paramDesc: "选项列表"
                        }
                    ],
                    responseParams: []
                }
            ]
        };

        const result = generator.generatePluginMarkdown(testData);

        // 验证嵌套对象的注释格式
        assert.ok(result.includes('user: { // object, 必填, 用户信息对象'));
        assert.ok(result.includes('name: "" // string, 必填, 用户姓名'));
        assert.ok(result.includes('tags: [ // array, 不必填, 用户标签列表'));
        assert.ok(result.includes('options: [ // array, 不必填, 选项列表'));

        // 验证数组元素注释
        assert.ok(result.includes('// string, 数组元素'));
    });

    test('应该正确生成数组格式', () => {
        const testData = {
            name: "数组格式测试",
            tools: [
                {
                    name: "数组接口",
                    pluginToolId: "array-test",
                    requestParams: [],
                    responseParams: [
                        {
                            paramName: "simple_array",
                            paramType: "array",
                            requiredFlag: false,
                            paramDesc: "简单数组"
                        },
                        {
                            paramName: "object_array",
                            paramType: "array",
                            requiredFlag: false,
                            paramDesc: "对象数组",
                            children: [
                                {
                                    paramName: "id",
                                    paramType: "integer",
                                    requiredFlag: true,
                                    paramDesc: "ID"
                                },
                                {
                                    paramName: "name",
                                    paramType: "string",
                                    requiredFlag: true,
                                    paramDesc: "名称"
                                }
                            ]
                        }
                    ]
                }
            ]
        };

        const result = generator.generatePluginMarkdown(testData);

        // 验证简单数组格式
        assert.ok(result.includes('simple_array: [ // array, 不必填, 简单数组'));
        assert.ok(result.includes('"" // string, 数组元素'));

        // 验证对象数组格式
        assert.ok(result.includes('object_array: [ // array, 不必填, 对象数组'));
        assert.ok(result.includes('{ // object, 数组元素'));
        assert.ok(result.includes('id: 0, // integer, 必填, ID'));
        assert.ok(result.includes('name: "" // string, 必填, 名称'));

        // 验证数组结束符号
        assert.ok(result.includes(']'));
    });
});
