import { MarkdownContentGenerator } from '../providers/handlers/markdownContentGenerator';

/**
 * 示例：如何使用增强的 MarkdownContentGenerator 生成包含 Fetch 配置的 Markdown 文档
 */
export class MarkdownGeneratorExample {
    private generator: MarkdownContentGenerator;

    constructor() {
        this.generator = new MarkdownContentGenerator();
    }

    /**
     * 生成示例插件的 Markdown 文档
     */
    generateExampleMarkdown(): string {
        // 模拟插件数据
        const pluginData = {
            name: "文本分析工具",
            pluginDesc: "这是一个用于分析文本内容的AI工具插件，支持情感分析、关键词提取等功能。",
            tools: [
                {
                    name: "情感分析",
                    toolDesc: "分析文本的情感倾向，返回正面、负面或中性的分析结果。",
                    pluginToolId: "sentiment-analysis-001",
                    toolUrl: "/api/saas/tool/v1/plugin/run/sentiment-analysis-001",
                    requestParams: [
                        {
                            paramName: "text",
                            paramType: "string",
                            requiredFlag: true,
                            paramDesc: "需要分析的文本内容",
                            inputMethod: "POST"
                        },
                        {
                            paramName: "language",
                            paramType: "string",
                            requiredFlag: false,
                            paramDesc: "文本语言，默认为中文"
                        },
                        {
                            paramName: "options",
                            paramType: "object",
                            requiredFlag: false,
                            paramDesc: "分析选项配置"
                        }
                    ],
                    responseParams: [
                        {
                            paramName: "sentiment",
                            paramType: "string",
                            paramDesc: "情感分析结果：positive/negative/neutral"
                        },
                        {
                            paramName: "confidence",
                            paramType: "number",
                            paramDesc: "置信度分数，范围0-1"
                        },
                        {
                            paramName: "details",
                            paramType: "object",
                            paramDesc: "详细分析结果",
                            children: [
                                {
                                    paramName: "positive_score",
                                    paramType: "number",
                                    paramDesc: "正面情感分数"
                                },
                                {
                                    paramName: "negative_score",
                                    paramType: "number",
                                    paramDesc: "负面情感分数"
                                },
                                {
                                    paramName: "neutral_score",
                                    paramType: "number",
                                    paramDesc: "中性情感分数"
                                }
                            ]
                        }
                    ]
                },
                {
                    name: "关键词提取",
                    toolDesc: "从文本中提取关键词和短语。",
                    pluginToolId: "keyword-extraction-002",
                    toolUrl: "/api/saas/tool/v1/plugin/run/keyword-extraction-002",
                    requestParams: [
                        {
                            paramName: "text",
                            paramType: "string",
                            requiredFlag: true,
                            paramDesc: "需要提取关键词的文本内容"
                        },
                        {
                            paramName: "max_keywords",
                            paramType: "integer",
                            requiredFlag: false,
                            paramDesc: "最大关键词数量，默认为10"
                        }
                    ],
                    responseParams: [
                        {
                            paramName: "keywords",
                            paramType: "array",
                            paramDesc: "提取的关键词列表"
                        },
                        {
                            paramName: "total_count",
                            paramType: "integer",
                            paramDesc: "关键词总数"
                        }
                    ]
                }
            ]
        };

        return this.generator.generatePluginMarkdown(pluginData);
    }

    /**
     * 生成简单示例的 Markdown 文档
     */
    generateSimpleExample(): string {
        const simpleData = {
            name: "计算器工具",
            pluginDesc: "简单的数学计算工具",
            tools: [
                {
                    name: "加法运算",
                    toolDesc: "执行两个数字的加法运算",
                    pluginToolId: "calculator-add",
                    requestParams: [
                        {
                            paramName: "a",
                            paramType: "number",
                            requiredFlag: true,
                            paramDesc: "第一个数字"
                        },
                        {
                            paramName: "b",
                            paramType: "number",
                            requiredFlag: true,
                            paramDesc: "第二个数字"
                        }
                    ],
                    responseParams: [
                        {
                            paramName: "result",
                            paramType: "number",
                            paramDesc: "计算结果"
                        },
                        {
                            paramName: "operation",
                            paramType: "string",
                            paramDesc: "执行的运算类型"
                        }
                    ]
                }
            ]
        };

        return this.generator.generatePluginMarkdown(simpleData);
    }
}

// 使用示例
export function runExample() {
    const example = new MarkdownGeneratorExample();
    
    console.log("=== 复杂示例 ===");
    console.log(example.generateExampleMarkdown());
    
    console.log("\n=== 简单示例 ===");
    console.log(example.generateSimpleExample());
}
