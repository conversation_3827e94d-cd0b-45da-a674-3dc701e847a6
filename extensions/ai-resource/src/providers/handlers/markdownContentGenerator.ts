import { I18n } from "../../utils/i18n";

// 插件工具服务 URL 路径常量
const PLUGIN_TOOL_SERVICE_PATH = "/api/saas/tool/v1/plugin/run";

// Fetch 请求和响应的类型定义
export interface FetchRequestConfig {
	method: string;
	url: string;
	headers: Record<string, string>;
	body?: any;
}

export interface FetchResponseData {
	code: number;
	msg: string;
	data: any;
}

export class MarkdownContentGenerator {
	public generatePluginMarkdown(data: any): string {
		let mdContent = `# ${I18n.t("markdown.service.title", data.name)}\n\n`;
		mdContent += `${data.pluginDesc || ""}\n\n`;

		if (data.tools && data.tools.length > 0) {
			data.tools.forEach((tool: any) => {
				const nestedStructures: any[] = [];
				const collectNestedStructures = (
					paramName: string,
					children: any[],
					level: number = 0,
				) => {
					nestedStructures.push({
						name: paramName,
						children: children,
						level: level,
					});
					children.forEach((child: any) => {
						if (child.children && child.children.length > 0) {
							collectNestedStructures(
								child.paramName,
								child.children,
								level + 1,
							);
						}
					});
				};

				mdContent += `## ${tool.name}\n\n`;
				mdContent += `${tool.toolDesc || ""}\n\n`;
				// 构建标准的 service URL 格式：{PLUGIN_TOOL_SERVICE_PATH}/{pluginToolId}
				const serviceUrl = tool.pluginToolId ?
					`${PLUGIN_TOOL_SERVICE_PATH}/${tool.pluginToolId}` :
					tool.toolUrl;
				mdContent += `### ${I18n.t("markdown.service.url")}\n\n\`POST ${serviceUrl}\`\n\n`;

				if (tool.requestParams && tool.requestParams.length > 0) {
					mdContent += this.generateRequestParamsMarkdown(tool.requestParams);
				}

				if (tool.responseParams && tool.responseParams.length > 0) {
					mdContent += this.generateResponseParamsMarkdown(tool.responseParams, collectNestedStructures);
				}

				if (nestedStructures.length > 0) {
					mdContent += this.generateNestedStructuresMarkdown(nestedStructures);
				}
			});
		}

		return mdContent;
	}

	private generateRequestParamsMarkdown(requestParams: any[]): string {
		let mdContent = `### ${I18n.t("markdown.request.params")}\n\n`;
		mdContent += `| ${I18n.t("markdown.table.paramName")} | ${I18n.t("markdown.table.type")} | ${I18n.t("markdown.table.required")} | ${I18n.t("markdown.table.description")} |\n`;
		mdContent += `| --- | --- | --- | --- |\n`;

		requestParams.forEach((param: any) => {
			const required = param.requiredFlag ? I18n.t("markdown.table.yes") : I18n.t("markdown.table.no");
			mdContent += `| ${param.paramName} | ${param.paramType} | ${required} | ${param.paramDesc || ""} |\n`;
		});

		// 添加 JSON 示例
		mdContent += "\n**请求示例：**\n";
		mdContent += "```json\n{\n";
		requestParams.forEach((param: any, index: number) => {
			mdContent += `  "${param.paramName}": ${this.getDefaultValueByType(param.paramType)}${index < requestParams.length - 1 ? "," : ""}\n`;
		});
		mdContent += "}\n```\n\n";

		// 添加 Fetch 请求配置 JSON
		mdContent += this.generateFetchRequestJson(requestParams);

		const inputMethod = requestParams.find((p: any) => p.inputMethod)?.inputMethod || "";
		if (inputMethod) {
			mdContent += `- ${I18n.t("markdown.request.type", inputMethod)}\n\n`;
		}

		return mdContent;
	}

	private getDefaultValueByType(type: string): string {
		switch (type.toLowerCase()) {
			case "string":
				return '""';
			case "number":
			case "integer":
				return "0";
			case "boolean":
				return "false";
			case "array":
				return "[]";
			case "object":
				return "{}";
			default:
				return '""';
		}
	}

	private generateResponseParamsMarkdown(responseParams: any[], collectNestedStructures: Function): string {
		let mdContent = `### ${I18n.t("markdown.response.params")}\n\n`;
		mdContent += `| ${I18n.t("markdown.table.fieldName")} | ${I18n.t("markdown.table.type")} | ${I18n.t("markdown.table.description")} |\n`;
		mdContent += `| --- | --- | --- |\n`;

		responseParams.forEach((param: any) => {
			mdContent += `| ${param.paramName} | ${param.paramType} | ${param.paramDesc || ""} |\n`;
			if (param.children && param.children.length > 0) {
				collectNestedStructures(param.paramName, param.children);
			}
		});

		// 添加 JSON 示例
		mdContent += "\n**响应示例：**\n";
		mdContent += "```json\n{\n";
		responseParams.forEach((param: any, index: number) => {
			if ((param.paramType === "array" || param.paramType === "Array") && param.children && param.children.length > 0) {
				// 处理对象数组
				mdContent += `  "${param.paramName}": [\n`;
				mdContent += `    {\n`;

				// 检查是否有 [Array Item] 子元素
				const arrayItemChild = param.children.find((child: any) => child.paramName === "[Array Item]");
				if (arrayItemChild && arrayItemChild.children && arrayItemChild.children.length > 0) {
					// 如果有 [Array Item]，处理其子元素
					arrayItemChild.children.forEach((grandChild: any, grandChildIndex: number) => {
						mdContent += `      "${grandChild.paramName}": ${this.getDefaultValueByType(grandChild.paramType)}${grandChildIndex < arrayItemChild.children.length - 1 ? "," : ""}\n`;
					});
				} else {
					// 否则直接处理子元素
					param.children.forEach((child: any, childIndex: number) => {
						if (child.paramName !== "[Array Item]") {
							mdContent += `      "${child.paramName}": ${this.getDefaultValueByType(child.paramType)}${childIndex < param.children.length - 1 ? "," : ""}\n`;
						}
					});
				}

				mdContent += `    }\n`;
				mdContent += `  ]${index < responseParams.length - 1 ? "," : ""}\n`;
			} else if (param.children && param.children.length > 0) {
				// 处理嵌套对象
				mdContent += `  "${param.paramName}": {\n`;
				param.children.forEach((child: any, childIndex: number) => {
					mdContent += `    "${child.paramName}": ${this.getDefaultValueByType(child.paramType)}${childIndex < param.children.length - 1 ? "," : ""}\n`;
				});
				mdContent += `  }${index < responseParams.length - 1 ? "," : ""}\n`;
			} else {
				mdContent += `  "${param.paramName}": ${this.getDefaultValueByType(param.paramType)}${index < responseParams.length - 1 ? "," : ""}\n`;
			}
		});
		mdContent += "}\n```\n\n";

		// 添加 Fetch 响应格式 JSON
		mdContent += this.generateFetchResponseJson(responseParams);

		return mdContent;
	}

	private generateNestedStructuresMarkdown(nestedStructures: any[]): string {
		let mdContent = "";

		nestedStructures.forEach((structure: any) => {
			const levelPrefix = "#".repeat(Math.min(4 + structure.level, 6));
			mdContent += `${levelPrefix} ${I18n.t("markdown.structure.title", structure.name)}\n\n`;
			mdContent += `| ${I18n.t("markdown.table.fieldName")} | ${I18n.t("markdown.table.type")} | ${I18n.t("markdown.table.description")} |\n`;
			mdContent += `| --- | --- | --- |\n`;

			structure.children.forEach((child: any) => {
				mdContent += `| ${child.paramName} | ${child.paramType} | ${child.paramDesc || ""} |\n`;
			});

			// 添加 JSON 示例
			mdContent += "\n**数据结构示例：**\n";
			mdContent += "```json\n{\n";
			structure.children.forEach((child: any, index: number) => {
				if (child.paramType === "array" && child.children && child.children.length > 0) {
					// 处理对象数组
					mdContent += `  "${child.paramName}": [\n`;
					mdContent += `    {\n`;
					child.children.forEach((grandChild: any, grandChildIndex: number) => {
						// 跳过 [Array Item] 这种特殊的参数名
						if (grandChild.paramName === "[Array Item]" && grandChild.children && grandChild.children.length > 0) {
							grandChild.children.forEach((greatGrandChild: any, greatGrandChildIndex: number) => {
								mdContent += `      "${greatGrandChild.paramName}": ${this.getDefaultValueByType(greatGrandChild.paramType)}${greatGrandChildIndex < grandChild.children.length - 1 ? "," : ""}\n`;
							});
						} else {
							mdContent += `      "${grandChild.paramName}": ${this.getDefaultValueByType(grandChild.paramType)}${grandChildIndex < child.children.length - 1 ? "," : ""}\n`;
						}
					});
					mdContent += `    }\n`;
					mdContent += `  ]${index < structure.children.length - 1 ? "," : ""}\n`;
				} else {
					mdContent += `  "${child.paramName}": ${this.getDefaultValueByType(child.paramType)}${index < structure.children.length - 1 ? "," : ""}\n`;
				}
			});
			mdContent += "}\n```\n\n";
		});

		return mdContent;
	}

	/**
	 * 生成 Fetch 请求配置 JSON
	 */
	private generateFetchRequestJson(requestParams: any[]): string {
		let mdContent = `**${I18n.t("markdown.fetch.request.title")}：**\n`;
		mdContent += "```javascript\n";

		// 构建完整的 fetch 配置
		mdContent += "{\n";
		mdContent += '  method: "POST",\n';
		mdContent += '  url: "${baseUrl}/api/saas/tool/v1/plugin/run/${pluginToolId}",\n';
		mdContent += "  headers: {\n";
		mdContent += '    "Content-Type": "application/json",\n';
		mdContent += '    "Authorization": "${apiKey}"\n';
		mdContent += "  },\n";
		mdContent += "  body: {\n";
		mdContent += "    params: {\n";

		// 生成带注释的参数
		mdContent += this.generateParametersWithComments(requestParams, 6);

		mdContent += "    }\n";
		mdContent += "  }\n";
		mdContent += "}\n";
		mdContent += "```\n\n";

		return mdContent;
	}

	/**
	 * 生成 Fetch 响应格式 JSON
	 */
	private generateFetchResponseJson(responseParams: any[]): string {
		let mdContent = `**${I18n.t("markdown.fetch.response.title")}：**\n`;
		mdContent += "```javascript\n";

		// 构建完整的响应格式
		mdContent += "{\n";
		mdContent += "  code: 200, // number, 响应状态码\n";
		mdContent += '  msg: "", // string, 响应消息\n';
		mdContent += "  data: {\n";

		// 生成带注释的响应参数
		mdContent += this.generateParametersWithComments(responseParams, 4);

		mdContent += "  }\n";
		mdContent += "}\n";
		mdContent += "```\n\n";

		return mdContent;
	}

	/**
	 * 生成带注释的参数列表
	 */
	private generateParametersWithComments(params: any[], indentLevel: number = 4): string {
		let content = "";
		const indent = " ".repeat(indentLevel);

		params.forEach((param: any, index: number) => {
			const isLast = index === params.length - 1;
			const required = param.requiredFlag ? "必填" : "不必填";
			const comment = `// ${param.paramType}, ${required}, ${param.paramDesc || ""}`;

			// 跳过 [Array Item] 这种特殊的参数名，直接处理其子元素
			if (param.paramName === "[Array Item]" && param.children && param.children.length > 0) {
				content += this.generateParametersWithComments(param.children, indentLevel);
				return;
			}

			if (param.paramType === "array" || param.paramType === "Array") {
				// 处理数组类型（优先检查数组类型）
				content += `${indent}${param.paramName}: [ ${comment}\n`;

				// 如果有子元素定义，生成对象数组
				if (param.children && param.children.length > 0) {
					// 检查子元素是否是 [Array Item]
					const hasArrayItem = param.children.some((child: any) => child.paramName === "[Array Item]");
					if (hasArrayItem) {
						// 如果有 [Array Item]，直接处理其子元素
						const arrayItemChild = param.children.find((child: any) => child.paramName === "[Array Item]");
						if (arrayItemChild && arrayItemChild.children && arrayItemChild.children.length > 0) {
							content += `${indent}  { // object, 数组元素\n`;
							content += this.generateParametersWithComments(arrayItemChild.children, indentLevel + 4);
							content += `${indent}  }\n`;
						}
					} else {
						content += `${indent}  { // object, 数组元素\n`;
						content += this.generateParametersWithComments(param.children, indentLevel + 4);
						content += `${indent}  }\n`;
					}
				} else {
					// 为基本类型数组添加示例元素
					const arrayElementType = this.getArrayElementType(param);
					const arrayElementComment = `// ${arrayElementType}, 数组元素`;
					content += `${indent}  ${this.getDefaultValueByType(arrayElementType)} ${arrayElementComment}\n`;
				}

				content += `${indent}]${isLast ? "" : ","}\n`;
			} else if (param.children && param.children.length > 0) {
				// 处理嵌套对象（非数组的对象类型）
				content += `${indent}${param.paramName}: { ${comment}\n`;
				content += this.generateParametersWithComments(param.children, indentLevel + 2);
				content += `${indent}}${isLast ? "" : ","}\n`;
			} else {
				// 处理基本类型
				const value = this.getDefaultValueByType(param.paramType);
				content += `${indent}${param.paramName}: ${value}${isLast ? "" : ","} ${comment}\n`;
			}
		});

		return content;
	}

	/**
	 * 获取数组元素类型
	 */
	private getArrayElementType(param: any): string {
		// 如果有子元素定义，使用第一个子元素的类型
		if (param.children && param.children.length > 0) {
			return param.children[0].paramType || "string";
		}
		// 默认返回 string 类型
		return "string";
	}
}
