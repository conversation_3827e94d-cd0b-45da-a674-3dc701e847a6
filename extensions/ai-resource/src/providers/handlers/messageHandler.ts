import * as vscode from "vscode";
import * as path from "path";
import * as fs from "fs";
import { getPluginInfo } from "../../service/api";
import { MarkdownProvider } from "../markdownProvider";
import { Logger } from "../../utils/logger";
import { JoycodeHelper } from "../../utils/joycodeHelper";
import { MarkdownContentGenerator } from "./markdownContentGenerator";
import { I18n } from "../../utils/i18n";
import { getCurrentApiKey, getCurrentBaseUrl } from "../../config";
import { getCurrentEnv } from "../../utils/globalContext";
import { MARKDOWN_SHOW_PREVIEW, VSCODE_OPEN_FOLDER, PLUGIN_DETAIL_SCHEME, AI_RESOURCES_OPEN_SETTINGS, JOYCODE_RESOURCE_IMPORT } from "../../constants/commands";

export class MessageHandler {
	private mdProvider: MarkdownProvider;
	private markdownGenerator: MarkdownContentGenerator;
	private extensionContext?: vscode.ExtensionContext;

	constructor(mdProvider: MarkdownProvider, extensionContext?: vscode.ExtensionContext) {
		this.mdProvider = mdProvider;
		this.markdownGenerator = new MarkdownContentGenerator();
		this.extensionContext = extensionContext;
	}

	/**
	 * 从docs目录读取对应的Markdown文件
	 * @param resourceName 资源名称
	 * @returns Markdown内容，如果文件不存在则返回null
	 */
	private readMarkdownFromDocs(resourceName: string): string | null {
		if (!this.extensionContext) {
			Logger.warn(`[MessageHandler] 扩展上下文未设置，无法读取docs文件`);
			return null;
		}

		try {
			// 构建可能的文件名列表
			const possibleFileNames = [
				`${resourceName}.md`,
				`${resourceName}接口文档.md`,
				`${resourceName}大模型网关接口文档.md`
			];

			// 如果是京东大模型网关，使用特定的文件名
			if (resourceName === "京东大模型网关" || resourceName === "jd-model-gateway") {
				possibleFileNames.unshift("jd-model-gateway-api-docs.md");
			}

			const docsPath = path.join(this.extensionContext.extensionPath, 'resources', 'docs');

			// 尝试读取每个可能的文件
			for (const fileName of possibleFileNames) {
				const filePath = path.join(docsPath, fileName);
				Logger.info(`[MessageHandler] 尝试读取文件: ${filePath}`);

				if (fs.existsSync(filePath)) {
					const content = fs.readFileSync(filePath, 'utf8');
					Logger.info(`[MessageHandler] 成功读取文件: ${fileName}, 内容长度: ${content.length}`);
					return content;
				}
			}

			Logger.warn(`[MessageHandler] 未找到对应的文档文件，资源名称: ${resourceName}`);
			return null;
		} catch (error) {
			Logger.error(`[MessageHandler] 读取docs文件失败: ${error instanceof Error ? error.message : String(error)}`);
			return null;
		}
	}

	/**
	 * 读取model-file.md文件内容
	 * @returns model-file.md的内容，如果文件不存在则返回空字符串
	 */
	private readModelFileContent(): string {
		if (!this.extensionContext) {
			Logger.warn(`[MessageHandler] 扩展上下文未设置，无法读取model-file.md`);
			return "";
		}

		try {
			const modelFilePath = path.join(this.extensionContext.extensionPath, 'resources', 'docs', 'model-file.md');

			if (fs.existsSync(modelFilePath)) {
				const content = fs.readFileSync(modelFilePath, 'utf8');
				Logger.info(`[MessageHandler] 成功读取model-file.md，内容长度: ${content.length}`);
				return content;
			} else {
				Logger.warn(`[MessageHandler] model-file.md文件不存在: ${modelFilePath}`);
				return "";
			}
		} catch (error) {
			Logger.error(`[MessageHandler] 读取model-file.md失败: ${error instanceof Error ? error.message : String(error)}`);
			return "";
		}
	}

	public async handleSelectMessage(message: any): Promise<void> {
		try {
			Logger.info(`[MessageHandler] 开始处理选择消息，ID: ${message.id}, 类型: ${message.pluginType}, 分类: ${message.catalog}, 名称: ${message.name}`);

			let mdContent = "";
			let fileName = "";

			// 优先尝试从docs目录读取对应的Markdown文件
			const resourceName = message.name || message.id;
			const docsContent = this.readMarkdownFromDocs(resourceName);

			if (docsContent) {
				// 成功从docs目录读取到文件
				Logger.info(`[MessageHandler] 从docs目录读取到文档内容，资源名称: ${resourceName}`);
				mdContent = docsContent;
				fileName = encodeURIComponent(resourceName);
			} else {
				// 回退到原来的逻辑
				Logger.info(`[MessageHandler] docs目录中未找到对应文档，使用原有逻辑`);

				// 根据pluginType决定处理方式
				if (message.pluginType === 'model') {
					// 大模型使用国际化模板
					Logger.info(`[MessageHandler] 处理大模型预览，名称: ${message.name}`);
					const modelName = message.name || message.id;
					mdContent = I18n.t("model.previewMd", modelName);
					fileName = encodeURIComponent(modelName);
				} else {
					// 插件使用API获取详情
					Logger.info(`[MessageHandler] 处理插件预览，插件ID: ${message.id}`);
					const res = await getPluginInfo(message.id);
					Logger.info(`[MessageHandler] API响应状态: ${res?.status}, 数据存在: ${!!res?.data?.data}`);

					const data = res?.data?.data;
					if (data) {
						Logger.info(`[MessageHandler] 生成插件Markdown内容，插件名称: ${data.name}`);
						mdContent = this.markdownGenerator.generatePluginMarkdown(data);
						fileName = encodeURIComponent(data.name);
					} else {
						Logger.warn(`[MessageHandler] 未获取到插件数据，显示默认内容`);
						mdContent = `# ${I18n.t("plugin.detail.title")}\n\n${I18n.t("plugin.detail.noInfo")}`;
						fileName = message.id;
					}
				}
			}

			const uri = vscode.Uri.parse(`${PLUGIN_DETAIL_SCHEME}:${fileName}.md`);

			Logger.info(`[MessageHandler] 设置Markdown内容，长度: ${mdContent.length}, URI: ${uri.toString()}`);
			this.mdProvider.setContent(mdContent, uri);

			// 等待一小段时间确保内容提供者已经准备好
			await new Promise(resolve => setTimeout(resolve, 100));

			Logger.info(`[MessageHandler] 执行markdown预览命令，URI: ${uri.toString()}`);
			await vscode.commands.executeCommand(MARKDOWN_SHOW_PREVIEW, uri);
			Logger.info(`[MessageHandler] markdown预览命令执行完成`);
		} catch (err) {
			Logger.error(`[MessageHandler] 处理选择消息失败: ${err instanceof Error ? err.message : String(err)}`);
			const mdContent = `# ${I18n.t("plugin.detail.title")}\n\n${I18n.t("plugin.detail.fetchFailed")}`;
			const fileName = message.id;
			const uri = vscode.Uri.parse(`${PLUGIN_DETAIL_SCHEME}:${fileName}.md`);
			this.mdProvider.setContent(mdContent, uri);

			// 等待一小段时间确保内容提供者已经准备好
			await new Promise(resolve => setTimeout(resolve, 100));

			await vscode.commands.executeCommand(MARKDOWN_SHOW_PREVIEW, uri);
		}
	}

	public async handleQuoteMessage(message: any): Promise<void> {
		try {
			Logger.info(`[MessageHandler] 开始处理引用消息，ID: ${message.id}, 类型: ${message.pluginType}, 分类: ${message.catalog}`);

			let mdContent = "";
			let data: any = null;

			// 根据pluginType决定处理方式
			if (message.pluginType === 'model') {
				// 大模型优先从docs目录读取文档
				Logger.info(`[MessageHandler] 处理大模型引用，名称: ${message.name}`);
				const modelName = message.name || message.id;

				// 优先尝试从docs目录读取对应的Markdown文件
				const docsContent = this.readMarkdownFromDocs(modelName);

				if (docsContent) {
					// 成功从docs目录读取到文件
					Logger.info(`[MessageHandler] 从docs目录读取到大模型文档内容，资源名称: ${modelName}`);
					mdContent = docsContent;
				} else {
					// 回退到国际化模板
					Logger.info(`[MessageHandler] docs目录中未找到对应文档，使用国际化模板`);
					mdContent = I18n.t("model.previewMd", modelName);
				}

				// 为大模型创建一个虚拟的data对象，用于后续文件名处理
				data = { name: modelName };
			} else {
				// 插件使用API获取详情
				Logger.info(`[MessageHandler] 处理插件引用，插件ID: ${message.id}`);
				const res = await getPluginInfo(message.id);
				data = res?.data?.data;

				if (data) {
					mdContent = this.markdownGenerator.generatePluginMarkdown(data, { includeTable: false });
				} else {
					mdContent = `# ${I18n.t("plugin.detail.title")}\n\n${I18n.t("plugin.detail.noInfo")}`;
				}
			}

			// 根据类型追加不同的配置说明
			if (data) {
				if (message.pluginType === 'model') {
					// 获取当前环境，判断是否为内部用户
					const currentEnv = getCurrentEnv();
					const isInternalUser = currentEnv === 'inner';

					Logger.info(`[MessageHandler] 大模型引用 - 当前环境: ${currentEnv}, 是否内部用户: ${isInternalUser}`);

					// 只有外部用户才追加 model-file.md 文件内容
					if (!isInternalUser) {
						Logger.info(`[MessageHandler] 外部用户，追加 model-file.md 文件内容`);
						const modelFileContent = this.readModelFileContent();
						if (modelFileContent) {
							mdContent += "\n\n" + modelFileContent;
						}
					} else {
						Logger.info(`[MessageHandler] 内部用户，跳过追加 model-file.md 文件内容`);
					}
				} else {
					// // 插件追加完整的配置说明
					// const joycodeDir = JoycodeHelper.getJoycodeDirName();
					// const apiKeyFileName = JoycodeHelper.getApiKeyFileName();
					// Logger.info(`[MessageHandler] 插件引用追加完整配置说明`);
					// mdContent += I18n.t("config.instructions", joycodeDir, apiKeyFileName);
				}
			}

			try {
				Logger.info("[MessageHandler] 引用时，检查工作空间并写md文件");

				// 首先检查是否有工作空间
				const hasWorkspace = !!vscode.workspace.workspaceFolders && vscode.workspace.workspaceFolders.length > 0;
				if (!hasWorkspace) {
					// 没有工作空间，提示用户打开项目
					const action = await vscode.window.showWarningMessage(
						I18n.t("quote.warning.noWorkspace"),
						I18n.t("quote.action.openFolder")
					);

					if (action === I18n.t("quote.action.openFolder")) {
						// 用户选择打开文件夹
						await vscode.commands.executeCommand(VSCODE_OPEN_FOLDER);
					}
					return; // 不继续执行引用操作
				}

				// 保存 Markdown 文件
				const fileName = `${data.name || message.id}.md`;
				const fileUri = await JoycodeHelper.saveMarkdownToAiResourceDir(
					fileName,
					mdContent,
				);
				Logger.info(`[MessageHandler] Markdown 文件已保存到：${fileUri?.fsPath || fileUri?.toString()}`);

				// 引用时确保 API Key 文件被更新（已经确认有工作空间）
				const currentApiKey = getCurrentApiKey();
				if (currentApiKey) {
					try {
						const currentBaseUrl = getCurrentBaseUrl();
						// 每次引用都更新 apikey.json 文件
						await JoycodeHelper.updateConfig({
							apikey: currentApiKey,
							baseUrl: currentBaseUrl
						});
						Logger.info("[MessageHandler] 引用时成功更新 API Key 文件和 baseUrl 到项目");
					} catch (apiKeyError) {
						Logger.warn("[MessageHandler] 引用时更新 API Key 文件失败: " + apiKeyError);
						// API Key 文件更新失败不影响主要功能，只记录警告
					}
				} else {
					Logger.warn("[MessageHandler] 引用时没有可用的 API Key，跳过文件更新");
				}

				// 文件保存成功后，调用资源导入指令
				try {
					Logger.info(`[MessageHandler] 文件保存成功，调用资源导入指令，文件名: ${fileName}`);

					await vscode.commands.executeCommand(JOYCODE_RESOURCE_IMPORT, {
						fileName: fileName
					});

					Logger.info(`[MessageHandler] 资源导入指令执行完成`);
				} catch (commandError) {
					Logger.warn(`[MessageHandler] 调用资源导入指令失败: ${commandError instanceof Error ? commandError.message : String(commandError)}`);
					// 指令调用失败不影响文件保存的成功提示
				}

				// vscode.window.showInformationMessage(
				// 	I18n.t("success.quote")
				// );
			} catch (e) {
				const msg = e instanceof Error ? e.message : String(e);
				vscode.window.showErrorMessage(`保存失败：${msg}`);
			}
		} catch (err) {
			Logger.error(
				`获取详情失败: ${err instanceof Error ? err.message : String(err)}`,
			);
			const msg = err instanceof Error ? err.message : String(err);
			vscode.window.showErrorMessage(`保存失败：${msg}`);
		}
	}

	public handleOpenSettingsMessage(): void {
		vscode.commands.executeCommand(AI_RESOURCES_OPEN_SETTINGS);
	}


}
