import * as vscode from "vscode";
import { Logger } from "../../utils/logger";
import { I18n } from "../../utils/i18n";

export class WebviewContentGenerator {
	private context: vscode.ExtensionContext;
	private webviewView?: vscode.WebviewView;

	constructor(context: vscode.ExtensionContext, webviewView?: vscode.WebviewView) {
		this.context = context;
		this.webviewView = webviewView;
	}

	public setWebviewView(webviewView: vscode.WebviewView) {
		this.webviewView = webviewView;
	}

	public generateEnvironmentErrorContent(): string {
		Logger.info("[WebviewContentGenerator] 显示非JoyCode环境错误页面");
		return `
		<!DOCTYPE html>
		<html lang="zh-CN">
		<head>
			<meta charset="UTF-8">
			<meta name="viewport" content="width=device-width, initial-scale=1.0">
			<title>${I18n.t("environment.error.title")}</title>
			<style>
				:root, body {
					/*
					 * VS Code 原生主题变量 - 会自动从当前主题获取
					 * 以下变量无需手动定义，VS Code 会自动提供：
					 *
					 * 基础背景和前景色：
					 * --vscode-editor-background: #18181BFF;
					 * --vscode-editor-foreground: #FFFFFFCC;
					 * --vscode-descriptionForeground: #CCCCCC;
					 *
					 * 错误相关：
					 * --vscode-errorForeground: #bf616a;
					 */

					/* 个性化颜色 - 主题中没有的自定义颜色 */
					--error-message-color: #ff6b6b;
				}
				body {
					display: flex;
					align-items: center;
					justify-content: center;
					height: 100vh;
					background: var(--vscode-editor-background, #18181B) !important;
					color: var(--vscode-editor-foreground, #CCCCCC) !important;
					margin: 0;
					font-family: var(--vscode-font-family);
				}

				/* 确保所有文字都有正确的颜色 */
				* {
					color: inherit;
				}
				.error-container {
					text-align: center;
					padding: 20px;
				}
				.error-message {
					font-size: 16px;
					color: var(--error-message-color);
					margin-bottom: 16px;
				}
				.error-description {
					font-size: 14px;
					color: var(--vscode-descriptionForeground);
				}
			</style>
		</head>
		<body>
			<div class="error-container">
				<div class="error-message">${I18n.t("environment.error.message")}</div>
				<div class="error-description">${I18n.t("environment.error.description")}</div>
			</div>
		</body>
		</html>
		`;
	}

	public generateLoginContent(): string {
		Logger.info("[WebviewContentGenerator] 显示登录页面");
		return `
		<!DOCTYPE html>
		<html lang="zh-CN">
		<head>
			<meta charset="UTF-8">
			<meta name="viewport" content="width=device-width, initial-scale=1.0">
			<title>${I18n.t("login.page.title")}</title>
			<style>
				:root, body {
					/*
					 * VS Code 原生主题变量 - 会自动从当前主题获取
					 * 以下变量无需手动定义，VS Code 会自动提供：
					 *
					 * 基础背景和前景色：
					 * --vscode-editor-background: #18181BFF;
					 * --vscode-editor-foreground: #FFFFFFCC;
					 * --vscode-sideBar-background: #141414;
					 * --vscode-sideBar-foreground: #CCCCCC99;
					 *
					 * 按钮相关：
					 * --vscode-button-background: #DBE0E9FF;
					 * --vscode-button-foreground: #18181BFF;
					 * --vscode-button-hoverBackground: #F0F7FFFF;
					 *
					 * 链接相关：
					 * --vscode-textLink-foreground: #4c9df3;
					 * --vscode-textLink-activeForeground: #4c9df3;
					 */

					/* 个性化颜色 - 主题中没有的自定义颜色 */
					--login-link-color: #1565c0;
					--login-link-hover-color: #0d47a1;
				}
				body {
					display: flex;
					align-items: center;
					justify-content: center;
					height: 100vh;
	background: var(--vscode-sideBar-background, #141414) !important;
				color: var(--vscode-sideBar-foreground, #CCCCCC) !important;
					margin: 0;
					font-family: var(--vscode-font-family);
				}

				/* 确保所有文字都有正确的颜色 */
				* {
					color: inherit;
				}
				.login-container {
					text-align: center;
					padding: 20px;
				}
				.login-btn {
					padding: 12px 32px;
					font-size: 16px;
					background: var(--vscode-button-background);
					color: var(--vscode-button-foreground);
					border: none;
					border-radius: 4px;
					cursor: pointer;
					transition: background-color 0.2s;
				}
				.login-btn:hover {
					background: var(--vscode-button-hoverBackground);
				}
				#loginBtn {
					color: var(--login-link-color);
					cursor: pointer;
					text-decoration: underline;
					transition: color 0.2s;
				}
				#loginBtn:hover {
					color: var(--login-link-hover-color);
				}
			</style>
		</head>
		<body>
			<div class="login-container">
				<div>${I18n.t("login.prompt").replace("{LOGIN_LINK}", '<span id="loginBtn">' + I18n.t("login.linkText") + '</span>')}</div>
			</div>
			<script>
				const vscode = acquireVsCodeApi();
				document.getElementById('loginBtn').addEventListener('click', () => {
					vscode.postMessage({ type: 'login' });
				});
			</script>
		</body>
		</html>
		`;
	}

	public generateResourceListContent(): string {
		Logger.info("[WebviewContentGenerator] 显示资源列表页面");
		return this.getResourceListHTML();
	}

	private getResourceListHTML(): string {
		return `
			<!DOCTYPE html>
			<html>
			<head>
				<meta charset="UTF-8">
				<meta name="viewport" content="width=device-width, initial-scale=1.0">
				<style>
					${this.getResourceListStyles()}
				</style>
			</head>
			<body>
				<div class="container">
					<div class="search-container">
						<svg class="search-icon" width="16" height="16" viewBox="0 0 16 16" fill="currentColor" style="transform: scaleX(-1);">
							<path d="M11.742 10.344a6.5 6.5 0 1 0-1.397 1.398h-.001c.*************.098.115l3.85 3.85a1 1 0 0 0 1.415-1.414l-3.85-3.85a1.007 1.007 0 0 0-.115-.1zM12 6.5a5.5 5.5 0 1 1-11 0 5.5 5.5 0 0 1 11 0z"/>
						</svg>
						<input type="text" class="search-input" placeholder="${I18n.t("search.placeholder")}">
					</div>
					<div class="catalog-filter-container">
						<div class="catalog-filter" id="catalogFilter">
							<button class="catalog-item active" data-catalog="">全部</button>
							<button class="catalog-item" data-catalog="大模型">大模型</button>
							<button class="catalog-item" data-catalog="API 服务">API 服务</button>
							<div class="catalog-indicator"></div>
						</div>
					</div>
					<div class="resource-list" id="resourceList"></div>
				</div>
				<script>
				${this.getResourceListScript()}
				</script>
			</body>
			</html>
		`;
	}

	private getResourceListStyles(): string {
		return `
			html, body { height: 100%; margin: 0; padding: 0; box-sizing: border-box; }
			:root, body {
				/*
				 * VS Code 原生主题变量 - 会自动从当前主题获取
				 * 以下变量无需手动定义，VS Code 会自动提供：
				 *
				 * 基础背景和前景色：
				 * --vscode-editor-background: #18181BFF;
				 * --vscode-editor-foreground: #FFFFFFCC;
				 * --vscode-sideBar-background: #141414;
				 * --vscode-sideBar-foreground: #CCCCCC99;
				 * --vscode-sideBar-border: #FFFFFF0D;
				 * --vscode-foreground: #CCCCCCdd;
				 * --vscode-descriptionForeground: #CCCCCC;
				 *
				 * 输入框相关：
				 * --vscode-input-background: #2A2A2A55;
				 * --vscode-input-foreground: #FFFFFFCC;
				 * --vscode-input-border: #303035FF;
				 * --vscode-input-placeholderForeground: #FFFFFF33;
				 *
				 * 按钮相关：
				 * --vscode-button-background: #DBE0E9FF;
				 * --vscode-button-foreground: #18181BFF;
				 * --vscode-button-hoverBackground: #F0F7FFFF;
				 * --vscode-button-secondaryBackground: #303035FF;
				 * --vscode-button-secondaryForeground: #ececec;
				 * --vscode-button-secondaryHoverBackground: #767676;
				 *
				 * 列表相关：
				 * --vscode-list-hoverBackground: #2A2A2A99;
				 * --vscode-list-hoverForeground: #FFFFFF;
				 * --vscode-list-activeSelectionBackground: #ffffff1d;
				 * --vscode-list-activeSelectionForeground: #FFFFFF;
				 * --vscode-list-inactiveSelectionBackground: #ffffff10;
				 * --vscode-list-focusBackground: #434C5E;
				 * --vscode-list-highlightForeground: #88C0D0;
				 *
				 * 下拉框相关：
				 * --vscode-dropdown-background: #1a1a1a;
				 * --vscode-dropdown-foreground: #FFFFFF80;
				 * --vscode-dropdown-border: #2A2A2A;
				 *
				 * 焦点和边框：
				 * --vscode-focusBorder: #30373a;
				 */

				/* 个性化颜色 - 主题中没有的自定义颜色 */
				--quote-tag-background: #F3FBFB;
				--quote-tag-foreground: #18181B;
			}
			body {
				height: 100vh;
				display: flex;
				flex-direction: column;
				background: var(--vscode-sideBar-background, #141414) !important;
				color: var(--vscode-sideBar-foreground, #CCCCCC) !important;
				font-family: var(--vscode-font-family);
				margin: 0;
			}

			/* 确保所有文字都有正确的颜色 */
			* {
				color: inherit;
			}
			.container { display: flex; flex-direction: column; height: 100vh; }
			.search-container { position: sticky; top: 0; z-index: 2; background: var(--vscode-sideBar-background, #141414) !important; border-bottom: 1px solid var(--vscode-sideBar-border, #FFFFFF0D); margin: 8px 8px 0; display: flex; align-items: center; gap: 6px; }
			.catalog-filter-container { position: sticky;  z-index: 2; background: var(--vscode-sideBar-background, #141414) !important; margin: 0 8px 0px; padding: 8px 0; position: relative; }
			.catalog-filter-container::after { content: ''; position: absolute; bottom: 8px; left: 0; width: 100%; height: 1px; background-color: var(--vscode-menu-separatorBackground, #FFFFFF0D); }
			.catalog-filter { display: flex; overflow-x: auto; scrollbar-width: none; -ms-overflow-style: none; position: relative; }
			.catalog-filter::-webkit-scrollbar { display: none; }
			.catalog-item { position: relative; z-index:3; padding: 10px 12px; cursor: pointer; white-space: nowrap; border: none; background-color: transparent; color: var(--vscode-descriptionForeground, rgba(255,255,255,0.5)); font-size: 12px; transition: color 0.3s ease; position: relative; outline: none; }
			.catalog-item:first-child { padding-left: 0; }
			.catalog-item:last-of-type { padding-right: 0; }
			.catalog-item:hover,.catalog-item.active { color: var(--vscode-foreground, rgba(255,255,255,0.8)); }
			.catalog-indicator { position: absolute; bottom: 0; left: 0; height: 2px; background-color: #247FFF; transition: all 0.3s ease; }
			.resource-list { flex: 1 1 0; overflow-y: auto; min-height: 0; padding: 0 8px 8px; }
			.resource-item { padding: 12px; border-radius: 6px; cursor: pointer; position: relative; display: flex; gap: 8px; }
			.resource-item:hover { background: var(--vscode-list-hoverBackground); }
			.resource-logo { width: 40px; height: 40px; flex-shrink: 0; border-radius: 4px; }
			.resource-content { flex: 1; min-width: 0; display: flex; flex-direction: column; }
			.resource-label { font-size: 14px; font-weight: 500; color: var(--vscode-foreground); line-height: 22px; white-space: nowrap; overflow: hidden; text-overflow: ellipsis; }
			.resource-description { font-size: 12px; color: var(--vscode-descriptionForeground); line-height: 18px; white-space: nowrap; overflow: hidden; text-overflow: ellipsis; }
			.quote-tag { display: none; height: 22px; margin-top: 10px; padding: 0 8px; background: var(--quote-tag-background); color: var(--quote-tag-foreground); border-radius: 4px; font-size: 12px; font-weight: 500; line-height: 22px; }
			.resource-item:hover .quote-tag{ display: inline-block; }
			.search-container .search-icon{ position: absolute; top: 7px; left: 12px; color: var(--vscode-input-placeholderForeground); }
			.search-input { width: 100%; padding: 6px 12px 6px 32px; border: 1px solid var(--vscode-input-border); background: var(--vscode-input-background); color: var(--vscode-input-foreground); outline: none; border-radius: 4px; box-sizing: border-box; font-size: 12px; line-height: 18px; }
			.search-input::placeholder { color: var(--vscode-input-placeholderForeground); }
			.search-input:focus { border-color: var(--vscode-focusBorder); }
		`;
	}

	private getResourceListScript(): string {
		return `
		(function() {
			const vscode = acquireVsCodeApi();

			// 检查DOM元素是否存在
			const resourceList = document.getElementById('resourceList');
			const searchInput = document.querySelector('.search-input');
			const catalogFilter = document.getElementById('catalogFilter');
			const catalogIndicator = catalogFilter ? catalogFilter.querySelector('.catalog-indicator') : null;

			// 简化检查逻辑，只检查关键元素
			if (!resourceList || !catalogFilter) {
				// 延迟重试而不是直接返回
				setTimeout(() => {
					// 重新执行初始化逻辑
					window.location.reload();
				}, 1000);
				return;
			}

			let currentPage = 1;
			let pageSize = 50;
			let loading = false;
			let finished = false;
			let query = '';
			let catalog = '';
			let allRecords = [];
			let scrollDebounceTimer = null;
			let isInitialRenderComplete = false;
			let catalogItems = catalogFilter ? catalogFilter.querySelectorAll('.catalog-item') : [];

			// 动态渲染分类按钮 - 注释掉动态分类功能，改为固定分类
			function renderCatalogItems(catalogs) {
				if (!catalogFilter) {
					return;
				}

				// 注释掉动态分类功能，改为固定的三个分类
				/*
				// 清空现有的分类按钮（保留"全部"按钮、"大模型"按钮和指示器）
				const allButton = catalogFilter.querySelector('.catalog-item[data-catalog=""]');
				const modelButton = catalogFilter.querySelector('.catalog-item[data-catalog="大模型"]');
				const indicator = catalogFilter.querySelector('.catalog-indicator');
				catalogFilter.innerHTML = '';

				// 重新添加"全部"按钮
				if (allButton) {
					catalogFilter.appendChild(allButton);
				} else {
					const defaultAllButton = document.createElement('button');
					defaultAllButton.className = 'catalog-item active';
					defaultAllButton.setAttribute('data-catalog', '');
					defaultAllButton.textContent = '全部';
					catalogFilter.appendChild(defaultAllButton);
				}

				// 重新添加"大模型"按钮
				if (modelButton) {
					catalogFilter.appendChild(modelButton);
				} else {
					const defaultModelButton = document.createElement('button');
					defaultModelButton.className = 'catalog-item';
					defaultModelButton.setAttribute('data-catalog', '大模型');
					defaultModelButton.textContent = '大模型';
					catalogFilter.appendChild(defaultModelButton);
				}

				// 添加动态分类按钮（排除"大模型"，因为已经固定添加了）
				if (catalogs && Array.isArray(catalogs)) {
					catalogs.forEach(catalogItem => {
						const catalogName = catalogItem.name || catalogItem;
						// 跳过"大模型"分类，因为已经固定添加了
						if (catalogName === '大模型') {
							return;
						}
						const button = document.createElement('button');
						button.className = 'catalog-item';
						button.setAttribute('data-catalog', catalogName);
						button.textContent = catalogName;
						catalogFilter.appendChild(button);
					});
				}

				// 重新添加指示器
				if (indicator) {
					catalogFilter.appendChild(indicator);
				} else {
					const newIndicator = document.createElement('div');
					newIndicator.className = 'catalog-indicator';
					catalogFilter.appendChild(newIndicator);
				}
				*/

				// 固定分类：全部、大模型、API 服务
				// 分类按钮已在HTML中固定定义，无需动态创建

				// 更新catalogItems引用
				catalogItems = catalogFilter.querySelectorAll('.catalog-item');

				// 重新绑定事件监听器
				bindCatalogEvents();
			}

			// 绑定分类事件监听器
			function bindCatalogEvents() {
				catalogItems.forEach(item => {
					// 移除旧的事件监听器（如果有的话）
					item.removeEventListener('click', handleCatalogClick);
					// 添加新的事件监听器
					item.addEventListener('click', handleCatalogClick);
				});

				// 添加鼠标滚轮横向滚动支持
				catalogFilter.removeEventListener('wheel', handleCatalogWheel);
				catalogFilter.addEventListener('wheel', handleCatalogWheel);
			}

			// 分类点击处理函数
			function handleCatalogClick(event) {
				const targetCatalog = event.target.getAttribute('data-catalog') || '';
				switchCatalog(targetCatalog);
			}

			// 分类滚轮处理函数
			function handleCatalogWheel(e) {
				e.preventDefault();
				catalogFilter.scrollLeft += e.deltaY;
			}

			// 更新指示器位置的函数
			function updateCatalogIndicator(activeItem) {
				const currentIndicator = catalogFilter ? catalogFilter.querySelector('.catalog-indicator') : null;
				if (!currentIndicator || !activeItem) return;

				// 获取文本内容的实际宽度
				const textContent = activeItem.textContent || '';
				const canvas = document.createElement('canvas');
				const context = canvas.getContext('2d');
				context.font = '12px Arial'; // 与CSS中的字体大小保持一致
				const textWidth = context.measureText(textContent).width;

				// 指示器宽度等于文本宽度
				const indicatorWidth = textWidth;
				const itemLeft = activeItem.offsetLeft;
				const itemWidth = activeItem.offsetWidth;
				const paddingLeft = parseFloat(getComputedStyle(activeItem).paddingLeft) || 0;

				// 指示器左边距 = 元素左边距 + 左内边距
				const indicatorLeft = itemLeft + paddingLeft;

				currentIndicator.style.width = indicatorWidth + 'px';
				currentIndicator.style.left = indicatorLeft + 'px';
			}

			// 切换分类的函数
			function switchCatalog(targetCatalog) {
				// 移除所有活动状态
				catalogItems.forEach(item => item.classList.remove('active'));

				// 找到目标分类项并设置为活动状态
				let activeItem = null;
				catalogItems.forEach(item => {
					if (item.getAttribute('data-catalog') === targetCatalog) {
						item.classList.add('active');
						activeItem = item;
					}
				});

				if (activeItem) {
					// 更新指示器位置
					updateCatalogIndicator(activeItem);

					// 滚动到选中的标签（居中显示）
					activeItem.scrollIntoView({
						behavior: 'smooth',
						block: 'nearest',
						inline: 'center'
					});
				}

				// 更新全局分类变量
				catalog = targetCatalog;

				// 重置分页并发送请求
				currentPage = 1;
				finished = false;
				allRecords = [];
				setLoading();

				// 发送搜索请求（包含分类过滤）
				vscode.postMessage({ type: 'search', query, catalog, pageNum: currentPage, pageSize });
			}

			function setLoading() {
				resourceList.innerHTML = '<div style="padding:24px;text-align:center;color:var(--vscode-descriptionForeground);">${I18n.t("list.loading")}</div>';
			}

			function setNoData() {
				resourceList.innerHTML = '<div style="padding:24px;text-align:center;color:var(--vscode-descriptionForeground);">${I18n.t("list.noData")}</div>';
			}

			function setBottomLoading() {
				let loadingDiv = document.getElementById('bottom-loading');
				if (!loadingDiv) {
					loadingDiv = document.createElement('div');
					loadingDiv.id = 'bottom-loading';
					loadingDiv.style.cssText = 'padding:16px;text-align:center;color:var(--vscode-descriptionForeground);';
					resourceList.appendChild(loadingDiv);
				}
				loadingDiv.innerHTML = '<span class="loading-spinner"></span> ${I18n.t("list.loading")}';
			}

			function removeBottomLoading() {
				const loadingDiv = document.getElementById('bottom-loading');
				if (loadingDiv) loadingDiv.remove();
			}

			function setNoMore() {
				let noMoreDiv = document.getElementById('bottom-nomore');
				if (!noMoreDiv) {
					noMoreDiv = document.createElement('div');
					noMoreDiv.id = 'bottom-nomore';
					noMoreDiv.style.cssText = 'padding:16px;text-align:center;color:var(--vscode-descriptionForeground);';
					resourceList.appendChild(noMoreDiv);
				}
				noMoreDiv.innerText = '${I18n.t("list.noMore")}';
			}

			function removeNoMore() {
				const noMoreDiv = document.getElementById('bottom-nomore');
				if (noMoreDiv) noMoreDiv.remove();
			}

			// 优化的虚拟滚动渲染函数 - 使用DOM复用避免重复渲染
			let renderedItems = new Map(); // 缓存已渲染的DOM元素
			let topPaddingEl = null;
			let bottomPaddingEl = null;
			let lastRenderedRange = { start: -1, end: -1 };

			function createItemElement(item) {
				const itemEl = document.createElement('div');
				itemEl.className = 'resource-item';
				itemEl.setAttribute('data-label', item.label);
				itemEl.setAttribute('data-id', item.id);

				// 默认占位图片 - 使用base64编码的简单图标，无背景色
				const defaultImg = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTEwIDEySDMwVjI4SDEwVjEyWiIgc3Ryb2tlPSIjNjY2NjY2IiBzdHJva2Utd2lkdGg9IjIiIGZpbGw9Im5vbmUiLz4KPGNpcmNsZSBjeD0iMTYiIGN5PSIxOCIgcj0iMiIgZmlsbD0iIzY2NjY2NiIvPgo8cGF0aCBkPSJNMTAgMjRMMTYgMThMMjIgMjRMMjggMTgiIHN0cm9rZT0iIzY2NjY2NiIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiLz4KPC9zdmc+Cg==';

				itemEl.innerHTML =
					'<img class="resource-logo" src="' + item.logo + '" alt="' + item.label + '" onerror="this.src=&quot;' + defaultImg + '&quot;;this.onerror=null">' +
					'<div class="resource-content">' +
						'<div class="resource-label" title="' + item.label + '">' + item.label + '</div>' +
						'<div class="resource-description" title="' + item.description + '">' + item.description + '</div>' +
					'</div>' +
					'<span class="quote-tag">${I18n.t("list.quote")}</span>';

				// 绑定事件监听器
				bindItemEvents(itemEl, item);

				return itemEl;
			}

			function bindItemEvents(itemEl, item) {
				// 检查是否已经绑定过事件，避免重复绑定
				if (itemEl.hasAttribute('data-events-bound')) {
					return;
				}

				// 主点击事件
				const clickHandler = (e) => {
					// 如果点击的是quote按钮，不处理主点击事件
					if (e.target.classList.contains('quote-tag')) {
						return;
					}

					// 检查初始渲染是否完成，避免首次渲染时的点击问题
					if (!isInitialRenderComplete) {
						console.log('初始渲染未完成，忽略点击事件');
						return;
					}

					console.log('点击资源列表项:', {
						id: item.id,
						label: item.label,
						description: item.description,
						logo: item.logo,
						icon: item.icon,
						pluginType: item.pluginType,
						catalog: item.catalog,
						name: item.name,
						timestamp: new Date().toISOString()
					});
					vscode.postMessage({
						type: 'select',
						id: item.id,
						pluginType: item.pluginType,
						catalog: item.catalog,
						name: item.name || item.label
					});
				};

				itemEl.addEventListener('click', clickHandler);

				// quote按钮点击事件
				const quoteBtn = itemEl.querySelector('.quote-tag');
				if (quoteBtn) {
					const quoteHandler = (e) => {
						e.stopPropagation();
						console.log('点击引用按钮:', {
							id: item.id,
							label: item.label,
							timestamp: new Date().toISOString()
						});
						vscode.postMessage({
							type: 'quote',
							id: item.id,
							pluginType: item.pluginType,
							catalog: item.catalog,
							name: item.name || item.label
						});
					};

					quoteBtn.addEventListener('click', quoteHandler);
				}

				// 标记已绑定事件
				itemEl.setAttribute('data-events-bound', 'true');
			}

			// 简化的渲染函数，确保稳定性
			function renderResourcesSimple(items) {
				if (!items || items.length === 0) {
					setNoData();
					return;
				}

				// 清空现有内容
				resourceList.innerHTML = '';

				// 创建文档片段以提高性能
				const fragment = document.createDocumentFragment();

				// 渲染所有项目
				items.forEach((item, index) => {
					try {
						const itemEl = createItemElement(item);
						fragment.appendChild(itemEl);
					} catch (error) {
						console.error('创建列表项失败', { index, error: error.message });
					}
				});

				// 一次性添加到DOM
				resourceList.appendChild(fragment);
			}

			let debounceTimer = null;
			searchInput.addEventListener('input', (e) => {
				query = e.target.value.trim();
				if (debounceTimer) clearTimeout(debounceTimer);
				setLoading();
				debounceTimer = setTimeout(() => {
					currentPage = 1;
					finished = false;
					allRecords = [];
					vscode.postMessage({ type: 'search', query, catalog, pageNum: currentPage, pageSize });
				}, 500);
			});

			// 初始绑定分类事件监听器
			bindCatalogEvents();

			// 初始化指示器位置
			setTimeout(() => {
				const activeItem = catalogFilter.querySelector('.catalog-item.active');
				if (activeItem) {
					updateCatalogIndicator(activeItem);
				}
			}, 50);

			// 渐进式加载流程：立即显示UI，异步加载数据
			function initializeData() {
				// 立即显示基础UI
				showInitialUI();

				// 注释掉获取分类数据的调用，改为使用固定分类
				/*
				setTimeout(() => {
					vscode.postMessage({ type: 'getCatalogList', catalog_type: 'plugin' });
				}, 50);
				*/

				// 异步获取列表数据
				setTimeout(() => {
					vscode.postMessage({ type: 'getList', query, catalog, pageNum: currentPage, pageSize });
				}, 100);
			}

			// 立即显示基础UI
			function showInitialUI() {
				// 只显示"全部"按钮，不显示其他分类，避免视觉晃动
				// 分类按钮将在收到真实数据后再显示

				// 显示加载状态
				setLoading();

				// 初始化指示器位置（针对现有的"全部"按钮）
				setTimeout(() => {
					const activeItem = catalogFilter.querySelector('.catalog-item.active');
					if (activeItem) {
						updateCatalogIndicator(activeItem);
					}
				}, 50);
			}

			// 获取列表数据
			function loadListData() {
				vscode.postMessage({ type: 'getList', query, catalog, pageNum: currentPage, pageSize });
			}

			// 直接初始化
			initializeData();

			window.addEventListener('message', event => {
				const msg = event.data;
				if (msg.type === 'listData') {
					// 检查数据格式
					if (!msg.records || !Array.isArray(msg.records)) {
						setNoData();
						return;
					}

					const newRecords = msg.records.map(item => ({
						label: item.name,
						description: item.pluginDesc,
						id: item.pluginId,
						logo: item.iconUrl || 'https://files.codelife.cc/website/github.svg',
						icon: 'icon-search',
						// 保留原始字段用于区分大模型和插件
						pluginType: item.pluginType,
						catalog: item.catalog,
						name: item.name,
						// 保留完整的原始数据
						...item
					}));

					if (currentPage === 1) {
						allRecords = newRecords;
						window._virtualStartIndex = 0;

						// 确保DOM更新 - 使用简化渲染确保稳定性
						setTimeout(() => {
							// 直接使用简化渲染，避免虚拟滚动的复杂性
							renderResourcesSimple(allRecords);

							// 首次渲染完成后，延迟一段时间再允许点击，确保DOM完全就绪
							setTimeout(() => {
								isInitialRenderComplete = true;
							}, 300);
						}, 50);
					} else {
						allRecords = allRecords.concat(newRecords);
						// 对于分页数据也使用简化渲染，确保一致性
						renderResourcesSimple(allRecords);
					}

					// 重置加载状态
					loading = false;
					removeBottomLoading();
					removeNoMore();

					// 检查是否已经加载完所有数据
					if ((!newRecords.length || (msg.records.length < pageSize)) && allRecords.length > 0) {
						finished = true;
						setNoMore();
					}
				} else if (msg.type === 'catalogListData') {
					// 注释掉动态分类处理逻辑，改为使用固定分类
					/*
					let catalogsToRender = [];

					// 检查数据格式并处理
					if (!msg.catalogs || !Array.isArray(msg.catalogs) || msg.catalogs.length === 0) {
						// 只在数据真正异常时才使用默认分类（不包含"大模型"，因为已经固定添加了）
						catalogsToRender = [
							{ name: '效率工具' },
							{ name: '知识问答' },
							{ name: '代码助手' }
						];
					} else {
						// 使用真实的分类数据
						catalogsToRender = msg.catalogs;
					}

					// 渲染分类按钮
					renderCatalogItems(catalogsToRender);

					// 重新初始化指示器位置
					setTimeout(() => {
						const activeItem = catalogFilter.querySelector('.catalog-item.active');
						if (activeItem) {
							updateCatalogIndicator(activeItem);
						}
					}, 50);

					// 分类数据处理完成，不需要重新加载列表（已经在初始化时加载了）
					// 如果需要根据新分类重新加载，可以在这里添加逻辑
					*/

					// 固定分类，无需处理动态数据，只需要初始化指示器位置
					setTimeout(() => {
						const activeItem = catalogFilter.querySelector('.catalog-item.active');
						if (activeItem) {
							updateCatalogIndicator(activeItem);
						}
					}, 50);
				}
			});

			${this.getScrollHandlerScript()}
		})();
		`;
	}



	private getScrollHandlerScript(): string {
		return `
			resourceList.addEventListener('scroll', function() {
				// 简化版本：只处理分页加载，不处理虚拟滚动
				// 懒加载分页逻辑
				if (loading || finished) {
					return;
				}

				if (scrollDebounceTimer) clearTimeout(scrollDebounceTimer);
				scrollDebounceTimer = setTimeout(() => {
					// 检查是否滚动到底部
					const isAtBottom = resourceList.scrollTop + resourceList.clientHeight >= resourceList.scrollHeight - 10;

					if (isAtBottom) {
						loading = true;
						setBottomLoading();
						currentPage++;

						// 根据当前是否有搜索查询或分类过滤来决定发送什么消息
						const messageType = (query.trim() || catalog) ? 'search' : 'getList';
						const message = {
							type: messageType,
							query: query.trim(),
							catalog: catalog,
							pageNum: currentPage,
							pageSize
						};
						vscode.postMessage(message);
					}
				}, 150);
			});
		`;
	}
}
