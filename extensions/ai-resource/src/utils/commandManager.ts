import * as vscode from "vscode";
import { SettingsWebviewPanel } from "../panels/settingsWebviewPanel";
import { JoycodeHelper } from "./joycodeHelper";
import { getCurrentBaseUrl } from "../config";
import { getCurrent<PERSON><PERSON><PERSON><PERSON> } from "../config";
import {
	AI_RESOURCES_SELECT_RESOURCE,
	AI_RESOURCES_FETCH_RESOURCES,
	AI_RESOURCES_OPEN_SETTINGS,
	AI_RESOURCES_WRITE_API_KEY,
	AI_RESOURCES_SHOW_DETAIL,
	MARKDOWN_SHOW_PREVIEW,
	AI_RESOURCE_DETAIL_SCHEME
} from "../constants/commands";

/**
 * 命令管理器 - 统一管理所有扩展命令
 */
export class CommandManager {
	private context: vscode.ExtensionContext;
	private settingsPanel: SettingsWebviewPanel;

	constructor(context: vscode.ExtensionContext, settingsPanel: SettingsWebviewPanel) {
		this.context = context;
		this.settingsPanel = settingsPanel;
	}

	/**
	 * 注册所有命令
	 */
	registerCommands(): void {
		this.registerResourceCommands();
		this.registerSettingsCommands();
		this.registerDetailCommands();
	}

	/**
	 * 注册资源相关命令
	 */
	private registerResourceCommands(): void {
		// 资源选择命令
		this.context.subscriptions.push(
			vscode.commands.registerCommand(AI_RESOURCES_SELECT_RESOURCE, (item) => {
				if (item) {
					vscode.commands.executeCommand(AI_RESOURCES_SHOW_DETAIL, item.id);
				}
			})
		);

		// 资源加载命令
		this.context.subscriptions.push(
			vscode.commands.registerCommand(AI_RESOURCES_FETCH_RESOURCES, async (_params) => {
				// TODO: 实现实际的资源获取逻辑
				return {
					records: [
						{
							pluginId: '1',
							name: '示例资源1',
							pluginDesc: '这是一个示例资源',
							iconUrl: 'https://files.codelife.cc/website/github.svg'
						},
						{
							pluginId: '2',
							name: '示例资源2',
							pluginDesc: '这是另一个示例资源',
							iconUrl: 'https://files.codelife.cc/website/github.svg'
						}
					]
				};
			})
		);
	}

	/**
	 * 注册设置相关命令
	 */
	private registerSettingsCommands(): void {
		this.context.subscriptions.push(
			// 打开设置面板
			vscode.commands.registerCommand(AI_RESOURCES_OPEN_SETTINGS, () => {
				this.settingsPanel.show();
			}),

			// 写入API key命令
			vscode.commands.registerCommand(AI_RESOURCES_WRITE_API_KEY, async () => {
				try {
					const keyList = await this.settingsPanel.fetchAndMaskKeyList();
					if (keyList && keyList.length > 0) {
						const apiKey = String(keyList[0].value);
						const baseUrl = getCurrentBaseUrl();

						// 验证必要条件：必须有 apikey 和 baseUrl
						if (!apiKey || !apiKey.trim()) {
							vscode.window.showWarningMessage("API Key 为空，无法写入文件");
							return;
						}

						if (!baseUrl || !baseUrl.trim()) {
							vscode.window.showWarningMessage("baseUrl 配置异常，无法写入文件");
							return;
						}

						await JoycodeHelper.ensureApiKeyFile(apiKey, baseUrl);
						vscode.window.showInformationMessage("API key 和 baseUrl 写入成功");
					} else {
						vscode.window.showWarningMessage("没有可用的API key");
					}
				} catch (error) {
					vscode.window.showErrorMessage("API key写入失败: " + error);
				}
			})
		);
	}

	/**
	 * 注册详情相关命令
	 */
	private registerDetailCommands(): void {
		this.context.subscriptions.push(
			vscode.commands.registerCommand(
				AI_RESOURCES_SHOW_DETAIL,
				(resourceId: string) => {
					// 检查是否有 API key
					if (!getCurrentApiKey()) {
						vscode.window.showWarningMessage("请先设置 API key", "打开设置").then(selection => {
							if (selection === "打开设置") {
								vscode.commands.executeCommand(AI_RESOURCES_OPEN_SETTINGS);
							}
						});
						return;
					}

					this.showResourceDetail(resourceId);
				}
			)
		);
	}

	/**
	 * 显示资源详情
	 */
	private showResourceDetail(resourceId: string): void {
		const mdContent = `# 插件详情\n\n暂无详情信息`;
		const uri = vscode.Uri.parse(`${AI_RESOURCE_DETAIL_SCHEME}:${resourceId}.md`);

		const provider = new (class implements vscode.TextDocumentContentProvider {
			provideTextDocumentContent() {
				return mdContent;
			}
		})();

		const reg = vscode.workspace.registerTextDocumentContentProvider(
			AI_RESOURCE_DETAIL_SCHEME,
			provider,
		);

		this.context.subscriptions.push(reg);
		vscode.commands.executeCommand(MARKDOWN_SHOW_PREVIEW, uri);
	}
}
