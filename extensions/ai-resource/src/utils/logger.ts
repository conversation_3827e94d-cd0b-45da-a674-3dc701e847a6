import * as vscode from 'vscode';
import { getCurrentEnv } from './globalContext';

export class Logger {
  private static outputChannel = vscode.window.createOutputChannel('资源插件');

  static info(message: string) {
    this.log('info', message);
  }

  static warn(message: string) {
    this.log('warn', message);
  }

  static error(message: string) {
    this.log('error', message);
  }

  private static log(type: 'info' | 'warn' | 'error', message: string) {
    return;
    const env = getCurrentEnv();
    // this.outputChannel.appendLine(env);

    let prefix = '';
    switch (type) {
      case 'info':
        prefix = '[信息]';
        break;
      case 'warn':
        prefix = '[警告]';
        break;
      case 'error':
        prefix = '[错误]';
        break;
    }
    const logMsg = `${prefix} ${message}`;
    this.outputChannel.appendLine(logMsg);
  }
}
