import * as vscode from "vscode";
import { getUserKeyList } from "../service/api";
import { setApi<PERSON>ey<PERSON>ist, setCurrentApiKeyToCache, getCurrentApiKey, getCurrentBaseUrl, isAuthReady } from "../config";
import { selectData } from "../types";
import { maskKey } from "../utils/mask";
import { Logger } from "../utils/logger";
import { I18n } from "../utils/i18n";
import { AuthenticationManager } from "../providers/auth/authenticationManager";
import { JoycodeHelper } from "../utils/joycodeHelper";
import { AI_RESOURCES_REFRESH } from "../constants/commands";

export class SettingsWebviewPanel {
	private panel: vscode.WebviewPanel | undefined;
	private context: vscode.ExtensionContext;
	private authManager: AuthenticationManager;

	constructor(context: vscode.ExtensionContext) {
		this.context = context;
		this.authManager = new AuthenticationManager();
	}

	/**
	 * 关闭设置面板
	 */
	public close(): void {
		if (this.panel) {
			Logger.info("[SettingsWebviewPanel] 关闭设置面板");
			this.panel.dispose();
			this.panel = undefined;
		}
	}

	public async fetchAndMaskKeyList() {
		try {
			const res = await getUserKeyList();
			let keyList: selectData[] = [];
			if (res && res.data && Array.isArray(res.data.data)) {
				keyList = res.data.data.map((item: any) => ({
					value: item.apiKey,
					label: maskKey(item.apiKey),
				}));
			}
			setApiKeyList(keyList);
			return keyList;
		} catch (e) {
			Logger.error(
				`[settingsWebviewPanel] 获取key列表失败: ${e instanceof Error ? e.message : String(e)}`,
			);
			return [];
		}
	}

	public async show() {
		if (this.panel) {
			this.panel.reveal();
			return;
		}
		this.panel = vscode.window.createWebviewPanel(
			"aiResourcesSettings",
			I18n.t("settings.title"),
			vscode.ViewColumn.One,
			{
				enableScripts: true,
				// 启用缓存：当webview隐藏时保持其状态和内容，避免重新加载
				retainContextWhenHidden: true,
				// 启用查找功能
				enableFindWidget: true,
			},
		);

		// 设置标签页图标，与扩展图标保持一致
		this.panel.iconPath = vscode.Uri.joinPath(this.context.extensionUri, "resources", "images", "ai.svg");

		// 检查登录状态
		const isLoggedIn = await this.authManager.isLoggedIn();
		const { isJoyCode } = this.authManager.getAuthenticationInfo();

		this.panel.webview.html = await this.getWebviewContent(isLoggedIn, isJoyCode);
		this.panel.onDidDispose(() => {
			this.panel = undefined;
		});
		this.panel.webview.onDidReceiveMessage(async (message) => {
			// Logger.info(`[settingsWebviewPanel] 收到webview消息: ${JSON.stringify(message)}`);
			switch (message.type) {
				case "login":
					// 处理登录请求
					await this.authManager.handleLogin();
					break;
				case "save":
					if (message.key) {
						// 设置到内存缓存和工作区状态
						await setCurrentApiKeyToCache(message.key);

						// 检查认证状态
						const authReady = isAuthReady();
						Logger.info(`[settingsWebviewPanel] 保存后认证状态: ${authReady ? '就绪' : '未就绪'}`);

						// 写入本地文件，必须确保有 apikey 和 baseUrl
						try {
							// 获取当前的 baseUrl（从全局配置中获取用户的配置）
							const baseUrl = getCurrentBaseUrl();

							// 验证必要条件：必须有 apikey 和 baseUrl
							if (!message.key || !message.key.trim()) {
								Logger.warn("设置面板：API Key 为空，跳过文件写入");
								vscode.window.showWarningMessage("API Key 不能为空");
								return;
							}

							if (!baseUrl || !baseUrl.trim()) {
								Logger.warn("设置面板：baseUrl 为空，跳过文件写入");
								vscode.window.showWarningMessage("baseUrl 配置异常，无法保存到文件");
								return;
							}

							Logger.info(`设置面板：准备保存 API Key 和 baseUrl: ${baseUrl}`);
							await JoycodeHelper.ensureApiKeyFile(message.key, baseUrl);
							Logger.info("设置面板：API Key 和 baseUrl 已保存到本地文件");
						} catch (error) {
							Logger.warn("设置面板：写入 API Key 到本地文件失败: " + error);
							vscode.window.showErrorMessage("保存到文件失败: " + error);
						}

						vscode.window.showInformationMessage(I18n.t("settings.success.apiKeySaved"));

						// 保存成功后，直接刷新资源列表的 webview
						try {
							Logger.info('[settingsWebviewPanel] 保存后刷新资源列表 webview');
							await vscode.commands.executeCommand(AI_RESOURCES_REFRESH);
							Logger.info('[settingsWebviewPanel] 资源列表 webview 刷新命令已执行');
						} catch (e) {
							Logger.error(
								`[settingsWebviewPanel] 保存后刷新资源列表失败: ${e instanceof Error ? e.message : String(e)}`,
							);
						}
					} else {
						vscode.window.showWarningMessage(I18n.t("settings.warning.noApiKeySelected"));
					}
					break;
				case "getNewKey":
					vscode.window.showInformationMessage(I18n.t("settings.info.gettingNewKey"));
					break;
				case "refreshKey":
					// 显示刷新提示
					vscode.window.showInformationMessage(I18n.t("settings.info.refreshingKeys"));

					// 只有在登录状态下才刷新key列表
					if (!isLoggedIn) {
						vscode.window.showWarningMessage(I18n.t("settings.warning.notLoggedIn"));
						this.panel?.webview.postMessage({ type: "keyList", list: [] });
						return;
					}

					try {
						Logger.info('[settingsWebviewPanel] 开始刷新用户key列表');
						// 重新获取并更新key列表
						const keyList = await this.fetchAndMaskKeyList();
						const currentApiKey = getCurrentApiKey();

						// 发送更新后的列表到前端
						this.panel?.webview.postMessage({
							type: "keyList",
							list: keyList,
							currentApiKey,
						});

						Logger.info(`[settingsWebviewPanel] 刷新完成，获取到 ${keyList.length} 个key`);
						vscode.window.showInformationMessage(I18n.t("settings.success.keysRefreshed"));
					} catch (e) {
						Logger.error(
							`[settingsWebviewPanel] 刷新key列表失败: ${e instanceof Error ? e.message : String(e)}`,
						);
						this.panel?.webview.postMessage({ type: "keyList", list: [] });
						vscode.window.showErrorMessage(I18n.t("settings.error.refreshKeyListFailedMessage"));
					}
					break;
				case "requestKeyList":
					// 只有在登录状态下才获取key列表
					if (!isLoggedIn) {
						this.panel?.webview.postMessage({ type: "keyList", list: [] });
						return;
					}
					try {
						// Logger.info('[settingsWebviewPanel] 开始获取用户key列表');
						const keyList = await this.fetchAndMaskKeyList();
						// Logger.info(`[settingsWebviewPanel] 获取到keyList: ${JSON.stringify(keyList)}`);
						const currentApiKey = getCurrentApiKey();
						this.panel?.webview.postMessage({
							type: "keyList",
							list: keyList,
							currentApiKey,
						});
					} catch (e) {
						Logger.error(
							`[settingsWebviewPanel] 获取key列表失败: ${e instanceof Error ? e.message : String(e)}`,
						);
						this.panel?.webview.postMessage({ type: "keyList", list: [] });
						vscode.window.showErrorMessage(I18n.t("settings.error.getKeyListFailedMessage"));
					}
					break;
			}
		});

		// 只有在登录状态下才初始化获取key列表
		if (isLoggedIn && isJoyCode) {
			try {
				// Logger.info('[settingsWebviewPanel] show激活时获取用户key列表');
				const keyList = await this.fetchAndMaskKeyList();
				// Logger.info(`[settingsWebviewPanel] 激活时获取到keyList: ${JSON.stringify(keyList)}`);
				const currentApiKey = getCurrentApiKey();
				this.panel.webview.postMessage({
					type: "keyList",
					list: keyList,
					currentApiKey,
				});
			} catch (e) {
				Logger.error(
					`[settingsWebviewPanel] 激活时获取key列表失败: ${e instanceof Error ? e.message : String(e)}`,
				);
				this.panel.webview.postMessage({ type: "keyList", list: [] });
				vscode.window.showErrorMessage(I18n.t("settings.error.activationGetKeyListFailedMessage"));
			}
		}
	}

	private async getWebviewContent(isLoggedIn: boolean = true, isJoyCode: boolean = true): Promise<string> {
		// 使用 joycode-common 扩展提供的 iconfont
		const joyCodeCommonExtension = vscode.extensions.getExtension('joycode.joycode-common');
		let cssUri: vscode.Uri;
		let fontBaseUri: string = '';

		if (joyCodeCommonExtension) {
			// 使用 joycode-common 扩展的 iconfont
			cssUri = this.panel!.webview.asWebviewUri(
				vscode.Uri.joinPath(
					joyCodeCommonExtension.extensionUri,
					"iconfont",
					"iconfont.css",
				),
			);

			// 获取字体文件的基础 URI
			fontBaseUri = this.panel!.webview.asWebviewUri(
				vscode.Uri.joinPath(
					joyCodeCommonExtension.extensionUri,
					"iconfont"
				),
			).toString();

			Logger.info(`[settingsWebviewPanel] 使用 joycode-common 扩展的 iconfont: ${cssUri.toString()}`);
		} else {
			// 回退到原来的路径
			cssUri = this.panel!.webview.asWebviewUri(
				vscode.Uri.joinPath(
					this.context.extensionUri,
					"media",
					"iconfont",
					"iconfont.css",
				),
			);
			Logger.warn(`[settingsWebviewPanel] joycode-common 扩展未找到，使用默认路径: ${cssUri.toString()}`);
		}

		// 如果不是JoyCode环境，显示环境错误
		if (!isJoyCode) {
			return this.getEnvironmentErrorContent(cssUri);
		}

		// 如果未登录，显示登录提示
		if (!isLoggedIn) {
			return this.getLoginRequiredContent(cssUri);
		}

		// 已登录，显示正常的设置界面
		return this.getNormalSettingsContent(cssUri, fontBaseUri);
	}

	private getEnvironmentErrorContent(cssUri: vscode.Uri): string {
		return `
			<!DOCTYPE html>
			<html>
			<head>
				<meta charset="UTF-8">
				<meta name="viewport" content="width=device-width, initial-scale=1.0">
				<link rel="stylesheet" href="${cssUri}">
				<style>
					body {
						background: var(--vscode-editor-background);
						color: var(--vscode-foreground);
						font-family: var(--vscode-font-family);
						padding: 20px;
						text-align: center;
					}
					.error-container {
						padding: 40px 20px;
					}
					.error-icon {
						font-size: 48px;
						color: #f48771;
						margin-bottom: 16px;
					}
					.error-title {
						font-size: 18px;
						font-weight: 600;
						margin-bottom: 8px;
					}
					.error-message {
						font-size: 14px;
						color: var(--vscode-descriptionForeground);
					}
				</style>
			</head>
			<body>
				<div class="error-container">
					<div class="error-icon">⚠️</div>
					<div class="error-title">${I18n.t("environment.error.title")}</div>
					<div class="error-message">${I18n.t("environment.error.useInJoyCode")}</div>
				</div>
			</body>
			</html>
		`;
	}

	private getLoginRequiredContent(cssUri: vscode.Uri): string {
		return `
			<!DOCTYPE html>
			<html>
			<head>
				<meta charset="UTF-8">
				<meta name="viewport" content="width=device-width, initial-scale=1.0">
				<link rel="stylesheet" href="${cssUri}">
				<style>
					body {
						background: var(--vscode-editor-background);
						color: var(--vscode-foreground);
						font-family: var(--vscode-font-family);
						padding: 20px;
						text-align: center;
					}
					.login-container {
						padding: 40px 20px;
					}
					.login-icon {
						font-size: 48px;
						color: #007acc;
						margin-bottom: 16px;
					}
					.login-title {
						font-size: 18px;
						font-weight: 600;
						margin-bottom: 8px;
					}
					.login-message {
						font-size: 14px;
						color: var(--vscode-descriptionForeground);
						margin-bottom: 24px;
					}
					.login-button {
						background: var(--vscode-button-background);
						color: var(--vscode-button-foreground);
						border: none;
						border-radius: 4px;
						padding: 8px 16px;
						font-size: 14px;
						cursor: pointer;
						transition: background-color 0.2s;
					}
					.login-button:hover {
						background: var(--vscode-button-hoverBackground);
					}
				</style>
			</head>
			<body>
				<div class="login-container">
					<div class="login-icon">🔐</div>
					<div class="login-title">${I18n.t("settings.loginRequired.title")}</div>
					<div class="login-message">${I18n.t("settings.loginRequired.message")}</div>
					<button class="login-button" id="loginBtn">${I18n.t("settings.loginRequired.loginButton")}</button>
				</div>
				<script>
					const vscode = acquireVsCodeApi();
					document.getElementById('loginBtn').addEventListener('click', () => {
						vscode.postMessage({ type: 'login' });
					});
				</script>
			</body>
			</html>
		`;
	}

	private getNormalSettingsContent(cssUri: vscode.Uri, fontBaseUri: string = ''): string {
		return `
			<!DOCTYPE html>
			<html>
			<head>
				<meta charset="UTF-8">
				<meta name="viewport" content="width=device-width, initial-scale=1.0">
				<link rel="stylesheet" href="${cssUri}">
				<style>
					${fontBaseUri ? `
					@font-face {
						font-family: "iconfont";
						src: url('${fontBaseUri}/iconfont.woff2?t=1749457383791') format('woff2'),
							 url('${fontBaseUri}/iconfont.woff?t=1749457383791') format('woff'),
							 url('${fontBaseUri}/iconfont.ttf?t=1749457383791') format('truetype'),
							 url('${fontBaseUri}/iconfont.svg?t=1749457383791#iconfont') format('svg');
					}
					` : ''}
					:root, body{
						/*
						 * VS Code 原生主题变量 - 会自动从当前主题获取
						 * 以下变量无需手动定义，VS Code 会自动提供：
						 *
						 * 基础背景和前景色：
						 * --vscode-editor-background: #18181BFF;
						 * --vscode-editor-foreground: #FFFFFFCC;
						 * --vscode-sideBar-background: #141414;
						 * --vscode-sideBar-foreground: #CCCCCC99;
						 * --vscode-sideBar-border: #FFFFFF0D;
						 * --vscode-foreground: #CCCCCCdd;
						 * --vscode-descriptionForeground: #CCCCCC;
						 *
						 * 输入框相关：
						 * --vscode-input-background: #2A2A2A55;
						 * --vscode-input-foreground: #FFFFFFCC;
						 * --vscode-input-border: #303035FF;
						 * --vscode-input-placeholderForeground: #FFFFFF33;
						 *
						 * 按钮相关：
						 * --vscode-button-background: #DBE0E9FF;
						 * --vscode-button-foreground: #18181BFF;
						 * --vscode-button-hoverBackground: #F0F7FFFF;
						 * --vscode-button-secondaryBackground: #303035FF;
						 * --vscode-button-secondaryForeground: #ececec;
						 *
						 * 列表相关：
						 * --vscode-list-hoverBackground: #2A2A2A99;
						 * --vscode-list-hoverForeground: #FFFFFF;
						 * --vscode-list-activeSelectionBackground: #ffffff1d;
						 * --vscode-list-activeSelectionForeground: #FFFFFF;
						 *
						 * 下拉框相关：
						 * --vscode-dropdown-background: #1a1a1a;
						 * --vscode-dropdown-foreground: #FFFFFF80;
						 * --vscode-dropdown-border: #2A2A2A;
						 *
						 * 焦点和边框：
						 * --vscode-focusBorder: #30373a;
						 */

						/* 个性化颜色 - 主题中没有的自定义颜色 */
						--joyCode-link-color: #247BFF;
						--save-btn-background: #F3FBFB;
						--save-btn-foreground: #18181B;
					}
					body { background: var(--vscode-editor-background, #18181B) !important; color: var(--vscode-foreground, #CCCCCC) !important; font-family: var(--vscode-font-family); padding: 0; margin: 0;}

					/* 确保所有文字都有正确的颜色 */
					* {
						color: inherit;
					}
					.settings-header { display:none; background: var(--vscode-editor-background); border-bottom: 1px solid var(--vscode-input-border); padding: 12px 16px; }
					.header-content { display: flex; align-items: center; gap: 8px; }
					.header-icon { color: var(--vscode-foreground); }
					.header-title { font-size: 14px; font-weight: 600; color: var(--vscode-foreground); }
					.settings-container { padding: 16px; }
					.settings-footer {  padding: 12px 16px; position: sticky; bottom: 0; z-index: 1; text-align: right; background: var(--vscode-editor-background); border-top: 1px solid var(--vscode-input-border); }
					.save-btn { padding: 4px 12px; background: var(--save-btn-background); color: var(--save-btn-foreground); border: none; border-radius: 2px; cursor: pointer; font-size: 12px;font-weight: 500; line-height: 18px }
					.form-group { margin-bottom: 8px; }
					.form-label { display: flex; justify-content: space-between; align-items: center; margin-bottom: 8px; font-size: 12px; color: var(--vscode-foreground); line-height: 18px; padding: 5px 0}
					.get-key { display:none; color: var(--joyCode-link-color); text-decoration: none; cursor: pointer; font-size: 12px; }
					.get-key:hover { text-decoration: underline; }
					.input-group { display: flex; gap: 8px; align-items: center; }
					.refresh-btn { background: none; border: none; color: var(--vscode-foreground); cursor: pointer; display: flex; align-items: center; justify-content: center; width: 32px; height: 32px; padding: 0; border-radius: 4px; }
					.refresh-btn:hover { opacity: 0.8; }
					.input-group .select-wrapper{ flex: 1 1; }
					.input-group .select-input{ width: calc(100% - 28px); }
					.input-group .select-input:focus{ outline: none; }
					.select-wrapper { position: relative; }
					input.select-input { width: 100%; padding: 4px 12px; background: var(--vscode-input-background); color: var(--vscode-input-foreground); border: 1px solid var(--vscode-input-border); border-radius: 4px; font-size: 12px; font-weight: normal; line-height: 18px; }
					.version-dropdown-panel { position: absolute; left: 0; right: 0; top: 100%; background: var(--vscode-dropdown-background); border: 1px solid var(--vscode-input-border); border-radius: 6px; box-shadow: 0 4px 24px 0 rgba(0,0,0,1); z-index: 9999; max-height: 200px; overflow-y: auto; display: none; text-align: left; }
					.version-dropdown-item { padding: 8px 8px; cursor: pointer; outline: none; display: block; color: var(--vscode-dropdown-foreground); background: transparent; transition: background 0.2s; }
					.version-dropdown-item:hover, .version-dropdown-item.selected { background: var(--vscode-list-hoverBackground); color: var(--vscode-list-hoverForeground); }
					.version-dropdown-icon { position: absolute; right: 8px; top: 50%; transform: translateY(-50%); width: 16px; height: 16px; pointer-events: none; display: flex; align-items: center; justify-content: center; }
					.version-dropdown-icon .icon-shang,.version-dropdown-icon .xia{ color: var(--vscode-input-foreground); font-size: 14px; }
					.select-wrapper .icon-shang { display: none; }
					.select-wrapper.active .icon-xia { display: none; }
					.select-wrapper.active .icon-shang { display: inline; }
				</style>
			</head>
			<body>
				<div class="settings-header">
					<div class="header-content">
						<svg class="header-icon" width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
							<path d="M13.7188 1.08008C14.3261 1.08008 14.8182 1.57234 14.8184 2.17969V13.6172C14.8184 14.2247 14.3263 14.7168 13.7188 14.7168H2.28125C1.67374 14.7168 1.18164 14.2247 1.18164 13.6172V2.17969C1.18184 1.57234 1.67386 1.08008 2.28125 1.08008H13.7188ZM2.38086 8.5V13.5176H13.6191V8.5H10.5273C10.2563 9.64629 9.22922 10.5 8 10.5C6.77078 10.5 5.74366 9.64629 5.47266 8.5H2.38086ZM8 6.5C7.2268 6.5 6.59961 7.12719 6.59961 7.90039C6.59981 8.67342 7.22692 9.30078 8 9.30078C8.77308 9.30078 9.40019 8.67342 9.40039 7.90039C9.40039 7.12719 8.7732 6.5 8 6.5ZM2.38086 7.30078H5.47266C5.74341 6.15414 6.77054 5.30078 8 5.30078C9.22946 5.30078 10.2566 6.15414 10.5273 7.30078H13.6191V2.2793H2.38086V7.30078Z" fill="currentColor"/>
						</svg>
						<span class="header-title">AI Resources Settings</span>
					</div>
				</div>
				<div class="settings-container">
					<div class="form-group">
						<div class="form-label">
							<span>API Key:</span>
							<a class="get-key">Get New Key</a>
						</div>
						<div class="input-group">
							<div class="select-wrapper">
								<input id="sky" type="text" class="template-version-input template-input select-input" readOnly />
								<span class="version-dropdown-icon">
									<svg class="icon-xia" viewBox="0 0 1024 1024" width="14" height="14"><path d="M521.984 659.52a12.8 12.8 0 0 1-19.968 0L272.64 372.8a12.8 12.8 0 0 1 9.984-20.8h458.752a12.8 12.8 0 0 1 9.984 20.8l-229.376 286.72z" fill="var(--vscode-input-foreground)"></path></svg>
									<svg class="icon-shang" viewBox="0 0 1024 1024" width="14" height="14"><path d="M521.984 364.48a12.8 12.8 0 0 0-19.968 0l-229.376 286.72a12.8 12.8 0 0 0 9.984 20.8h458.752a12.8 12.8 0 0 0 9.984-20.8l-229.376-286.72z" fill="var(--vscode-input-foreground)"></path></svg>
								</span>
							</div>
							<button class="refresh-btn" type="button" title="${I18n.t("settings.button.refresh")}">
								<svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
									<path d="M8 3a5 5 0 1 0 4.546 2.914.5.5 0 0 1 .908-.417A6 6 0 1 1 8 2v1z"/>
									<path d="M8 4.466V.534a.25.25 0 0 1 .41-.192l2.36 1.966c.12.1.12.284 0 .384L8.41 4.658A.25.25 0 0 1 8 4.466z"/>
								</svg>
							</button>
						</div>
					</div>
				</div>
				<div class="settings-footer">
					<button class="save-btn">${I18n.t("settings.button.save")}</button>
				</div>
				<script>
				(function() {
					function initSelectOption(id, list, currentApiKey) {
						const versionInput = document.getElementById(id);
						const versionSelectWrapper = versionInput && versionInput.parentElement;
						if (versionSelectWrapper) {
							let versionDropdown = versionSelectWrapper.querySelector('.version-dropdown-panel');
							if (!versionDropdown) {
								versionDropdown = document.createElement('div');
								versionDropdown.className = 'dropdown-panel version-dropdown-panel';
								versionDropdown.style.zIndex = '200';
								versionSelectWrapper.appendChild(versionDropdown);
							}
							versionDropdown.innerHTML = '';
							if (!list || list.length === 0) {
								const emptyDiv = document.createElement('div');
								emptyDiv.className = 'version-dropdown-item';
								emptyDiv.textContent = '${I18n.t("settings.noAvailableKeys")}';
								emptyDiv.style.color = 'var(--vscode-descriptionForeground)';
								emptyDiv.style.cursor = 'default';
								versionDropdown.appendChild(emptyDiv);
								versionInput.value = '';
								versionInput.setAttribute('data-selected-value', '');
								return versionInput;
							}
							let defaultIdx = 0;
							if (currentApiKey) {
								const idx = list.findIndex(item => item.value === currentApiKey);
								if (idx !== -1) defaultIdx = idx;
							}
							list.forEach((ver, idx) => {
								const opt = document.createElement('div');
								opt.className = 'version-dropdown-item';
								opt.textContent = ver.label;
								opt.setAttribute('data-value', ver.value);
								if (idx === defaultIdx) opt.classList.add('selected');
								versionDropdown.appendChild(opt);
								opt.addEventListener('click', (e) => {
									e.stopPropagation();
									versionInput.value = ver.label;
									versionInput.setAttribute('data-selected-value', ver.value);
									Array.from(versionDropdown.children).forEach((child) => child.classList.remove('selected'));
									opt.classList.add('selected');
									versionDropdown.style.display = 'none';
									versionSelectWrapper.classList.remove('active');
								});
							});
							versionInput.value = list[defaultIdx].label;
							versionInput.setAttribute('data-selected-value', list[defaultIdx].value);
							versionInput.addEventListener('click', (e) => {
								e.stopPropagation();
								document.querySelectorAll('.version-dropdown-panel').forEach((panel) => {
									if (panel!== versionDropdown) panel.style.display = 'none';
								});
								versionDropdown.style.display = 'block';
								versionSelectWrapper.classList.add('active');
								if (!versionSelectWrapper._outsideClickListener) {
									versionSelectWrapper._outsideClickListener = (event) => {
										if (!versionSelectWrapper.contains(event.target)) {
											versionDropdown.style.display = 'none';
											versionSelectWrapper.classList.remove('active');
											document.removeEventListener('click', versionSelectWrapper._outsideClickListener, true);
											versionSelectWrapper._outsideClickListener = null;
										}
									};
									document.addEventListener('click', versionSelectWrapper._outsideClickListener, true);
								}
							});
							document.addEventListener('click', (e) => {
								if (!versionSelectWrapper.contains(e.target)) {
									versionDropdown.style.display = 'none';
									versionSelectWrapper.classList.remove('active');
								}
							});
							return versionInput;
						}
						return undefined;
					}
					const vscode = acquireVsCodeApi();
					const skySelect = initSelectOption('sky', []);
					document.querySelector('.save-btn').addEventListener('click', () => {
						const skyInput = document.getElementById('sky');
						const selectedKey = skyInput ? skyInput.getAttribute('data-selected-value') : '';
						vscode.postMessage({ type: 'save', key: selectedKey });
					});
					document.querySelector('.get-key').addEventListener('click', () => {
						vscode.postMessage({ type: 'getNewKey' });
					});
					document.querySelector('.refresh-btn').addEventListener('click', () => {
						vscode.postMessage({ type: 'refreshKey' });
					});
					window.addEventListener('message', event => {
						const msg = event.data;
						if (msg.type === 'keyList') {
							initSelectOption('sky', msg.list || [], msg.currentApiKey);
						}
					});
					vscode.postMessage({ type: 'requestKeyList' });
				})();
				</script>
			</body>
			</html>
		`;
	}
}
