# 文本分析工具 服务接口API文档

这是一个用于分析文本内容的AI工具插件，支持情感分析、关键词提取等功能。

## 情感分析

分析文本的情感倾向，返回正面、负面或中性的分析结果。

### 接口地址

`POST /api/saas/tool/v1/plugin/run/sentiment-analysis-001`

### 接口入参

| 参数名 | 类型 | 必填 | 说明 |
| --- | --- | --- | --- |
| text | string | 是 | 需要分析的文本内容 |
| language | string | 否 | 文本语言，默认为中文 |
| options | object | 否 | 分析选项配置 |

**请求示例：**
```json
{
  "text": "",
  "language": "",
  "options": {}
}
```

**Fetch 请求配置：**
```javascript
{
  method: "POST",
  url: "${baseUrl}/api/saas/tool/v1/plugin/run/${pluginToolId}",
  headers: {
    "Content-Type": "application/json",
    "Authorization": "${apiKey}"
  },
  body: {
    params: {
      text: "", // string, 必填, 需要分析的文本内容
      language: "", // string, 不必填, 文本语言，默认为中文
      options: { // object, 不必填, 分析选项配置
        includeScore: false, // boolean, 不必填, 是否包含详细分数
        categories: [ // array, 不必填, 分析类别列表
          "" // string, 数组元素
        ]
      }
    }
  }
}
```

- 请求类型： `POST`

### 接口出参

| 字段名 | 类型 | 说明 |
| --- | --- | --- |
| sentiment | string | 情感分析结果：positive/negative/neutral |
| confidence | number | 置信度分数，范围0-1 |
| details | object | 详细分析结果 |

**响应示例：**
```json
{
  "sentiment": "",
  "confidence": 0,
  "details": {
    "positive_score": 0,
    "negative_score": 0,
    "neutral_score": 0
  }
}
```

**Fetch 响应格式：**
```javascript
{
  code: 200, // number, 响应状态码
  msg: "", // string, 响应消息
  data: {
    sentiment: "", // string, 情感分析结果：positive/negative/neutral
    confidence: 0, // number, 置信度分数，范围0-1
    details: { // object, 详细分析结果
      positive_score: 0, // number, 正面情感分数
      negative_score: 0, // number, 负面情感分数
      neutral_score: 0 // number, 中性情感分数
    }
  }
}
```

#### details 结构

| 字段名 | 类型 | 说明 |
| --- | --- | --- |
| positive_score | number | 正面情感分数 |
| negative_score | number | 负面情感分数 |
| neutral_score | number | 中性情感分数 |

**数据结构示例：**
```json
{
  "positive_score": 0,
  "negative_score": 0,
  "neutral_score": 0
}
```

## 关键词提取

从文本中提取关键词和短语。

### 接口地址

`POST /api/saas/tool/v1/plugin/run/keyword-extraction-002`

### 接口入参

| 参数名 | 类型 | 必填 | 说明 |
| --- | --- | --- | --- |
| text | string | 是 | 需要提取关键词的文本内容 |
| max_keywords | integer | 否 | 最大关键词数量，默认为10 |

**请求示例：**
```json
{
  "text": "",
  "max_keywords": 0
}
```

**Fetch 请求配置：**
```javascript
{
  method: "POST",
  url: "${baseUrl}/api/saas/tool/v1/plugin/run/${pluginToolId}",
  headers: {
    "Content-Type": "application/json",
    "Authorization": "${apiKey}"
  },
  body: {
    params: {
      text: "", // string, 必填, 需要提取关键词的文本内容
      max_keywords: 0 // integer, 不必填, 最大关键词数量，默认为10
    }
  }
}
```

### 接口出参

| 字段名 | 类型 | 说明 |
| --- | --- | --- |
| keywords | array | 提取的关键词列表 |
| total_count | integer | 关键词总数 |

**响应示例：**
```json
{
  "keywords": [],
  "total_count": 0
}
```

**Fetch 响应格式：**
```javascript
{
  code: 200, // number, 响应状态码
  msg: "", // string, 响应消息
  data: {
    keywords: [ // array, 提取的关键词列表
      "" // string, 数组元素
    ],
    total_count: 0 // integer, 关键词总数
  }
}
```
