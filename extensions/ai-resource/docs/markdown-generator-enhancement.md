# Markdown 内容生成器增强功能

## 概述

`MarkdownContentGenerator` 类已经增强，现在可以生成包含 Fetch 请求配置和响应格式的完整 Markdown 文档。这个增强功能使开发者能够更容易地理解和使用 API 接口。

## 新增功能

### 1. Fetch 请求配置生成

生成符合标准 Fetch API 格式的请求配置 JSON，包括：
- HTTP 方法
- 请求 URL
- 请求头（包括认证信息）
- 请求体（包含参数包装）

### 2. Fetch 响应格式生成

生成标准化的响应格式 JSON，包括：
- 状态码 (code)
- 消息 (msg)
- 数据 (data)

### 3. 类型定义

新增了两个接口类型：
- `FetchRequestConfig`: 定义 Fetch 请求配置结构
- `FetchResponseData`: 定义 Fetch 响应数据结构

## 使用示例

### 基本用法

```typescript
import { MarkdownContentGenerator } from './providers/handlers/markdownContentGenerator';

const generator = new MarkdownContentGenerator();

const pluginData = {
    name: "示例工具",
    pluginDesc: "这是一个示例工具",
    tools: [
        {
            name: "示例接口",
            toolDesc: "示例接口描述",
            pluginToolId: "example-tool-001",
            requestParams: [
                {
                    paramName: "query",
                    paramType: "string",
                    requiredFlag: true,
                    paramDesc: "查询参数"
                }
            ],
            responseParams: [
                {
                    paramName: "result",
                    paramType: "string",
                    paramDesc: "查询结果"
                }
            ]
        }
    ]
};

const markdown = generator.generatePluginMarkdown(pluginData);
console.log(markdown);
```

### 生成的 Markdown 示例

生成的 Markdown 文档将包含以下部分：

#### 1. 基本信息
```markdown
# 示例工具 服务接口API文档

这是一个示例工具

## 示例接口

示例接口描述

### 接口地址

`POST /api/saas/tool/v1/plugin/run/example-tool-001`
```

#### 2. 请求参数表格
```markdown
### 接口入参

| 参数名 | 类型 | 必填 | 说明 |
| --- | --- | --- | --- |
| query | string | 是 | 查询参数 |
```

#### 3. 请求示例
```markdown
**请求示例：**
```json
{
  "query": ""
}
```

#### 4. Fetch 请求配置
```markdown
**Fetch 请求配置：**
```json
{
  "method": "POST",
  "url": "${baseUrl}/api/saas/tool/v1/plugin/run/${pluginToolId}",
  "headers": {
    "Content-Type": "application/json",
    "Authorization": "${apiKey}"
  },
  "body": {
    "params": {
      "query": ""
    }
  }
}
```

#### 5. 响应参数表格
```markdown
### 接口出参

| 字段名 | 类型 | 说明 |
| --- | --- | --- |
| result | string | 查询结果 |
```

#### 6. 响应示例
```markdown
**响应示例：**
```json
{
  "result": ""
}
```

#### 7. Fetch 响应格式
```markdown
**Fetch 响应格式：**
```json
{
  "code": 200,
  "msg": "",
  "data": {
    "result": ""
  }
}
```

## 高级功能

### 嵌套参数支持

生成器支持嵌套参数结构，会自动生成相应的结构说明和 JSON 示例。

### 多种数据类型支持

支持以下数据类型的默认值生成：
- `string`: `""`
- `number`/`integer`: `0`
- `boolean`: `false`
- `array`: `[]`
- `object`: `{}`

### 国际化支持

所有文本标签都支持国际化，可以根据用户语言环境显示相应的文本。

## 配置说明

### 国际化键值

在 `package.nls.zh-cn.json` 中添加了以下新的翻译键：
- `markdown.fetch.request.title`: "Fetch 请求配置"
- `markdown.fetch.response.title`: "Fetch 响应格式"

### 扩展配置

可以通过修改以下常量来自定义配置：
- `PLUGIN_TOOL_SERVICE_PATH`: 插件工具服务路径

## 测试

运行测试以验证功能：

```bash
npm test
```

测试文件位于 `src/test/markdownContentGenerator.test.ts`，包含以下测试用例：
- 基本 Markdown 生成
- 嵌套参数处理
- 不同数据类型处理
- 空参数列表处理

## 示例代码

完整的使用示例可以在 `src/examples/markdownGeneratorExample.ts` 中找到。
