# 数组格式示例

## 修复前的问题

之前生成的数组格式不正确：

```javascript
// 错误的格式
sub_task_result_list: { // Array, 不必填, 
  [Array Item]: { // Object, 不必填, 
    sub_task_error_code: 0, // Integer, 不必填, 
    // ...
  }
}
```

## 修复后的正确格式

现在生成的数组格式正确：

```javascript
// 正确的格式
sub_task_result_list: [ // array, 不必填, 子任务结果列表
  { // object, 数组元素
    sub_task_error_code: 0, // integer, 不必填, 子任务错误码
    sub_task_progress_detail: 0, // integer, 不必填, 子任务进度详情
    sub_task_progress: 0, // integer, 不必填, 子任务进度
    final_image_list: [ // array, 不必填, 最终图片列表
      { // object, 数组元素
        img_url: "" // string, 不必填, 图片URL
      }
    ],
    sub_task_id: "", // string, 不必填, 子任务ID
    sub_task_status: "" // string, 不必填, 子任务状态
  }
]
```

## 完整的响应格式示例

```javascript
{
  code: 200, // number, 响应状态码
  msg: "", // string, 响应消息
  data: {
    log_id: "", // string, 不必填, 日志ID
    data: { // object, 不必填, 数据对象
      task_progress_detail: 0, // integer, 不必填, 任务进度详情
      task_progress: 0, // integer, 不必填, 任务进度
      task_status: "", // string, 不必填, 任务状态
      task_id: 0, // integer, 不必填, 任务ID
      sub_task_result_list: [ // array, 不必填, 子任务结果列表
        { // object, 数组元素
          sub_task_error_code: 0, // integer, 不必填, 子任务错误码
          sub_task_progress_detail: 0, // integer, 不必填, 子任务进度详情
          sub_task_progress: 0, // integer, 不必填, 子任务进度
          final_image_list: [ // array, 不必填, 最终图片列表
            { // object, 数组元素
              img_url: "" // string, 不必填, 图片URL
            }
          ],
          sub_task_id: "", // string, 不必填, 子任务ID
          sub_task_status: "" // string, 不必填, 子任务状态
        }
      ]
    }
  }
}
```

## 数组类型处理规则

### 1. 简单类型数组

对于基本类型的数组（如字符串数组、数字数组）：

```javascript
tags: [ // array, 不必填, 标签列表
  "" // string, 数组元素
]
```

### 2. 对象类型数组

对于包含对象的数组：

```javascript
users: [ // array, 不必填, 用户列表
  { // object, 数组元素
    id: 0, // integer, 必填, 用户ID
    name: "", // string, 必填, 用户名
    email: "" // string, 不必填, 邮箱
  }
]
```

### 3. 嵌套数组

对于包含嵌套数组的复杂结构：

```javascript
categories: [ // array, 不必填, 分类列表
  { // object, 数组元素
    id: 0, // integer, 必填, 分类ID
    name: "", // string, 必填, 分类名称
    items: [ // array, 不必填, 分类项目
      { // object, 数组元素
        item_id: 0, // integer, 必填, 项目ID
        item_name: "" // string, 必填, 项目名称
      }
    ]
  }
]
```

## 关键改进点

1. **正确的数组语法**：使用 `[]` 而不是 `{}`
2. **清晰的元素标识**：使用 `// object, 数组元素` 标识数组中的对象
3. **正确的嵌套**：支持数组中嵌套数组的复杂结构
4. **类型一致性**：所有类型名称使用小写（array, object, string 等）
5. **必填状态**：正确显示 "必填" 或 "不必填" 状态
