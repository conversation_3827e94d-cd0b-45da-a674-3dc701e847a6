{"displayName": "资源", "description": "资源", "viewContainer.title": "资源", "view.name": "资源", "command.openSettings.title": "设置", "command.showAiResources.title": "显示资源", "config.joycodeDir.description": "JoyCode 配置目录名称", "config.apiKeyFile.description": "API Key 配置文件的文件名", "login.prompt": "请{LOGIN_LINK}您的账号以获得更好的体验", "login.linkText": "登录", "success.quote": "引用成功！", "list.loading": "加载中...", "list.noData": "暂无数据", "list.noMore": "没有更多了", "list.quote": "引用", "search.placeholder": "搜索", "environment.error.title": "环境错误", "environment.error.message": "⚠️ 环境错误", "environment.error.description": "请在 JoyCode 软件中使用该插件", "environment.error.useInJoyCode": "请在joyCode软件中使用该插件！", "login.page.title": "登录 JoyCode", "plugin.detail.title": "插件详情", "plugin.detail.noInfo": "暂无详情信息", "plugin.detail.fetchFailed": "获取详情失败，请稍后重试", "config.instructions": "\n# 配置说明\n\n## API 凭证配置\n\n- 从 `{0}/resources/{1}` 文件中读取用户的apiKey与baseUrl。\n- 示例场景下，读取apikey.json文件，将apiKey与baseUrl直接写入代码中。\n- 每次接口访问，都应在header中写入授权信息，例如：Authorization={apiKey}\n- 请求时请拼接baseUrl\n- 本接口有跨域访问限制\n\n## 入参规则\n- 入参外层需要使用\"params\"去包裹，示例：入参是query\n```json\n{\n  params:{\n      query:\"测试\"\n  }\n}\n```\n## 出参规则\n- 数据返回结构体使用\"data\"包裹，示例：出参是log_id, data\n```json\n{\n  code: 200,\n  msg: \"\",\n  data: {\n    log_id: 'xxx',\n    data: {}\n  }\n}\n```\n\n## 框架适配\n\n需要根据项目使用的技术框架，将apiKey与baseUrl放入到项目配置文件中。\n\n### 命名规范\n- 命名空间: `joycode.resources`\n- 配置项: \n  - `joycode.resources.api_key`\n  - `joycode.resources.base_url`\n\n### 示例配置\n\n#### Spring Boot 项目\n\n在 `application.yml` 中配置：\n\n```yaml\n# application.yml\njoycode:\n  resources:\n    api_key: your-api-key\n    base_url: your-base-url\n```\n\n或在 `application.properties` 中配置：\n\n```properties\n# application.properties\njoycode.resources.api_key=your-api-key\njoycode.resources.base_url=your-base-url\n```\n\n其他框架请参照各自的配置规范进行相应配置。", "markdown.service.title": "{0} 服务接口API文档", "markdown.service.name": "服务名称", "markdown.service.description": "服务描述", "markdown.service.url": "接口地址", "markdown.request.params": "接口入参", "markdown.request.type": "请求类型： `{0}`", "markdown.response.params": "接口出参", "markdown.structure.title": "{0} 结构", "markdown.table.paramName": "参数名", "markdown.table.fieldName": "字段名", "markdown.table.type": "类型", "markdown.table.required": "必填", "markdown.table.description": "说明", "markdown.table.yes": "是", "markdown.table.no": "否", "markdown.fetch.request.title": "Fetch 请求配置", "markdown.fetch.response.title": "Fetch 响应格式", "joycode.error.readOnlyFileSystem": "当前环境为只读文件系统，无法更新配置文件。", "joycode.error.permissionDenied": "权限不足，无法更新配置文件。请检查目录权限。", "joycode.error.noWorkspace": "无法获取工作空间路径", "joycode.error.workspaceNotFound": "未找到工作区，无法保存文件", "joycode.guide.manualSetup": "请手动创建配置目录和文件：\n\n1. 在项目根目录创建 {1}/resources 文件夹\n2. 在 {1}/resources 文件夹中创建 {2} 文件\n3. 文件内容示例：\n{\n  \"apikey\": \"your-api-key-here\",\n  \"baseUrl\": \"https://joyagent.jd.com\"\n}\n\n目标路径：{0}", "joycode.guide.title": "无法自动创建配置文件，请手动创建", "joycode.guide.viewSteps": "查看详细步骤", "joycode.guide.openDirectory": "打开目录", "settings.title": "资源设置", "settings.success.apiKeySaved": "已保存选中的 API Key", "settings.success.keysRefreshed": "API Key 列表已刷新", "settings.warning.noApiKeySelected": "未选择 API Key，未保存", "settings.warning.notLoggedIn": "未登录，无法刷新 API Key 列表", "settings.info.gettingNewKey": "正在获取新的 API key...", "settings.info.refreshingKeys": "正在刷新 API Key 列表...", "settings.error.activationGetKeyListFailedMessage": "激活时获取Key列表失败", "settings.error.getKeyListFailedMessage": "获取Key列表失败", "settings.error.refreshKeyListFailedMessage": "刷新Key列表失败", "settings.loginRequired.title": "需要登录", "settings.loginRequired.message": "请登录 JoyCode 以访问设置", "settings.loginRequired.loginButton": "登录", "settings.button.refresh": "刷新", "settings.button.save": "保存", "quote.warning.noWorkspace": "请先打开一个项目文件夹，以便保存引用的 Markdown 文件。", "quote.action.openFolder": "打开文件夹", "model.previewMd": "# {0}\n\n## 介绍\n\n该模型接口兼容openAI标准。\n\n## 接口 URL\n\n`POST {baseUrl}/api/saas/openai-u/v1/chat/completions`\n\n## 请求参数\n\n### Headers\n| 参数名   | 类型   | 是否必传 | 描述     |\n| -------- | ------ | -------- | -------- |\n| Authorization | string | 是       | {apiKey} |\n\n### Body\n| 参数名   | 类型   | 是否必传 | 描述     |\n| -------- | ------ | -------- | -------- |\n| message  | object | 是       |      |\n| - role  | string | 否       |      |\n| - content | string | 是       |  |\n| model | string | 是       |  |\n\n\n## 响应参数\n\n| 参数名 | 类型 | 描述 |\n|--------|------|------|\n| created | int | 创建时间戳 |\n| usage | object | 使用情况统计 |\n| - completion_tokens | int | 完成的令牌数 |\n| - prompt_tokens | int | 提示的令牌数 |\n| - total_tokens | int | 总令牌数 |\n| model | string | 模型名称 |\n| id | string | 响应ID |\n| choices | array | 选择项 |\n| - finish_reason | string | 结束原因 |\n| - index | int | 选择项索引 |\n| - message | object | 消息内容 |\n| -- role | string | 角色（assistant） |\n| -- content | string | 内容 |", "model.fileMd": "## 接口使用补充说明\n- 从 `{0}/resources/{1}` 文件中读取用户的apiKey与baseUrl。\n- 示例场景下，读取{1}文件，将apiKey与baseUrl直接写入代码中。\n- 每次接口访问，都应在header中写入授权信息，例如：Authorization={apiKey}\n- 请求时请拼接 baseUrl"}