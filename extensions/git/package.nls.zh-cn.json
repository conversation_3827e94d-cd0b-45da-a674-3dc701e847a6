{"displayName": "Git", "description": "Git 源代码管理集成", "command.continueInLocalClone": "在本地克隆存储库并在桌面上打开...", "command.continueInLocalClone.qualifiedName": "继续在新的本地克隆中工作", "command.clone": "克隆", "command.cloneRecursive": "克隆(递归)", "command.init": "初始化仓库", "command.openRepository": "打开仓库", "command.reopenClosedRepositories": "重新打开已关闭的仓库...", "command.close": "关闭仓库", "command.closeOtherRepositories": "关闭其他仓库", "command.refresh": "刷新", "command.openChange": "打开更改", "command.openAllChanges": "打开所有更改", "command.openFile": "打开文件", "command.openHEADFile": "打开文件 (HEAD)", "command.stage": "暂存更改", "command.stageAll": "暂存所有更改", "command.stageAllTracked": "暂存所有跟踪的更改", "command.stageAllUntracked": "暂存所有未跟踪的更改", "command.stageAllMerge": "暂存所有合并更改", "command.stageSelectedRanges": "暂存所选范围", "command.revertSelectedRanges": "放弃所选范围", "command.stageChange": "暂存更改", "command.stageSelection": "暂存选择", "command.stageBlock": "暂存块", "command.revertChange": "放弃更改", "command.unstage": "取消暂存更改", "command.unstageAll": "取消暂存所有更改", "command.unstageSelectedRanges": "取消暂存所选范围", "command.rename": "重命名", "command.clean": "放弃更改", "command.cleanAll": "放弃所有更改", "command.cleanAllTracked": "放弃所有跟踪的更改", "command.cleanAllUntracked": "放弃所有未跟踪的更改", "command.closeAllDiffEditors": "关闭所有差异编辑器", "command.closeAllUnmodifiedEditors": "关闭所有未修改的编辑器", "command.commit": "提交", "command.commitAmend": "提交 (修改)", "command.commitSigned": "提交 (签名)", "command.commitStaged": "提交暂存的更改", "command.commitEmpty": "提交空更改", "command.commitStagedSigned": "提交暂存的更改 (签名)", "command.commitStagedAmend": "提交暂存的更改 (修改)", "command.commitAll": "提交所有更改", "command.commitAllSigned": "提交所有更改 (签名)", "command.commitAllAmend": "提交所有更改 (修改)", "command.commitNoVerify": "提交 (跳过验证)", "command.commitStagedNoVerify": "提交暂存的更改 (跳过验证)", "command.commitEmptyNoVerify": "提交空更改 (跳过验证)", "command.commitStagedSignedNoVerify": "提交暂存的更改 (签名, 跳过验证)", "command.commitAmendNoVerify": "提交 (修改, 跳过验证)", "command.commitSignedNoVerify": "提交 (签名, 跳过验证)", "command.commitStagedAmendNoVerify": "提交暂存的更改 (修改, 跳过验证)", "command.commitAllNoVerify": "提交所有更改 (跳过验证)", "command.commitAllSignedNoVerify": "提交所有更改 (签名, 跳过验证)", "command.commitAllAmendNoVerify": "提交所有更改 (修改, 跳过验证)", "command.commitMessageAccept": "接受提交消息", "command.commitMessageDiscard": "放弃提交消息", "command.restoreCommitTemplate": "恢复提交模板", "command.undoCommit": "撤销上次提交", "command.checkout": "检出到...", "command.checkoutDetached": "检出到 (分离)...", "command.branch": "创建分支...", "command.branchFrom": "从...创建分支", "command.deleteBranch": "删除分支...", "command.deleteRemoteBranch": "删除远程分支...", "command.renameBranch": "重命名分支...", "command.cherryPick": "挑选...", "command.cherryPickAbort": "中止挑选", "command.merge": "合并...", "command.mergeAbort": "中止合并", "command.rebase": "变基分支...", "command.createTag": "创建标记...", "command.deleteTag": "删除标记...", "command.deleteRemoteTag": "删除远程标记...", "command.fetch": "获取", "command.fetchPrune": "获取 (修剪)", "command.fetchAll": "从所有远程获取", "command.pull": "拉取", "command.pullRebase": "拉取 (变基)", "command.pullFrom": "从...拉取", "command.push": "推送", "command.pushForce": "推送 (强制)", "command.pushTo": "推送到...", "command.pushToForce": "推送到... (强制)", "command.pushFollowTags": "推送 (跟随标记)", "command.pushFollowTagsForce": "推送 (跟随标记, 强制)", "command.pushTags": "推送标记", "command.addRemote": "添加远程...", "command.removeRemote": "移除远程", "command.sync": "同步", "command.syncRebase": "同步 (变基)", "command.publish": "发布分支...", "command.showOutput": "显示 Git 输出", "command.ignore": "添加到 .gitignore", "command.revealInExplorer": "在资源管理器视图中显示", "command.revealFileInOS.linux": "打开包含文件夹", "command.revealFileInOS.mac": "在 Finder 中显示", "command.revealFileInOS.windows": "在文件资源管理器中显示", "command.rebaseAbort": "中止变基", "command.stashIncludeUntracked": "储藏 (包括未跟踪的)", "command.stash": "储藏", "command.stashStaged": "储藏暂存的更改", "command.stashPop": "弹出储藏...", "command.stashPopLatest": "弹出最新储藏", "command.stashPopEditor": "弹出储藏", "command.stashApply": "应用储藏...", "command.stashApplyLatest": "应用最新储藏", "command.stashApplyEditor": "应用储藏", "command.stashDrop": "删除储藏...", "command.stashDropAll": "删除所有储藏...", "command.stashDropEditor": "删除储藏", "command.stashView": "查看储藏...", "command.timelineOpenDiff": "打开更改", "command.timelineCopyCommitId": "复制提交 ID", "command.timelineCopyCommitMessage": "复制提交消息", "command.timelineSelectForCompare": "选择用于比较", "command.timelineCompareWithSelected": "与所选项比较", "command.manageUnsafeRepositories": "管理不安全存储库", "command.openRepositoriesInParentFolders": "在父文件夹中打开存储库", "command.viewChanges": "打开更改", "command.viewStagedChanges": "打开暂存的更改", "command.viewUntrackedChanges": "打开未跟踪的更改", "command.viewCommit": "打开提交", "command.graphCheckout": "检出", "command.graphCheckoutDetached": "检出 (分离)", "command.graphCherryPick": "挑选", "command.graphDeleteBranch": "删除分支", "command.graphDeleteTag": "删除标记", "command.blameToggleEditorDecoration": "切换 Git 责任编辑器装饰", "command.blameToggleStatusBarItem": "切换 Git 责任状态栏项", "command.api.getRepositories": "获取存储库", "command.api.getRepositoryState": "获取存储库状态", "command.api.getRemoteSources": "获取远程源", "command.git.acceptMerge": "完成合并", "command.git.openMergeEditor": "在合并编辑器中解决", "command.git.runGitMerge": "使用 Git 计算冲突", "command.git.runGitMergeDiff3": "使用 Git 计算冲突 (Diff3)", "config.enabled": "是否启用 Git。", "config.path": "git 可执行文件的路径和文件名，例如 `C:\\Program Files\\Git\\bin\\git.exe` (Windows)。这也可以是包含多个查找路径的字符串值数组。", "config.autoRepositoryDetection": "配置何时应自动检测存储库。", "config.autoRepositoryDetection.true": "扫描当前打开文件夹的子文件夹和打开文件的父文件夹。", "config.autoRepositoryDetection.false": "禁用自动存储库扫描。", "config.autoRepositoryDetection.subFolders": "扫描当前打开文件夹的子文件夹。", "config.autoRepositoryDetection.openEditors": "扫描打开文件的父文件夹。", "config.autorefresh": "是否启用自动刷新。", "config.autofetch": "设置为 true 时，将自动从当前 Git 存储库的默认远程获取提交。设置为 `all` 将从所有远程获取。", "config.autofetchPeriod": "启用 `#git.autofetch#` 时，每次自动 git 获取之间的持续时间（秒）。", "config.confirmSync": "同步 Git 存储库前确认。", "config.countBadge": "控制 Git 计数徽章。", "config.countBadge.all": "计算所有更改。", "config.countBadge.tracked": "仅计算跟踪的更改。", "config.countBadge.off": "关闭计数器。", "config.checkoutType": "控制运行 `检出到...` 时列出的 Git 引用类型。", "config.checkoutType.local": "本地分支", "config.checkoutType.tags": "标记", "config.checkoutType.remote": "远程分支", "config.defaultBranchName": "初始化新 Git 存储库时默认分支的名称（例如：main、trunk、development）。设置为空时，将使用 Git 中配置的默认分支名称。**注意：** 需要 Git 版本 `2.28.0` 或更高版本。", "config.branchPrefix": "创建新分支时使用的前缀。", "config.branchProtection": "受保护分支列表。默认情况下，在将更改提交到受保护分支之前会显示提示。可以使用 `#git.branchProtectionPrompt#` 设置控制提示。", "config.branchProtectionPrompt": "控制是否在将更改提交到受保护分支之前显示提示。", "config.branchProtectionPrompt.alwaysCommit": "始终将更改提交到受保护分支。", "config.branchProtectionPrompt.alwaysCommitToNewBranch": "始终将更改提交到新分支。", "config.branchProtectionPrompt.alwaysPrompt": "在将更改提交到受保护分支之前始终提示。", "config.branchRandomNameDictionary": "用于随机生成分支名称的字典列表。每个值表示用于生成分支名称段的字典。支持的字典：`adjectives`、`animals`、`colors` 和 `numbers`。", "config.branchRandomNameDictionary.adjectives": "随机形容词", "config.branchRandomNameDictionary.animals": "随机动物名称", "config.branchRandomNameDictionary.colors": "随机颜色名称", "config.branchRandomNameDictionary.numbers": "100 到 999 之间的随机数字", "config.branchRandomNameEnable": "控制是否在创建新分支时生成随机名称。", "config.branchValidationRegex": "用于验证新分支名称的正则表达式。", "config.branchWhitespaceChar": "用于替换新分支名称中空白字符的字符，以及分隔随机生成分支名称段的字符。", "config.ignoreLegacyWarning": "忽略旧版 Git 警告。", "config.ignoreMissingGitWarning": "忽略 Git 缺失时的警告。", "config.ignoreWindowsGit27Warning": "忽略在 Windows 上安装 Git 2.25 - 2.26 时的警告。", "config.ignoreLimitWarning": "忽略存储库中更改过多时的警告。", "config.ignoreRebaseWarning": "忽略拉取时分支可能已被变基的警告。", "config.defaultCloneDirectory": "克隆 Git 存储库的默认位置。", "config.useEditorAsCommitInput": "控制是否在提交输入框中未提供消息时使用完整文本编辑器来编写提交消息。", "config.verboseCommit": "启用 `#git.useEditorAsCommitInput#` 时启用详细输出。", "config.enableSmartCommit": "当没有暂存更改时提交所有更改。", "config.smartCommitChanges": "控制智能提交自动暂存哪些更改。", "config.smartCommitChanges.all": "自动暂存所有更改。", "config.smartCommitChanges.tracked": "仅自动暂存跟踪的更改。", "config.suggestSmartCommit": "建议启用智能提交（当没有暂存更改时提交所有更改）。", "config.enableCommitSigning": "启用使用 GPG、X.509 或 SSH 的提交签名。", "config.discardAllScope": "控制 `放弃所有更改` 命令放弃哪些更改。`all` 放弃所有更改。`tracked` 仅放弃跟踪的文件。`prompt` 每次运行操作时显示提示对话框。", "config.decorations.enabled": "控制 Git 是否为资源管理器和打开的编辑器视图提供颜色和徽章。", "config.enableStatusBarSync": "控制 Git 同步命令是否出现在状态栏中。", "config.followTagsWhenSync": "运行同步命令时推送所有带注释的标记。", "config.replaceTagsWhenPull": "运行拉取命令时在冲突情况下自动用远程标记替换本地标记。", "config.promptToSaveFilesBeforeStash": "控制 Git 是否应在储藏更改前检查未保存的文件。", "config.promptToSaveFilesBeforeStash.always": "检查任何未保存的文件。", "config.promptToSaveFilesBeforeStash.staged": "仅检查未保存的暂存文件。", "config.promptToSaveFilesBeforeStash.never": "禁用此检查。", "config.promptToSaveFilesBeforeCommit": "控制 Git 是否应在提交前检查未保存的文件。", "config.promptToSaveFilesBeforeCommit.always": "检查任何未保存的文件。", "config.promptToSaveFilesBeforeCommit.staged": "仅检查未保存的暂存文件。", "config.promptToSaveFilesBeforeCommit.never": "禁用此检查。", "config.postCommitCommand": "成功提交后运行 git 命令。", "config.postCommitCommand.none": "提交后不运行任何命令。", "config.postCommitCommand.push": "成功提交后运行 'git push'。", "config.postCommitCommand.sync": "成功提交后运行 'git pull' 和 'git push'。", "config.rememberPostCommitCommand": "记住提交后运行的最后一个 git 命令。", "config.openAfterClone": "控制是否在克隆后自动打开存储库。", "config.openAfterClone.always": "始终在当前窗口中打开。", "config.openAfterClone.alwaysNewWindow": "始终在新窗口中打开。", "config.openAfterClone.whenNoFolderOpen": "仅在未打开文件夹时在当前窗口中打开。", "config.openAfterClone.prompt": "始终提示操作。", "config.showInlineOpenFileAction": "控制是否在 Git 更改视图中显示内联打开文件操作。", "config.showPushSuccessNotification": "控制是否在推送成功时显示通知。", "config.inputValidation": "控制是否显示提交消息输入验证诊断。", "config.inputValidationLength": "控制显示警告的提交消息长度阈值。", "config.inputValidationSubjectLength": "控制显示警告的提交消息主题长度阈值。取消设置以继承 `#git.inputValidationLength#` 的值。", "config.detectSubmodules": "控制是否自动检测 Git 子模块。", "config.detectSubmodulesLimit": "控制检测到的 Git 子模块的限制。", "config.alwaysShowStagedChangesResourceGroup": "始终显示暂存更改资源组。", "config.alwaysSignOff": "控制所有提交的签名标志。", "config.ignoreSubmodules": "忽略文件树中子模块的修改。", "config.ignoredRepositories": "要忽略的 Git 存储库列表。", "config.scanRepositories": "搜索 Git 存储库的路径列表。", "config.commandsToLog": {"message": "将其 `stdout` 记录到 [git 输出](command:git.showOutput) 的 git 命令列表（例如：commit、push）。如果 git 命令配置了客户端钩子，客户端钩子的 `stdout` 也将记录到 [git 输出](command:git.showOutput)。", "comment": ["{Locked='](command:git.showOutput'}", "Do not translate the 'command:*' part inside of the '(..)'. It is an internal command syntax for JoyCode", "Please make sure there is no space between the right bracket and left parenthesis:  ]( this is an internal syntax for links"]}, "config.showProgress": "控制 Git 操作是否应显示进度。", "config.rebaseWhenSync": "强制 Git 在运行同步命令时使用变基。", "config.confirmEmptyCommits": "始终确认为 'Git: 提交空更改' 命令创建空提交。", "config.fetchOnPull": "启用时，拉取时获取所有分支。否则，仅获取当前分支。", "config.pullBeforeCheckout": "控制是否在检出没有传出提交的分支之前快进该分支。", "config.pullTags": "拉取时获取所有标记。", "config.pruneOnFetch": "获取时修剪。", "config.autoStash": "拉取前储藏任何更改，成功拉取后恢复它们。", "config.allowForcePush": "控制是否启用强制推送（带或不带租约）。", "config.useForcePushWithLease": "控制强制推送是否使用更安全的 force-with-lease 变体。", "config.useForcePushIfIncludes": "控制强制推送是否使用更安全的 force-if-includes 变体。注意：此设置需要启用 `#git.useForcePushWithLease#` 设置，以及 Git 版本 `2.30.0` 或更高版本。", "config.confirmForcePush": "控制是否在强制推送前要求确认。", "config.allowNoVerifyCommit": "控制是否允许不运行 pre-commit 和 commit-msg 钩子的提交。", "config.confirmNoVerifyCommit": "控制是否在不验证提交前要求确认。", "config.closeDiffOnOperation": "控制在储藏、提交、放弃、暂存或取消暂存更改时是否应自动关闭差异编辑器。", "config.openDiffOnClick": "控制单击更改时是否应打开差异编辑器。否则将打开常规编辑器。", "config.supportCancellation": "控制运行同步操作时是否出现通知，允许用户取消操作。", "config.branchSortOrder": "控制分支的排序顺序。", "config.untrackedChanges": "控制未跟踪更改的行为。", "config.untrackedChanges.mixed": "所有更改（跟踪和未跟踪）一起出现并表现相同。", "config.untrackedChanges.separate": "未跟踪的更改在源代码管理视图中单独显示。它们也被排除在几个操作之外。", "config.untrackedChanges.hidden": "未跟踪的更改被隐藏并从几个操作中排除。", "config.requireGitUserConfig": "控制是否需要显式的 Git 用户配置或允许 Git 在缺失时猜测。", "config.showCommitInput": "控制是否在 Git 源代码管理面板中显示提交输入。", "config.terminalAuthentication": "控制是否启用 JoyCode 作为集成终端中生成的 Git 进程的身份验证处理程序。注意：需要重新启动终端以获取此设置的更改。", "config.terminalGitEditor": "控制是否启用 JoyCode 作为集成终端中生成的 Git 进程的 Git 编辑器。注意：需要重新启动终端以获取此设置的更改。", "config.timeline.showAuthor": "控制是否在时间线视图中显示提交作者。", "config.timeline.showUncommitted": "控制是否在时间线视图中显示未提交的更改。", "config.timeline.date": "控制时间线视图中项目使用哪个日期。", "config.timeline.date.committed": "使用提交日期", "config.timeline.date.authored": "使用创作日期", "config.useCommitInputAsStashMessage": "控制是否使用提交输入框中的消息作为默认储藏消息。", "config.showActionButton": "控制是否在源代码管理视图中显示操作按钮。", "config.showActionButton.commit": "当本地分支有准备提交的修改文件时显示提交更改的操作按钮。", "config.showActionButton.publish": "当本地分支没有跟踪远程分支时显示发布本地分支的操作按钮。", "config.showActionButton.sync": "当本地分支领先或落后于远程分支时显示同步更改的操作按钮。", "config.statusLimit": "控制如何限制可以从 Git 状态命令解析的更改数量。可以设置为 0 表示无限制。", "config.experimental.installGuide": "Git 设置流程的实验性改进。", "config.repositoryScanIgnoredFolders": "当 `#git.autoRepositoryDetection#` 设置为 `true` 或 `subFolders` 时，扫描 Git 存储库时忽略的文件夹列表。", "config.repositoryScanMaxDepth": "当 `#git.autoRepositoryDetection#` 设置为 `true` 或 `subFolders` 时，扫描工作区文件夹中 Git 存储库时使用的深度控制。可以设置为 `-1` 表示无限制。", "config.useIntegratedAskPass": "控制是否应覆盖 GIT_ASKPASS 以使用集成版本。", "config.mergeEditor": "为当前冲突的文件打开合并编辑器。", "config.optimisticUpdate": "控制是否在运行 git 命令后乐观地更新源代码管理视图的状态。", "config.openRepositoryInParentFolders": "控制是否应打开工作区或打开文件的父文件夹中的存储库。", "config.openRepositoryInParentFolders.always": "始终打开工作区或打开文件的父文件夹中的存储库。", "config.openRepositoryInParentFolders.never": "从不打开工作区或打开文件的父文件夹中的存储库。", "config.openRepositoryInParentFolders.prompt": "在打开工作区或打开文件的父文件夹中的存储库之前提示。", "config.publishBeforeContinueOn": "控制从 Git 存储库使用继续工作时是否发布未发布的 Git 状态。", "config.publishBeforeContinueOn.always": "从 Git 存储库使用继续工作时始终发布未发布的 Git 状态", "config.publishBeforeContinueOn.never": "从 Git 存储库使用继续工作时从不发布未发布的 Git 状态", "config.publishBeforeContinueOn.prompt": "从 Git 存储库使用继续工作时提示发布未发布的 Git 状态", "config.similarityThreshold": "控制一对添加/删除文件中的更改被视为重命名的相似性索引阈值（与文件大小相比的添加/删除量）。**注意：** 需要 Git 版本 `2.18.0` 或更高版本。", "config.blameEditorDecoration.enabled": "控制是否使用编辑器装饰在编辑器中显示责任信息。", "config.blameEditorDecoration.template": "责任信息编辑器装饰的模板。支持的变量：\n\n* `hash`: 提交哈希\n\n* `hashShort`: 根据 `#git.commitShortHashLength#` 的提交哈希的前 N 个字符\n\n* `subject`: 提交消息的第一行\n\n* `authorName`: 作者姓名\n\n* `authorEmail`: 作者电子邮件\n\n* `authorDate`: 作者日期\n\n* `authorDateAgo`: 现在与作者日期之间的时间差\n\n", "config.blameStatusBarItem.enabled": "控制是否在状态栏中显示责任信息。", "config.blameStatusBarItem.template": "责任信息状态栏项的模板。支持的变量：\n\n* `hash`: 提交哈希\n\n* `hashShort`: 根据 `#git.commitShortHashLength#` 的提交哈希的前 N 个字符\n\n* `subject`: 提交消息的第一行\n\n* `authorName`: 作者姓名\n\n* `authorEmail`: 作者电子邮件\n\n* `authorDate`: 作者日期\n\n* `authorDateAgo`: 现在与作者日期之间的时间差\n\n", "config.commitShortHashLength": "控制提交短哈希的长度。", "config.diagnosticsCommitHook.Enabled": "控制是否在提交前检查未解决的诊断。", "config.diagnosticsCommitHook.Sources": "控制提交前要考虑的源列表（**项目**）和最低严重性（**值**）。**注意：** 要忽略来自特定源的诊断，请将源添加到列表中并将最低严重性设置为 `none`。", "config.discardUntrackedChangesToTrash": "控制放弃未跟踪的更改是否将文件移动到回收站（Windows）、废纸篓（macOS、Linux）而不是永久删除它们。**注意：** 连接到远程或在 Linux 中作为 snap 包运行时，此设置无效。", "submenu.explorer": "Git", "submenu.commit": "提交", "submenu.commit.amend": "修改", "submenu.commit.signoff": "签名", "submenu.changes": "更改", "submenu.pullpush": "拉取, 推送", "submenu.branch": "分支", "submenu.remotes": "远程", "submenu.stash": "储藏", "submenu.tags": "标记", "colors.added": "已添加资源的颜色。", "colors.modified": "已修改资源的颜色。", "colors.stageModified": "已暂存的已修改资源的颜色。", "colors.stageDeleted": "已暂存的已删除资源的颜色。", "colors.deleted": "已删除资源的颜色。", "colors.renamed": "已重命名或复制资源的颜色。", "colors.untracked": "未跟踪资源的颜色。", "colors.ignored": "已忽略资源的颜色。", "colors.conflict": "有冲突的资源的颜色。", "colors.submodule": "子模块资源的颜色。", "colors.incomingAdded": "传入已添加资源的颜色。", "colors.incomingDeleted": "传入已删除资源的颜色。", "colors.incomingRenamed": "传入已重命名资源的颜色。", "colors.incomingModified": "传入已修改资源的颜色。", "colors.blameEditorDecoration": "责任编辑器装饰的颜色。", "view.workbench.scm.missing.windows": {"message": "[下载适用于 Windows 的 Git](https://git-scm.com/download/win)\n安装后，请[重新加载](command:workbench.action.reloadWindow) (或[执行故障排除](command:git.showOutput))。可以[从商城](command:workbench.extensions.search?%22%40category%3A%5C%22scm%20providers%5C%22%22)安装其他源代码管理提供程序。", "comment": ["{Locked='](command:workbench.action.reloadWindow'}", "{Locked='](command:git.showOutput'}", "{Locked='](command:workbench.extensions.search?%22%40category%3A%5C%22scm%20providers%5C%22%22'}", "Do not translate the 'command:*' part inside of the '(..)'. It is an internal command syntax for JoyCode", "Please make sure there is no space between the right bracket and left parenthesis:  ]( this is an internal syntax for links"]}, "view.workbench.scm.missing.mac": {"message": "[下载适用于 macOS 的 Git](https://git-scm.com/download/mac)\n安装后，请[重新加载](command:workbench.action.reloadWindow) (或[执行故障排除](command:git.showOutput))。可以[从商城](command:workbench.extensions.search?%22%40category%3A%5C%22scm%20providers%5C%22%22)安装其他源代码管理提供程序。", "comment": ["{Locked='](command:workbench.action.reloadWindow'}", "{Locked='](command:git.showOutput'}", "{Locked='](command:workbench.extensions.search?%22%40category%3A%5C%22scm%20providers%5C%22%22'}", "Do not translate the 'command:*' part inside of the '(..)'. It is an internal command syntax for JoyCode", "Please make sure there is no space between the right bracket and left parenthesis:  ]( this is an internal syntax for links"]}, "view.workbench.scm.missing.linux": {"message": "源代码管理取决于将安装的 Git。\n[下载适用于 Linux 的 Git](https://git-scm.com/download/linux)\n安装后，请[重新加载](command:workbench.action.reloadWindow) (或[执行故障排除](command:git.showOutput))。可以[从商城](command:workbench.extensions.search?%22%40category%3A%5C%22scm%20providers%5C%22%22)安装其他源代码管理提供程序。", "comment": ["{Locked='](command:workbench.action.reloadWindow'}", "{Locked='](command:git.showOutput'}", "{Locked='](command:workbench.extensions.search?%22%40category%3A%5C%22scm%20providers%5C%22%22'}", "Do not translate the 'command:*' part inside of the '(..)'. It is an internal command syntax for JoyCode", "Please make sure there is no space between the right bracket and left parenthesis:  ]( this is an internal syntax for links"]}, "view.workbench.scm.missing": {"message": "安装 Git (一种流行的源代码管理系统)，以跟踪代码更改并与他人协作。在我们的 [Git 指南](https://aka.ms/vscode-scm) 中了解详细信息。", "comment": ["{Locked='](https://aka.ms/vscode-scm'}", "Please make sure there is no space between the right bracket and left parenthesis:  ]( this is an internal syntax for links"]}, "view.workbench.scm.disabled": {"message": "如果要使用 Git 功能，请在[设置](command:workbench.action.openSettings?%5B%22git.enabled%22%5D)中启用 Git。\n若要详细了解如何在 JoyCode 中使用 Git 和源代码管理参阅我们的文档。", "comment": ["{Locked='](command:workbench.action.openSettings?%5B%22git.enabled%22%5D'}", "Do not translate the 'command:*' part inside of the '(..)'. It is an internal command syntax for JoyCode", "Please make sure there is no space between the right bracket and left parenthesis:  ]( this is an internal syntax for links"]}, "view.workbench.scm.empty": {"message": "为了使用 Git 功能，可打开包含 Git 仓库的文件夹或从 URL 克隆。\n[打开文件夹](command:vscode.openFolder)\n[克隆仓库](command:git.cloneRecursive)\n若要详细了解如何在 JoyCode 中使用 Git 和源代码管理参阅我们的文档。", "comment": ["{Locked='](command:vscode.openFolder'}", "Do not translate the 'command:*' part inside of the '(..)'. It is an internal command syntax for JoyCode", "Please make sure there is no space between the right bracket and left parenthesis:  ]( this is an internal syntax for links"]}, "view.workbench.scm.folder": {"message": "当前打开的文件夹中没有 Git 存储库。可初始化一个仓库，它将实现 Git 提供支持的源代码管理功能。\n[初始化仓库](command:git.init?%5Btrue%5D)\n若要详细了解如何在 JoyCode 中使用 Git 和源代码管理参阅我们的文档。", "comment": ["{Locked='](command:git.init?%5Btrue%5D'}", "Do not translate the 'command:*' part inside of the '(..)'. It is an internal command syntax for JoyCode", "Please make sure there is no space between the right bracket and left parenthesis:  ]( this is an internal syntax for links"]}, "view.workbench.scm.workspace": {"message": "当前打开的工作区中没有任何包含 Git 仓库的文件夹。可初始化某文件夹上的一个仓库，该仓库将实现 Git 提供支持的源代码管理功能。\n[初始化仓库](command:git.init)\n若要详细了解如何在 JoyCode 中使用 Git 和源代码管理参阅我们的文档。", "comment": ["{Locked='](command:git.init'}", "Do not translate the 'command:*' part inside of the '(..)'. It is an internal command syntax for JoyCode", "Please make sure there is no space between the right bracket and left parenthesis:  ]( this is an internal syntax for links"]}, "view.workbench.scm.emptyWorkspace": {"message": "当前打开的工作区中没有任何包含 Git 仓库的文件夹。\n[将文件夹添加到工作区](command:workbench.action.addRootFolder)\n若要详细了解如何在 JoyCode 中使用 Git 和源代码管理参阅我们的文档。", "comment": ["{Locked='](command:workbench.action.addRootFolder'}", "Do not translate the 'command:*' part inside of the '(..)'. It is an internal command syntax for JoyCode", "Please make sure there is no space between the right bracket and left parenthesis:  ]( this is an internal syntax for links"]}, "view.workbench.scm.scanFolderForRepositories": {"message": "正在扫描 Git 存储库的文件夹..."}, "view.workbench.scm.scanWorkspaceForRepositories": {"message": "正在扫描工作区中的 Git 存储库..."}, "view.workbench.scm.repositoryInParentFolders": {"message": "在工作区的父文件夹或打开的文件中找到了 Git 存储库。\n[打开存储库](command:git.openRepositoriesInParentFolders)\n使用[git.openRepositoryInParentFolders](command:workbench.action.openSettings?%5B%22git.openRepositoryInParentFolders%22%5D)设置来控制是打开工作区或打开文件的父文件夹中的 Git 存储库。若要了解详细信息，请[阅读我们的文档](https://aka.ms/vscode-git-repository-in-parent-folders)。", "comment": ["{Locked='](command:git.openRepositoriesInParentFolders'}", "{Locked='](command:workbench.action.openSettings?%5B%22git.openRepositoryInParentFolders%22%5D'}", "Do not translate the 'command:*' part inside of the '(..)'. It is an internal command syntax for JoyCode", "Please make sure there is no space between the right bracket and left parenthesis:  ]( this is an internal syntax for links"]}, "view.workbench.scm.repositoriesInParentFolders": {"message": "在工作区的父文件夹或打开的文件中找到了 Git 存储库。\n[打开存储库](command:git.openRepositoriesInParentFolders)\n使用[git.openRepositoryInParentFolders](command:workbench.action.openSettings?%5B%22git.openRepositoryInParentFolders%22%5D)设置来控制是打开工作区或打开文件的父文件夹中的 Git 存储库。若要了解详细信息，请[阅读我们的文档](https://aka.ms/vscode-git-repository-in-parent-folders)。", "comment": ["{Locked='](command:git.openRepositoriesInParentFolders'}", "{Locked='](command:workbench.action.openSettings?%5B%22git.openRepositoryInParentFolders%22%5D'}", "Do not translate the 'command:*' part inside of the '(..)'. It is an internal command syntax for JoyCode", "Please make sure there is no space between the right bracket and left parenthesis:  ]( this is an internal syntax for links"]}, "view.workbench.scm.unsafeRepository": {"message": "检测到的 Git 存储库可能不安全，因为该文件夹由当前用户以外的其他人所有。\n[管理不安全存储库](command:git.manageUnsafeRepositories)\n要详细了解不安全存储库，[请阅读我们的文档](https://aka.ms/vscode-git-unsafe-repository)。", "comment": ["{Locked='](command:git.manageUnsafeRepositories'}", "Do not translate the 'command:*' part inside of the '(..)'. It is an internal command syntax for JoyCode", "Please make sure there is no space between the right bracket and left parenthesis:  ]( this is an internal syntax for links"]}, "view.workbench.scm.unsafeRepositories": {"message": "检测到的 Git 存储库可能不安全，因为该文件夹由当前用户以外的其他人所有。\n[管理不安全存储库](command:git.manageUnsafeRepositories)\n要详细了解不安全存储库，[请阅读我们的文档](https://aka.ms/vscode-git-unsafe-repository)。", "comment": ["{Locked='](command:git.manageUnsafeRepositories'}", "Do not translate the 'command:*' part inside of the '(..)'. It is an internal command syntax for JoyCode", "Please make sure there is no space between the right bracket and left parenthesis:  ]( this is an internal syntax for links"]}, "view.workbench.scm.closedRepository": {"message": "找到以前关闭的 Git 存储库。\n[重新打开已关闭的存储库](command:git.reopenClosedRepositories)\n若要详细了解如何在 JoyCode 中使用 Git 和源代码管理参阅我们的文档。", "comment": ["{Locked='](command:git.reopenClosedRepositories'}", "Do not translate the 'command:*' part inside of the '(..)'. It is an internal command syntax for JoyCode", "Please make sure there is no space between the right bracket and left parenthesis:  ]( this is an internal syntax for links"]}, "view.workbench.scm.closedRepositories": {"message": "找到以前关闭的 Git 存储库。\n[重新打开已关闭的存储库](command:git.reopenClosedRepositories)\n若要详细了解如何在 JoyCode 中使用 Git 和源代码管理参阅我们的文档。", "comment": ["{Locked='](command:git.reopenClosedRepositories'}", "Do not translate the 'command:*' part inside of the '(..)'. It is an internal command syntax for JoyCode", "Please make sure there is no space between the right bracket and left parenthesis:  ]( this is an internal syntax for links"]}, "view.workbench.cloneRepository": {"message": "您可以在本地克隆存储库。\n[克隆存储库](command:git.clone '启用 Git 扩展后立即克隆存储库')", "comment": ["{Locked='](command:git.clone'}", "Do not translate the 'command:*' part inside of the '(..)'. It is an internal command syntax for JoyCode", "Please make sure there is no space between the right bracket and left parenthesis:  ]( this is an internal syntax for links"]}, "view.workbench.learnMore": "若要详细了解如何在 JoyCode 中使用 Git 和源代码管理参阅我们的文档。"}