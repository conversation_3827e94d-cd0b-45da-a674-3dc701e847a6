import * as vscode from 'vscode';

export class Logger {
    private static outputChannel: vscode.OutputChannel;
    private static isProduction() {
        return (vscode.env as any).joyCoderEnv === 'prod';
    };

    static init(context: vscode.ExtensionContext) {
        this.outputChannel = vscode.window.createOutputChannel('CloudDev');
    }

    static info(message: string) {
        if (this.isProduction()) return;
        const log = `[INFO] ${new Date().toISOString()} ${message}`;
        this.outputChannel.appendLine(log);
    }

    static error(message: string, error?: any) {
        if (this.isProduction()) return;
        const errorMessage = error ? `${message}: ${error.toString()}` : message;
        const log = `[ERROR] ${new Date().toISOString()} ${errorMessage}`;
        this.outputChannel.appendLine(log);
    }

    static debug(message: string) {
        if (this.isProduction()) return;
        const log = `[DEBUG] ${new Date().toISOString()} ${message}`;
        this.outputChannel.appendLine(log);
    }

    static show() {
        if (this.isProduction()) return;
        this.outputChannel.show();
    }
}
