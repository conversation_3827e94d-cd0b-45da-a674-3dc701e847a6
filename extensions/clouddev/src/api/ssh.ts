import fs from 'fs';
import * as vscode from 'vscode';

import { GlobalStateManager } from '../utils/globalStateManager';
import { defaultClouddevSSHConfigPath, defaultSSHKeyPath } from '../constant';

export const parseSSHConfig = (filePath: string) => {
    return new Promise((resolve, reject) => {
        fs.readFile(filePath, 'utf-8', (err: any, data: any) => {
            if (err) {
                return reject(err);
            }

            const lines = data.split('\n');
            const devboxList = [] as any[];
            let currentHostObj = {} as any;

            lines.forEach((line: string) => {
                line = line.trim();

                if (line.startsWith('Host ')) {
                    if (currentHostObj.strictHostKeyChecking) {
                        currentHostObj.remotePath = GlobalStateManager.getWorkDir(
                            currentHostObj.host
                        );
                        devboxList.push(currentHostObj);
                    }
                    currentHostObj = { host: line.split(' ')[1] };
                } else if (line.startsWith('HostName ')) {
                    currentHostObj.hostName = line.split(' ')[1];
                } else if (line.startsWith('User ')) {
                    currentHostObj.user = line.split(' ')[1];
                } else if (line.startsWith('Port ')) {
                    currentHostObj.port = line.split(' ')[1];
                } else if (line.startsWith('IdentityFile ')) {
                    currentHostObj.identityFile = line.split(' ')[1];
                } else if (line.startsWith('IdentitiesOnly ')) {
                    currentHostObj.identitiesOnly = line.split(' ')[1];
                } else if (line.startsWith('StrictHostKeyChecking ')) {
                    currentHostObj.strictHostKeyChecking = line.split(' ')[1];
                }
            });

            // the last one
            if (!!currentHostObj.strictHostKeyChecking) {
                currentHostObj.remotePath = GlobalStateManager.getWorkDir(
                    currentHostObj.host
                );
                devboxList.push(currentHostObj);
            }

            resolve(devboxList);
        });
    });
};

export async function deleteSSHConfig(deletedHostLabel: string) {
    // 1. remove remote-ssh config
    const existingSSHHostPlatforms = vscode.workspace
        .getConfiguration('remote.SSH')
        .get<{ [host: string]: string }>('remotePlatform', {});
    const newSSHHostPlatforms = Object.keys(
        existingSSHHostPlatforms
    ).reduce((acc: { [host: string]: string }, host: string) => {
        if (host.startsWith(deletedHostLabel)) {
            return acc;
        }
        acc[host] = existingSSHHostPlatforms[host];
        return acc;
    }, {});
    await vscode.workspace
        .getConfiguration('remote.SSH')
        .update(
            'remotePlatform',
            newSSHHostPlatforms,
            vscode.ConfigurationTarget.Global
        );
    // 2. remove ssh config
    const content = await fs.promises.readFile(
        defaultClouddevSSHConfigPath,
        'utf8'
    );
    const lines = content.split('\n');

    let newLines = [];
    let skipLines = false;

    for (let i = 0; i < lines.length; i++) {
        const line = lines[i].trim();

        if (line.startsWith('Host ')) {
            const hostValue = line.split(' ')[1];
            if (hostValue === deletedHostLabel) {
                skipLines = true;
                continue;
            } else {
                skipLines = false;
            }
        }

        if (skipLines && line.startsWith('Host ')) {
            skipLines = false;
        }

        if (!skipLines) {
            newLines.push(lines[i]);
        }
    }

    await fs.promises.writeFile(defaultClouddevSSHConfigPath, newLines.join('\n'));

    // 3. delete private key file
    const privateKeyPath = `${defaultSSHKeyPath}/${deletedHostLabel}`;
    fs.rmSync(privateKeyPath);
}
