import * as vscode from 'vscode';
import fetch, { HeadersInit, RequestInfo } from 'node-fetch';
import { GlobalStateManager } from '../utils/globalStateManager';
import { LoginUser } from './user';
import { Logger } from '../common/logger';

interface ResponseData<T> {
    code: number;

    message: string;

    data: T;

    success: boolean;

    reqId: string,
}

async function resolveResponseValue(response: fetch.Response, targetType?: string) {
    const type = (response.headers.get('Content-Type') || 'text/');
    const autoType = type.startsWith('text/') ? 'text' : 'json';
    switch (targetType || autoType) {
        case 'text':
            return response.text();
        case 'json':
            return response.json();
        default:
            return undefined;
    }
}

export class ApiError extends Error {
    responseData: ResponseData<any>;

    constructor(responseData: ResponseData<any>) {
        super(responseData.message || '未知异常');
        this.responseData = responseData;
    }
}

export class Http {
    baseUrl: string;
    headers: HeadersInit = {};

    constructor(baseUrl: string, headers?: HeadersInit) {
        this.baseUrl = baseUrl;
        this.headers = headers || {
            'Content-Type': 'application/json',
        };
    }

    updateBaseUrl(baseUrl: string) {
        this.baseUrl = baseUrl;
    }

    get<T>(url: RequestInfo | URL, data?: { params: object }): Promise<T> {
        let wholeUrl = `${this.baseUrl}${url}`;
        if (data?.params) {
            const params = new URLSearchParams();
            Object.entries(data?.params ?? {}).forEach(([key, value]) => {
                // params.append(key, encodeURIComponent(value));
                value && params.append(key, value);
            });
            wholeUrl = `${this.baseUrl}${url}?${params.toString()}`;
        }
        return Http.request<T>(wholeUrl, {
            method: 'GET',
            headers: {
                ...this.headers,
                ptKey: GlobalStateManager.getPtKey(),
            },
        });
    }

    put<T>(url: RequestInfo | URL, data?: object): Promise<T> {
        return Http.request(this.baseUrl + url, {
            method: 'PUT',
            headers: {
                ...this.headers,
                ptKey: GlobalStateManager.getPtKey(),
            },
            body: data && JSON.stringify(data),
        });
    }

    post<T>(url: RequestInfo | URL, data?: object): Promise<T> {
        return Http.request(this.baseUrl + url, {
            method: 'POST',
            headers: {
                ...this.headers,
                ptKey: GlobalStateManager.getPtKey(),
            },
            body: data && JSON.stringify(data),
        });
    }

    delete(url: RequestInfo | URL, data?: object) {
        return Http.request(this.baseUrl + url, {
            method: 'DELETE',
            headers: {
                ...this.headers,
                ptKey: GlobalStateManager.getPtKey(),
            },
            body: data && JSON.stringify(data),
        });
    }

    static async request<T>(url: URL | RequestInfo, init?: any): Promise<T | any> {
        try {
            const res = await fetch(url, init);
            const result = await resolveResponseValue(res) as ResponseData<T>;
            const { data, code, message, reqId } = result;
            if (res.ok && (code === 200 || code === 0)) {
                return data;
            } else if (code === 401 || code === 500100 || code === 500101 || code === 500102) {
                // vscode.window.showInformationMessage(`API Error: ${code}:${message}`);
                Logger.info(`登录异常 API Error-[${reqId}]: ${code}:${message}`);
                vscode.commands.executeCommand('clouddev.welcome');
            } else {
                throw new ApiError(result);
            }
        } catch (error: any) {
            let message = error.message;
            if (error.responseData) {
                message = `API Error -[${error.responseData?.reqId}]- ${error.responseData?.code}:${error.responseData?.message}`;
            }
            vscode.window.showInformationMessage(message);
            Logger.error(message);
            throw error;
        }
    }
}

const {
    joyCoderBaseUrl = 'http://joycoder-api-inner-pr.jd.com',
    // joyCoderEnv = 'prod',
} = vscode.env as any;

export const clouddevHttp = new Http(`${joyCoderBaseUrl}/devcloud/api/v1`);
// export const clouddevHttp = new Http(`http://11.50.39.70:8080/api/v1`);
export const joycoderHttp = new Http(`${joyCoderBaseUrl}/api/saas`, {
    'Content-Type': 'application/json',
    loginType: 'PIN',
});
