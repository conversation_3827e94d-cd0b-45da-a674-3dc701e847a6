import * as os from 'os';
import { Logger } from "../common/logger";
import { clouddevHttp, joycoderHttp } from "./http";
import { getProjectAbbr, pickProjectColor } from "../utils/projectUtils";

export interface TemplateData {
    name: string,
    version: {
        id: number,
        name: string,
    }[]
}
export interface SSHConfigData {
    projectId: string,
    host: string,
    user: string,
    port: 0,
    privateKey: string
}

export enum ProjectStatus {
    Pending = 'Pending',
    Running = 'Running',
    Shutting = 'Shutting',
    Shutdown = 'Shutdown',
}
export enum DevMode {
    Remote = 1,
    Local = 2,
}
export class Project {
    private static cachedProject = new Map();

    id: number = 0;

    name: string = '';

    projectName: string = '';

    status: string = '';

    port: number = 8080;

    url?: string;

    abbrName?: string;

    bgColor?: string;

    templateName: string = 'Html';

    projectTemplateName?: string = 'Html';

    projectTemplateIconUrl?: string;

    projectTemplateId?: number;

    sshConfig?: SSHConfigData;

    deleteLocalFiles?: boolean;

    description: string = 'no description';

    serverInfo: string = '';

    devMode: DevMode = DevMode.Remote;

    databaseInfo?: string;

    databaseHost?: string;

    databasePort?: string;

    databaseName?: string;

    localLocation?: string;

    constructor(options?: Project) {
        Object.assign(this, options);

        // 新旧数据字段不一致，后期统一处理
        this.name = this.name || this.projectName;
        this.projectName = this.projectName || this.name;

        this.projectTemplateName = this.projectTemplateName
            || this.templateName;

        // 如果没有缩写名和背景色，使用工具方法生成
        if (!this.abbrName && this.name) {
            this.abbrName = getProjectAbbr(this.name);
        }
        if (!this.bgColor && this.name) {
            this.bgColor = pickProjectColor(this.name);
        }
    }

    static getOSInfo(): string {
        return `${os.type()} ${os.release()} ${os.arch()}`;
    }

    static async fetchProjectList() {
        const res = await clouddevHttp.post<{
            records: Project[],
        }>('/projects/my', { page: { num: 1, size: 100 } });
        const templateAppIds = [
            ...new Set(res.records.map(item => item.projectTemplateId).filter(Boolean)),
        ];
        const templates = await joycoderHttp.post<{
            id: number,
            imgUrl?: string,
        }[]>('/templateApp/v1/app', { templateAppIds });
        return res.records.map((item: Project) => {
            const project = new Project(item);
            const template = templates.find((
                template: { id: number | undefined; },
            ) => template.id === project.projectTemplateId);
            project.projectTemplateIconUrl = template?.imgUrl ?? '';
            Project.cachedProject.set(`${item.name}-${item.id}`, project);
            return project;
        });
    }

    static async create(data: Project) {
        return clouddevHttp.post<Project>('/projects', {
            ...data,
            allowExternal: true,
        });
    }

    get isRunning() {
        return this.status === ProjectStatus.Running;
    }

    static async fetchTemplates() {
        return clouddevHttp.get<{
            languages: TemplateData[],
            frameworks: TemplateData[],
        }>('/templates');
    }

    async fetchSSHConfig() {
        const ssh = await clouddevHttp.get<SSHConfigData>(`/projects/${this.id}/ssh-config`);
        this.sshConfig = ssh;
        return ssh;
    }

    delete() {
        return clouddevHttp.delete(`/projects/${this.id}`);
    }

    async fetchDetail(forceUpdate = false) {
        const cacheKey = `${this.name}-${this.id}`;
        const cachedProject = Project.cachedProject.get(cacheKey);
        if (!forceUpdate && cachedProject) {
            Object.assign(this, cachedProject);
            Logger.info(`Using cached project details for ${this.name}`);
            return;
        }
        const data = await clouddevHttp.get<Project>(`/projects/${this.id}`);
        const { databaseHost, databasePort, databaseName } = data;
        Object.assign(this, {
            ...data,
            databaseInfo: databaseHost
                ? `${databaseHost}:${databasePort}/${databaseName}`
                : this.databaseInfo,
        });
        Logger.info(`Fetched project detail for ${this.name}`);
        Project.cachedProject.set(cacheKey, this);
    }

    async fetchReleaseList(pageNum: number, pageSize: number) {
        return clouddevHttp.post<{
            records: {
                id: number,
                imageTag: string,
                description: string,
                status: string,
                createdAt: string,
            }[],
            total: number,
        }>(`/projects/${this.id}/release/list`, { page: { num: pageNum, size: pageSize } });
    }

    fetchImageName() {
        return clouddevHttp.get<string>(`/projects/${this.id}/imageName`);
    }

    release(data: object) {
        return clouddevHttp.post(`/projects/${this.id}/release`, data);
    }

    /**
     * 关机： 保持资源和端口号
     * @returns boolean 是否关机成功
     */
    stop() {
        return clouddevHttp.post(`/projects/${this.id}/shutdown`);
    }

    /**
     * 启动
     * @returns boolean 是否关机成功
     */
    start() {
        return clouddevHttp.post(`/projects/${this.id}/start`);
    }

    static async fetchAppList(projectId?: number) {
        const { records } = await clouddevHttp.post<{
            records: {
                id: number,
                name: string,
            }[],
        }>('/apps/list', {
            projectId,
            fetchStatus: false,
            page: { num: 1, size: 100 },
        });
        return records;
    }
}
