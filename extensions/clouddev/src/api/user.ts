import * as vscode from 'vscode';
import { getLocalizedText } from '../utils/l10nHelper';
import { GlobalStateManager } from '../utils/globalStateManager';
import { Logger } from '../common/logger';
import { clouddevHttp, joycoderHttp } from './http';

const messages = {
    login: getLocalizedText('Login successful'),
};
export const callbackId = 'joycoder.callback.cloudDev';

export class LoginUser {
    constructor(options?: LoginUser) {
        Object.assign(this, options);
    }

    static init(context: vscode.ExtensionContext) {
        // 检查是否在远程环境
        const isRemote = !!vscode.env.remoteName;
        Logger.info(`LoginUser.init 被调用，当前环境: ${isRemote ? '远程' : '本地'}, uriScheme: ${vscode.env.uriScheme}`);

        const loginStatusChangedCommand = vscode.commands.registerCommand(
            callbackId,
            (params: any) => {
                // 处理登入/登出逻辑
                Logger.info('当前登录态发生变化');
                Logger.info(`joyCoderBaseUrl: ${(vscode.env as any)?.joyCoderBaseUrl}`);
                Logger.info(`joyCoderEnv: ${(vscode.env as any)?.joyCoderEnv}`);
                const joyCoderBaseUrl = (vscode.env as any)?.joyCoderBaseUrl;
                clouddevHttp.updateBaseUrl(`${joyCoderBaseUrl}/devcloud/api/v1`);
                joycoderHttp.updateBaseUrl(`${joyCoderBaseUrl}/api/saas`);
                if (params?.userInfo?.pt_key) {
                    Logger.info('用户登录成功，设置 ptKey 并执行刷新命令');
                    GlobalStateManager.setPtKey(params?.userInfo?.pt_key);
                    vscode.commands.executeCommand('clouddev.refresh');
                } else {
                    // 用户退出登录
                    Logger.info('用户退出登录，清除 ptKey 并显示登录页面');
                    GlobalStateManager.setPtKey(''); // 清除 ptKey
                    vscode.commands.executeCommand('clouddev.welcome');
                }
            }
        );
        context.subscriptions.push(loginStatusChangedCommand);

        // 检查登录状态
        vscode.commands.executeCommand('workbench.action.joycoderIsLoggedIn', callbackId).then((isLoggedIn) => {
            if (!isLoggedIn) {
                vscode.commands.executeCommand('clouddev.welcome');
            } else {
                vscode.commands.executeCommand('workbench.action.joycoderGetLoginInfo', (userInfo: any) => {
                    GlobalStateManager.setPtKey(userInfo?.pt_key);
                    vscode.commands.executeCommand('clouddev.refresh');
                });
            }
        });
    }

    static isLogin() {
        return !!GlobalStateManager.getPtKey();
    }

    static async login() {
        vscode.commands.executeCommand(
            'workbench.action.joycoderLogin',
            callbackId
        );
    }

    static async currentAccountIsArrear() {
        return clouddevHttp.post<boolean>('/bills/isArrear');
    }
}
