import * as vscode from 'vscode';
import * as os from 'os';
import { Logger } from './common/logger';
import { <PERSON>riHandler } from './utils/handleUri';
import { ToolCommands } from './commands/tools';
import { RemoteSSHConnector } from './commands/remoteConnector';
import { NetworkViewProvider } from './providers/NetworkViewProvider';
import { GlobalStateManager } from './utils/globalStateManager';
import { DashboardViewProvider } from './providers/DashboardViewProvider';
import { LoginUser } from './api/user';
import { initializeL10n, getLocalizedText } from './utils/l10nHelper';

export async function activate(context: vscode.ExtensionContext) {
    try {
        // Logger
        Logger.init(context);

        Logger.info(`扩展激活，当前环境: ${vscode.env.remoteName || '本地'}`);
        Logger.info(`操作系统: ${os.type()} ${os.release()} ${os.arch()}`);
        Logger.info(`VSCode 版本: ${vscode.version}`);

        // Initialize l10n
        initializeL10n(context);

        // globalState manager
        try {
            GlobalStateManager.init(context);
        } catch (error) {
            Logger.error('GlobalStateManager 初始化失败:', error);
        }

        // 设置初始上下文值 - 基于当前登录状态
        const isLoggedIn = LoginUser.isLogin();
        await vscode.commands.executeCommand('setContext', 'clouddevLoggedIn', isLoggedIn);

        // 用户登录信息 - 这里会检查登录状态并可能更新上下文值
        try {
            LoginUser.init(context);
        } catch (error) {
            Logger.error('LoginUser 初始化失败:', error);
        }

        // tools
        try {
            const tools = new ToolCommands(context);
            context.subscriptions.push(tools);
        } catch (error) {
            Logger.error('ToolCommands 初始化失败:', error);
        }

        // remote connector
        try {
            const remoteConnector = new RemoteSSHConnector(context);
            context.subscriptions.push(remoteConnector);
        } catch (error) {
            Logger.error('RemoteSSHConnector 初始化失败:', error);
        }

        // clouddevList view
        try {
            const dashboardViewProvider = new DashboardViewProvider(context);
            context.subscriptions.push(dashboardViewProvider);
        } catch (error) {
            Logger.error('DashboardViewProvider 初始化失败:', error);
        }

        // network view
        try {
            const networkViewProvider = new NetworkViewProvider(context);
            context.subscriptions.push(networkViewProvider);
        } catch (error) {
            Logger.error('NetworkViewProvider 初始化失败:', error);
        }

        // handle uri
        try {
            const uriHandler = new UriHandler();
            context.subscriptions.push(
                vscode.window.registerUriHandler({
                    handleUri: (uri) => uriHandler.handle(uri),
                })
            );
        } catch (error) {
            Logger.error('UriHandler 初始化失败:', error);
        }

        Logger.info('Your extension "clouddev" is now active!');
    } catch (error) {
        Logger.error('扩展激活过程中发生错误:', error);
        throw error; // 重新抛出错误，确保 VSCode 知道激活失败
    }
}

export function deactivate() {
    Logger.info('Your extension "clouddev" is being deactivated.');
}
