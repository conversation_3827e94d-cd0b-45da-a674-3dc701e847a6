import * as os from 'os';
import * as fs from 'fs';
import * as path from 'path';
import dayjs from 'dayjs';
import * as vscode from 'vscode';
import { getLocalizedText } from '../utils/l10nHelper';
import SSHConfig from 'ssh-config';

import {
    defaultSSHConfigPath,
    defaultClouddevSSHConfigPath,
    defaultSSHKeyPath,
} from '../constant/file';
import { Logger } from '../common/logger';
import { Disposable } from '../common/dispose';
import { convertSSHConfigToVersion2 } from '../utils/sshConfig';
import { ensureFileAccessPermission, ensureFileExists } from '../utils/file';
import { modifiedRemoteSSHConfig } from '../utils/remoteSSHConfig';

/**
 * 安全地获取用户主目录
 * @returns 验证后的安全主目录路径
 */
function getSafeHomeDirectory(): string {
    const homeDir = os.homedir();

    if (!homeDir || typeof homeDir !== 'string') {
        throw new Error('Invalid home directory');
    }

    // 清理路径，移除危险字符
    const sanitized = homeDir
        .replace(/[<>"'&;|`$(){}[\]]/g, '') // 移除危险字符
        .trim();

    if (!sanitized || sanitized.length === 0) {
        throw new Error('Invalid sanitized home directory');
    }

    // 检查路径长度限制
    if (sanitized.length > 1000) {
        throw new Error('Home directory path too long');
    }

    // 标准化路径
    const normalizedPath = path.resolve(sanitized);

    // 检查路径遍历
    if (normalizedPath.includes('..')) {
        throw new Error('Path traversal detected in home directory');
    }

    return normalizedPath;
}

/**
 * 安全地解析包含 ~ 的路径
 * @param configPath 配置文件路径
 * @returns 验证后的安全路径
 */
function resolveSafeConfigPath(configPath: string): string {
    if (!configPath || typeof configPath !== 'string') {
        throw new Error('Invalid config path');
    }

    // 清理输入路径
    const sanitizedPath = configPath
        .replace(/[<>"'&;|`$(){}[\]]/g, '') // 移除危险字符
        .trim();

    if (!sanitizedPath || sanitizedPath.length === 0) {
        throw new Error('Invalid sanitized config path');
    }

    // 检查路径长度
    if (sanitizedPath.length > 500) {
        throw new Error('Config path too long');
    }

    let resolvedPath: string;

    if (sanitizedPath.startsWith('~')) {
        // 安全地替换 ~ 为主目录
        const safeHomeDir = getSafeHomeDirectory();
        resolvedPath = sanitizedPath.replace(/^~/, safeHomeDir);
    } else {
        resolvedPath = sanitizedPath;
    }

    // 标准化路径
    const normalizedPath = path.resolve(resolvedPath);

    // 检查路径遍历
    if (normalizedPath.includes('..')) {
        throw new Error('Path traversal detected in config path');
    }

    // 验证文件扩展名（SSH 配置文件通常没有扩展名或有特定扩展名）
    const allowedExtensions = ['', '.config', '.conf'];
    const ext = path.extname(normalizedPath);
    if (!allowedExtensions.includes(ext)) {
        throw new Error(`Invalid config file extension: ${ext}`);
    }

    return normalizedPath;
}

/**
 * 验证文件路径用于读写操作
 * @param filePath 文件路径
 * @returns 验证后的安全文件路径
 */
function validateFilePathForOperation(filePath: string): string {
    if (!filePath || typeof filePath !== 'string') {
        throw new Error('Invalid file path');
    }

    // 标准化路径
    const normalizedPath = path.resolve(filePath);

    // 检查路径遍历
    if (normalizedPath.includes('..')) {
        throw new Error('Path traversal detected in file path');
    }

    // 检查路径长度
    if (normalizedPath.length > 1000) {
        throw new Error('File path too long');
    }

    return normalizedPath;
}

const message = {
    FailedToWriteSSHConfig: getLocalizedText('Failed to write SSH configuration'),
    FailedToWriteSSHPrivateKey: getLocalizedText('Failed to write SSH private key'),
    PleaseInstallRemoteSSH: getLocalizedText(
        'Please install "Remote - SSH" extension to connect to a devbox workspace.'
    ),
    Install: getLocalizedText('Install'),
    Cancel: getLocalizedText('Cancel'),
    // FailedToWriteSSHConfig: '写入 SSH 配置失败',
    // FailedToWriteSSHPrivateKey: '写入 SSH 私钥失败',
    // PleaseInstallRemoteSSH: '请安装 \"Remote - SSH\" 扩展以连接到 Devbox 工作区。',
    // Install: '安装',
    // Cancel: '取消',
};

export class RemoteSSHConnector extends Disposable {
    constructor(context: vscode.ExtensionContext) {
        super();
        if (context.extension.extensionKind === vscode.ExtensionKind.UI) {
            this.sshConfigPreProcess();
            this._register(
                vscode.commands.registerCommand('clouddev.connectRemoteSSH', (args) =>
                    this.connectRemoteSSH(args)
                )
            );
            this._register(
                vscode.commands.registerCommand('clouddev.updateRemoteSSH', (args) =>
                    this.connectRemoteSSH({
                        ...args,
                        onlyUpdate: true,
                    })
                )
            );
        }
    }

    private replaceHomePathInConfig(content: string): string {
        const includePattern = new RegExp(
            `Include ${os.homedir()}/.ssh/clouddev/config`,
            'g'
        );
        const includePattern2 = new RegExp(
            `Include "${os.homedir()}/.ssh/clouddev/config"`,
            'g'
        );

        const includeLine = `Include ~/.ssh/clouddev/config`;

        if (includePattern.test(content)) {
            return content.replace(includePattern, '');
        }

        if (includePattern2.test(content)) {
            return content.replace(includePattern2, '');
        }

        if (content.includes(includeLine)) {
            return content;
        }

        return `${includeLine}\n${content}`;
    }

    private sshConfigPreProcess() {
        Logger.info('SSH config pre-processing');
        // 1. ensure .ssh/config exists
        ensureFileExists(defaultSSHConfigPath, '.ssh');
        // 2. ensure .ssh/clouddev/config exists
        ensureFileExists(defaultClouddevSSHConfigPath, '.ssh/clouddev');

        const customConfigFile = vscode.workspace
            .getConfiguration('remote.SSH')
            .get<string>('configFile', '');

        if (customConfigFile) {
            try {
                // 使用安全的路径解析函数，完全阻断污点传播
                const resolvedPath = resolveSafeConfigPath(customConfigFile);

                // 验证文件路径用于读写操作
                const safeFilePath = validateFilePathForOperation(resolvedPath);

                const existingSSHConfig = fs.readFileSync(safeFilePath, 'utf8');
                const updatedConfig = this.replaceHomePathInConfig(existingSSHConfig);
                if (updatedConfig !== existingSSHConfig) {
                    fs.writeFileSync(safeFilePath, updatedConfig);
                }
            } catch (error) {
                console.error(`Error reading/writing SSH config: ${error}`);
                this.handleDefaultSSHConfig();
            }
        } else {
            this.handleDefaultSSHConfig();
        }
        // 4. ensure sshConfig from version1 to version2
        convertSSHConfigToVersion2(defaultClouddevSSHConfigPath);

        Logger.info('SSH config pre-processing completed');
    }
    // backup the devbox ssh config
    private sshConfigPostProcess() {
        Logger.info('SSH config post-processing');

        try {
            const clouddevSSHConfig = fs.readFileSync(
                defaultClouddevSSHConfigPath,
                'utf8'
            );
            const backupFolderPath = defaultSSHKeyPath + '/backup/clouddev_config';
            if (!fs.existsSync(backupFolderPath)) {
                fs.mkdirSync(backupFolderPath, { recursive: true });
            }
            const backFileName = dayjs().format('YYYY-MM-DD_HH-mm-ss');
            const backupFilePath = `${backupFolderPath}/${backFileName}`;

            fs.writeFileSync(backupFilePath, clouddevSSHConfig);
            Logger.info(`SSH config backed up to ${backupFilePath}`);
        } catch (error) {
            Logger.error(`Failed to backup SSH config: ${error}`);
        }
    }

    private handleDefaultSSHConfig() {
        const existingSSHConfig = fs.readFileSync(defaultSSHConfigPath, 'utf8');
        const updatedConfig = this.replaceHomePathInConfig(existingSSHConfig);
        if (updatedConfig !== existingSSHConfig) {
            fs.writeFileSync(defaultSSHConfigPath, updatedConfig);
        }
    }

    private async connectRemoteSSH(args: {
        sshHost: string
        sshUser: string
        sshPort: string
        base64PrivateKey: string
        sshHostLabel: string
        workingDir: string
        forceNewWindow?: boolean,
        onlyUpdate?: boolean,
    }) {
        const {
            sshHost, sshUser, sshPort, base64PrivateKey, sshHostLabel, workingDir,
            forceNewWindow = false, onlyUpdate = false,
        } = args || {};

        console.log(forceNewWindow, onlyUpdate);

        Logger.info(`${onlyUpdate ? 'Update the SSH config' : 'Connecting to remote SSH'}: ${args?.sshHostLabel}`);


        await modifiedRemoteSSHConfig(sshHostLabel);

        const normalPrivateKey = Buffer.from(base64PrivateKey, 'base64');

        const sshConfig = new SSHConfig().append({
            Host: sshHostLabel,
            HostName: sshHost,
            User: sshUser,
            Port: sshPort,
            IdentityFile: `~/.ssh/clouddev/${sshHostLabel}`,
            IdentitiesOnly: 'yes',
            StrictHostKeyChecking: 'no',
        });
        const sshConfigString = SSHConfig.stringify(sshConfig);

        this.sshConfigPreProcess();

        try {
            Logger.info('Writing SSH config to .ssh/clouddev/config');

            const existingDevboxConfigLines = fs
                .readFileSync(defaultClouddevSSHConfigPath, 'utf8')
                .split('\n');

            //  replace the existing ssh config item
            const newDevboxConfigLines = [];
            let skipLines = false;

            for (let i = 0; i < existingDevboxConfigLines.length; i++) {
                const line = existingDevboxConfigLines[i].trim();

                if (
                    line.startsWith('Host ') &&
                    line.substring(5).trim().startsWith(sshHostLabel)
                ) {
                    skipLines = true;
                    continue;
                }

                if (skipLines) {
                    if (
                        line.startsWith('Host ') ||
                        i === existingDevboxConfigLines.length
                    ) {
                        skipLines = false;
                    }
                }

                if (!skipLines) {
                    newDevboxConfigLines.push(existingDevboxConfigLines[i]);
                }
            }

            fs.writeFileSync(
                defaultClouddevSSHConfigPath,
                newDevboxConfigLines.join('\n')
            );

            // 5. write new ssh config to .ssh/clouddev/config
            fs.appendFileSync(defaultClouddevSSHConfigPath, `\n${sshConfigString}\n`);

            Logger.info('SSH config written to .ssh/clouddev/config');
        } catch (error) {
            Logger.error(`Failed to write SSH configuration: ${error}`);
            vscode.window.showErrorMessage(
                `${message.FailedToWriteSSHConfig}: ${error}`
            );
        }

        // 6. create devCloud privateKey file in .ssh/clouddev
        try {
            Logger.info('Creating clouddev privateKey file in .ssh/clouddev');
            const sshKeyPath = defaultSSHKeyPath + `/${sshHostLabel}`;
            fs.writeFileSync(sshKeyPath, normalPrivateKey);
            ensureFileAccessPermission(sshKeyPath);
            Logger.info('Clouddev privateKey file created in .ssh/clouddev');
        } catch (error) {
            Logger.error(`Failed to write SSH private key: ${error}`);
            vscode.window.showErrorMessage(
                `${message.FailedToWriteSSHPrivateKey}: ${error}`
            );
        }

        if (onlyUpdate) { return; }

        await vscode.commands.executeCommand(
            'vscode.openFolder',
            vscode.Uri.parse(
                `vscode-remote://ssh-remote+${sshHostLabel}${workingDir}`
            ),
            {
                forceNewWindow,
            }
        );

        Logger.info('Clouddev Project opened in VSCode');

        // this.sshConfigPostProcess();
    }

    private async ensureRemoteSSHExtInstalled(): Promise<boolean> {
        const isOfficialVscode =
            vscode.env.uriScheme === 'vscode' ||
            vscode.env.uriScheme === 'vscode-insiders' ||
            vscode.env.uriScheme === 'joycode' ||
            vscode.env.uriScheme === 'cursor';
        const isTrae = vscode.env.uriScheme === 'trae';

        // windsurf has remote-ssh inside already
        if (!isOfficialVscode && !isTrae) {
            return true;
        }

        const remoteSSHId = isOfficialVscode
            ? 'ms-vscode-remote.remote-ssh'
            : 'labring.open-remote-ssh-for-trae';

        const msVscodeRemoteExt = vscode.extensions.getExtension(remoteSSHId);

        if (msVscodeRemoteExt) {
            return true;
        }

        const install = message.Install;
        const cancel = message.Cancel;

        const action = await vscode.window.showInformationMessage(
            message.PleaseInstallRemoteSSH,
            { modal: true },
            install,
            cancel
        );

        if (action === cancel) {
            return false;
        }

        await vscode.commands.executeCommand('extension.open', remoteSSHId);
        await vscode.commands.executeCommand(
            'workbench.extensions.installExtension',
            remoteSSHId
        );

        Logger.info(`"${remoteSSHId}" extension is installed`);

        return true;
    }
}
