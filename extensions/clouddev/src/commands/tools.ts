import * as vscode from 'vscode';
import { Disposable } from '../common/dispose';
import { Logger } from '../common/logger';
export class ToolCommands extends Disposable {
    constructor(context: vscode.ExtensionContext) {
        super();
        // open external link
        this._register(
            vscode.commands.registerCommand('clouddev.openExternalLink', (args) => {
                vscode.env.openExternal(vscode.Uri.parse(args));
            })
        );

        // 注册查看日志的命令
        this._register(
            vscode.commands.registerCommand('clouddev.showLog', (args) => {
                Logger.show();
            })
        );
    }
}
