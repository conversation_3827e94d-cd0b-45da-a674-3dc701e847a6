import * as vscode from 'vscode';
import * as fs from 'fs';
import { getLocalizedText } from '../utils/l10nHelper';
import { Project } from '../api/project';
import { Logger } from '../common/logger';
import { LoginUser } from '../api/user';
import {
    DevCloudDomainMap,
    SHOW_DEPLOYMENT_DIALOG_COMMAND_ID,
    SHOW_GENERATE_PROCESS_DIALOG_COMMAND_ID,
    SHOW_GENERATE_VERSION_DIALOG_COMMAND_ID,
} from '../constant';

export class ProjectDetailViewProvider {
    private static instance: ProjectDetailViewProvider;
    private panel?: vscode.WebviewPanel;
    private readonly _extensionUri: vscode.Uri;
    private currentProject?: Project;

    private constructor(extensionUri: vscode.Uri) {
        this._extensionUri = extensionUri;
    }

    public static getInstance(extensionUri: vscode.Uri): ProjectDetailViewProvider {
        if (!ProjectDetailViewProvider.instance && extensionUri) {
            ProjectDetailViewProvider.instance = new ProjectDetailViewProvider(extensionUri);
        }
        return ProjectDetailViewProvider.instance;
    }

    public static isInstanceExists(): boolean {
        return !!ProjectDetailViewProvider.instance;
    }

    public async showProjectDetail(project: Project) {
        this.currentProject = new Project(project);
        try {
            // 获取最新的项目详情
            await this.currentProject.fetchDetail(true);

            if (!this.panel) {
                // 创建新的webview panel
                this.panel = vscode.window.createWebviewPanel(
                    'projectDetail',
                    this.currentProject.name,
                    vscode.ViewColumn.One,
                    {
                        enableScripts: true,
                        localResourceRoots: [
                            this._extensionUri,
                            vscode.Uri.joinPath(this._extensionUri, 'resource')
                        ]
                    }
                );

                // 监听面板关闭事件
                this.panel.onDidDispose(() => {
                    this.panel = undefined;
                });

                // 设置webview消息处理
                this.panel.webview.onDidReceiveMessage(async (message) => {
                    try {
                        const joyCoderEnv = (vscode.env as any).joyCoderEnv as keyof typeof DevCloudDomainMap;
                        Logger.info(`joyCoderEnv: ${(vscode.env as any)?.joyCoderEnv}`);
                        switch (message.command) {
                            case 'getI18nTexts':
                                // 发送国际化文本到 webview
                                this.panel?.webview.postMessage({
                                    command: 'setI18nTexts',
                                    texts: {
                                        Success: getLocalizedText('Success'),
                                        Pending: getLocalizedText('Pending'),
                                        Failed: getLocalizedText('Failed'),
                                        Deploy: getLocalizedText('Deploy'),
                                        'Insufficient Balance': getLocalizedText('Insufficient Balance'),
                                        'More Actions': getLocalizedText('More Actions'),
                                        'No Image History': getLocalizedText('No Image History')
                                    }
                                });
                                break;
                            case 'initReleases':
                                if (!this.currentProject) {
                                    throw new Error('No project is currently open');
                                }
                                const releases = await this.currentProject.fetchReleaseList(message.page, message.pageSize);
                                this.panel?.webview.postMessage({
                                    command: 'appendReleases',
                                    releases: releases
                                });
                                break;
                            case 'loadMoreReleases':
                                if (!this.currentProject) {
                                    throw new Error('No project is currently open');
                                }
                                const moreReleases = await this.currentProject.fetchReleaseList(message.page, message.pageSize);
                                this.panel?.webview.postMessage({
                                    command: 'appendReleases',
                                    releases: moreReleases
                                });
                                break;
                            case 'createImage':
                                if (await LoginUser.currentAccountIsArrear()) {
                                    ProjectDetailViewProvider.arrearModal();
                                    return;
                                }
                                if (!this.currentProject) {
                                    throw new Error('No project is currently open');
                                }
                                const imageName = await this.currentProject.fetchImageName();
                                const {
                                    confirmed,
                                    data,
                                }: {
                                    confirmed: boolean,
                                    data: object,
                                } = await vscode.commands.executeCommand(
                                    SHOW_GENERATE_VERSION_DIALOG_COMMAND_ID,
                                    imageName,
                                );
                                if (confirmed) {
                                    if (!this.currentProject) {
                                        throw new Error('No project is currently open');
                                    }
                                    await this.currentProject.release(data);
                                    // 刷新项目详情
                                    await this.showProjectDetail(this.currentProject);
                                }
                                break;
                            case 'deploy':
                                if (await LoginUser.currentAccountIsArrear()) {
                                    ProjectDetailViewProvider.arrearModal();
                                    return;
                                }
                                const imageTag = message.imageTag;
                                if (!this.currentProject) {
                                    throw new Error('No project is currently open');
                                }
                                const appList = await Project.fetchAppList(this.currentProject.id);
                                if (!appList?.length) {
                                    vscode.env.openExternal(vscode.Uri.parse(`${DevCloudDomainMap[joyCoderEnv]}/apps/create?projectId=${this.currentProject.id
                                        }&imageTag=${imageTag}`));
                                    return;
                                }
                                // 打开部署对话框命令
                                const { action, deployment }: {
                                    action: string,
                                    deployment?: { id: number }
                                } = await vscode.commands.executeCommand(SHOW_DEPLOYMENT_DIALOG_COMMAND_ID, appList);
                                if (action === 'createApp') {
                                    vscode.env.openExternal(vscode.Uri.parse(`${DevCloudDomainMap[joyCoderEnv]}/apps/create?projectId=${this.currentProject.id
                                        }&imageTag=${imageTag}`));
                                    return;
                                }
                                if (action === 'updateImageAndConfig' && deployment) {
                                    vscode.env.openExternal(vscode.Uri.parse(`${DevCloudDomainMap[joyCoderEnv]}/apps/update/${deployment.id}?projectId=${this.currentProject.id
                                        }&imageTag=${imageTag}`));
                                    return;
                                }
                                break;
                        }
                    } catch (error) {
                        Logger.error('Failed to handle webview message:', error);
                        vscode.window.showErrorMessage('操作失败: ' + (error instanceof Error ? error.message : String(error)));
                    }
                });
            } else {
                this.panel?.webview.postMessage({
                    command: 'refresh',
                });
            }

            // 更新面板标题
            this.panel.title = this.currentProject.name;

            // 设置panel图标
            const iconSvg = `
            <svg width="16" height="16" viewBox="0 0 16 16" xmlns="http://www.w3.org/2000/svg">
                <text x="50%" y="50%" dominant-baseline="central" dy=".1em" text-anchor="middle" fill="${this.currentProject.bgColor}" font-size="10">${this.currentProject.abbrName}</text>
            </svg>`;
            this.panel.iconPath = vscode.Uri.parse(`data:image/svg+xml;base64,${Buffer.from(iconSvg).toString('base64')}`);

            // 更新webview内容
            this.panel.webview.html = this._getWebviewContent(this.currentProject);

            // 显示面板
            this.panel.reveal();

        } catch (error) {
            Logger.error('Failed to show project detail:', error);
            vscode.window.showErrorMessage('Failed to show project detail');
        }
    }

    private _getWebviewContent(project: Project): string {
        // 获取资源URI
        const getUri = (...paths: string[]) => {
            return this.panel?.webview.asWebviewUri(vscode.Uri.joinPath(this._extensionUri, ...paths)).toString() || '';
        };

        // 读取HTML模板
        const htmlContent = fs.readFileSync(
            vscode.Uri.joinPath(this._extensionUri, 'views', 'project_detail.html').fsPath,
            'utf8'
        );

        // 准备替换变量
        const replacements: Record<string, string> = {
            '#{webview.resourcePath}': getUri(),
            '#{webview.css}': getUri('views', 'project_detail.css'),
            '#{webview.js}': getUri('views', 'project_detail.js'),
            '#{webview.iconfont}': getUri('resource', 'iconfont', 'iconfont.css'),
            '#{Server Info}': getLocalizedText('Server Info'),
            '#{Dev Mode}': getLocalizedText('Dev Mode'),
            '#{Template}': getLocalizedText('Template'),
            '#{Database}': getLocalizedText('Database'),
            '#{Domain}': getLocalizedText('Domain'),
            '#{Export Ports}': getLocalizedText('Export Ports'),
            '#{Basic Info}': getLocalizedText('Basic Info'),
            '#{Image History}': getLocalizedText('Image History'),
            '#{Create Image}': getLocalizedText('Create Image'),
            '#{Version}': getLocalizedText('Version'),
            '#{Status}': getLocalizedText('Status'),
            '#{Create Time}': getLocalizedText('Create Time'),
            '#{Description}': getLocalizedText('Description'),
            '#{Actions}': getLocalizedText('Actions'),
            '#{No Image History}': getLocalizedText('No Image History'),
            '#{Loading...}': getLocalizedText('Loading...'),
            '#{project.serverInfo}': project.serverInfo || '-',
            '#{project.devMode}': project.devMode === 1 ? getLocalizedText('Remote') : getLocalizedText('Local'),
            '#{project.projectTemplateName}': project.projectTemplateName || '-',
            '#{project.databaseInfo}': project.databaseInfo || '-',
            '#{project.url}': project.url || '-',
            '#{project.displayUrl}': project.url ?
                `<a href="${project.url}">${project.url.replace(/http(s)?:\/\//, '')}</a>` : '-',
            '#{project.port}': String(project.port || '8080'),
            '#{project.displayImage}': project.devMode === 1 ? 'remote' : 'local',
            '#{project.createImageButton}':
                `${project.status !== 'Shutdown' ? 'disabled' : ''}
                title="${project.status !== 'Shutdown' ? getLocalizedText('Please shutdown first to save state') : getLocalizedText('Generate Image')}"
                style="${project.status !== 'Shutdown' ? 'opacity: 0.5; cursor: not-allowed;' : ''}"
                onclick="${project.status === 'Shutdown' ? 'createImage()' : ''}"`,
        };

        // 替换所有变量
        let webviewContent = htmlContent;
        Object.entries(replacements).forEach(([key, value]) => {
            webviewContent = webviewContent.replace(new RegExp(key.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'g'), value);
        });

        return webviewContent;
    }

    public static async arrearModal() {
        const { action }: { action: string } = await vscode.commands
            .executeCommand(SHOW_GENERATE_PROCESS_DIALOG_COMMAND_ID);
        if (action === 'createApp') {
            vscode.env.openExternal(vscode.Uri.parse('https://capital.jdcloud.com/cost/capital/recharge'));
        }
    }
}
