import * as vscode from 'vscode';
import { getLocalizedText } from '../utils/l10nHelper';
import { Logger } from '../common/logger';
import { Disposable } from '../common/dispose';

const messages = {
    loginTitle: getLocalizedText('Login to enjoy cloud development'),
    loginButton: getLocalizedText('Login'),
};

export class WelcomeViewProvider
    extends Disposable
    implements vscode.WebviewViewProvider {
    public static readonly viewType = 'clouddevWelcome';

    private _extensionUri: vscode.Uri;
    private _view?: vscode.WebviewView;

    constructor(context: vscode.ExtensionContext) {
        Logger.info('Initializing WelcomeViewProvider');
        super();
        this._extensionUri = context.extensionUri;

        this._register(
            vscode.commands.registerCommand('clouddev.welcome', () => {
                Logger.info('执行 clouddev.welcome 命令，显示登录页面');
                // 设置上下文变量，控制视图的显示
                vscode.commands.executeCommand('setContext', 'clouddevLoggedIn', false);

                // 确保 clouddevDashboard 视图容器可见
                // try {
                //     // 尝试聚焦到 clouddevDashboard 视图容器
                //     vscode.commands.executeCommand('clouddevDashboard');
                // } catch (error) {
                //     Logger.error('无法聚焦到 clouddevDashboard 视图容器', error);
                // }
            })
        );

        // 记录扩展路径，用于调试
        const isRemote = !!vscode.env.remoteName;
        Logger.info(
            `WelcomeViewProvider 初始化，当前环境: ${isRemote ? '远程' : '本地'}`
        );
        Logger.info(`扩展路径: ${this._extensionUri.fsPath}`);
    }

    public resolveWebviewView(
        webviewView: vscode.WebviewView,
        _context: vscode.WebviewViewResolveContext,
        _token: vscode.CancellationToken
    ) {
        this._view = webviewView;

        // 检查是否在远程环境
        const isRemote = !!vscode.env.remoteName;
        Logger.info(
            `resolveWebviewView 被调用，当前环境: ${isRemote ? '远程' : '本地'}`
        );

        // 设置 WebView 选项，确保能够访问扩展目录中的资源
        webviewView.webview.options = {
            enableScripts: true,
            localResourceRoots: [this._extensionUri],
        };

        webviewView.webview.html = this._getHtmlForWebview(webviewView.webview);

        // 处理 WebView 消息
        webviewView.webview.onDidReceiveMessage(async (message) => {
            switch (message.command) {
                case 'login':
                    vscode.commands.executeCommand('workbench.action.joycoderLogin');
                    return;
            }
        });
    }

    private _getHtmlForWebview(webview: vscode.Webview): string {
        const isEnglish = vscode.env.language.startsWith('en');
        const welcomeText = isEnglish
            ? 'For a better experience, please {0} to your account'
            : '请{0}您的账号以获得更好的体验';
        const loginText = isEnglish ? 'login' : '登录';

        return /* html */ `
            <!DOCTYPE html>
            <html lang="zh-CN">
            <head>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>Welcome</title>
                <style>
                    body {
                        --login-link-color: #1565c0;
                        --login-link-hover-color: #0d47a1;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        height: 100vh;
                        background: var(--vscode-sideBar-background, #141414) !important;
                        color: var(--vscode-sideBar-foreground, #CCCCCC) !important;
                        margin: 0;
                        font-family: var(--vscode-font-family);
                    }
                    #loginTitle {
                        text-align: center;
                        padding: 20px;
                    }
                    .login-link {
                        color: var(--login-link-color);
                        cursor: pointer;
                        text-decoration: underline;
                        transition: color 0.2s;
                    }
                    .login-link:hover {
                        color: var(--login-link-hover-color);
                    }
                </style>
            </head>
            <body>
                <div id="loginTitle">
                    ${welcomeText.replace('{0}', `<a class="login-link" onclick="login()">${loginText}</a>`)}
                </div>

                <script>
                    // 注入 VSCode API
                    const vscode = acquireVsCodeApi();
                    function login() {
                        vscode.postMessage({
                            command: 'login'
                        });
                    }
                </script>
            </body>
            </html>
        `;
    }
}
