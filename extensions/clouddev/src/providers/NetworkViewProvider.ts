import * as vscode from 'vscode';
import { getLocalizedText } from '../utils/l10nHelper';
import { Logger } from '../common/logger';
import { Disposable } from '../common/dispose';
import { Project } from '../api/project';
import { GlobalStateManager } from '../utils/globalStateManager';

interface Network {
	address: string
	port: number
	protocol: string
}

const messages = {
	port: getLocalizedText('Port'),
	protocol: getLocalizedText('Protocol'),
	address: getLocalizedText('Address'),
	openInBrowser: getLocalizedText('Open in Browser'),
	previewInEditor: getLocalizedText('Preview in Editor'),
	// port: '端口',
	// protocol: '协议',
	// address: '地址',
	// openInBrowser: '在浏览器中打开',
	// previewInEditor: '在编辑器中预览',
};

export class NetworkViewProvider
	extends Disposable
	implements vscode.WebviewViewProvider {
	private _view?: vscode.WebviewView;
	private _extensionUri: vscode.Uri;

	constructor(context: vscode.ExtensionContext) {
		super();
		Logger.info('Initializing NetworkViewProvider');
		this._extensionUri = context.extensionUri;
		context.subscriptions.push(
			vscode.window.registerWebviewViewProvider('clouddevNetworkView', this, {
				webviewOptions: {
					retainContextWhenHidden: true,
				},
			})
		);
		this._register(
			vscode.commands.registerCommand('clouddev.refreshNetwork', () => {
				this.refreshNetworks();
			})
		);
	}

	public async resolveWebviewView(
		webviewView: vscode.WebviewView,
		context: vscode.WebviewViewResolveContext,
		token: vscode.CancellationToken
	) {
		this._view = webviewView;

		webviewView.webview.options = {
			enableScripts: true,
		};

		webviewView.webview.onDidReceiveMessage(async (message) => {
			switch (message.command) {
				case 'refresh':
					await this.refreshNetworks();
					break;
				case 'openExternal':
					vscode.commands.executeCommand('clouddev.openExternalLink', message.url);
					break;
				case 'openIntegrated':
					vscode.commands.executeCommand('simpleBrowser.show', message.url);
					break;
			}
		});

		await this.refreshNetworks();
	}

	private async refreshNetworks() {
		if (!this._view) {
			Logger.info('View is not available, skipping network refresh');
			return;
		}

		let currentProject = new Project();
		const workspaceName = vscode.workspace.name;

		Logger.info(`当前workspace name: ${workspaceName}`);
		Logger.info(`当前项目: ${JSON.stringify(GlobalStateManager.getCurrentProject())}`);

		try {
			const match = workspaceName?.match(/workspace \[SSH: (.+)_(\d+)\]/);
			const storedProject = GlobalStateManager.getCurrentProject();
			if (match) {
				const [, projectName, id] = match;
				if (projectName === storedProject.name && +id === storedProject.id) {
					currentProject = storedProject;
				} else {
					currentProject.name = projectName;
					currentProject.id = +id;
					await currentProject.fetchDetail();
				}
			} else {
				currentProject = storedProject;
			}

			// const data = fs.readFileSync('/config/.clouddev/url', 'utf8');
			// const [port, url] = data.split(',');

			Logger.info(`network.address: ${currentProject.url} and port: ${currentProject.port}`);

			const parsedUrl = new URL(currentProject.url ?? '');

			const networkItems = [{
				address: `${parsedUrl.hostname}${parsedUrl.pathname}`,
				port: +currentProject.port,
				protocol: parsedUrl.protocol.replace(':', '').toUpperCase(),
			}] as Network[];

			Logger.debug(`Network items: ${JSON.stringify(networkItems)}`);
			this._view.webview.html = this.getWebviewContent(networkItems);
		} catch (e) {
			Logger.error("Failed to fetch project details", e);
		}
	}

	private getWebviewContent(networks: Network[]) {
		const networkIconUri = this._view?.webview.asWebviewUri(
			vscode.Uri.joinPath(this._extensionUri, 'images', 'network.svg')
		);
		const openExternalIconUri = this._view?.webview.asWebviewUri(
			vscode.Uri.joinPath(this._extensionUri, 'images', 'dark', 'link-external.svg')
		);

		return `
			<!DOCTYPE html>
			<html>
				<head>
					<style>
						body {
							padding: 0;
							margin: 0;
							height: 100vh;
							overflow: auto;
						}
						table {
							width: 100%;
							border-collapse: collapse;
						}
						th, td {
							padding: 0px 8px;
							text-align: left;
							border: none;
							color: var(--vscode-foreground) !important;
							font-size: 13px;
							font-family: var(--vscode-font-family);
						}
						th {
							font-size: 14px !important;
							position: sticky;
							top: 0;
							z-index: 1;
							font-weight: 600;
						}
						tr:nth-child(even) {
							background-color: color-mix(in srgb, var(--vscode-list-hoverBackground) 30%, transparent);
						}
						td {
							padding: 0px 8px;
							text-align: left;
							border: none;
							color: var(--vscode-editor-foreground);
						}
						td:nth-child(4) {
							color: var(--vscode-textLink-foreground) !important;
							text-decoration: none;
							cursor: pointer;
						}
						td:nth-child(4):hover {
							text-decoration: underline;
						}
						.icon-svg {
							width: 16px;
							height: 16px;
							vertical-align: middle;
							display: inline-block;
						}
						.action-btn {
							cursor: pointer;
							padding: 2px;
							border-radius: 3px;
							vertical-align: middle;
							transition: background 0.2s;
						}
						.action-btn:hover {
							background: var(--vscode-list-hoverBackground);
						}
						.actions {
							opacity: 0;
							transition: opacity 0.2s;
						}
						tr:hover .actions {
							opacity: 1;
						}
					</style>
				</head>
				<body>
					<table>
						<thead>
							<tr>
								<th style="width: 16px;"></th>
								<th>${messages.port}</th>
								<th>${messages.protocol}</th>
								<th>${messages.address}</th>
							</tr>
						</thead>
						<tbody>
							${networks
				.map(
					(network) => `
								<tr>
									<td style="width: 16px;">
										<img src="${networkIconUri}" class="icon-svg" alt="network"/>
									</td>
									<td>${network.port}</td>
									<td>${network.protocol}</td>
									<td>
										<a href="http://${network.address}" title="${messages.openInBrowser}">${network.address}</a>
										<span class="actions">
											<img src="${openExternalIconUri}" class="icon-svg action-btn" onclick="openExternal('http://${network.address}')" title="${messages.openInBrowser}" alt="open in browser"/>
										</span>
									</td>
								</tr>
							`
				)
				.join('')}
						</tbody>
					</table>
					<script>
						const vscode = acquireVsCodeApi();
						function openExternal(url) {
							vscode.postMessage({
								command: 'openExternal',
								url: url
							});
						}
						function openIntegrated(url) {
							vscode.postMessage({
								command: 'openIntegrated',
								url: url
							});
						}
					</script>
				</body>
			</html>
		`;
	}
}
