import * as vscode from 'vscode';
import * as path from 'path';
import * as fs from 'fs';
import { DevMode, Project, ProjectStatus } from '../api/project';
import { Logger } from '../common/logger';
import { Disposable } from '../common/dispose';
import { GlobalStateManager } from '../utils/globalStateManager';
import { getLocalizedText } from '../utils/l10nHelper';
import { deleteSSHConfig, parseSSHConfig } from '../api/ssh';
import { defaultClouddevSSHConfigPath, DELETE_PROJECT_COMMAND_ID, PROJECT_SETTINGS_COMMAND_ID } from '../constant';
import { DevboxListItem } from '../types/devbox';
import { ProjectDetailViewProvider } from './ProjectDetailViewProvider';
import { LoginUser } from '../api/user';

const messages = {
    deleteProjectFailed: getLocalizedText('Delete Project failed.'),
    external: getLocalizedText('Open in New Window'),
    open: getLocalizedText('Open in Current Window'),
    settings: getLocalizedText('Settings'),
    delete: getLocalizedText('Delete'),
    shutdownSuccess: getLocalizedText('Project shutdown successful, the SSH connection will be unavailable.'),
    startupSuccess: getLocalizedText('Project startup successful'),
    stop: getLocalizedText('Stop'),
    start: getLocalizedText('Start'),
    local: getLocalizedText('Local'),
    remote: getLocalizedText('Remote'),
    search: getLocalizedText('Search'),
};

export class ProjectListViewProvider
    extends Disposable
    implements vscode.WebviewViewProvider {

    private _view?: vscode.WebviewView;
    private _extensionUri: vscode.Uri;

    constructor(private readonly context: vscode.ExtensionContext) {
        super();
        Logger.info('Initializing ProjectListViewProvider');
        this._extensionUri = context.extensionUri;
    }

    public resolveWebviewView(
        webviewView: vscode.WebviewView,
        context: vscode.WebviewViewResolveContext,
        _token: vscode.CancellationToken,
    ) {
        this._view = webviewView;

        webviewView.webview.options = {
            enableScripts: true,
            localResourceRoots: [
                this._extensionUri,
                vscode.Uri.joinPath(this._extensionUri, 'resource')
            ]
        };

        Logger.info('ProjectListViewProvider resolveWebviewView');

        webviewView.webview.html = this._getHtmlForWebview(webviewView.webview);

        webviewView.webview.onDidReceiveMessage(async (message) => {
            Logger.info(`Received message from webview: ${message.command}`);
            switch (message.command) {
                case 'getProjects':
                    await this._handleGetProjects();
                    return;
                case 'openProject':
                    await this.handleOpenProject(message.data);
                    return;
                case 'delete':
                    await this.handleDeleteProject(message.data);
                    return;
                case 'settings':
                    await this._handleSettingProject(message.data);
                    return;
                case 'externalProject':
                    await this.handleOpenProject(message.data, true);
                    return;
                case 'selectProject':
                    await this.handleSelectProject(message.data);
                    return;
                case 'pollProjectStatus':
                    await this._handlePollProjectStatus(message.data);
                    return;
                case 'stop':
                    await this._handleStopProject(message.data);
                    return;
                case 'start':
                    await this._handleStartProject(message.data);
                    return;
            }
        });
    }

    private _getHtmlForWebview(webview: vscode.Webview): string {
        const htmlUri = vscode.Uri.joinPath(this._extensionUri, 'views', 'project_list.html');
        let html = fs.readFileSync(htmlUri.fsPath, 'utf8');

        // 生成 nonce
        const nonce = this._getNonce();

        // 使用 webview.asWebviewUri 转换资源 URI
        const styleUri = webview.asWebviewUri(vscode.Uri.joinPath(this._extensionUri, 'views', 'project_list.css'));
        const scriptUri = webview.asWebviewUri(vscode.Uri.joinPath(this._extensionUri, 'views', 'project_list.js'));
        const toolkitUri = webview.asWebviewUri(vscode.Uri.joinPath(this._extensionUri, 'views', 'toolkit.min.js'));
        const iconfontUri = webview.asWebviewUri(vscode.Uri.joinPath(this._extensionUri, 'resource', 'iconfont', 'iconfont.css'));

        // 更新 Content-Security-Policy
        const csp = `default-src 'none'; style-src ${webview.cspSource} 'unsafe-inline'; script-src 'nonce-${nonce}' ${webview.cspSource}; img-src ${webview.cspSource} data: https:; font-src ${webview.cspSource} data:;`;

        // 替换资源路径、nonce 和 CSP
        html = html.replace('${styleUri}', styleUri.toString());
        html = html.replace('${scriptUri}', scriptUri.toString());
        html = html.replace(/\$\{nonce\}/g, nonce);
        html = html.replace('${webview.cspSource}', csp);
        html = html.replace('${toolkitUri}', toolkitUri.toString());
        html = html.replace('${iconfontUri}', iconfontUri.toString());

        return html;
    }

    private _getActiveProjects() {
        const folders = vscode.workspace.workspaceFolders;
        return folders?.map(folder => {
            const project = new Project();
            if (folder.uri.scheme === 'vscode-remote') {
                const match = folder.uri.authority?.match(/ssh-remote\+(.+)_(\d+)/);
                const [, name, id] = match ?? [];
                project.id = +id;
                project.name = name;
                project.devMode = DevMode.Remote;
            } else {
                project.name = folder.name;
                project.localLocation = folder.uri.path;
                project.devMode = DevMode.Local;
            }
            return project;
        });
    }

    private _getNonce() {
        let text = '';
        const possible = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
        for (let i = 0; i < 32; i++) {
            text += possible.charAt(Math.floor(Math.random() * possible.length));
        }
        return text;
    }

    private async _handleGetProjects() {
        try {
            const projects = await Project.fetchProjectList();

            const list = (await parseSSHConfig(defaultClouddevSSHConfigPath)) as DevboxListItem[];
            list.forEach((item) => {
                if (!projects.find(p => item.host === `${p.name}_${p.id}`)) {
                    deleteSSHConfig(item.host);
                }
            });

            await Promise.all(
                projects.filter(project => project.devMode === DevMode.Local)
                    .map(async project => await project.fetchDetail(true))
            );

            const activeProjects = this._getActiveProjects();
            let highlightProject: Project | undefined = undefined;

            const isEqual = (a: Project, b: Project) => {
                if (a.devMode === DevMode.Local) {
                    return a.name === b.name && a.localLocation === b.localLocation;
                }
                return a.name === b.name && a.id === b.id;
            };

            if (activeProjects && activeProjects.length > 0) {
                projects.sort((a: Project, b: Project) => {
                    const idxA = activeProjects.findIndex((p) => isEqual(p, a));
                    const idxB = activeProjects.findIndex((p) => isEqual(p, b));
                    // 未找到的放后面
                    return (idxA === -1 ? Number.MAX_SAFE_INTEGER : idxA) - (idxB === -1 ? Number.MAX_SAFE_INTEGER : idxB);
                });
                // 取排序后列表中第一个出现在 activeProjects 的项目
                highlightProject = projects.find(p => activeProjects.find((a) => a.name === p.name));
            }

            if (highlightProject && ProjectDetailViewProvider.isInstanceExists()) {
                ProjectDetailViewProvider.getInstance(this._extensionUri).showProjectDetail(highlightProject);
            }

            this._view?.webview.postMessage({
                command: 'setProjects',
                projects,
                highlightProjectName: highlightProject?.name,
                messages
            });
        } catch (error) {
            Logger.error('Failed to get projects:', error);
            vscode.window.showErrorMessage('Failed to get project list');
        }
    }

    private async _handleSettingProject(project: Project) {
        const currentProject = new Project(project);
        await currentProject.fetchDetail(true);
        vscode.commands.executeCommand(PROJECT_SETTINGS_COMMAND_ID, currentProject);
    }

    public async handleOpenProject(project: Project, forceNewWindow = false) {
        const currentProject = new Project(project);
        await currentProject.fetchDetail(true);
        if (currentProject.devMode === DevMode.Remote
            && currentProject.status !== ProjectStatus.Running) {
            vscode.window.showErrorMessage(getLocalizedText(`Current project status: ${currentProject.status}`));
            this.refresh();
            return;
        }
        Logger.info(`Opening project: ${currentProject?.name}`);
        try {
            // 存储当前项目到 globalState
            GlobalStateManager.setCurrentProject(currentProject);
            if (currentProject.devMode === DevMode.Remote) {
                const data = await currentProject.fetchSSHConfig();

                vscode.commands.executeCommand('clouddev.connectRemoteSSH', {
                    sshHost: data.host,
                    sshUser: data.user,
                    sshPort: data.port,
                    base64PrivateKey: data.privateKey,
                    sshHostLabel: `${currentProject.name}_${currentProject.id}`,
                    workingDir: '/config/workspace',
                    forceNewWindow: forceNewWindow,
                });
            } else if (currentProject.localLocation) {
                vscode.commands.executeCommand(
                    'vscode.openFolder',
                    vscode.Uri.file(currentProject.localLocation),
                    forceNewWindow,
                );
            }
        } catch (error) {
            Logger.error('Failed to open project:', error);
            vscode.window.showErrorMessage('Failed to open project');
        }
    }

    public async handleDeleteProject(project: Project) {
        const currentProject = new Project(project);
        if (!project.localLocation) {
            await currentProject.fetchDetail(true);
        }
        // 弹框取消 deletedData = false
        // 弹框确认 deletedData = { isRemote: boolean, checked: boolean}
        const deletedData: {
            isRemote: boolean, checked: boolean
        } | false = await vscode.commands.executeCommand(DELETE_PROJECT_COMMAND_ID, currentProject);
        if (!deletedData) { return; }
        try {
            const deletedHostLabel = `${currentProject.name}_${currentProject.id}`;
            Logger.info(`Deleting project: ${deletedHostLabel}`);
            await currentProject.delete();
            this.refresh();
            const list = (await parseSSHConfig(defaultClouddevSSHConfigPath)) as DevboxListItem[];
            if (!list.find((item) => item.host === deletedHostLabel)) {
                Logger.debug(`Not found matching SSH config for ${deletedHostLabel}`);
            } else {
                await deleteSSHConfig(deletedHostLabel);
            }
        } catch (error) {
            Logger.error('Failed to delete project:', error);
            vscode.window.showErrorMessage(
                `${messages.deleteProjectFailed}: ${(error as Error).message}`
            );
        }

        const { isRemote, checked } = deletedData;

        const needDeleteFolderIndex = this._projectFolderIndex(currentProject);
        if (needDeleteFolderIndex > -1) {
            if (isRemote) {
                await vscode.commands.executeCommand('workbench.action.remote.close');
            }
            // 删除本地目录
            if (checked && currentProject.localLocation) {
                try {
                    await vscode.workspace.fs.delete(
                        vscode.Uri.file(currentProject.localLocation),
                        { recursive: true, useTrash: false }
                    );
                    await vscode.workspace.updateWorkspaceFolders(needDeleteFolderIndex, 1);
                    Logger.info(`Local directory ${currentProject.localLocation} deleted.`);
                    // 如果工作区已空，自动关闭/删除当前工作区
                    if ((vscode.workspace.workspaceFolders?.length ?? 0) === 0) {
                        await vscode.commands.executeCommand('workbench.action.closeFolder');
                    }
                } catch (err) {
                    Logger.error('Failed to delete local directory:', err);
                }
            }
            vscode.commands.executeCommand('editorGroupWatermark.forceRender');
            // vscode.commands.executeCommand('clouddevDashboard');
        }
    }

    public async handleSelectProject(project: Project) {
        try {
            Logger.info(`Selecting project: ${project.name}`);
            await ProjectDetailViewProvider.getInstance(this._extensionUri).showProjectDetail(project);
        } catch (error) {
            Logger.error('Failed to select project:', error);
            vscode.window.showErrorMessage('Failed to select project');
        }
    }

    public refresh() {
        if (this._view?.visible) {
            this._handleGetProjects();
        }
    }

    private async _handlePollProjectStatus(project: Project) {
        const currentProject = new Project(project);
        try {
            await currentProject.fetchDetail(true);
            // 通知前端更新项目状态
            this._view?.webview.postMessage({
                command: 'updateProject',
                project: currentProject
            });
        } catch (error) {
            Logger.error('Failed to poll project status:', error);
        }
    }

    private async _handleStopProject(project: Project) {
        const currentProject = new Project(project);
        try {
            await currentProject.fetchDetail(true);
            if (currentProject.devMode === DevMode.Remote
                && currentProject.status !== ProjectStatus.Running) {
                vscode.window.showErrorMessage(getLocalizedText(`Current project status: ${currentProject.status}`));
                this.refresh();
                return;
            }
            await currentProject.stop();
            vscode.window.showInformationMessage(messages.shutdownSuccess.replace(/(Project|项目)/, `「${project.name}」`));
            // if (this._projectFolderIndex(currentProject) > -1) {
            //     await vscode.commands.executeCommand('workbench.action.remote.close');
            // }
            // 刷新项目列表
            this.refresh();
        } catch (error) {
            Logger.error('Failed to stop project:', error);
            vscode.window.showErrorMessage('Failed to stop project');
        }
    }

    private async _handleStartProject(project: Project) {
        if (await LoginUser.currentAccountIsArrear()) {
            ProjectDetailViewProvider.arrearModal();
            return;
        }
        const currentProject = new Project(project);
        try {
            await currentProject.fetchDetail(true);
            if (currentProject.status !== ProjectStatus.Shutdown) {
                vscode.window.showErrorMessage(getLocalizedText(`Current project status: ${currentProject.status}`));
                this.refresh();
                return;
            }
            await currentProject.start();
            vscode.window.showInformationMessage(messages.startupSuccess.replace(/(Project|项目)/, `「${project.name}」`));
            Logger.info(`Start project: ${currentProject.name}`);

            // 项目启动时更新当前项目的ssh配置
            const data = await currentProject.fetchSSHConfig();

            vscode.commands.executeCommand('clouddev.updateRemoteSSH', {
                sshHost: data.host,
                sshUser: data.user,
                sshPort: data.port,
                base64PrivateKey: data.privateKey,
                sshHostLabel: `${currentProject.name}_${currentProject.id}`,
                workingDir: '/config/workspace',
            });
            // 刷新项目列表
            this.refresh();
        } catch (error) {
            Logger.error('Failed to start project:', error);
            vscode.window.showErrorMessage('Failed to start project');
        }
    }

    private _projectFolderIndex(currentProject: Project) {
        // 通过 _getActiveProjects 判断被删除项目是否是当前打开的项目
        const activeProjects = this._getActiveProjects() || [];
        return activeProjects.findIndex(project => {
            if (project.name === project.name) {
                return project.id === currentProject.id
                    || project.localLocation === currentProject.localLocation;
            }
            return false;
        });
    }

    public setVisibility(visible: boolean) {
        Logger.info(`Setting ProjectListView visibility to: ${visible}`);
        if (visible && this._view) {
            this._view.show(true);
        }
    }
}
