import * as vscode from 'vscode';

import { Logger } from '../common/logger';
import { Disposable } from '../common/dispose';
import { Project } from '../api/project';
import { LoginUser } from '../api/user';
import { WelcomeViewProvider } from './welcomeViewProvider';
import { ProjectListViewProvider } from './ProjectListViewProvider';
import { NEW_PROJECT_COMMAND_ID } from '../constant';

export class DashboardViewProvider extends Disposable {
    constructor(context: vscode.ExtensionContext) {
        super();
        console.log(vscode.env.language);
        Logger.info('Initializing ClouddevDashboard');
        Logger.debug(`Extension path: ${context.extensionPath}`);

        // view
        const projectTreeDataProvider = new ProjectListViewProvider(context);
        const welcomeViewProvider = new WelcomeViewProvider(context);

        const devboxListView = vscode.window.registerWebviewViewProvider(
            'clouddevListView',
            projectTreeDataProvider
        );
        const welcomeView = vscode.window.registerWebviewViewProvider(
            'clouddevWelcome',
            welcomeViewProvider
        );
        this._register(devboxListView);
        this._register(welcomeView);

        // commands
        this._register(
            vscode.commands.registerCommand('clouddev.refresh', async () => {
                Logger.info('Refreshing project list');
                await vscode.commands.executeCommand('setContext', 'clouddevLoggedIn', true);
                projectTreeDataProvider.refresh();
            })
        );

        this._register(
            vscode.commands.registerCommand(
                'clouddev.createProject',
                async () => {
                    if (!LoginUser.isLogin()) {
                        vscode.commands.executeCommand('clouddev.welcome');
                        return;
                    }
                    Logger.info('Creating new project');
                    vscode.commands.executeCommand(NEW_PROJECT_COMMAND_ID);
                    // const newProject = new Project();
                    // Logger.debug(`New project initialized with ID: ${newProject.id}`);
                }
            )
        );

        this._register(
            vscode.commands.registerCommand('clouddev.openProject', async (
                project?: Project,
                forceNewWindow?: boolean,
            ) => {
                if (!project) { return; }
                await projectTreeDataProvider.handleOpenProject(project, forceNewWindow);
            })
        );

        this._register(
            vscode.commands.registerCommand('clouddev.selectProject', async (project?: Project) => {
                if (!project) { return; }
                await projectTreeDataProvider.handleSelectProject(project);
            })
        );

        this._register(
            vscode.commands.registerCommand('clouddev.deleteProject', async (project?: Project) => {
                if (!project) { return; }
                await projectTreeDataProvider.handleDeleteProject(project);
            })
        );
    }
}
