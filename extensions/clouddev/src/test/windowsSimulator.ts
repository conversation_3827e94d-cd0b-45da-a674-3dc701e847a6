import * as vscode from 'vscode';
import * as os from 'os';
import { Logger } from '../common/logger';

export class WindowsSimulator {
    private originalPlatform: string;
    private originalArch: string;

    constructor() {
        this.originalPlatform = os.platform();
        this.originalArch = os.arch();
    }

    public simulate() {
        Object.defineProperty(os, 'platform', { value: () => 'win32' });
        Object.defineProperty(os, 'arch', { value: () => 'x64' });
        Object.defineProperty(process, 'platform', { value: 'win32' });
        Object.defineProperty(process, 'arch', { value: 'x64' });

        Logger.info('Simulating Windows environment');
        Logger.info(`Simulated OS: ${os.platform()} ${os.release()} ${os.arch()}`);
    }

    public restore() {
        Object.defineProperty(os, 'platform', { value: () => this.originalPlatform });
        Object.defineProperty(os, 'arch', { value: () => this.originalArch });
        Object.defineProperty(process, 'platform', { value: this.originalPlatform });
        Object.defineProperty(process, 'arch', { value: this.originalArch });

        Logger.info('Restored original environment');
        Logger.info(`Restored OS: ${os.platform()} ${os.release()} ${os.arch()}`);
    }
}

export function runInWindowsSimulation(testFunction: () => Promise<void>) {
    return async () => {
        const simulator = new WindowsSimulator();
        simulator.simulate();
        try {
            await testFunction();
        } finally {
            simulator.restore();
        }
    };
}

// 使用示例
// runInWindowsSimulation(async () => {
//     // 在这里运行你的测试代码
//     await vscode.commands.executeCommand('clouddev.someCommand');
//     // 进行断言或其他测试逻辑
// })();
