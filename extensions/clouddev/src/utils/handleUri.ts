import * as vscode from 'vscode';
import { GlobalStateManager } from './globalStateManager';
import { Logger } from '../common/logger';
import { Project } from '../api/project';

export class UriHandler {
    constructor() { }

    public async handle(uri: vscode.Uri): Promise<void> {
        Logger.info(`Handling URI: ${uri.toString()}`);

        const queryParams = new URLSearchParams(uri.query);

        const userInfo = queryParams.get('info');
        if (userInfo) {
            GlobalStateManager.setUserInfo(JSON.parse(userInfo));
            return;
        }

        const projectId = queryParams.get('projectId');
        Logger.info(`Project ID from URI: ${projectId}`);
        if (projectId) {
            try {
                const project = new Project();
                project.id = parseInt(projectId, 10);
                await vscode.commands.executeCommand('clouddev.openProject', project, true);
            } catch (error) {
                Logger.error(`Failed to open project ${projectId}: ${error}`);
                vscode.window.showErrorMessage(`无法打开项目：${error}`);
            }
            return;
        }
    }
}
