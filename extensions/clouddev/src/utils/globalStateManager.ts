import * as vscode from 'vscode';
import { UserInfo } from '../constant';
import { Project } from '../api/project';

interface DevboxGlobalState {
    workDir: string
}
interface JoyCoderUserState {
    userName: string,
    ptKey: string,
}

export class GlobalStateManager {
    private static context: vscode.ExtensionContext;

    static init(context: vscode.ExtensionContext) {
        GlobalStateManager.context = context;
    }

    static getUserInfo() {
        const state = (GlobalStateManager.context.globalState.get(
            UserInfo
        ));
        return state;
    }

    static setUserInfo(userInfo: any) {
        GlobalStateManager.context.globalState.update(UserInfo, userInfo);
    }

    static getPtKey() {
        const state = (GlobalStateManager.context.globalState.get(
            'joyCoderUser'
        ) as JoyCoderUserState) || {
            ptKey: '',
        };
        return state.ptKey;
    }

    static setPtKey(ptKey: string) {
        const state = (GlobalStateManager.context.globalState.get(
            'joyCoderUser'
        ) as JoyCoderUserState) || {
            ptKey: '',
        };
        GlobalStateManager.context.globalState.update('joyCoderUser', {
            ...state,
            ptKey,
        });
    }

    static getWorkDir(devboxId: string): string | undefined {
        const state = (GlobalStateManager.context.globalState.get(
            devboxId
        ) as DevboxGlobalState) || {
            workDir: '',
        };
        return state.workDir;
    }

    static setWorkDir(devboxId: string, workDir: string) {
        const state =
            (GlobalStateManager.context.globalState.get(
                devboxId
            ) as DevboxGlobalState) || {};
        const newState = {
            ...state,
            workDir,
        };
        GlobalStateManager.context.globalState.update(devboxId, newState);
    }

    static setCurrentProject(project: any) {
        GlobalStateManager.context.globalState.update('currentProject', project);
    }

    static getCurrentProject() {
        return GlobalStateManager.context.globalState.get('currentProject') as Project;
    }
}
