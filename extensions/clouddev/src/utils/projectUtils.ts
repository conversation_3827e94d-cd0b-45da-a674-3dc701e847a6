/**
 * 项目相关的工具方法
 */

/**
 * 根据项目名生成一个固定的颜色
 */
export function pickProjectColor(projectName: string): string {
    const colors = ['#6c63ff', '#8e44ad', '#e1b34c', '#4c9fe1', '#e67e22', '#2ecc71'];
    let hash = 0;
    for (let i = 0; i < projectName.length; i++) {
        hash = projectName.charCodeAt(i) + ((hash << 5) - hash);
    }
    return colors[Math.abs(hash) % colors.length];
}

/**
 * 从项目名称中提取首字母
 */
function extractInitials(projectName: string): string[] {
    // 处理驼峰格式
    const camelCaseWords = projectName.split(/(?=[A-Z])/);
    if (camelCaseWords.length > 1) {
        return camelCaseWords.map(word => word[0]);
    }

    // 处理下划线格式
    const underscoreWords = projectName.split(/[_-]/);
    if (underscoreWords.length > 1) {
        return underscoreWords.map(word => word[0]);
    }

    // 处理其他格式（空格或其他分隔符）
    return projectName.split(/\W+/).map(word => word[0]);
}

/**
 * 生成项目名称的两字母缩写
 */
export function getProjectAbbr(projectName: string): string {
    const initials = extractInitials(projectName)
        .filter(char => char) // 过滤空字符
        .map(char => char.toUpperCase()); // 转换为大写

    // 如果没有足够的首字母，使用第一个单词的前两个字母
    if (initials.length === 0) {
        return projectName.slice(0, 2).toUpperCase();
    } else if (initials.length === 1) {
        const firstWord = projectName.split(/[\W_]/)[0];
        return (initials[0] + (firstWord[1] || '')).toUpperCase();
    }

    // 返回前两个首字母
    return initials.slice(0, 2).join('');
}
