import * as vscode from 'vscode';
import * as fs from 'fs';
import { Logger } from '../common/logger';

let isInitialized = false;
let extensionContext: vscode.ExtensionContext | null = null;

export function initializeL10n(context?: vscode.ExtensionContext) {
    isInitialized = true;
    if (context) {
        extensionContext = context;
        Logger.info(`[L10N] Extension context set, extensionPath: ${context.extensionPath}`);
    }
}

/**
 * 获取当前 VSCode 语言环境
 */
function getCurrentLanguage(): string {
    const language = vscode.env.language;
    return language;
}

/**
 * 读取 ./l10n 目录下本地 NLS 资源
 */
function getL10nResource(lang: string): Record<string, string> | null {
    // 使用扩展上下文获取正确的路径
    let possibleL10nDirs: vscode.Uri[] = [];

    if (extensionContext) {
        // 使用扩展上下文的路径（最可靠）
        const contextL10nUri = vscode.Uri.joinPath(extensionContext.extensionUri, 'l10n');
        possibleL10nDirs.push(contextL10nUri);
    } else {
        // 回退到基于 __dirname 的路径
        const extensionUri = vscode.Uri.file(__dirname);
        possibleL10nDirs = [
            vscode.Uri.joinPath(extensionUri, '../l10n'),           // 编译后的相对路径
            vscode.Uri.joinPath(extensionUri, '../../l10n'),        // 源码相对路径
            vscode.Uri.joinPath(extensionUri, '../../../l10n'),     // 其他可能的路径
        ];
    }

    // 读取 bundle.l10n.{lang}.json
    const candidates: string[] = [];
    for (const l10nUri of possibleL10nDirs) {
        candidates.push(
            vscode.Uri.joinPath(l10nUri, `bundle.l10n.zh-CN.json`).fsPath,
            vscode.Uri.joinPath(l10nUri, `bundle.l10n.zh-cn.json`).fsPath
        );
    }

    for (const file of candidates) {
        if (fs.existsSync(file)) {
            try {
                const content = JSON.parse(fs.readFileSync(file, 'utf-8'));
                return content;
            } catch (error) {
                if (isInitialized) {
                    Logger.error(`[L10N] Error parsing l10n file ${file}:`, error);
                }
            }
        }
    }
    return null;
}

/**
 * 简单判断文本是否为目标语言
 */
function isTextInLanguage(text: string, lang: string): boolean {
    if (lang.startsWith('zh')) {
        return /[\u4e00-\u9fa5]/.test(text);
    } else if (lang.startsWith('en')) {
        return /^[\x00-\x7F\s.,?!'"""]+$/.test(text);
    }
    return true;
}

/**
 * 获取国际化文本，自动修正语言不符的情况
 */
export function getLocalizedText(key: string, ...args: any[]): string {
    const l10nText = vscode.l10n.t(key, ...args);
    const lang = getCurrentLanguage();

    const isCorrectLanguage = isTextInLanguage(l10nText, lang);

    if (isCorrectLanguage) {
        return l10nText;
    }

    const l10nResource = getL10nResource(lang);
    if (l10nResource && l10nResource[key]) {
        let text = l10nResource[key];
        args.forEach((arg, idx) => {
            text = text.replace(new RegExp(`\\{${idx}\\}`, 'g'), arg);
        });
        return text;
    }
    return l10nText;
}
