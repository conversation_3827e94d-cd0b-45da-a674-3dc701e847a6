{"name": "clouddev", "displayName": "%displayName%", "description": "%description%", "version": "1.0.1-8", "publisher": "JDCOM", "icon": "images/icon.png", "engines": {"vscode": "^1.98.2"}, "keywords": ["devbox", "remote development", "remote"], "categories": ["Other"], "extensionKind": ["ui", "workspace"], "repository": {"type": "git", "url": "https://coding.jd.com/cloudide/vscode-clouddev.git"}, "activationEvents": ["onStartupFinished"], "extensionDependencies": ["joycode.joycode-common"], "main": "./out/extension.js", "l10n": "./l10n", "capabilities": {"untrustedWorkspaces": {"supported": true}}, "contributes": {"commands": [{"command": "clouddev.connectRemoteSSH", "title": "%clouddev.connectRemoteSSH.title%"}, {"command": "clouddev.updateRemoteSSH", "title": "%clouddev.updateRemoteSSH.title%"}, {"command": "clouddev.welcome", "title": "%clouddev.welcomePage.title%"}, {"command": "clouddev.showLog", "title": "%clouddev.showLog.title%"}, {"command": "clouddev.refresh", "title": "%clouddev.refresh.title%", "icon": {"light": "images/light/refresh.svg", "dark": "images/dark/refresh.svg"}}, {"command": "clouddev.selectProject", "title": "%clouddev.select.title%", "icon": {"light": "images/light/open.svg", "dark": "images/dark/open.svg"}}, {"command": "clouddev.openProject", "title": "%clouddev.open.title%", "icon": {"light": "images/light/open.svg", "dark": "images/dark/open.svg"}}, {"command": "clouddev.createProject", "title": "%clouddev.create.title%", "icon": {"light": "images/light/create.svg", "dark": "images/dark/create.svg"}}, {"command": "clouddev.deleteProject", "title": "%clouddev.delete.title%", "icon": {"light": "images/light/delete.svg", "dark": "images/dark/delete.svg"}}, {"command": "clouddev.openExternalLink", "title": "%clouddev.openInBrowser.title%"}, {"command": "clouddev.refreshNetwork", "title": "%clouddev.refreshNetwork.title%", "icon": {"light": "images/light/refresh.svg", "dark": "images/dark/refresh.svg"}}], "views": {}, "menus": {"view/title": [{"command": "clouddev.createProject", "when": "view == clouddevListView", "group": "navigation"}, {"command": "clouddev.refresh", "when": "view == clouddevListView", "group": "navigation"}, {"command": "clouddev.refreshNetwork", "when": "view == clouddevNetworkView", "group": "navigation"}], "view/item/context": [{"command": "clouddev.openProject", "when": "view == clouddevListView && viewItem == clouddev", "group": "inline@1"}, {"command": "clouddev.deleteProject", "when": "view == clouddevListView && viewItem == clouddev", "group": "inline@2"}]}}, "scripts": {"compile": "gulp compile-extension:clouddev", "watch": "gulp watch-extension:clouddev"}, "devDependencies": {"@types/mocha": "^10.0.10", "@types/node": "20.x", "@types/node-fetch": "^2.6.12", "@types/vscode": "^1.94.0", "@typescript-eslint/eslint-plugin": "^8.22.0", "@typescript-eslint/parser": "^8.22.0", "@vscode/l10n": "^0.0.18", "@vscode/test-cli": "^0.0.10", "@vscode/test-electron": "^2.4.1", "cross-env": "^7.0.3", "esbuild": "^0.24.2", "eslint": "^9.19.0", "npm-run-all": "^4.1.5", "rimraf": "^5.0.0", "typescript": "^5.7.3"}, "dependencies": {"dayjs": "^1.11.13", "execa": "^9.5.2", "node-fetch": "^2.7.0", "ssh-config": "^5.0.3"}}