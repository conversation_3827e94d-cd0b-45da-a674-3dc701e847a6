{"displayName": "Projects", "description": "To make the remote development experience infinitely close to local development, with all cloud operations seamlessly integrated into the existing workflow.", "clouddev.connectRemoteSSH.title": "Projects: Connect to Remote SSH", "clouddev.updateRemoteSSH.title": "Projects: Update SSH Config", "clouddev.refresh.title": "Projects: Refresh", "clouddev.showLog.title": "Projects: Show Logs", "clouddev.create.title": "Projects: Create new Project", "clouddev.open.title": "Projects: Open Project", "clouddev.select.title": "Projects: Select Project", "clouddev.delete.title": "Projects: Delete Project", "clouddev.openInBrowser.title": "Projects: Open in Browser", "clouddev.copy.title": "Projects: Copy", "clouddev.refreshDatabase.title": "Projects: Refresh Database", "clouddev.refreshNetwork.title": "Projects: Refresh Network", "clouddev.gotoDatabaseWebPage.title": "Projects: Open Database Web Page", "clouddev.myProjects.title": "My Projects", "clouddev.projectInfo.title": "Project Information", "clouddev.network.title": "Network", "clouddev.database.title": "Database", "clouddev.clouddev.title": "Projects", "clouddev.welcome.title": "No Project yet, please create a new Project.\n [Create Project](command:clouddev.createProject)", "clouddev.welcomePage.title": "Projects Welcome Page"}