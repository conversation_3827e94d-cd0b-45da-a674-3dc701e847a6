#  Cloud-Dev

该插件旨在引导客户进行远程开发，要求对用户来讲简化流程、节省时间、降低门槛，主要提供以下功能：

点击插件 → 选择语言快速创建项目→ 开始编码 → 运行 → 获得可访问的公网链接

- 新增/删除项目
- SSH


帮助文档：https://joyspace.jd.com/pages/qiuoOrQdjoUsaUHtE62R

用户群（京ME）：10207661005

注意事项

插件数据来自行云平台（xingyun.jd.com），请确保网络链接能访问行云相关服务,如coding ,行云部署,需求任务等

## 开发者指南

### Windows 环境模拟测试

为了确保插件在 Windows 平台上正常运行,我们提供了一个 Windows 环境模拟器。这个模拟器可以帮助开发者在非 Windows 系统上测试插件的 Windows 兼容性。

使用方法:

1. 在 `extensions/clouddev/src/test/windowsSimulator.ts` 文件中,你可以找到 `WindowsSimulator` 类和 `runInWindowsSimulation` 函数。

2. 要使用模拟器进行测试,可以按照以下方式编写测试代码:

```typescript
import { runInWindowsSimulation } from './test/windowsSimulator';

runInWindowsSimulation(async () => {
    // 在这里运行你的测试代码
    await vscode.commands.executeCommand('clouddev.someCommand');
    // 进行断言或其他测试逻辑
})();
```

3. 这个模拟器会临时修改 `os.platform()` 和 `os.arch()` 的返回值,使其模拟 Windows 环境。

4. 在测试完成后,模拟器会自动恢复原始环境设置。

注意: 这个模拟器主要用于测试与操作系统相关的代码路径。它不能完全模拟 Windows 的文件系统或其他底层系统特性。对于需要真实 Windows 环境的测试,仍然建议在实际的 Windows 机器上进行。

### 调试提示

- 使用 VSCode 的调试功能来逐步执行代码,特别关注文件路径处理和系统特定操作的部分。
- 查看输出面板中的日志信息,我们已经在关键位置添加了详细的日志记录。
- 如果遇到 Windows 特定的问题,请确保在实际的 Windows 环境中进行最终验证。

通过使用这个模拟器和遵循这些调试提示,我们可以更容易地识别和解决潜在的 Windows 兼容性问题。

### 初始化过程

为了解决潜在的初始化顺序问题,我们对插件的初始化过程进行了优化。主要更改如下:

1. 在 `l10nHelper.ts` 中,我们添加了一个 `isInitialized` 标志和 `initializeL10n` 函数。这确保了本地化功能只在正确初始化后才会使用 Logger。

2. 在 `extension.ts` 中,我们在 Logger 初始化之后立即调用 `initializeL10n` 函数。初始化顺序如下:
   - Logger 初始化
   - l10n 初始化
   - GlobalStateManager 初始化
   - 其他组件初始化

3. 所有使用 Logger 的模块现在都会检查 `isInitialized` 标志,以确保只在正确初始化后才使用日志功能。

这些更改应该解决了之前遇到的初始化顺序问题,特别是在 Windows 平台上的问题。

### 贡献指南

如果你想为这个项目做出贡献,请遵循以下步骤:

1. Fork 这个仓库
2. 创建你的特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交你的更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 开启一个 Pull Request

在提交 Pull Request 之前,请确保你的代码符合我们的编码规范,并且所有测试都已通过。
