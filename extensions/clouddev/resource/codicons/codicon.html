<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>codicon | The icon font for Visual Studio Code </title>

    <style type="text/css">

        html {
            box-sizing: border-box;
            font-size: 16px;
        }

        *, *:before, *:after {
            box-sizing: inherit;
        }

        body, h1, h2, h3, h4, h5, h6, p, ol, ul {
            margin: 0;
            padding: 0;
            font-weight: normal;
        }

        ol, ul {
            list-style: none;
        }

        img {
            max-width: 100%;
            height: auto;
        }

        body {
            font-family: -apple-system,BlinkMacSystemFont,Segoe UI,Helvetica,Arial,sans-serif,Apple Color Emoji,Segoe UI Emoji;
            margin: 0;
            padding: 10px 20px;
            text-align: center;
            background-color: #f8f8f8;
        }

        h1 {
            font-weight: bold;
            margin: 24px;
        }

        .icon {
            width: 100px;
            display: inline-block;
            margin: 8px;
        }

        .icon:hover {
            cursor: pointer;
        }

        .icon:hover .inner {
            box-shadow: 0px 0px 12px rgba(0, 0, 0, 0.24);
        }

        .icon .inner {
            display: inline-block;
            width: 100%;
            text-align: center;
            background-color: white;
            box-shadow: 0px 0px 12px rgba(0, 0, 0, 0.06);
            border-radius: 4px;
            transition: all .3s ease-in-out;
        }

        .icon .inner i {
            padding: 16px 0;
            font-size: 48px;
            color: #333;
            overflow: hidden;
        }

        .icon .inner::before {
            overflow: hidden;
        }

        .label {
            margin-top: 8px;
            display: inline-block;
            width: 100%;
            box-sizing: border-box;
            padding: 4px;
            font-size: 10px;
            color: #666;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .description {
            display: none;
        }

        #search {
            display: flex;
            width: 100%;
            font-size: 16px;
            padding: 12px 16px;
            margin: 0 auto;
            max-width: 900px;
            margin-bottom: 24px;
            border: 1px solid rgba(0,0,0,.1);
            box-shadow: 0px 0px 12px rgba(0, 0, 0, 0.06);
        }

        #search:focus {
            outline: none !important;
            border-color: #18a0fb;
        }

        #copier {
            position: absolute;
            top: -9999px;
            left: -9999px;
        }

        #notification {
            position: fixed;
            margin: auto;
            bottom: 40px;
            left: 50%;
            width: auto;
            transform: translateX(-50%);
            color: white;
            background-color: #212121;
            padding: 8px 24px;
            border-radius: 8px;
            opacity: 0;
            transition: opacity .3s ease-in-out;
            box-shadow: 0px 0px 12px rgba(0, 0, 0, 0.2);
        }

        #notification-id {
            font-weight: bold;
        }

        #notification.show{
            opacity: .9;
        }

    </style>

    <link rel="stylesheet" type="text/css" href="codicon.css" />
</head>
<body>

    <h1>codicon</h1>

    <input type="text" id="search" placeholder="Search for icon names">

    <div id="icons">
        <div class="icon" data-name="account" title="account">
            <span class="inner">
                <i class="codicon codicon-account" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>account</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="activate-breakpoints" title="activate-breakpoints">
            <span class="inner">
                <i class="codicon codicon-activate-breakpoints" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>activate-breakpoints</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="add" title="add">
            <span class="inner">
                <i class="codicon codicon-add" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>add</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="archive" title="archive">
            <span class="inner">
                <i class="codicon codicon-archive" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>archive</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="arrow-both" title="arrow-both">
            <span class="inner">
                <i class="codicon codicon-arrow-both" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>arrow-both</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="arrow-circle-down" title="arrow-circle-down">
            <span class="inner">
                <i class="codicon codicon-arrow-circle-down" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>arrow-circle-down</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="arrow-circle-left" title="arrow-circle-left">
            <span class="inner">
                <i class="codicon codicon-arrow-circle-left" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>arrow-circle-left</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="arrow-circle-right" title="arrow-circle-right">
            <span class="inner">
                <i class="codicon codicon-arrow-circle-right" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>arrow-circle-right</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="arrow-circle-up" title="arrow-circle-up">
            <span class="inner">
                <i class="codicon codicon-arrow-circle-up" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>arrow-circle-up</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="arrow-down" title="arrow-down">
            <span class="inner">
                <i class="codicon codicon-arrow-down" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>arrow-down</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="arrow-left" title="arrow-left">
            <span class="inner">
                <i class="codicon codicon-arrow-left" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>arrow-left</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="arrow-right" title="arrow-right">
            <span class="inner">
                <i class="codicon codicon-arrow-right" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>arrow-right</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="arrow-small-down" title="arrow-small-down">
            <span class="inner">
                <i class="codicon codicon-arrow-small-down" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>arrow-small-down</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="arrow-small-left" title="arrow-small-left">
            <span class="inner">
                <i class="codicon codicon-arrow-small-left" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>arrow-small-left</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="arrow-small-right" title="arrow-small-right">
            <span class="inner">
                <i class="codicon codicon-arrow-small-right" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>arrow-small-right</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="arrow-small-up" title="arrow-small-up">
            <span class="inner">
                <i class="codicon codicon-arrow-small-up" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>arrow-small-up</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="arrow-swap" title="arrow-swap">
            <span class="inner">
                <i class="codicon codicon-arrow-swap" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>arrow-swap</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="arrow-up" title="arrow-up">
            <span class="inner">
                <i class="codicon codicon-arrow-up" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>arrow-up</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="azure-devops" title="azure-devops">
            <span class="inner">
                <i class="codicon codicon-azure-devops" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>azure-devops</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="azure" title="azure">
            <span class="inner">
                <i class="codicon codicon-azure" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>azure</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="beaker-stop" title="beaker-stop">
            <span class="inner">
                <i class="codicon codicon-beaker-stop" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>beaker-stop</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="beaker" title="beaker">
            <span class="inner">
                <i class="codicon codicon-beaker" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>beaker</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="bell-dot" title="bell-dot">
            <span class="inner">
                <i class="codicon codicon-bell-dot" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>bell-dot</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="bell-slash-dot" title="bell-slash-dot">
            <span class="inner">
                <i class="codicon codicon-bell-slash-dot" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>bell-slash-dot</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="bell-slash" title="bell-slash">
            <span class="inner">
                <i class="codicon codicon-bell-slash" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>bell-slash</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="bell" title="bell">
            <span class="inner">
                <i class="codicon codicon-bell" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>bell</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="blank" title="blank">
            <span class="inner">
                <i class="codicon codicon-blank" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>blank</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="bold" title="bold">
            <span class="inner">
                <i class="codicon codicon-bold" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>bold</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="book" title="book">
            <span class="inner">
                <i class="codicon codicon-book" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>book</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="bookmark" title="bookmark">
            <span class="inner">
                <i class="codicon codicon-bookmark" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>bookmark</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="bracket-dot" title="bracket-dot">
            <span class="inner">
                <i class="codicon codicon-bracket-dot" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>bracket-dot</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="bracket-error" title="bracket-error">
            <span class="inner">
                <i class="codicon codicon-bracket-error" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>bracket-error</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="briefcase" title="briefcase">
            <span class="inner">
                <i class="codicon codicon-briefcase" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>briefcase</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="broadcast" title="broadcast">
            <span class="inner">
                <i class="codicon codicon-broadcast" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>broadcast</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="browser" title="browser">
            <span class="inner">
                <i class="codicon codicon-browser" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>browser</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="bug" title="bug">
            <span class="inner">
                <i class="codicon codicon-bug" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>bug</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="calendar" title="calendar">
            <span class="inner">
                <i class="codicon codicon-calendar" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>calendar</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="call-incoming" title="call-incoming">
            <span class="inner">
                <i class="codicon codicon-call-incoming" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>call-incoming</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="call-outgoing" title="call-outgoing">
            <span class="inner">
                <i class="codicon codicon-call-outgoing" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>call-outgoing</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="case-sensitive" title="case-sensitive">
            <span class="inner">
                <i class="codicon codicon-case-sensitive" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>case-sensitive</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="check-all" title="check-all">
            <span class="inner">
                <i class="codicon codicon-check-all" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>check-all</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="check" title="check">
            <span class="inner">
                <i class="codicon codicon-check" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>check</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="checklist" title="checklist">
            <span class="inner">
                <i class="codicon codicon-checklist" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>checklist</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="chevron-down" title="chevron-down">
            <span class="inner">
                <i class="codicon codicon-chevron-down" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>chevron-down</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="chevron-left" title="chevron-left">
            <span class="inner">
                <i class="codicon codicon-chevron-left" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>chevron-left</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="chevron-right" title="chevron-right">
            <span class="inner">
                <i class="codicon codicon-chevron-right" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>chevron-right</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="chevron-up" title="chevron-up">
            <span class="inner">
                <i class="codicon codicon-chevron-up" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>chevron-up</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="chip" title="chip">
            <span class="inner">
                <i class="codicon codicon-chip" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>chip</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="chrome-close" title="chrome-close">
            <span class="inner">
                <i class="codicon codicon-chrome-close" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>chrome-close</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="chrome-maximize" title="chrome-maximize">
            <span class="inner">
                <i class="codicon codicon-chrome-maximize" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>chrome-maximize</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="chrome-minimize" title="chrome-minimize">
            <span class="inner">
                <i class="codicon codicon-chrome-minimize" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>chrome-minimize</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="chrome-restore" title="chrome-restore">
            <span class="inner">
                <i class="codicon codicon-chrome-restore" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>chrome-restore</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="circle-filled" title="circle-filled">
            <span class="inner">
                <i class="codicon codicon-circle-filled" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>circle-filled</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="circle-large-filled" title="circle-large-filled">
            <span class="inner">
                <i class="codicon codicon-circle-large-filled" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>circle-large-filled</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="circle-large" title="circle-large">
            <span class="inner">
                <i class="codicon codicon-circle-large" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>circle-large</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="circle-slash" title="circle-slash">
            <span class="inner">
                <i class="codicon codicon-circle-slash" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>circle-slash</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="circle-small-filled" title="circle-small-filled">
            <span class="inner">
                <i class="codicon codicon-circle-small-filled" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>circle-small-filled</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="circle-small" title="circle-small">
            <span class="inner">
                <i class="codicon codicon-circle-small" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>circle-small</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="circle" title="circle">
            <span class="inner">
                <i class="codicon codicon-circle" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>circle</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="circuit-board" title="circuit-board">
            <span class="inner">
                <i class="codicon codicon-circuit-board" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>circuit-board</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="clear-all" title="clear-all">
            <span class="inner">
                <i class="codicon codicon-clear-all" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>clear-all</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="clippy" title="clippy">
            <span class="inner">
                <i class="codicon codicon-clippy" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>clippy</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="close-all" title="close-all">
            <span class="inner">
                <i class="codicon codicon-close-all" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>close-all</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="close" title="close">
            <span class="inner">
                <i class="codicon codicon-close" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>close</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="cloud-download" title="cloud-download">
            <span class="inner">
                <i class="codicon codicon-cloud-download" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>cloud-download</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="cloud-upload" title="cloud-upload">
            <span class="inner">
                <i class="codicon codicon-cloud-upload" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>cloud-upload</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="cloud" title="cloud">
            <span class="inner">
                <i class="codicon codicon-cloud" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>cloud</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="code-oss" title="code-oss">
            <span class="inner">
                <i class="codicon codicon-code-oss" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>code-oss</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="code" title="code">
            <span class="inner">
                <i class="codicon codicon-code" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>code</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="coffee" title="coffee">
            <span class="inner">
                <i class="codicon codicon-coffee" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>coffee</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="collapse-all" title="collapse-all">
            <span class="inner">
                <i class="codicon codicon-collapse-all" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>collapse-all</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="color-mode" title="color-mode">
            <span class="inner">
                <i class="codicon codicon-color-mode" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>color-mode</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="combine" title="combine">
            <span class="inner">
                <i class="codicon codicon-combine" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>combine</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="comment-discussion" title="comment-discussion">
            <span class="inner">
                <i class="codicon codicon-comment-discussion" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>comment-discussion</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="comment-draft" title="comment-draft">
            <span class="inner">
                <i class="codicon codicon-comment-draft" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>comment-draft</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="comment-unresolved" title="comment-unresolved">
            <span class="inner">
                <i class="codicon codicon-comment-unresolved" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>comment-unresolved</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="comment" title="comment">
            <span class="inner">
                <i class="codicon codicon-comment" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>comment</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="compass-active" title="compass-active">
            <span class="inner">
                <i class="codicon codicon-compass-active" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>compass-active</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="compass-dot" title="compass-dot">
            <span class="inner">
                <i class="codicon codicon-compass-dot" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>compass-dot</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="compass" title="compass">
            <span class="inner">
                <i class="codicon codicon-compass" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>compass</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="copilot" title="copilot">
            <span class="inner">
                <i class="codicon codicon-copilot" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>copilot</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="copy" title="copy">
            <span class="inner">
                <i class="codicon codicon-copy" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>copy</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="coverage" title="coverage">
            <span class="inner">
                <i class="codicon codicon-coverage" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>coverage</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="credit-card" title="credit-card">
            <span class="inner">
                <i class="codicon codicon-credit-card" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>credit-card</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="dash" title="dash">
            <span class="inner">
                <i class="codicon codicon-dash" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>dash</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="dashboard" title="dashboard">
            <span class="inner">
                <i class="codicon codicon-dashboard" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>dashboard</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="database" title="database">
            <span class="inner">
                <i class="codicon codicon-database" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>database</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="debug-all" title="debug-all">
            <span class="inner">
                <i class="codicon codicon-debug-all" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>debug-all</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="debug-alt-small" title="debug-alt-small">
            <span class="inner">
                <i class="codicon codicon-debug-alt-small" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>debug-alt-small</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="debug-alt" title="debug-alt">
            <span class="inner">
                <i class="codicon codicon-debug-alt" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>debug-alt</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="debug-breakpoint-conditional-unverified" title="debug-breakpoint-conditional-unverified">
            <span class="inner">
                <i class="codicon codicon-debug-breakpoint-conditional-unverified" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>debug-breakpoint-conditional-unverified</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="debug-breakpoint-conditional" title="debug-breakpoint-conditional">
            <span class="inner">
                <i class="codicon codicon-debug-breakpoint-conditional" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>debug-breakpoint-conditional</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="debug-breakpoint-data-unverified" title="debug-breakpoint-data-unverified">
            <span class="inner">
                <i class="codicon codicon-debug-breakpoint-data-unverified" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>debug-breakpoint-data-unverified</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="debug-breakpoint-data" title="debug-breakpoint-data">
            <span class="inner">
                <i class="codicon codicon-debug-breakpoint-data" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>debug-breakpoint-data</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="debug-breakpoint-function-unverified" title="debug-breakpoint-function-unverified">
            <span class="inner">
                <i class="codicon codicon-debug-breakpoint-function-unverified" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>debug-breakpoint-function-unverified</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="debug-breakpoint-function" title="debug-breakpoint-function">
            <span class="inner">
                <i class="codicon codicon-debug-breakpoint-function" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>debug-breakpoint-function</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="debug-breakpoint-log-unverified" title="debug-breakpoint-log-unverified">
            <span class="inner">
                <i class="codicon codicon-debug-breakpoint-log-unverified" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>debug-breakpoint-log-unverified</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="debug-breakpoint-log" title="debug-breakpoint-log">
            <span class="inner">
                <i class="codicon codicon-debug-breakpoint-log" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>debug-breakpoint-log</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="debug-breakpoint-unsupported" title="debug-breakpoint-unsupported">
            <span class="inner">
                <i class="codicon codicon-debug-breakpoint-unsupported" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>debug-breakpoint-unsupported</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="debug-console" title="debug-console">
            <span class="inner">
                <i class="codicon codicon-debug-console" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>debug-console</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="debug-continue-small" title="debug-continue-small">
            <span class="inner">
                <i class="codicon codicon-debug-continue-small" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>debug-continue-small</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="debug-continue" title="debug-continue">
            <span class="inner">
                <i class="codicon codicon-debug-continue" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>debug-continue</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="debug-coverage" title="debug-coverage">
            <span class="inner">
                <i class="codicon codicon-debug-coverage" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>debug-coverage</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="debug-disconnect" title="debug-disconnect">
            <span class="inner">
                <i class="codicon codicon-debug-disconnect" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>debug-disconnect</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="debug-line-by-line" title="debug-line-by-line">
            <span class="inner">
                <i class="codicon codicon-debug-line-by-line" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>debug-line-by-line</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="debug-pause" title="debug-pause">
            <span class="inner">
                <i class="codicon codicon-debug-pause" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>debug-pause</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="debug-rerun" title="debug-rerun">
            <span class="inner">
                <i class="codicon codicon-debug-rerun" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>debug-rerun</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="debug-restart-frame" title="debug-restart-frame">
            <span class="inner">
                <i class="codicon codicon-debug-restart-frame" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>debug-restart-frame</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="debug-restart" title="debug-restart">
            <span class="inner">
                <i class="codicon codicon-debug-restart" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>debug-restart</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="debug-reverse-continue" title="debug-reverse-continue">
            <span class="inner">
                <i class="codicon codicon-debug-reverse-continue" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>debug-reverse-continue</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="debug-stackframe-active" title="debug-stackframe-active">
            <span class="inner">
                <i class="codicon codicon-debug-stackframe-active" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>debug-stackframe-active</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="debug-stackframe" title="debug-stackframe">
            <span class="inner">
                <i class="codicon codicon-debug-stackframe" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>debug-stackframe</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="debug-start" title="debug-start">
            <span class="inner">
                <i class="codicon codicon-debug-start" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>debug-start</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="debug-step-back" title="debug-step-back">
            <span class="inner">
                <i class="codicon codicon-debug-step-back" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>debug-step-back</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="debug-step-into" title="debug-step-into">
            <span class="inner">
                <i class="codicon codicon-debug-step-into" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>debug-step-into</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="debug-step-out" title="debug-step-out">
            <span class="inner">
                <i class="codicon codicon-debug-step-out" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>debug-step-out</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="debug-step-over" title="debug-step-over">
            <span class="inner">
                <i class="codicon codicon-debug-step-over" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>debug-step-over</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="debug-stop" title="debug-stop">
            <span class="inner">
                <i class="codicon codicon-debug-stop" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>debug-stop</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="debug" title="debug">
            <span class="inner">
                <i class="codicon codicon-debug" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>debug</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="desktop-download" title="desktop-download">
            <span class="inner">
                <i class="codicon codicon-desktop-download" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>desktop-download</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="device-camera-video" title="device-camera-video">
            <span class="inner">
                <i class="codicon codicon-device-camera-video" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>device-camera-video</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="device-camera" title="device-camera">
            <span class="inner">
                <i class="codicon codicon-device-camera" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>device-camera</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="device-mobile" title="device-mobile">
            <span class="inner">
                <i class="codicon codicon-device-mobile" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>device-mobile</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="diff-added" title="diff-added">
            <span class="inner">
                <i class="codicon codicon-diff-added" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>diff-added</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="diff-ignored" title="diff-ignored">
            <span class="inner">
                <i class="codicon codicon-diff-ignored" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>diff-ignored</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="diff-modified" title="diff-modified">
            <span class="inner">
                <i class="codicon codicon-diff-modified" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>diff-modified</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="diff-multiple" title="diff-multiple">
            <span class="inner">
                <i class="codicon codicon-diff-multiple" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>diff-multiple</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="diff-removed" title="diff-removed">
            <span class="inner">
                <i class="codicon codicon-diff-removed" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>diff-removed</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="diff-renamed" title="diff-renamed">
            <span class="inner">
                <i class="codicon codicon-diff-renamed" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>diff-renamed</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="diff-single" title="diff-single">
            <span class="inner">
                <i class="codicon codicon-diff-single" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>diff-single</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="diff" title="diff">
            <span class="inner">
                <i class="codicon codicon-diff" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>diff</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="discard" title="discard">
            <span class="inner">
                <i class="codicon codicon-discard" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>discard</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="edit" title="edit">
            <span class="inner">
                <i class="codicon codicon-edit" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>edit</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="editor-layout" title="editor-layout">
            <span class="inner">
                <i class="codicon codicon-editor-layout" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>editor-layout</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="ellipsis" title="ellipsis">
            <span class="inner">
                <i class="codicon codicon-ellipsis" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>ellipsis</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="empty-window" title="empty-window">
            <span class="inner">
                <i class="codicon codicon-empty-window" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>empty-window</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="error-small" title="error-small">
            <span class="inner">
                <i class="codicon codicon-error-small" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>error-small</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="error" title="error">
            <span class="inner">
                <i class="codicon codicon-error" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>error</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="exclude" title="exclude">
            <span class="inner">
                <i class="codicon codicon-exclude" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>exclude</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="expand-all" title="expand-all">
            <span class="inner">
                <i class="codicon codicon-expand-all" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>expand-all</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="export" title="export">
            <span class="inner">
                <i class="codicon codicon-export" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>export</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="extensions" title="extensions">
            <span class="inner">
                <i class="codicon codicon-extensions" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>extensions</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="eye-closed" title="eye-closed">
            <span class="inner">
                <i class="codicon codicon-eye-closed" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>eye-closed</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="eye" title="eye">
            <span class="inner">
                <i class="codicon codicon-eye" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>eye</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="feedback" title="feedback">
            <span class="inner">
                <i class="codicon codicon-feedback" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>feedback</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="file-binary" title="file-binary">
            <span class="inner">
                <i class="codicon codicon-file-binary" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>file-binary</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="file-code" title="file-code">
            <span class="inner">
                <i class="codicon codicon-file-code" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>file-code</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="file-media" title="file-media">
            <span class="inner">
                <i class="codicon codicon-file-media" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>file-media</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="file-pdf" title="file-pdf">
            <span class="inner">
                <i class="codicon codicon-file-pdf" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>file-pdf</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="file-submodule" title="file-submodule">
            <span class="inner">
                <i class="codicon codicon-file-submodule" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>file-submodule</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="file-symlink-directory" title="file-symlink-directory">
            <span class="inner">
                <i class="codicon codicon-file-symlink-directory" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>file-symlink-directory</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="file-symlink-file" title="file-symlink-file">
            <span class="inner">
                <i class="codicon codicon-file-symlink-file" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>file-symlink-file</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="file-zip" title="file-zip">
            <span class="inner">
                <i class="codicon codicon-file-zip" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>file-zip</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="file" title="file">
            <span class="inner">
                <i class="codicon codicon-file" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>file</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="files" title="files">
            <span class="inner">
                <i class="codicon codicon-files" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>files</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="filter-filled" title="filter-filled">
            <span class="inner">
                <i class="codicon codicon-filter-filled" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>filter-filled</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="filter" title="filter">
            <span class="inner">
                <i class="codicon codicon-filter" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>filter</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="flame" title="flame">
            <span class="inner">
                <i class="codicon codicon-flame" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>flame</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="fold-down" title="fold-down">
            <span class="inner">
                <i class="codicon codicon-fold-down" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>fold-down</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="fold-up" title="fold-up">
            <span class="inner">
                <i class="codicon codicon-fold-up" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>fold-up</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="fold" title="fold">
            <span class="inner">
                <i class="codicon codicon-fold" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>fold</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="folder-active" title="folder-active">
            <span class="inner">
                <i class="codicon codicon-folder-active" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>folder-active</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="folder-library" title="folder-library">
            <span class="inner">
                <i class="codicon codicon-folder-library" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>folder-library</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="folder-opened" title="folder-opened">
            <span class="inner">
                <i class="codicon codicon-folder-opened" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>folder-opened</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="folder" title="folder">
            <span class="inner">
                <i class="codicon codicon-folder" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>folder</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="game" title="game">
            <span class="inner">
                <i class="codicon codicon-game" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>game</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="gear" title="gear">
            <span class="inner">
                <i class="codicon codicon-gear" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>gear</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="gift" title="gift">
            <span class="inner">
                <i class="codicon codicon-gift" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>gift</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="gist-secret" title="gist-secret">
            <span class="inner">
                <i class="codicon codicon-gist-secret" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>gist-secret</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="gist" title="gist">
            <span class="inner">
                <i class="codicon codicon-gist" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>gist</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="git-commit" title="git-commit">
            <span class="inner">
                <i class="codicon codicon-git-commit" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>git-commit</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="git-compare" title="git-compare">
            <span class="inner">
                <i class="codicon codicon-git-compare" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>git-compare</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="git-fetch" title="git-fetch">
            <span class="inner">
                <i class="codicon codicon-git-fetch" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>git-fetch</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="git-merge" title="git-merge">
            <span class="inner">
                <i class="codicon codicon-git-merge" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>git-merge</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="git-pull-request-closed" title="git-pull-request-closed">
            <span class="inner">
                <i class="codicon codicon-git-pull-request-closed" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>git-pull-request-closed</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="git-pull-request-create" title="git-pull-request-create">
            <span class="inner">
                <i class="codicon codicon-git-pull-request-create" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>git-pull-request-create</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="git-pull-request-draft" title="git-pull-request-draft">
            <span class="inner">
                <i class="codicon codicon-git-pull-request-draft" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>git-pull-request-draft</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="git-pull-request-go-to-changes" title="git-pull-request-go-to-changes">
            <span class="inner">
                <i class="codicon codicon-git-pull-request-go-to-changes" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>git-pull-request-go-to-changes</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="git-pull-request-new-changes" title="git-pull-request-new-changes">
            <span class="inner">
                <i class="codicon codicon-git-pull-request-new-changes" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>git-pull-request-new-changes</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="git-pull-request" title="git-pull-request">
            <span class="inner">
                <i class="codicon codicon-git-pull-request" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>git-pull-request</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="git-stash-apply" title="git-stash-apply">
            <span class="inner">
                <i class="codicon codicon-git-stash-apply" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>git-stash-apply</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="git-stash-pop" title="git-stash-pop">
            <span class="inner">
                <i class="codicon codicon-git-stash-pop" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>git-stash-pop</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="git-stash" title="git-stash">
            <span class="inner">
                <i class="codicon codicon-git-stash" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>git-stash</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="github-action" title="github-action">
            <span class="inner">
                <i class="codicon codicon-github-action" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>github-action</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="github-alt" title="github-alt">
            <span class="inner">
                <i class="codicon codicon-github-alt" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>github-alt</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="github-inverted" title="github-inverted">
            <span class="inner">
                <i class="codicon codicon-github-inverted" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>github-inverted</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="github-project" title="github-project">
            <span class="inner">
                <i class="codicon codicon-github-project" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>github-project</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="github" title="github">
            <span class="inner">
                <i class="codicon codicon-github" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>github</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="globe" title="globe">
            <span class="inner">
                <i class="codicon codicon-globe" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>globe</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="go-to-file" title="go-to-file">
            <span class="inner">
                <i class="codicon codicon-go-to-file" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>go-to-file</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="go-to-search" title="go-to-search">
            <span class="inner">
                <i class="codicon codicon-go-to-search" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>go-to-search</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="grabber" title="grabber">
            <span class="inner">
                <i class="codicon codicon-grabber" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>grabber</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="graph-left" title="graph-left">
            <span class="inner">
                <i class="codicon codicon-graph-left" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>graph-left</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="graph-line" title="graph-line">
            <span class="inner">
                <i class="codicon codicon-graph-line" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>graph-line</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="graph-scatter" title="graph-scatter">
            <span class="inner">
                <i class="codicon codicon-graph-scatter" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>graph-scatter</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="graph" title="graph">
            <span class="inner">
                <i class="codicon codicon-graph" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>graph</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="gripper" title="gripper">
            <span class="inner">
                <i class="codicon codicon-gripper" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>gripper</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="group-by-ref-type" title="group-by-ref-type">
            <span class="inner">
                <i class="codicon codicon-group-by-ref-type" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>group-by-ref-type</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="heart-filled" title="heart-filled">
            <span class="inner">
                <i class="codicon codicon-heart-filled" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>heart-filled</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="heart" title="heart">
            <span class="inner">
                <i class="codicon codicon-heart" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>heart</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="history" title="history">
            <span class="inner">
                <i class="codicon codicon-history" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>history</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="home" title="home">
            <span class="inner">
                <i class="codicon codicon-home" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>home</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="horizontal-rule" title="horizontal-rule">
            <span class="inner">
                <i class="codicon codicon-horizontal-rule" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>horizontal-rule</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="hubot" title="hubot">
            <span class="inner">
                <i class="codicon codicon-hubot" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>hubot</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="inbox" title="inbox">
            <span class="inner">
                <i class="codicon codicon-inbox" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>inbox</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="indent" title="indent">
            <span class="inner">
                <i class="codicon codicon-indent" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>indent</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="info" title="info">
            <span class="inner">
                <i class="codicon codicon-info" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>info</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="insert" title="insert">
            <span class="inner">
                <i class="codicon codicon-insert" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>insert</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="inspect" title="inspect">
            <span class="inner">
                <i class="codicon codicon-inspect" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>inspect</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="issue-draft" title="issue-draft">
            <span class="inner">
                <i class="codicon codicon-issue-draft" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>issue-draft</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="issue-reopened" title="issue-reopened">
            <span class="inner">
                <i class="codicon codicon-issue-reopened" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>issue-reopened</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="issues" title="issues">
            <span class="inner">
                <i class="codicon codicon-issues" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>issues</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="italic" title="italic">
            <span class="inner">
                <i class="codicon codicon-italic" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>italic</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="jersey" title="jersey">
            <span class="inner">
                <i class="codicon codicon-jersey" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>jersey</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="json" title="json">
            <span class="inner">
                <i class="codicon codicon-json" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>json</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="kebab-vertical" title="kebab-vertical">
            <span class="inner">
                <i class="codicon codicon-kebab-vertical" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>kebab-vertical</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="key" title="key">
            <span class="inner">
                <i class="codicon codicon-key" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>key</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="law" title="law">
            <span class="inner">
                <i class="codicon codicon-law" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>law</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="layers-active" title="layers-active">
            <span class="inner">
                <i class="codicon codicon-layers-active" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>layers-active</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="layers-dot" title="layers-dot">
            <span class="inner">
                <i class="codicon codicon-layers-dot" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>layers-dot</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="layers" title="layers">
            <span class="inner">
                <i class="codicon codicon-layers" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>layers</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="layout-activitybar-left" title="layout-activitybar-left">
            <span class="inner">
                <i class="codicon codicon-layout-activitybar-left" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>layout-activitybar-left</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="layout-activitybar-right" title="layout-activitybar-right">
            <span class="inner">
                <i class="codicon codicon-layout-activitybar-right" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>layout-activitybar-right</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="layout-centered" title="layout-centered">
            <span class="inner">
                <i class="codicon codicon-layout-centered" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>layout-centered</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="layout-menubar" title="layout-menubar">
            <span class="inner">
                <i class="codicon codicon-layout-menubar" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>layout-menubar</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="layout-panel-center" title="layout-panel-center">
            <span class="inner">
                <i class="codicon codicon-layout-panel-center" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>layout-panel-center</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="layout-panel-justify" title="layout-panel-justify">
            <span class="inner">
                <i class="codicon codicon-layout-panel-justify" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>layout-panel-justify</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="layout-panel-left" title="layout-panel-left">
            <span class="inner">
                <i class="codicon codicon-layout-panel-left" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>layout-panel-left</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="layout-panel-off" title="layout-panel-off">
            <span class="inner">
                <i class="codicon codicon-layout-panel-off" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>layout-panel-off</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="layout-panel-right" title="layout-panel-right">
            <span class="inner">
                <i class="codicon codicon-layout-panel-right" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>layout-panel-right</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="layout-panel" title="layout-panel">
            <span class="inner">
                <i class="codicon codicon-layout-panel" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>layout-panel</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="layout-sidebar-left-off" title="layout-sidebar-left-off">
            <span class="inner">
                <i class="codicon codicon-layout-sidebar-left-off" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>layout-sidebar-left-off</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="layout-sidebar-left" title="layout-sidebar-left">
            <span class="inner">
                <i class="codicon codicon-layout-sidebar-left" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>layout-sidebar-left</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="layout-sidebar-right-off" title="layout-sidebar-right-off">
            <span class="inner">
                <i class="codicon codicon-layout-sidebar-right-off" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>layout-sidebar-right-off</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="layout-sidebar-right" title="layout-sidebar-right">
            <span class="inner">
                <i class="codicon codicon-layout-sidebar-right" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>layout-sidebar-right</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="layout-statusbar" title="layout-statusbar">
            <span class="inner">
                <i class="codicon codicon-layout-statusbar" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>layout-statusbar</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="layout" title="layout">
            <span class="inner">
                <i class="codicon codicon-layout" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>layout</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="library" title="library">
            <span class="inner">
                <i class="codicon codicon-library" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>library</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="lightbulb-autofix" title="lightbulb-autofix">
            <span class="inner">
                <i class="codicon codicon-lightbulb-autofix" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>lightbulb-autofix</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="lightbulb-sparkle" title="lightbulb-sparkle">
            <span class="inner">
                <i class="codicon codicon-lightbulb-sparkle" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>lightbulb-sparkle</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="lightbulb" title="lightbulb">
            <span class="inner">
                <i class="codicon codicon-lightbulb" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>lightbulb</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="link-external" title="link-external">
            <span class="inner">
                <i class="codicon codicon-link-external" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>link-external</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="link" title="link">
            <span class="inner">
                <i class="codicon codicon-link" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>link</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="list-filter" title="list-filter">
            <span class="inner">
                <i class="codicon codicon-list-filter" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>list-filter</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="list-flat" title="list-flat">
            <span class="inner">
                <i class="codicon codicon-list-flat" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>list-flat</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="list-ordered" title="list-ordered">
            <span class="inner">
                <i class="codicon codicon-list-ordered" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>list-ordered</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="list-selection" title="list-selection">
            <span class="inner">
                <i class="codicon codicon-list-selection" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>list-selection</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="list-tree" title="list-tree">
            <span class="inner">
                <i class="codicon codicon-list-tree" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>list-tree</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="list-unordered" title="list-unordered">
            <span class="inner">
                <i class="codicon codicon-list-unordered" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>list-unordered</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="live-share" title="live-share">
            <span class="inner">
                <i class="codicon codicon-live-share" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>live-share</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="loading" title="loading">
            <span class="inner">
                <i class="codicon codicon-loading" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>loading</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="location" title="location">
            <span class="inner">
                <i class="codicon codicon-location" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>location</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="lock-small" title="lock-small">
            <span class="inner">
                <i class="codicon codicon-lock-small" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>lock-small</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="lock" title="lock">
            <span class="inner">
                <i class="codicon codicon-lock" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>lock</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="magnet" title="magnet">
            <span class="inner">
                <i class="codicon codicon-magnet" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>magnet</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="mail-read" title="mail-read">
            <span class="inner">
                <i class="codicon codicon-mail-read" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>mail-read</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="mail" title="mail">
            <span class="inner">
                <i class="codicon codicon-mail" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>mail</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="map-filled" title="map-filled">
            <span class="inner">
                <i class="codicon codicon-map-filled" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>map-filled</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="map-vertical-filled" title="map-vertical-filled">
            <span class="inner">
                <i class="codicon codicon-map-vertical-filled" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>map-vertical-filled</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="map-vertical" title="map-vertical">
            <span class="inner">
                <i class="codicon codicon-map-vertical" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>map-vertical</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="map" title="map">
            <span class="inner">
                <i class="codicon codicon-map" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>map</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="markdown" title="markdown">
            <span class="inner">
                <i class="codicon codicon-markdown" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>markdown</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="megaphone" title="megaphone">
            <span class="inner">
                <i class="codicon codicon-megaphone" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>megaphone</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="mention" title="mention">
            <span class="inner">
                <i class="codicon codicon-mention" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>mention</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="menu" title="menu">
            <span class="inner">
                <i class="codicon codicon-menu" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>menu</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="merge" title="merge">
            <span class="inner">
                <i class="codicon codicon-merge" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>merge</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="mic-filled" title="mic-filled">
            <span class="inner">
                <i class="codicon codicon-mic-filled" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>mic-filled</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="mic" title="mic">
            <span class="inner">
                <i class="codicon codicon-mic" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>mic</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="milestone" title="milestone">
            <span class="inner">
                <i class="codicon codicon-milestone" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>milestone</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="mirror" title="mirror">
            <span class="inner">
                <i class="codicon codicon-mirror" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>mirror</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="mortar-board" title="mortar-board">
            <span class="inner">
                <i class="codicon codicon-mortar-board" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>mortar-board</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="move" title="move">
            <span class="inner">
                <i class="codicon codicon-move" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>move</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="multiple-windows" title="multiple-windows">
            <span class="inner">
                <i class="codicon codicon-multiple-windows" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>multiple-windows</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="music" title="music">
            <span class="inner">
                <i class="codicon codicon-music" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>music</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="mute" title="mute">
            <span class="inner">
                <i class="codicon codicon-mute" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>mute</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="new-file" title="new-file">
            <span class="inner">
                <i class="codicon codicon-new-file" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>new-file</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="new-folder" title="new-folder">
            <span class="inner">
                <i class="codicon codicon-new-folder" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>new-folder</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="newline" title="newline">
            <span class="inner">
                <i class="codicon codicon-newline" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>newline</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="no-newline" title="no-newline">
            <span class="inner">
                <i class="codicon codicon-no-newline" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>no-newline</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="note" title="note">
            <span class="inner">
                <i class="codicon codicon-note" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>note</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="notebook-template" title="notebook-template">
            <span class="inner">
                <i class="codicon codicon-notebook-template" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>notebook-template</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="notebook" title="notebook">
            <span class="inner">
                <i class="codicon codicon-notebook" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>notebook</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="octoface" title="octoface">
            <span class="inner">
                <i class="codicon codicon-octoface" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>octoface</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="open-preview" title="open-preview">
            <span class="inner">
                <i class="codicon codicon-open-preview" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>open-preview</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="organization" title="organization">
            <span class="inner">
                <i class="codicon codicon-organization" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>organization</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="output" title="output">
            <span class="inner">
                <i class="codicon codicon-output" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>output</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="package" title="package">
            <span class="inner">
                <i class="codicon codicon-package" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>package</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="paintcan" title="paintcan">
            <span class="inner">
                <i class="codicon codicon-paintcan" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>paintcan</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="pass-filled" title="pass-filled">
            <span class="inner">
                <i class="codicon codicon-pass-filled" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>pass-filled</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="pass" title="pass">
            <span class="inner">
                <i class="codicon codicon-pass" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>pass</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="percentage" title="percentage">
            <span class="inner">
                <i class="codicon codicon-percentage" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>percentage</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="person-add" title="person-add">
            <span class="inner">
                <i class="codicon codicon-person-add" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>person-add</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="person" title="person">
            <span class="inner">
                <i class="codicon codicon-person" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>person</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="piano" title="piano">
            <span class="inner">
                <i class="codicon codicon-piano" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>piano</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="pie-chart" title="pie-chart">
            <span class="inner">
                <i class="codicon codicon-pie-chart" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>pie-chart</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="pin" title="pin">
            <span class="inner">
                <i class="codicon codicon-pin" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>pin</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="pinned-dirty" title="pinned-dirty">
            <span class="inner">
                <i class="codicon codicon-pinned-dirty" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>pinned-dirty</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="pinned" title="pinned">
            <span class="inner">
                <i class="codicon codicon-pinned" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>pinned</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="play-circle" title="play-circle">
            <span class="inner">
                <i class="codicon codicon-play-circle" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>play-circle</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="play" title="play">
            <span class="inner">
                <i class="codicon codicon-play" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>play</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="plug" title="plug">
            <span class="inner">
                <i class="codicon codicon-plug" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>plug</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="preserve-case" title="preserve-case">
            <span class="inner">
                <i class="codicon codicon-preserve-case" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>preserve-case</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="preview" title="preview">
            <span class="inner">
                <i class="codicon codicon-preview" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>preview</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="primitive-square" title="primitive-square">
            <span class="inner">
                <i class="codicon codicon-primitive-square" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>primitive-square</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="project" title="project">
            <span class="inner">
                <i class="codicon codicon-project" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>project</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="pulse" title="pulse">
            <span class="inner">
                <i class="codicon codicon-pulse" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>pulse</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="question" title="question">
            <span class="inner">
                <i class="codicon codicon-question" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>question</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="quote" title="quote">
            <span class="inner">
                <i class="codicon codicon-quote" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>quote</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="radio-tower" title="radio-tower">
            <span class="inner">
                <i class="codicon codicon-radio-tower" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>radio-tower</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="reactions" title="reactions">
            <span class="inner">
                <i class="codicon codicon-reactions" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>reactions</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="record-keys" title="record-keys">
            <span class="inner">
                <i class="codicon codicon-record-keys" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>record-keys</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="record-small" title="record-small">
            <span class="inner">
                <i class="codicon codicon-record-small" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>record-small</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="record" title="record">
            <span class="inner">
                <i class="codicon codicon-record" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>record</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="redo" title="redo">
            <span class="inner">
                <i class="codicon codicon-redo" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>redo</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="references" title="references">
            <span class="inner">
                <i class="codicon codicon-references" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>references</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="refresh" title="refresh">
            <span class="inner">
                <i class="codicon codicon-refresh" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>refresh</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="regex" title="regex">
            <span class="inner">
                <i class="codicon codicon-regex" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>regex</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="remote-explorer" title="remote-explorer">
            <span class="inner">
                <i class="codicon codicon-remote-explorer" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>remote-explorer</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="remote" title="remote">
            <span class="inner">
                <i class="codicon codicon-remote" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>remote</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="remove" title="remove">
            <span class="inner">
                <i class="codicon codicon-remove" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>remove</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="replace-all" title="replace-all">
            <span class="inner">
                <i class="codicon codicon-replace-all" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>replace-all</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="replace" title="replace">
            <span class="inner">
                <i class="codicon codicon-replace" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>replace</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="reply" title="reply">
            <span class="inner">
                <i class="codicon codicon-reply" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>reply</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="repo-clone" title="repo-clone">
            <span class="inner">
                <i class="codicon codicon-repo-clone" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>repo-clone</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="repo-fetch" title="repo-fetch">
            <span class="inner">
                <i class="codicon codicon-repo-fetch" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>repo-fetch</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="repo-force-push" title="repo-force-push">
            <span class="inner">
                <i class="codicon codicon-repo-force-push" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>repo-force-push</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="repo-forked" title="repo-forked">
            <span class="inner">
                <i class="codicon codicon-repo-forked" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>repo-forked</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="repo-pull" title="repo-pull">
            <span class="inner">
                <i class="codicon codicon-repo-pull" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>repo-pull</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="repo-push" title="repo-push">
            <span class="inner">
                <i class="codicon codicon-repo-push" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>repo-push</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="repo" title="repo">
            <span class="inner">
                <i class="codicon codicon-repo" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>repo</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="report" title="report">
            <span class="inner">
                <i class="codicon codicon-report" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>report</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="request-changes" title="request-changes">
            <span class="inner">
                <i class="codicon codicon-request-changes" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>request-changes</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="robot" title="robot">
            <span class="inner">
                <i class="codicon codicon-robot" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>robot</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="rocket" title="rocket">
            <span class="inner">
                <i class="codicon codicon-rocket" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>rocket</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="root-folder-opened" title="root-folder-opened">
            <span class="inner">
                <i class="codicon codicon-root-folder-opened" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>root-folder-opened</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="root-folder" title="root-folder">
            <span class="inner">
                <i class="codicon codicon-root-folder" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>root-folder</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="rss" title="rss">
            <span class="inner">
                <i class="codicon codicon-rss" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>rss</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="ruby" title="ruby">
            <span class="inner">
                <i class="codicon codicon-ruby" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>ruby</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="run-above" title="run-above">
            <span class="inner">
                <i class="codicon codicon-run-above" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>run-above</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="run-all-coverage" title="run-all-coverage">
            <span class="inner">
                <i class="codicon codicon-run-all-coverage" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>run-all-coverage</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="run-all" title="run-all">
            <span class="inner">
                <i class="codicon codicon-run-all" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>run-all</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="run-below" title="run-below">
            <span class="inner">
                <i class="codicon codicon-run-below" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>run-below</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="run-coverage" title="run-coverage">
            <span class="inner">
                <i class="codicon codicon-run-coverage" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>run-coverage</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="run-errors" title="run-errors">
            <span class="inner">
                <i class="codicon codicon-run-errors" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>run-errors</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="save-all" title="save-all">
            <span class="inner">
                <i class="codicon codicon-save-all" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>save-all</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="save-as" title="save-as">
            <span class="inner">
                <i class="codicon codicon-save-as" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>save-as</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="save" title="save">
            <span class="inner">
                <i class="codicon codicon-save" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>save</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="screen-full" title="screen-full">
            <span class="inner">
                <i class="codicon codicon-screen-full" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>screen-full</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="screen-normal" title="screen-normal">
            <span class="inner">
                <i class="codicon codicon-screen-normal" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>screen-normal</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="search-fuzzy" title="search-fuzzy">
            <span class="inner">
                <i class="codicon codicon-search-fuzzy" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>search-fuzzy</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="search-stop" title="search-stop">
            <span class="inner">
                <i class="codicon codicon-search-stop" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>search-stop</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="search" title="search">
            <span class="inner">
                <i class="codicon codicon-search" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>search</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="send" title="send">
            <span class="inner">
                <i class="codicon codicon-send" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>send</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="server-environment" title="server-environment">
            <span class="inner">
                <i class="codicon codicon-server-environment" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>server-environment</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="server-process" title="server-process">
            <span class="inner">
                <i class="codicon codicon-server-process" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>server-process</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="server" title="server">
            <span class="inner">
                <i class="codicon codicon-server" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>server</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="settings-gear" title="settings-gear">
            <span class="inner">
                <i class="codicon codicon-settings-gear" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>settings-gear</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="settings" title="settings">
            <span class="inner">
                <i class="codicon codicon-settings" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>settings</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="share" title="share">
            <span class="inner">
                <i class="codicon codicon-share" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>share</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="shield" title="shield">
            <span class="inner">
                <i class="codicon codicon-shield" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>shield</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="sign-in" title="sign-in">
            <span class="inner">
                <i class="codicon codicon-sign-in" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>sign-in</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="sign-out" title="sign-out">
            <span class="inner">
                <i class="codicon codicon-sign-out" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>sign-out</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="smiley" title="smiley">
            <span class="inner">
                <i class="codicon codicon-smiley" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>smiley</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="snake" title="snake">
            <span class="inner">
                <i class="codicon codicon-snake" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>snake</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="sort-precedence" title="sort-precedence">
            <span class="inner">
                <i class="codicon codicon-sort-precedence" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>sort-precedence</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="source-control" title="source-control">
            <span class="inner">
                <i class="codicon codicon-source-control" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>source-control</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="sparkle-filled" title="sparkle-filled">
            <span class="inner">
                <i class="codicon codicon-sparkle-filled" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>sparkle-filled</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="sparkle" title="sparkle">
            <span class="inner">
                <i class="codicon codicon-sparkle" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>sparkle</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="split-horizontal" title="split-horizontal">
            <span class="inner">
                <i class="codicon codicon-split-horizontal" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>split-horizontal</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="split-vertical" title="split-vertical">
            <span class="inner">
                <i class="codicon codicon-split-vertical" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>split-vertical</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="squirrel" title="squirrel">
            <span class="inner">
                <i class="codicon codicon-squirrel" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>squirrel</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="star-empty" title="star-empty">
            <span class="inner">
                <i class="codicon codicon-star-empty" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>star-empty</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="star-full" title="star-full">
            <span class="inner">
                <i class="codicon codicon-star-full" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>star-full</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="star-half" title="star-half">
            <span class="inner">
                <i class="codicon codicon-star-half" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>star-half</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="stop-circle" title="stop-circle">
            <span class="inner">
                <i class="codicon codicon-stop-circle" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>stop-circle</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="surround-with" title="surround-with">
            <span class="inner">
                <i class="codicon codicon-surround-with" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>surround-with</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="symbol-array" title="symbol-array">
            <span class="inner">
                <i class="codicon codicon-symbol-array" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>symbol-array</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="symbol-boolean" title="symbol-boolean">
            <span class="inner">
                <i class="codicon codicon-symbol-boolean" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>symbol-boolean</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="symbol-class" title="symbol-class">
            <span class="inner">
                <i class="codicon codicon-symbol-class" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>symbol-class</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="symbol-color" title="symbol-color">
            <span class="inner">
                <i class="codicon codicon-symbol-color" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>symbol-color</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="symbol-constant" title="symbol-constant">
            <span class="inner">
                <i class="codicon codicon-symbol-constant" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>symbol-constant</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="symbol-enum-member" title="symbol-enum-member">
            <span class="inner">
                <i class="codicon codicon-symbol-enum-member" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>symbol-enum-member</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="symbol-enum" title="symbol-enum">
            <span class="inner">
                <i class="codicon codicon-symbol-enum" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>symbol-enum</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="symbol-event" title="symbol-event">
            <span class="inner">
                <i class="codicon codicon-symbol-event" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>symbol-event</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="symbol-field" title="symbol-field">
            <span class="inner">
                <i class="codicon codicon-symbol-field" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>symbol-field</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="symbol-file" title="symbol-file">
            <span class="inner">
                <i class="codicon codicon-symbol-file" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>symbol-file</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="symbol-interface" title="symbol-interface">
            <span class="inner">
                <i class="codicon codicon-symbol-interface" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>symbol-interface</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="symbol-key" title="symbol-key">
            <span class="inner">
                <i class="codicon codicon-symbol-key" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>symbol-key</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="symbol-keyword" title="symbol-keyword">
            <span class="inner">
                <i class="codicon codicon-symbol-keyword" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>symbol-keyword</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="symbol-method" title="symbol-method">
            <span class="inner">
                <i class="codicon codicon-symbol-method" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>symbol-method</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="symbol-misc" title="symbol-misc">
            <span class="inner">
                <i class="codicon codicon-symbol-misc" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>symbol-misc</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="symbol-namespace" title="symbol-namespace">
            <span class="inner">
                <i class="codicon codicon-symbol-namespace" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>symbol-namespace</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="symbol-numeric" title="symbol-numeric">
            <span class="inner">
                <i class="codicon codicon-symbol-numeric" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>symbol-numeric</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="symbol-operator" title="symbol-operator">
            <span class="inner">
                <i class="codicon codicon-symbol-operator" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>symbol-operator</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="symbol-parameter" title="symbol-parameter">
            <span class="inner">
                <i class="codicon codicon-symbol-parameter" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>symbol-parameter</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="symbol-property" title="symbol-property">
            <span class="inner">
                <i class="codicon codicon-symbol-property" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>symbol-property</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="symbol-ruler" title="symbol-ruler">
            <span class="inner">
                <i class="codicon codicon-symbol-ruler" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>symbol-ruler</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="symbol-snippet" title="symbol-snippet">
            <span class="inner">
                <i class="codicon codicon-symbol-snippet" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>symbol-snippet</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="symbol-string" title="symbol-string">
            <span class="inner">
                <i class="codicon codicon-symbol-string" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>symbol-string</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="symbol-structure" title="symbol-structure">
            <span class="inner">
                <i class="codicon codicon-symbol-structure" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>symbol-structure</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="symbol-variable" title="symbol-variable">
            <span class="inner">
                <i class="codicon codicon-symbol-variable" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>symbol-variable</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="sync-ignored" title="sync-ignored">
            <span class="inner">
                <i class="codicon codicon-sync-ignored" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>sync-ignored</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="sync" title="sync">
            <span class="inner">
                <i class="codicon codicon-sync" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>sync</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="table" title="table">
            <span class="inner">
                <i class="codicon codicon-table" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>table</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="tag" title="tag">
            <span class="inner">
                <i class="codicon codicon-tag" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>tag</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="target" title="target">
            <span class="inner">
                <i class="codicon codicon-target" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>target</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="tasklist" title="tasklist">
            <span class="inner">
                <i class="codicon codicon-tasklist" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>tasklist</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="telescope" title="telescope">
            <span class="inner">
                <i class="codicon codicon-telescope" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>telescope</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="terminal-bash" title="terminal-bash">
            <span class="inner">
                <i class="codicon codicon-terminal-bash" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>terminal-bash</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="terminal-cmd" title="terminal-cmd">
            <span class="inner">
                <i class="codicon codicon-terminal-cmd" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>terminal-cmd</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="terminal-debian" title="terminal-debian">
            <span class="inner">
                <i class="codicon codicon-terminal-debian" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>terminal-debian</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="terminal-linux" title="terminal-linux">
            <span class="inner">
                <i class="codicon codicon-terminal-linux" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>terminal-linux</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="terminal-powershell" title="terminal-powershell">
            <span class="inner">
                <i class="codicon codicon-terminal-powershell" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>terminal-powershell</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="terminal-tmux" title="terminal-tmux">
            <span class="inner">
                <i class="codicon codicon-terminal-tmux" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>terminal-tmux</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="terminal-ubuntu" title="terminal-ubuntu">
            <span class="inner">
                <i class="codicon codicon-terminal-ubuntu" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>terminal-ubuntu</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="terminal" title="terminal">
            <span class="inner">
                <i class="codicon codicon-terminal" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>terminal</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="text-size" title="text-size">
            <span class="inner">
                <i class="codicon codicon-text-size" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>text-size</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="three-bars" title="three-bars">
            <span class="inner">
                <i class="codicon codicon-three-bars" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>three-bars</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="thumbsdown-filled" title="thumbsdown-filled">
            <span class="inner">
                <i class="codicon codicon-thumbsdown-filled" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>thumbsdown-filled</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="thumbsdown" title="thumbsdown">
            <span class="inner">
                <i class="codicon codicon-thumbsdown" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>thumbsdown</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="thumbsup-filled" title="thumbsup-filled">
            <span class="inner">
                <i class="codicon codicon-thumbsup-filled" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>thumbsup-filled</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="thumbsup" title="thumbsup">
            <span class="inner">
                <i class="codicon codicon-thumbsup" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>thumbsup</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="tools" title="tools">
            <span class="inner">
                <i class="codicon codicon-tools" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>tools</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="trash" title="trash">
            <span class="inner">
                <i class="codicon codicon-trash" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>trash</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="triangle-down" title="triangle-down">
            <span class="inner">
                <i class="codicon codicon-triangle-down" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>triangle-down</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="triangle-left" title="triangle-left">
            <span class="inner">
                <i class="codicon codicon-triangle-left" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>triangle-left</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="triangle-right" title="triangle-right">
            <span class="inner">
                <i class="codicon codicon-triangle-right" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>triangle-right</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="triangle-up" title="triangle-up">
            <span class="inner">
                <i class="codicon codicon-triangle-up" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>triangle-up</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="twitter" title="twitter">
            <span class="inner">
                <i class="codicon codicon-twitter" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>twitter</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="type-hierarchy-sub" title="type-hierarchy-sub">
            <span class="inner">
                <i class="codicon codicon-type-hierarchy-sub" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>type-hierarchy-sub</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="type-hierarchy-super" title="type-hierarchy-super">
            <span class="inner">
                <i class="codicon codicon-type-hierarchy-super" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>type-hierarchy-super</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="type-hierarchy" title="type-hierarchy">
            <span class="inner">
                <i class="codicon codicon-type-hierarchy" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>type-hierarchy</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="unfold" title="unfold">
            <span class="inner">
                <i class="codicon codicon-unfold" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>unfold</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="ungroup-by-ref-type" title="ungroup-by-ref-type">
            <span class="inner">
                <i class="codicon codicon-ungroup-by-ref-type" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>ungroup-by-ref-type</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="unlock" title="unlock">
            <span class="inner">
                <i class="codicon codicon-unlock" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>unlock</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="unmute" title="unmute">
            <span class="inner">
                <i class="codicon codicon-unmute" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>unmute</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="unverified" title="unverified">
            <span class="inner">
                <i class="codicon codicon-unverified" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>unverified</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="variable-group" title="variable-group">
            <span class="inner">
                <i class="codicon codicon-variable-group" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>variable-group</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="verified-filled" title="verified-filled">
            <span class="inner">
                <i class="codicon codicon-verified-filled" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>verified-filled</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="verified" title="verified">
            <span class="inner">
                <i class="codicon codicon-verified" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>verified</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="versions" title="versions">
            <span class="inner">
                <i class="codicon codicon-versions" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>versions</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="vm-active" title="vm-active">
            <span class="inner">
                <i class="codicon codicon-vm-active" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>vm-active</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="vm-connect" title="vm-connect">
            <span class="inner">
                <i class="codicon codicon-vm-connect" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>vm-connect</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="vm-outline" title="vm-outline">
            <span class="inner">
                <i class="codicon codicon-vm-outline" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>vm-outline</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="vm-running" title="vm-running">
            <span class="inner">
                <i class="codicon codicon-vm-running" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>vm-running</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="vm" title="vm">
            <span class="inner">
                <i class="codicon codicon-vm" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>vm</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="vr" title="vr">
            <span class="inner">
                <i class="codicon codicon-vr" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>vr</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="vscode-insiders" title="vscode-insiders">
            <span class="inner">
                <i class="codicon codicon-vscode-insiders" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>vscode-insiders</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="vscode" title="vscode">
            <span class="inner">
                <i class="codicon codicon-vscode" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>vscode</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="wand" title="wand">
            <span class="inner">
                <i class="codicon codicon-wand" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>wand</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="warning" title="warning">
            <span class="inner">
                <i class="codicon codicon-warning" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>warning</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="watch" title="watch">
            <span class="inner">
                <i class="codicon codicon-watch" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>watch</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="whitespace" title="whitespace">
            <span class="inner">
                <i class="codicon codicon-whitespace" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>whitespace</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="whole-word" title="whole-word">
            <span class="inner">
                <i class="codicon codicon-whole-word" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>whole-word</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="window" title="window">
            <span class="inner">
                <i class="codicon codicon-window" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>window</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="word-wrap" title="word-wrap">
            <span class="inner">
                <i class="codicon codicon-word-wrap" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>word-wrap</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="workspace-trusted" title="workspace-trusted">
            <span class="inner">
                <i class="codicon codicon-workspace-trusted" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>workspace-trusted</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="workspace-unknown" title="workspace-unknown">
            <span class="inner">
                <i class="codicon codicon-workspace-unknown" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>workspace-unknown</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="workspace-untrusted" title="workspace-untrusted">
            <span class="inner">
                <i class="codicon codicon-workspace-untrusted" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>workspace-untrusted</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="zoom-in" title="zoom-in">
            <span class="inner">
                <i class="codicon codicon-zoom-in" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>zoom-in</span>
            <span class='description'></span>
        </div>
        <div class="icon" data-name="zoom-out" title="zoom-out">
            <span class="inner">
                <i class="codicon codicon-zoom-out" aria-hidden="true"></i>
            </span>
            <br>
            <span class='label'>zoom-out</span>
            <span class='description'></span>
        </div>
    </div>

    <div id="notification">📋 Copied: <span id="notification-id"></span></div>
    <input type="text" id="copier">

<script type="text/javascript">
    let icons = document.getElementsByClassName('icon');
    let copier = document.getElementById('copier');
    let notification = document.getElementById('notification');
    let notificationText = document.getElementById('notification-id');
    let search = document.getElementById('search');
    let timer;
    let descriptions = [
        {
            name: 'account',
            description: 'person people face user contact outline'
        },
        {
            name: 'activate-breakpoints',
            description: 'dot circle toggle switch'
        },
        {
            name: 'add',
            description: 'combine plus add more new'
        },
        {
            name: 'archive',
            description: 'save box delivery package'
        },
        {
            name: 'arrow-both',
            description: 'switch swap'
        },
        {
            name: 'beaker',
            description: 'test lab tube tool'
        },
        {
            name: 'bell',
            description: 'alert notify notification'
        },
        {
            name: 'bell-dot',
            description: 'alert notify notification'
        },
        {
            name: 'bold',
            description: 'text format weight font style'
        },
        {
            name: 'book',
            description: 'library text read open'
        },
        {
            name: 'bookmark',
            description: 'book save later'
        },
        {
            name: 'briefcase',
            description: 'work workplace'
        },
        {
            name: 'broadcast',
            description: 'tower signal connect connection'
        },
        {
            name: 'browser',
            description: 'web internet page window'
        },
        {
            name: 'bug',
            description: 'error issue insect block'
        },
        {
            name: 'calendar',
            description: 'day time week month date schedule event planner'
        },
        {
            name: 'call-incoming',
            description: 'phone cell voice connection direction'
        },
        {
            name: 'call-outgoing',
            description: 'phone cell voice connection direction'
        },
        {
            name: 'case-sensitive',
            description: 'search filter option letters words'
        },
        {
            name: 'check-all',
            description: 'checkmark select everything checked mark complete finish done accept approve'
        },
        {
            name: 'check',
            description: 'checkmark select everything checked mark complete finish done accept approve'
        },
        {
            name: 'checklist',
            description: 'checkmark select everything checked mark complete finish done accept todo task text'
        },
        {
            name: 'chevron-down',
            description: 'twistie tree node folder fold collapse'
        },
        {
            name: 'chevron-left',
            description: 'twistie tree node folder fold collapse'
        },
        {
            name: 'chevron-right',
            description: 'twistie tree node folder fold collapse'
        },
        {
            name: 'chevron-up',
            description: 'twistie tree node folder fold collapse'
        },
        {
            name: 'chrome-close',
            description: 'menu bar window dismiss'
        },
        {
            name: 'chrome-maximize',
            description: 'menu bar window large increase'
        },
        {
            name: 'chrome-minimize',
            description: 'menu bar window small decrease'
        },
        {
            name: 'chrome-restore',
            description: 'menu bar window'
        },
        {
            name: 'circle-filled',
            description: 'dot round small bullet breakpoint'
        },
        {
            name: 'circle-large-filled',
            description: 'dot round bullet'
        },
        {
            name: 'circle-large',
            description: 'dot round bullet'
        },
        {
            name: 'circle',
            description: 'dot round small bullet unverified breakpoint'
        },
        {
            name: 'circle-slash',
            description: 'error block stop disable'
        },
        {
            name: 'circuit-board',
            description: 'iot device process lines'
        },
        {
            name: 'clear-all',
            description: 'reset remove'
        },
        {
            name: 'clippy',
            description: 'clipboard document edit copy'
        },
        {
            name: 'close-all',
            description: 'remove bulk'
        },
        {
            name: 'close',
            description: 'remove x cancel stop miltiply'
        },
        {
            name: 'cloud-download',
            description: 'install import'
        },
        {
            name: 'cloud-upload',
            description: 'export'
        },
        {
            name: 'cloud',
            description: 'online service'
        },
        {
            name: 'code',
            description: 'embed script programming server'
        },
        {
            name: 'collapse-all',
            description: 'bulk fold minimize'
        },
        {
            name: 'color-mode',
            description: 'switch dark light contrast mode toggle'
        },
        {
            name: 'comment-discussion',
            description: 'dialog message bubble chat'
        },
        {
            name: 'comment',
            description: 'dialog message bubble chat'
        },
        {
            name: 'credit-card',
            description: 'payment merchant money'
        },
        {
            name: 'dash',
            description: 'line minus subtract hyphen'
        },
        {
            name: 'dashboard',
            description: 'panel stats dial'
        },
        {
            name: 'database',
            description: 'sql db storage online cloud'
        },
        {
            name: 'debug-alt-small',
            description: 'run'
        },
        {
            name: 'debug-alt',
            description: 'run'
        },
        {
            name: 'debug-console',
            description: 'terminal command input compile build'
        },
        {
            name: 'debug-disconnect',
            description: 'stop unplug eject'
        },
        {
            name: 'desktop-download',
            description: 'install'
        },
        {
            name: 'device-camera-video',
            description: 'movie record capture'
        },
        {
            name: 'device-camera',
            description: 'capture picture image'
        },
        {
            name: 'device-mobile',
            description: 'phone handheld smartphone'
        },
        {
            name: 'diff-added',
            description: 'git change'
        },
        {
            name: 'diff-ignored',
            description: 'git change'
        },
        {
            name: 'diff-modified',
            description: 'git change'
        },
        {
            name: 'diff-renamed',
            description: 'git change'
        },
        {
            name: 'diff-removed',
            description: 'git change'
        },
        {
            name: 'play-circle',
            description: 'run'
        },
        {
            name: 'pass',
            description: 'play check checkmark outline issue closed'
        },
        {
            name: 'pass-filled',
            description: 'play check checkmark filled issue closed'
        },
        {
            name: 'play',
            description: 'run'
        },
    ];

    descriptions.some(function(item) {
        let findIcon = document.querySelectorAll(`[data-name="${item.name}"]`);
        findIcon[0].querySelector('.description').innerHTML += item.description;
    });

    for(i=0;i<icons.length;i++){
        let icon = icons[i]
        icon.onclick = function(e){
            let name = this.getAttribute('data-name');

            copier.value = name;
            copier.select();
            copier.setSelectionRange(0, 99999)
            document.execCommand('copy');

            notificationText.innerHTML = name;
            animateNotification();
            e.preventDefault();
        }
    }

    function animateNotification(){
        window.clearTimeout(timer);
        search.focus();
        notification.classList.add('show');
        timer = window.setTimeout(function(){
            notification.classList.remove('show');
        }, 3000);
    }

    function sanitizeText(string){
        return string = string.replace(/-/gi,' ');
    }

    search.addEventListener('keyup', function(){
        let filter = search.value.toUpperCase();
        let wrapper = document.getElementById('icons');
        let icon = wrapper.getElementsByTagName('div');

        for (i = 0; i < icon.length; i++) {
            let textInner = sanitizeText(icon[i].innerText)
            let textContents = sanitizeText(icon[i].textContent);
            compareText = textContents || textInner;

            if (compareText.toUpperCase().indexOf(filter) > -1) {
                icon[i].style.display = '';
            } else {
                icon[i].style.display = 'none';
            }
        }
    });

    search.focus();

</script>
</body>
</html>