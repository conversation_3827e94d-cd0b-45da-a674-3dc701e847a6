short_name,character,unicode
account,,EB99
activate-breakpoints,,EA97
add,,EA60
archive,,EA98
arrow-both,,EA99
arrow-circle-down,,EBFC
arrow-circle-left,,EBFD
arrow-circle-right,,EBFE
arrow-circle-up,,EBFF
arrow-down,,EA9A
arrow-left,,EA9B
arrow-right,,EA9C
arrow-small-down,,EA9D
arrow-small-left,,EA9E
arrow-small-right,,EA9F
arrow-small-up,,EAA0
arrow-swap,,EBCB
arrow-up,,EAA1
azure-devops,,EBE8
azure,,EBD8
beaker-stop,,EBE1
beaker,,EA79
bell-dot,,EB9A
bell-slash-dot,,EC09
bell-slash,,EC08
bell,,EAA2
blank,,EC03
bold,,EAA3
book,,EAA4
bookmark,,EAA5
bracket-dot,,EBE5
bracket-error,,EBE6
briefcase,,EAAC
broadcast,,EAAD
browser,,EAAE
bug,,EAAF
calendar,,EAB0
call-incoming,,EB92
call-outgoing,,EB93
case-sensitive,,EAB1
check-all,,EBB1
check,,EAB2
checklist,,EAB3
chevron-down,,EAB4
chevron-left,,EAB5
chevron-right,,EAB6
chevron-up,,EAB7
chip,,EC19
chrome-close,,EAB8
chrome-maximize,,EAB9
chrome-minimize,,EABA
chrome-restore,,EABB
circle-filled,,EA71
circle-large-filled,,EBB4
circle-large,,EBB5
circle-slash,,EABD
circle-small-filled,,EB8A
circle-small,,EC07
circle,,EABC
circuit-board,,EABE
clear-all,,EABF
clippy,,EAC0
close-all,,EAC1
close,,EA76
cloud-download,,EAC2
cloud-upload,,EAC3
cloud,,EBAA
code-oss,,EC2B
code,,EAC4
coffee,,EC15
collapse-all,,EAC5
color-mode,,EAC6
combine,,EBB6
comment-discussion,,EAC7
comment-draft,,EC0E
comment-unresolved,,EC0A
comment,,EA6B
compass-active,,EBD7
compass-dot,,EBD6
compass,,EBD5
copilot,,EC1E
copy,,EBCC
coverage,,EC2E
credit-card,,EAC9
dash,,EACC
dashboard,,EACD
database,,EACE
debug-all,,EBDC
debug-alt-small,,EBA8
debug-alt,,EB91
debug-breakpoint-conditional-unverified,,EAA6
debug-breakpoint-conditional,,EAA7
debug-breakpoint-data-unverified,,EAA8
debug-breakpoint-data,,EAA9
debug-breakpoint-function-unverified,,EB87
debug-breakpoint-function,,EB88
debug-breakpoint-log-unverified,,EAAA
debug-breakpoint-log,,EAAB
debug-breakpoint-unsupported,,EB8C
debug-console,,EB9B
debug-continue-small,,EBE0
debug-continue,,EACF
debug-coverage,,EBDD
debug-disconnect,,EAD0
debug-line-by-line,,EBD0
debug-pause,,EAD1
debug-rerun,,EBC0
debug-restart-frame,,EB90
debug-restart,,EAD2
debug-reverse-continue,,EB8E
debug-stackframe-active,,EB89
debug-stackframe,,EB8B
debug-start,,EAD3
debug-step-back,,EB8F
debug-step-into,,EAD4
debug-step-out,,EAD5
debug-step-over,,EAD6
debug-stop,,EAD7
debug,,EAD8
desktop-download,,EA78
device-camera-video,,EAD9
device-camera,,EADA
device-mobile,,EADB
diff-added,,EADC
diff-ignored,,EADD
diff-modified,,EADE
diff-multiple,,EC23
diff-removed,,EADF
diff-renamed,,EAE0
diff-single,,EC22
diff,,EAE1
discard,,EAE2
edit,,EA73
editor-layout,,EAE3
ellipsis,,EA7C
empty-window,,EAE4
error-small,,EBFB
error,,EA87
exclude,,EAE5
expand-all,,EB95
export,,EBAC
extensions,,EAE6
eye-closed,,EAE7
eye,,EA70
feedback,,EB96
file-binary,,EAE8
file-code,,EAE9
file-media,,EAEA
file-pdf,,EAEB
file-submodule,,EAEC
file-symlink-directory,,EAED
file-symlink-file,,EAEE
file-zip,,EAEF
file,,EA7B
files,,EAF0
filter-filled,,EBCE
filter,,EAF1
flame,,EAF2
fold-down,,EAF3
fold-up,,EAF4
fold,,EAF5
folder-active,,EAF6
folder-library,,EBDF
folder-opened,,EAF7
folder,,EA83
game,,EC17
gear,,EAF8
gift,,EAF9
gist-secret,,EAFA
git-commit,,EAFC
git-compare,,EAFD
git-fetch,,F101
git-merge,,EAFE
git-pull-request-closed,,EBDA
git-pull-request-create,,EBBC
git-pull-request-draft,,EBDB
git-pull-request-go-to-changes,,EC0B
git-pull-request-new-changes,,EC0C
git-pull-request,,EA64
git-stash-apply,,EC27
git-stash-pop,,EC28
git-stash,,EC26
github-action,,EAFF
github-alt,,EB00
github-inverted,,EBA1
github-project,,EC2F
github,,EA84
globe,,EB01
go-to-file,,EA94
go-to-search,,EC32
grabber,,EB02
graph-left,,EBAD
graph-line,,EBE2
graph-scatter,,EBE3
graph,,EB03
gripper,,EB04
group-by-ref-type,,EB97
heart-filled,,EC04
heart,,EB05
history,,EA82
home,,EB06
horizontal-rule,,EB07
hubot,,EB08
inbox,,EB09
indent,,EBF9
info,,EA74
insert,,EC11
inspect,,EBD1
issue-draft,,EBD9
issue-reopened,,EB0B
issues,,EB0C
italic,,EB0D
jersey,,EB0E
json,,EB0F
kebab-vertical,,EB10
key,,EB11
law,,EB12
layers-active,,EBD4
layers-dot,,EBD3
layers,,EBD2
layout-activitybar-left,,EBEC
layout-activitybar-right,,EBED
layout-centered,,EBF7
layout-menubar,,EBF6
layout-panel-center,,EBEF
layout-panel-justify,,EBF0
layout-panel-left,,EBEE
layout-panel-off,,EC01
layout-panel-right,,EBF1
layout-panel,,EBF2
layout-sidebar-left-off,,EC02
layout-sidebar-left,,EBF3
layout-sidebar-right-off,,EC00
layout-sidebar-right,,EBF4
layout-statusbar,,EBF5
layout,,EBEB
library,,EB9C
lightbulb-autofix,,EB13
lightbulb-sparkle,,EC1F
lightbulb,,EA61
link-external,,EB14
link,,EB15
list-filter,,EB83
list-flat,,EB84
list-ordered,,EB16
list-selection,,EB85
list-tree,,EB86
list-unordered,,EB17
live-share,,EB18
loading,,EB19
location,,EB1A
lock-small,,EBE7
lock,,EA75
magnet,,EBAE
mail-read,,EB1B
mail,,EB1C
map-filled,,EC06
map-vertical-filled,,EC31
map-vertical,,EC30
map,,EC05
markdown,,EB1D
megaphone,,EB1E
mention,,EB1F
menu,,EB94
merge,,EBAB
mic-filled,,EC1C
mic,,EC12
milestone,,EB20
mirror,,EA69
mortar-board,,EB21
move,,EB22
multiple-windows,,EB23
music,,EC1B
mute,,EB24
new-file,,EA7F
new-folder,,EA80
newline,,EBEA
no-newline,,EB25
note,,EB26
notebook-template,,EBBF
notebook,,EBAF
octoface,,EB27
open-preview,,EB28
organization,,EA7E
output,,EB9D
package,,EB29
paintcan,,EB2A
pass-filled,,EBB3
pass,,EBA4
percentage,,EC33
person-add,,EBCD
person,,EA67
piano,,EC1A
pie-chart,,EBE4
pin,,EB2B
pinned-dirty,,EBB2
pinned,,EBA0
play-circle,,EBA6
play,,EB2C
plug,,EB2D
preserve-case,,EB2E
preview,,EB2F
primitive-square,,EA72
project,,EB30
pulse,,EB31
question,,EB32
quote,,EB33
radio-tower,,EB34
reactions,,EB35
record-keys,,EA65
record-small,,EBFA
record,,EBA7
redo,,EBB0
references,,EB36
refresh,,EB37
regex,,EB38
remote-explorer,,EB39
remote,,EB3A
remove,,EB3B
replace-all,,EB3C
replace,,EB3D
reply,,EA7D
repo-clone,,EB3E
repo-fetch,,EC1D
repo-force-push,,EB3F
repo-forked,,EA63
repo-pull,,EB40
repo-push,,EB41
repo,,EA62
report,,EB42
request-changes,,EB43
robot,,EC20
rocket,,EB44
root-folder-opened,,EB45
root-folder,,EB46
rss,,EB47
ruby,,EB48
run-above,,EBBD
run-all-coverage,,EC2D
run-all,,EB9E
run-below,,EBBE
run-coverage,,EC2C
run-errors,,EBDE
save-all,,EB49
save-as,,EB4A
save,,EB4B
screen-full,,EB4C
screen-normal,,EB4D
search-fuzzy,,EC0D
search-stop,,EB4E
search,,EA6D
send,,EC0F
server-environment,,EBA3
server-process,,EBA2
server,,EB50
settings-gear,,EB51
settings,,EB52
share,,EC25
shield,,EB53
sign-in,,EA6F
sign-out,,EA6E
smiley,,EB54
snake,,EC16
sort-precedence,,EB55
source-control,,EA68
sparkle-filled,,EC21
sparkle,,EC10
split-horizontal,,EB56
split-vertical,,EB57
squirrel,,EB58
star-empty,,EA6A
star-full,,EB59
star-half,,EB5A
stop-circle,,EBA5
surround-with,,EC24
symbol-array,,EA8A
symbol-boolean,,EA8F
symbol-class,,EB5B
symbol-color,,EB5C
symbol-constant,,EB5D
symbol-enum-member,,EB5E
symbol-enum,,EA95
symbol-event,,EA86
symbol-field,,EB5F
symbol-file,,EB60
symbol-interface,,EB61
symbol-key,,EA93
symbol-keyword,,EB62
symbol-method,,EA8C
symbol-misc,,EB63
symbol-namespace,,EA8B
symbol-numeric,,EA90
symbol-operator,,EB64
symbol-parameter,,EA92
symbol-property,,EB65
symbol-ruler,,EA96
symbol-snippet,,EB66
symbol-string,,EB8D
symbol-structure,,EA91
symbol-variable,,EA88
sync-ignored,,EB9F
sync,,EA77
table,,EBB7
tag,,EA66
target,,EBF8
tasklist,,EB67
telescope,,EB68
terminal-bash,,EBCA
terminal-cmd,,EBC4
terminal-debian,,EBC5
terminal-linux,,EBC6
terminal-powershell,,EBC7
terminal-tmux,,EBC8
terminal-ubuntu,,EBC9
terminal,,EA85
text-size,,EB69
three-bars,,EB6A
thumbsdown-filled,,EC13
thumbsdown,,EB6B
thumbsup-filled,,EC14
thumbsup,,EB6C
tools,,EB6D
trash,,EA81
triangle-down,,EB6E
triangle-left,,EB6F
triangle-right,,EB70
triangle-up,,EB71
twitter,,EB72
type-hierarchy-sub,,EBBA
type-hierarchy-super,,EBBB
type-hierarchy,,EBB9
unfold,,EB73
ungroup-by-ref-type,,EB98
unlock,,EB74
unmute,,EB75
unverified,,EB76
variable-group,,EBB8
verified-filled,,EBE9
verified,,EB77
versions,,EB78
vm-active,,EB79
vm-connect,,EBA9
vm-outline,,EB7A
vm-running,,EB7B
vm,,EA7A
vr,,EC18
vscode-insiders,,EC2A
vscode,,EC29
wand,,EBCF
warning,,EA6C
watch,,EB7C
whitespace,,EB7D
whole-word,,EB7E
window,,EB7F
word-wrap,,EB80
workspace-trusted,,EBC1
workspace-unknown,,EBC3
workspace-untrusted,,EBC2
zoom-in,,EB81
zoom-out,,EB82
