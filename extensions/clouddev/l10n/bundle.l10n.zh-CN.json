{"Type": "数据库类型", "Username": "用户名", "Password": "密码", "Host": "主机", "Port": "端口", "Are you sure to delete?": "确定删除吗？", "Delete Project failed.": "删除 Project 失败。", "The current project status is unavailable. Now it cannot be opened. Do you want to delete its local ssh configuration?": "当前项目状态不可用，现在不能打开。你想删除关于它的本地ssh配置吗？", "Protocol": "协议", "Address": "地址", "Open in Browser": "在浏览器中打开", "Preview in Editor": "在编辑器中预览", "Open Database Web Terminal": "打开数据库 Web 终端", "Failed to write SSH configuration": "写入 SSH 配置失败", "Failed to write SSH private key": "写入 SSH 私钥失败", "Please install \"Remote - SSH\" extension to connect to a devbox workspace.": "请安装 \"Remote - SSH\" 扩展以连接到 Devbox 工作区。", "Install": "安装", "Cancel": "取消", "Project created successfully": "创建项目成功", "Failed to create project": "创建项目失败", "Cancel to create project": "已取消创建项目", "Login successful": "登录成功", "Project Configuration": "项目配置", "Login to enjoy cloud development": "登录立享云端开发体验", "Login": "登录", "No project data, please create or select a project first.": "暂无项目数据，请先创建或选择一个项目。", "Create Project": "新建项目", "Project Name": "项目名称", "Please enter project name": "请输入项目名称", "Project name must start with a letter and contain only letters, numbers, or underscores. (length 2-60)": "项目名称需以字母开头，仅包含字母、数字或下划线，长度为2-60个字符", "Language": "语言", "Version": "版本", "Select Version": "请选择版本", "Select Language First": "请先选择语言", "Service Port": "服务端口", "Create": "创建", "Creating...": "创建中...", "Public URL": "公网链接", "Open in New Window": "在新窗口打开", "Open in Current Window": "当前窗口打开", "Settings": "设置", "Delete": "删除", "Stop": "停止", "Start": "启动", "Template": "模板", "Server Info": "服务器信息", "Dev Mode": "开发模式", "Database": "数据库", "Domain": "域名", "Export Ports": "端口", "Load More": "加载更多", "No Image History": "暂无镜像历史", "Deploy": "部署", "More Actions": "更多操作", "Insufficient Balance": "账户余额不足", "Basic Info": "基本信息", "Image History": "镜像历史", "Create Image": "生成镜像", "Status": "状态", "Create Time": "创建时间", "Description": "描述", "Actions": "操作", "Loading...": "加载中...", "Local": "本地", "Remote": "远程", "Search": "搜索", "Success": "生成成功", "Pending": "生成中", "Failed": "生成失败", "Current project status: Pending": "当前项目状态: 环境准备中", "Current project status: Running": "当前项目状态: 运行中", "Current project status: Shutting": "当前项目状态: 关机中", "Current project status: Shutdown": "当前项目状态: 已关机", "Project shutdown successful, the SSH connection will be unavailable.": "项目关机成功，SSH连接将不可用", "Project startup successful": "项目启动成功", "Please shutdown first to save state": "请先关机保存状态再操作", "Generate Image": "生成镜像"}