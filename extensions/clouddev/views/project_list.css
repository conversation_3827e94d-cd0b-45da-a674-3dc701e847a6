.app-container {
	container-type: inline-size;
}

@container (min-width: 200px) and (max-width: 250px) {
	.app-container .project-card {
		grid-template-columns: 24px auto 1fr;
		gap: 4px;
	}
	.app-container .project-card:hover,
	.app-container .project-card.selected {
		grid-template-columns: 24px minmax(20px, 20%) minmax(20px, 20%) 1fr;
	}
	/* .app-container .project-card:hover .tag,
	.app-container .project-card.selected .tag {
		display: none;
	} */
	.app-container .project-card .project-avatar {
		width: 24px;
		height: 24px;
		font-size: 10px;
		font-weight: normal;
	}
	/* .project-card:hover .project-actions, .project-card.selected .project-actions {
		grid-column: span 3;
	} */

}

@container (max-width: 200px) {
	.app-container .project-card {
		grid-template-columns: auto 1fr;
		gap: 4px;
	}
	.app-container .project-card:hover,
	.app-container .project-card.selected {
		grid-template-columns: minmax(20px, 20%) minmax(20px, 20%) 1fr;
	}
	/* .app-container .project-card:hover .tag,
	.app-container .project-card.selected .tag {
		display: none;
	} */
	.app-container .project-card .project-avatar {
		display: none;
	}
	/* .project-card:hover .project-actions, .project-card.selected .project-actions {
		grid-column: span 3;
	} */

}

body {
	font-family: 'Segoe UI', 'Arial', sans-serif;
	margin: 0;
	padding: 0;
	height: 100vh;
	overflow: hidden;
}

.app-container {
	position: relative;
	width: 100vw;
	height: 100vh;
	overflow-y: auto;
    overflow-x: hidden;
}

.search-container {
	display: flex;
	align-items: center;
	margin: 8px;
	position: relative;
}

.search-icon {
	position: absolute;
	left: 8px;
	top: 6px;
	transform: rotate(-90deg);
	color: var(--vscode-input-placeholderForeground);
	pointer-events: none;
	z-index: 1;
}

.codicon {
	font-family: "codicon" !important;
	font-size: 16px;
	line-height: 1;
}

.search-input {
	width: 100%;
	margin: 0;
}

vscode-text-field::part(control) {
	padding-left: 30px !important;
}

.project-list {
	display: flex;
	flex-direction: column;
}

.project-card {
	display: grid;
	grid-template-columns: 40px auto 1fr;
	cursor: pointer;
	padding: 12px;
	gap: 4px 8px;
	border: 1px solid var(--background);
}

.project-card.selected,
.project-card:hover {
	color: var(--vscode-list-inactiveSelectionForeground);
	background-color: var(--vscode-list-inactiveSelectionBackground);
}

.project-avatar {
	width: 40px;
	height: 40px;
	border-radius: 50%;
	background: #6c63ff;
	color: #fff;
	font-weight: 700;
	font-size: 16px;
	display: flex;
	align-items: center;
	justify-content: center;
	flex-shrink: 0;
	grid-row: span 3;
}

.project-title-row {
	display: flex;
	align-items: center;
	grid-column: span 2;
	gap: 4px;
}

.project-title {
	font-size: 14px;
	font-weight: 500;
	word-break: break-all;
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
}

.project-desc {
	font-size: 12px;
	word-break: break-all;
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
	min-width: 0;
	grid-column: span 2;
	color: var(--vscode-descriptionForeground);
}

.tag {
	display: inline-flex;
	align-items: center;
	gap: 4px;
	margin-top: 4px;
	font-size: 12px;
	color: var(--vscode-descriptionForeground);
	line-height: 1;
}

.tag span {
	display: block;
	min-width: 0;
	overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;

}

.tag .iconfont {
	font-size: 14px;
}

.project-template-icon {
	width: 14px;
	height: 14px;
}

.project-actions {
	display: none;
	align-items: center;
	gap: 8px;
	margin-top: 4px;
	align-self: flex-end;
	justify-self: flex-end;
}

.project-card:hover,
.project-card.selected {
	grid-template-columns: 40px auto auto 1fr;
}

.project-card:hover .project-title-row,
.project-card.selected .project-title-row {
	grid-column: span 3;
}

.project-card:hover .project-desc,
.project-card.selected .project-desc {
	grid-column: span 3;
}

.project-card:hover .project-actions,
.project-card.selected .project-actions {
	display: flex;
}

.project-action-btn {
	width: 14px;
	height: 14px;
	background: none;
	border: none;
	outline: none;
	cursor: pointer;
	padding: 0;
	display: flex !important;
	align-items: center;
	justify-content: center;
	color: var(--vscode-icon-foreground);
	opacity: 0.85;
	transition: opacity 0.12s;
}

.project-action-btn:hover {
	opacity: 1;
	color: var(--vscode-icon-foreground);
}

.project-action-btn::before {
	font-size: 14px;
}

.project-status-dot {
	font-size: 14px;
	vertical-align: middle;
}

vscode-text-field::part(root) {
	border-width: 0;
}

vscode-text-field::part(control) {
	width: 100%;
	height: 28px;
	padding: 0 6px;
	border: 1px solid var(--vscode-dropdown-background);
	border-radius: 6px;
	background: var(--input-background);
	color: var(--vscode-dropdown-foreground);
	font-family: var(--vscode-font-family);
	box-sizing: border-box;
}

vscode-text-field::part(control):focus,
vscode-text-field::part(control):hover {
	outline: 1px solid var(--vscode-focusBorder);
	outline-offset: -1px;
}

.project-action-btn.disabled {
    cursor: not-allowed;
}

/* 呼吸动画效果 */
@keyframes breathing {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.4;
    }
}

.breathing-animation {
    animation: breathing 2s ease-in-out infinite;
}
