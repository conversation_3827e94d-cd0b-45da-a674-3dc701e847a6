<!DOCTYPE html>
<html>
<head>
    <link href="#{webview.iconfont}" rel="stylesheet" />
    <link href="#{webview.css}" rel="stylesheet" />
</head>
<body>
    <div class="section">
        <div class="section-title">#{Basic Info}</div>
        <div class="info-grid">
            <div class="info-label">#{Server Info}:</div>
            <div class="info-value" title="#{project.serverInfo}">#{project.serverInfo}</div>

            <div class="info-label">#{Dev Mode}:</div>
            <div class="info-value" title="#{project.devMode}">#{project.devMode}</div>

            <div class="info-label">#{Template}:</div>
            <div class="info-value" title="#{project.projectTemplateName}">#{project.projectTemplateName}</div>

            <div class="info-label">#{Database}:</div>
            <div class="info-value" title="#{project.databaseInfo}">#{project.databaseInfo}</div>

            <div class="info-label">#{Domain}:</div>
            <div class="info-value" title="#{project.url}">#{project.displayUrl}</div>

            <div class="info-label">#{Export Ports}:</div>
            <div class="info-value" title="#{project.port}">#{project.port}</div>
        </div>
    </div>

    <div class="section #{project.displayImage}">
        <div class="section-title">
            #{Image History}
            <button class="create-btn" #{project.createImageButton}>
                + #{Create Image}
            </button>
        </div>
        <div class="table-container">
            <table class="table">
                <thead class="table-header">
                    <tr>
                        <th>#{Version}</th>
                        <th>#{Status}</th>
                        <th>#{Create Time}</th>
                        <th>#{Description}</th>
                        <th>#{Actions}</th>
                    </tr>
                </thead>
                <tbody>
                    <tr><td colspan="5" class="no-data">#{No Image History}</td></tr>
                </tbody>
            </table>
            <div class="loading" id="loading">
                #{Loading...}
            </div>
        </div>
    </div>
    <script>
        window.accountAmount = Number('#{accountAmount}');
        window.hasMoreData = '#{hasMoreData}' === 'true';
    </script>
    <script src="#{webview.js}"></script>
</body>
</html>
