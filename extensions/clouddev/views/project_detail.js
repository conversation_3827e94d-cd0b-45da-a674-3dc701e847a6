const vscode = acquireVsCodeApi();
let currentPage = 1;
const pageSize = 20;
let isLoading = false;
let hasMoreData = window.hasMoreData;

function createImage() {
	vscode.postMessage({
		command: 'createImage'
	});
}

function deploy(imageTag) {
	vscode.postMessage({
		command: 'deploy',
		imageTag: imageTag
	});
}

// 存储国际化文本
let i18nTexts = {
	Success: 'Success',
	Pending: 'Pending',
	Failed: 'Failed',
	Deploy: 'Deploy',
	'Insufficient Balance': 'Insufficient Balance',
	'More Actions': 'More Actions',
	'No Image History': 'No Image History'
};

function getStatusText(status) {
	switch (status) {
		case 'Success':
			return i18nTexts.Success;
		case 'Pending':
			return i18nTexts.Pending;
		case 'Failed':
			return i18nTexts.Failed;
		default:
			return status;
	}
}

function renderReleaseRow(release) {
	const statusText = getStatusText(release.status);
	const formattedDate = new Date(release.createdAt).toLocaleString('zh-CN');

	return `
		<tr>
			<td class="table-cell">${release.imageTag}</td>
			<td class="table-cell">${statusText}</td>
			<td class="table-cell">${formattedDate}</td>
			<td class="table-cell" title="${release.description}">${release.description}</td>
			<td class="table-cell">
				<button class="action-btn" title="${i18nTexts.Deploy}"
					data-image-tag="${release.imageTag}"
					${release.status === 'Pending' ? 'disabled' : ''}>
					${i18nTexts.Deploy}
				</button>
			</td>
		</tr>
	`;
	// <span class="iconfont icon-bushu"></span>
	// <button class="action-btn" title="${i18nTexts['More Actions']}">
	// 	<span class="iconfont icon-gengduo"></span>
	// </button>
}

function appendReleases(releaseTable, releases) {
	// 如果是第一页，清空表格内容
	if (currentPage === 1) {
		releaseTable.innerHTML = '';
	}

	if (!releases?.records?.length) {
		// 如果没有数据，显示暂无数据提示
		releaseTable.innerHTML = `<tr><td colspan="5" class="no-data">${i18nTexts['No Image History']}</td></tr>`;
		return;
	}

	const rows = releases.records.map(renderReleaseRow).join('');
	releaseTable.insertAdjacentHTML('beforeend', rows);
}

function loadMore() {
	if (isLoading || !hasMoreData) return;

	isLoading = true;
	document.getElementById('loading').style.display = 'block';

	currentPage += 1;
	vscode.postMessage({
		command: 'loadMoreReleases',
		page: currentPage,
		pageSize: pageSize
	});
}

// 初始化事件监听
document.addEventListener('DOMContentLoaded', () => {
	// 初始化时请求国际化文本
	vscode.postMessage({ command: 'getI18nTexts' });
	const releaseTable = document.querySelector('.table tbody');
	const loadingEl = document.getElementById('loading');

	// 使用事件委托处理部署按钮点击
	releaseTable?.addEventListener('click', (event) => {
		const target = event.target;
		const deployButton = target.closest('.action-btn');

		if (deployButton && !deployButton.hasAttribute('disabled')) {
			const imageTag = deployButton.getAttribute('data-image-tag');
			if (imageTag) {
				deploy(imageTag);
			}
		}
	});

	// 监听页面滚动事件
	window.addEventListener('scroll', () => {
		if (isLoading || !hasMoreData) return;

		const scrollHeight = document.documentElement.scrollHeight;
		const scrollTop = document.documentElement.scrollTop;
		const clientHeight = document.documentElement.clientHeight;

		// 当滚动到距离底部100px时触发加载
		if (scrollHeight - scrollTop - clientHeight < 100) {
			loadMore();
		}
	});

	// 处理来自extension的消息
	window.addEventListener('message', event => {
		const message = event.data;
		switch (message.command) {
			case 'refresh':
				currentPage = 1;
				vscode.postMessage({
					command: 'initReleases',
					page: currentPage,
					pageSize: pageSize,
				});
				break;
			case 'setI18nTexts':
				i18nTexts = { ...i18nTexts, ...message.texts };
				// 在获取到国际化文本后再请求发布列表
				currentPage = 1;
				vscode.postMessage({
					command: 'initReleases',
					page: currentPage,
					pageSize: pageSize,
				});
				break;
			case 'appendReleases':
				const releases = message.releases;
				isLoading = false;
				loadingEl.style.display = 'none';
				hasMoreData = releases.total > currentPage * pageSize;
				appendReleases(releaseTable, releases);
				break;
		}
	});
});
