const e=function(){if("undefined"!=typeof globalThis)return globalThis;if("undefined"!=typeof global)return global;if("undefined"!=typeof self)return self;if("undefined"!=typeof window)return window;try{return new Function("return this")()}catch(e){return{}}}();void 0===e.trustedTypes&&(e.trustedTypes={createPolicy:(e,t)=>t});const t={configurable:!1,enumerable:!1,writable:!1};void 0===e.FAST&&Reflect.defineProperty(e,"FAST",Object.assign({value:Object.create(null)},t));const i=e.FAST;if(void 0===i.getById){const e=Object.create(null);Reflect.defineProperty(i,"getById",Object.assign({value(t,i){let s=e[t];return void 0===s&&(s=i?e[t]=i():null),s}},t))}const s=Object.freeze([]);function o(){const e=new WeakMap;return function(t){let i=e.get(t);if(void 0===i){let s=Reflect.getPrototypeOf(t);for(;void 0===i&&null!==s;)i=e.get(s),s=Reflect.getPrototypeOf(s);i=void 0===i?[]:i.slice(0),e.set(t,i)}return i}}const n=e.FAST.getById(1,(()=>{const t=[],i=[];function s(){if(i.length)throw i.shift()}function o(e){try{e.call()}catch(e){i.push(e),setTimeout(s,0)}}function n(){let e=0;for(;e<t.length;)if(o(t[e]),e++,e>1024){for(let i=0,s=t.length-e;i<s;i++)t[i]=t[i+e];t.length-=e,e=0}t.length=0}return Object.freeze({enqueue:function(i){t.length<1&&e.requestAnimationFrame(n),t.push(i)},process:n})})),r=e.trustedTypes.createPolicy("fast-html",{createHTML:e=>e});let a=r;const l=`fast-${Math.random().toString(36).substring(2,8)}`,d=`${l}{`,c=`}${l}`,h=Object.freeze({supportsAdoptedStyleSheets:Array.isArray(document.adoptedStyleSheets)&&"replace"in CSSStyleSheet.prototype,setHTMLPolicy(e){if(a!==r)throw new Error("The HTML policy can only be set once.");a=e},createHTML:e=>a.createHTML(e),isMarker:e=>e&&8===e.nodeType&&e.data.startsWith(l),extractDirectiveIndexFromMarker:e=>parseInt(e.data.replace(`${l}:`,"")),createInterpolationPlaceholder:e=>`${d}${e}${c}`,createCustomAttributePlaceholder(e,t){return`${e}="${this.createInterpolationPlaceholder(t)}"`},createBlockPlaceholder:e=>`\x3c!--${l}:${e}--\x3e`,queueUpdate:n.enqueue,processUpdates:n.process,nextUpdate:()=>new Promise(n.enqueue),setAttribute(e,t,i){null==i?e.removeAttribute(t):e.setAttribute(t,i)},setBooleanAttribute(e,t,i){i?e.setAttribute(t,""):e.removeAttribute(t)},removeChildNodes(e){for(let t=e.firstChild;null!==t;t=e.firstChild)e.removeChild(t)},createTemplateWalker:e=>document.createTreeWalker(e,133,null,!1)});class u{constructor(e,t){this.sub1=void 0,this.sub2=void 0,this.spillover=void 0,this.source=e,this.sub1=t}has(e){return void 0===this.spillover?this.sub1===e||this.sub2===e:-1!==this.spillover.indexOf(e)}subscribe(e){const t=this.spillover;if(void 0===t){if(this.has(e))return;if(void 0===this.sub1)return void(this.sub1=e);if(void 0===this.sub2)return void(this.sub2=e);this.spillover=[this.sub1,this.sub2,e],this.sub1=void 0,this.sub2=void 0}else{-1===t.indexOf(e)&&t.push(e)}}unsubscribe(e){const t=this.spillover;if(void 0===t)this.sub1===e?this.sub1=void 0:this.sub2===e&&(this.sub2=void 0);else{const i=t.indexOf(e);-1!==i&&t.splice(i,1)}}notify(e){const t=this.spillover,i=this.source;if(void 0===t){const t=this.sub1,s=this.sub2;void 0!==t&&t.handleChange(i,e),void 0!==s&&s.handleChange(i,e)}else for(let s=0,o=t.length;s<o;++s)t[s].handleChange(i,e)}}class p{constructor(e){this.subscribers={},this.sourceSubscribers=null,this.source=e}notify(e){var t;const i=this.subscribers[e];void 0!==i&&i.notify(e),null===(t=this.sourceSubscribers)||void 0===t||t.notify(e)}subscribe(e,t){var i;if(t){let i=this.subscribers[t];void 0===i&&(this.subscribers[t]=i=new u(this.source)),i.subscribe(e)}else this.sourceSubscribers=null!==(i=this.sourceSubscribers)&&void 0!==i?i:new u(this.source),this.sourceSubscribers.subscribe(e)}unsubscribe(e,t){var i;if(t){const i=this.subscribers[t];void 0!==i&&i.unsubscribe(e)}else null===(i=this.sourceSubscribers)||void 0===i||i.unsubscribe(e)}}const f=i.getById(2,(()=>{const e=/(:|&&|\|\||if)/,t=new WeakMap,i=h.queueUpdate;let s,n=e=>{throw new Error("Must call enableArrayObservation before observing arrays.")};function r(e){let i=e.$fastController||t.get(e);return void 0===i&&(Array.isArray(e)?i=n(e):t.set(e,i=new p(e))),i}const a=o();class l{constructor(e){this.name=e,this.field=`_${e}`,this.callback=`${e}Changed`}getValue(e){return void 0!==s&&s.watch(e,this.name),e[this.field]}setValue(e,t){const i=this.field,s=e[i];if(s!==t){e[i]=t;const o=e[this.callback];"function"==typeof o&&o.call(e,s,t),r(e).notify(this.name)}}}class d extends u{constructor(e,t,i=!1){super(e,t),this.binding=e,this.isVolatileBinding=i,this.needsRefresh=!0,this.needsQueue=!0,this.first=this,this.last=null,this.propertySource=void 0,this.propertyName=void 0,this.notifier=void 0,this.next=void 0}observe(e,t){this.needsRefresh&&null!==this.last&&this.disconnect();const i=s;s=this.needsRefresh?this:void 0,this.needsRefresh=this.isVolatileBinding;const o=this.binding(e,t);return s=i,o}disconnect(){if(null!==this.last){let e=this.first;for(;void 0!==e;)e.notifier.unsubscribe(this,e.propertyName),e=e.next;this.last=null,this.needsRefresh=this.needsQueue=!0}}watch(e,t){const i=this.last,o=r(e),n=null===i?this.first:{};if(n.propertySource=e,n.propertyName=t,n.notifier=o,o.subscribe(this,t),null!==i){if(!this.needsRefresh){let t;s=void 0,t=i.propertySource[i.propertyName],s=this,e===t&&(this.needsRefresh=!0)}i.next=n}this.last=n}handleChange(){this.needsQueue&&(this.needsQueue=!1,i(this))}call(){null!==this.last&&(this.needsQueue=!0,this.notify(this))}records(){let e=this.first;return{next:()=>{const t=e;return void 0===t?{value:void 0,done:!0}:(e=e.next,{value:t,done:!1})},[Symbol.iterator]:function(){return this}}}}return Object.freeze({setArrayObserverFactory(e){n=e},getNotifier:r,track(e,t){void 0!==s&&s.watch(e,t)},trackVolatile(){void 0!==s&&(s.needsRefresh=!0)},notify(e,t){r(e).notify(t)},defineProperty(e,t){"string"==typeof t&&(t=new l(t)),a(e).push(t),Reflect.defineProperty(e,t.name,{enumerable:!0,get:function(){return t.getValue(this)},set:function(e){t.setValue(this,e)}})},getAccessors:a,binding(e,t,i=this.isVolatileBinding(e)){return new d(e,t,i)},isVolatileBinding:t=>e.test(t.toString())})}));function b(e,t){f.defineProperty(e,t)}const g=i.getById(3,(()=>{let e=null;return{get:()=>e,set(t){e=t}}}));class m{constructor(){this.index=0,this.length=0,this.parent=null,this.parentContext=null}get event(){return g.get()}get isEven(){return this.index%2==0}get isOdd(){return this.index%2!=0}get isFirst(){return 0===this.index}get isInMiddle(){return!this.isFirst&&!this.isLast}get isLast(){return this.index===this.length-1}static setEvent(e){g.set(e)}}f.defineProperty(m.prototype,"index"),f.defineProperty(m.prototype,"length");const v=Object.seal(new m);class y{constructor(){this.targetIndex=0}}class x extends y{constructor(){super(...arguments),this.createPlaceholder=h.createInterpolationPlaceholder}}class C extends y{constructor(e,t,i){super(),this.name=e,this.behavior=t,this.options=i}createPlaceholder(e){return h.createCustomAttributePlaceholder(this.name,e)}createBehavior(e){return new this.behavior(e,this.options)}}function w(e,t){this.source=e,this.context=t,null===this.bindingObserver&&(this.bindingObserver=f.binding(this.binding,this,this.isBindingVolatile)),this.updateTarget(this.bindingObserver.observe(e,t))}function $(e,t){this.source=e,this.context=t,this.target.addEventListener(this.targetName,this)}function k(){this.bindingObserver.disconnect(),this.source=null,this.context=null}function I(){this.bindingObserver.disconnect(),this.source=null,this.context=null;const e=this.target.$fastView;void 0!==e&&e.isComposed&&(e.unbind(),e.needsBindOnly=!0)}function T(){this.target.removeEventListener(this.targetName,this),this.source=null,this.context=null}function O(e){h.setAttribute(this.target,this.targetName,e)}function S(e){h.setBooleanAttribute(this.target,this.targetName,e)}function A(e){if(null==e&&(e=""),e.create){this.target.textContent="";let t=this.target.$fastView;void 0===t?t=e.create():this.target.$fastTemplate!==e&&(t.isComposed&&(t.remove(),t.unbind()),t=e.create()),t.isComposed?t.needsBindOnly&&(t.needsBindOnly=!1,t.bind(this.source,this.context)):(t.isComposed=!0,t.bind(this.source,this.context),t.insertBefore(this.target),this.target.$fastView=t,this.target.$fastTemplate=e)}else{const t=this.target.$fastView;void 0!==t&&t.isComposed&&(t.isComposed=!1,t.remove(),t.needsBindOnly?t.needsBindOnly=!1:t.unbind()),this.target.textContent=e}}function R(e){this.target[this.targetName]=e}function E(e){const t=this.classVersions||Object.create(null),i=this.target;let s=this.version||0;if(null!=e&&e.length){const o=e.split(/\s+/);for(let e=0,n=o.length;e<n;++e){const n=o[e];""!==n&&(t[n]=s,i.classList.add(n))}}if(this.classVersions=t,this.version=s+1,0!==s){s-=1;for(const e in t)t[e]===s&&i.classList.remove(e)}}class D extends x{constructor(e){super(),this.binding=e,this.bind=w,this.unbind=k,this.updateTarget=O,this.isBindingVolatile=f.isVolatileBinding(this.binding)}get targetName(){return this.originalTargetName}set targetName(e){if(this.originalTargetName=e,void 0!==e)switch(e[0]){case":":if(this.cleanedTargetName=e.substr(1),this.updateTarget=R,"innerHTML"===this.cleanedTargetName){const e=this.binding;this.binding=(t,i)=>h.createHTML(e(t,i))}break;case"?":this.cleanedTargetName=e.substr(1),this.updateTarget=S;break;case"@":this.cleanedTargetName=e.substr(1),this.bind=$,this.unbind=T;break;default:this.cleanedTargetName=e,"class"===e&&(this.updateTarget=E)}}targetAtContent(){this.updateTarget=A,this.unbind=I}createBehavior(e){return new P(e,this.binding,this.isBindingVolatile,this.bind,this.unbind,this.updateTarget,this.cleanedTargetName)}}class P{constructor(e,t,i,s,o,n,r){this.source=null,this.context=null,this.bindingObserver=null,this.target=e,this.binding=t,this.isBindingVolatile=i,this.bind=s,this.unbind=o,this.updateTarget=n,this.targetName=r}handleChange(){this.updateTarget(this.bindingObserver.observe(this.source,this.context))}handleEvent(e){m.setEvent(e);const t=this.binding(this.source,this.context);m.setEvent(null),!0!==t&&e.preventDefault()}}let B=null;class F{addFactory(e){e.targetIndex=this.targetIndex,this.behaviorFactories.push(e)}captureContentBinding(e){e.targetAtContent(),this.addFactory(e)}reset(){this.behaviorFactories=[],this.targetIndex=-1}release(){B=this}static borrow(e){const t=B||new F;return t.directives=e,t.reset(),B=null,t}}function L(e){if(1===e.length)return e[0];let t;const i=e.length,s=e.map((e=>"string"==typeof e?()=>e:(t=e.targetName||t,e.binding))),o=new D(((e,t)=>{let o="";for(let n=0;n<i;++n)o+=s[n](e,t);return o}));return o.targetName=t,o}const H=c.length;function V(e,t){const i=t.split(d);if(1===i.length)return null;const s=[];for(let t=0,o=i.length;t<o;++t){const o=i[t],n=o.indexOf(c);let r;if(-1===n)r=o;else{const t=parseInt(o.substring(0,n));s.push(e.directives[t]),r=o.substring(n+H)}""!==r&&s.push(r)}return s}function M(e,t,i=!1){const s=t.attributes;for(let o=0,n=s.length;o<n;++o){const r=s[o],a=r.value,l=V(e,a);let d=null;null===l?i&&(d=new D((()=>a)),d.targetName=r.name):d=L(l),null!==d&&(t.removeAttributeNode(r),o--,n--,e.addFactory(d))}}function N(e,t,i){const s=V(e,t.textContent);if(null!==s){let o=t;for(let n=0,r=s.length;n<r;++n){const r=s[n],a=0===n?t:o.parentNode.insertBefore(document.createTextNode(""),o.nextSibling);"string"==typeof r?a.textContent=r:(a.textContent=" ",e.captureContentBinding(r)),o=a,e.targetIndex++,a!==t&&i.nextNode()}e.targetIndex--}}const z=document.createRange();class j{constructor(e,t){this.fragment=e,this.behaviors=t,this.source=null,this.context=null,this.firstChild=e.firstChild,this.lastChild=e.lastChild}appendTo(e){e.appendChild(this.fragment)}insertBefore(e){if(this.fragment.hasChildNodes())e.parentNode.insertBefore(this.fragment,e);else{const t=this.lastChild;if(e.previousSibling===t)return;const i=e.parentNode;let s,o=this.firstChild;for(;o!==t;)s=o.nextSibling,i.insertBefore(o,e),o=s;i.insertBefore(t,e)}}remove(){const e=this.fragment,t=this.lastChild;let i,s=this.firstChild;for(;s!==t;)i=s.nextSibling,e.appendChild(s),s=i;e.appendChild(t)}dispose(){const e=this.firstChild.parentNode,t=this.lastChild;let i,s=this.firstChild;for(;s!==t;)i=s.nextSibling,e.removeChild(s),s=i;e.removeChild(t);const o=this.behaviors,n=this.source;for(let e=0,t=o.length;e<t;++e)o[e].unbind(n)}bind(e,t){const i=this.behaviors;if(this.source!==e)if(null!==this.source){const s=this.source;this.source=e,this.context=t;for(let o=0,n=i.length;o<n;++o){const n=i[o];n.unbind(s),n.bind(e,t)}}else{this.source=e,this.context=t;for(let s=0,o=i.length;s<o;++s)i[s].bind(e,t)}}unbind(){if(null===this.source)return;const e=this.behaviors,t=this.source;for(let i=0,s=e.length;i<s;++i)e[i].unbind(t);this.source=null}static disposeContiguousBatch(e){if(0!==e.length){z.setStartBefore(e[0].firstChild),z.setEndAfter(e[e.length-1].lastChild),z.deleteContents();for(let t=0,i=e.length;t<i;++t){const i=e[t],s=i.behaviors,o=i.source;for(let e=0,t=s.length;e<t;++e)s[e].unbind(o)}}}}class _{constructor(e,t){this.behaviorCount=0,this.hasHostBehaviors=!1,this.fragment=null,this.targetOffset=0,this.viewBehaviorFactories=null,this.hostBehaviorFactories=null,this.html=e,this.directives=t}create(e){if(null===this.fragment){let e;const t=this.html;if("string"==typeof t){e=document.createElement("template"),e.innerHTML=h.createHTML(t);const i=e.content.firstElementChild;null!==i&&"TEMPLATE"===i.tagName&&(e=i)}else e=t;const i=function(e,t){const i=e.content;document.adoptNode(i);const s=F.borrow(t);M(s,e,!0);const o=s.behaviorFactories;s.reset();const n=h.createTemplateWalker(i);let r;for(;r=n.nextNode();)switch(s.targetIndex++,r.nodeType){case 1:M(s,r);break;case 3:N(s,r,n);break;case 8:h.isMarker(r)&&s.addFactory(t[h.extractDirectiveIndexFromMarker(r)])}let a=0;(h.isMarker(i.firstChild)||1===i.childNodes.length&&t.length)&&(i.insertBefore(document.createComment(""),i.firstChild),a=-1);const l=s.behaviorFactories;return s.release(),{fragment:i,viewBehaviorFactories:l,hostBehaviorFactories:o,targetOffset:a}}(e,this.directives);this.fragment=i.fragment,this.viewBehaviorFactories=i.viewBehaviorFactories,this.hostBehaviorFactories=i.hostBehaviorFactories,this.targetOffset=i.targetOffset,this.behaviorCount=this.viewBehaviorFactories.length+this.hostBehaviorFactories.length,this.hasHostBehaviors=this.hostBehaviorFactories.length>0}const t=this.fragment.cloneNode(!0),i=this.viewBehaviorFactories,s=new Array(this.behaviorCount),o=h.createTemplateWalker(t);let n=0,r=this.targetOffset,a=o.nextNode();for(let e=i.length;n<e;++n){const e=i[n],t=e.targetIndex;for(;null!==a;){if(r===t){s[n]=e.createBehavior(a);break}a=o.nextNode(),r++}}if(this.hasHostBehaviors){const t=this.hostBehaviorFactories;for(let i=0,o=t.length;i<o;++i,++n)s[n]=t[i].createBehavior(e)}return new j(t,s)}render(e,t,i){"string"==typeof t&&(t=document.getElementById(t)),void 0===i&&(i=t);const s=this.create(i);return s.bind(e,v),s.appendTo(t),s}}const q=/([ \x09\x0a\x0c\x0d])([^\0-\x1F\x7F-\x9F "'>=/]+)([ \x09\x0a\x0c\x0d]*=[ \x09\x0a\x0c\x0d]*(?:[^ \x09\x0a\x0c\x0d"'`<>=]*|"[^"]*|'[^']*))$/;function U(e,...t){const i=[];let s="";for(let o=0,n=e.length-1;o<n;++o){const n=e[o];let r=t[o];if(s+=n,r instanceof _){const e=r;r=()=>e}if("function"==typeof r&&(r=new D(r)),r instanceof x){const e=q.exec(n);null!==e&&(r.targetName=e[2])}r instanceof y?(s+=r.createPlaceholder(i.length),i.push(r)):s+=r}return s+=e[e.length-1],new _(s,i)}class K{constructor(){this.targets=new WeakSet}addStylesTo(e){this.targets.add(e)}removeStylesFrom(e){this.targets.delete(e)}isAttachedTo(e){return this.targets.has(e)}withBehaviors(...e){return this.behaviors=null===this.behaviors?e:this.behaviors.concat(e),this}}function W(e){return e.map((e=>e instanceof K?W(e.styles):[e])).reduce(((e,t)=>e.concat(t)),[])}function G(e){return e.map((e=>e instanceof K?e.behaviors:null)).reduce(((e,t)=>null===t?e:(null===e&&(e=[]),e.concat(t))),null)}K.create=(()=>{if(h.supportsAdoptedStyleSheets){const e=new Map;return t=>new Y(t,e)}return e=>new Z(e)})();let Q=(e,t)=>{e.adoptedStyleSheets=[...e.adoptedStyleSheets,...t]},X=(e,t)=>{e.adoptedStyleSheets=e.adoptedStyleSheets.filter((e=>-1===t.indexOf(e)))};if(h.supportsAdoptedStyleSheets)try{document.adoptedStyleSheets.push(),document.adoptedStyleSheets.splice(),Q=(e,t)=>{e.adoptedStyleSheets.push(...t)},X=(e,t)=>{for(const i of t){const t=e.adoptedStyleSheets.indexOf(i);-1!==t&&e.adoptedStyleSheets.splice(t,1)}}}catch(e){}class Y extends K{constructor(e,t){super(),this.styles=e,this.styleSheetCache=t,this._styleSheets=void 0,this.behaviors=G(e)}get styleSheets(){if(void 0===this._styleSheets){const e=this.styles,t=this.styleSheetCache;this._styleSheets=W(e).map((e=>{if(e instanceof CSSStyleSheet)return e;let i=t.get(e);return void 0===i&&(i=new CSSStyleSheet,i.replaceSync(e),t.set(e,i)),i}))}return this._styleSheets}addStylesTo(e){Q(e,this.styleSheets),super.addStylesTo(e)}removeStylesFrom(e){X(e,this.styleSheets),super.removeStylesFrom(e)}}let J=0;class Z extends K{constructor(e){super(),this.styles=e,this.behaviors=null,this.behaviors=G(e),this.styleSheets=W(e),this.styleClass="fast-style-class-"+ ++J}addStylesTo(e){const t=this.styleSheets,i=this.styleClass;e=this.normalizeTarget(e);for(let s=0;s<t.length;s++){const o=document.createElement("style");o.innerHTML=t[s],o.className=i,e.append(o)}super.addStylesTo(e)}removeStylesFrom(e){const t=(e=this.normalizeTarget(e)).querySelectorAll(`.${this.styleClass}`);for(let i=0,s=t.length;i<s;++i)e.removeChild(t[i]);super.removeStylesFrom(e)}isAttachedTo(e){return super.isAttachedTo(this.normalizeTarget(e))}normalizeTarget(e){return e===document?document.body:e}}const ee=Object.freeze({locate:o()}),te={toView:e=>e?"true":"false",fromView:e=>null!=e&&"false"!==e&&!1!==e&&0!==e},ie={toView(e){if(null==e)return null;const t=1*e;return isNaN(t)?null:t.toString()},fromView(e){if(null==e)return null;const t=1*e;return isNaN(t)?null:t}};class se{constructor(e,t,i=t.toLowerCase(),s="reflect",o){this.guards=new Set,this.Owner=e,this.name=t,this.attribute=i,this.mode=s,this.converter=o,this.fieldName=`_${t}`,this.callbackName=`${t}Changed`,this.hasCallback=this.callbackName in e.prototype,"boolean"===s&&void 0===o&&(this.converter=te)}setValue(e,t){const i=e[this.fieldName],s=this.converter;void 0!==s&&(t=s.fromView(t)),i!==t&&(e[this.fieldName]=t,this.tryReflectToAttribute(e),this.hasCallback&&e[this.callbackName](i,t),e.$fastController.notify(this.name))}getValue(e){return f.track(e,this.name),e[this.fieldName]}onAttributeChangedCallback(e,t){this.guards.has(e)||(this.guards.add(e),this.setValue(e,t),this.guards.delete(e))}tryReflectToAttribute(e){const t=this.mode,i=this.guards;i.has(e)||"fromView"===t||h.queueUpdate((()=>{i.add(e);const s=e[this.fieldName];switch(t){case"reflect":const t=this.converter;h.setAttribute(e,this.attribute,void 0!==t?t.toView(s):s);break;case"boolean":h.setBooleanAttribute(e,this.attribute,s)}i.delete(e)}))}static collect(e,...t){const i=[];t.push(ee.locate(e));for(let s=0,o=t.length;s<o;++s){const o=t[s];if(void 0!==o)for(let t=0,s=o.length;t<s;++t){const s=o[t];"string"==typeof s?i.push(new se(e,s)):i.push(new se(e,s.property,s.attribute,s.mode,s.converter))}}return i}}function oe(e,t){let i;function s(e,t){arguments.length>1&&(i.property=t),ee.locate(e.constructor).push(i)}return arguments.length>1?(i={},void s(e,t)):(i=void 0===e?{}:e,s)}const ne={mode:"open"},re={},ae=i.getById(4,(()=>{const e=new Map;return Object.freeze({register:t=>!e.has(t.type)&&(e.set(t.type,t),!0),getByType:t=>e.get(t)})}));class le{constructor(e,t=e.definition){"string"==typeof t&&(t={name:t}),this.type=e,this.name=t.name,this.template=t.template;const i=se.collect(e,t.attributes),s=new Array(i.length),o={},n={};for(let e=0,t=i.length;e<t;++e){const t=i[e];s[e]=t.attribute,o[t.name]=t,n[t.attribute]=t}this.attributes=i,this.observedAttributes=s,this.propertyLookup=o,this.attributeLookup=n,this.shadowOptions=void 0===t.shadowOptions?ne:null===t.shadowOptions?void 0:Object.assign(Object.assign({},ne),t.shadowOptions),this.elementOptions=void 0===t.elementOptions?re:Object.assign(Object.assign({},re),t.elementOptions),this.styles=void 0===t.styles?void 0:Array.isArray(t.styles)?K.create(t.styles):t.styles instanceof K?t.styles:K.create([t.styles])}get isDefined(){return!!ae.getByType(this.type)}define(e=customElements){const t=this.type;if(ae.register(this)){const e=this.attributes,i=t.prototype;for(let t=0,s=e.length;t<s;++t)f.defineProperty(i,e[t]);Reflect.defineProperty(t,"observedAttributes",{value:this.observedAttributes,enumerable:!0})}return e.get(this.name)||e.define(this.name,t,this.elementOptions),this}}le.forType=ae.getByType;const de=new WeakMap,ce={bubbles:!0,composed:!0,cancelable:!0};function he(e){return e.shadowRoot||de.get(e)||null}class ue extends p{constructor(e,t){super(e),this.boundObservables=null,this.behaviors=null,this.needsInitialization=!0,this._template=null,this._styles=null,this._isConnected=!1,this.$fastController=this,this.view=null,this.element=e,this.definition=t;const i=t.shadowOptions;if(void 0!==i){const t=e.attachShadow(i);"closed"===i.mode&&de.set(e,t)}const s=f.getAccessors(e);if(s.length>0){const t=this.boundObservables=Object.create(null);for(let i=0,o=s.length;i<o;++i){const o=s[i].name,n=e[o];void 0!==n&&(delete e[o],t[o]=n)}}}get isConnected(){return f.track(this,"isConnected"),this._isConnected}setIsConnected(e){this._isConnected=e,f.notify(this,"isConnected")}get template(){return this._template}set template(e){this._template!==e&&(this._template=e,this.needsInitialization||this.renderTemplate(e))}get styles(){return this._styles}set styles(e){this._styles!==e&&(null!==this._styles&&this.removeStyles(this._styles),this._styles=e,this.needsInitialization||null===e||this.addStyles(e))}addStyles(e){const t=he(this.element)||this.element.getRootNode();if(e instanceof HTMLStyleElement)t.append(e);else if(!e.isAttachedTo(t)){const i=e.behaviors;e.addStylesTo(t),null!==i&&this.addBehaviors(i)}}removeStyles(e){const t=he(this.element)||this.element.getRootNode();if(e instanceof HTMLStyleElement)t.removeChild(e);else if(e.isAttachedTo(t)){const i=e.behaviors;e.removeStylesFrom(t),null!==i&&this.removeBehaviors(i)}}addBehaviors(e){const t=this.behaviors||(this.behaviors=new Map),i=e.length,s=[];for(let o=0;o<i;++o){const i=e[o];t.has(i)?t.set(i,t.get(i)+1):(t.set(i,1),s.push(i))}if(this._isConnected){const e=this.element;for(let t=0;t<s.length;++t)s[t].bind(e,v)}}removeBehaviors(e,t=!1){const i=this.behaviors;if(null===i)return;const s=e.length,o=[];for(let n=0;n<s;++n){const s=e[n];if(i.has(s)){const e=i.get(s)-1;0===e||t?i.delete(s)&&o.push(s):i.set(s,e)}}if(this._isConnected){const e=this.element;for(let t=0;t<o.length;++t)o[t].unbind(e)}}onConnectedCallback(){if(this._isConnected)return;const e=this.element;this.needsInitialization?this.finishInitialization():null!==this.view&&this.view.bind(e,v);const t=this.behaviors;if(null!==t)for(const[i]of t)i.bind(e,v);this.setIsConnected(!0)}onDisconnectedCallback(){if(!this._isConnected)return;this.setIsConnected(!1);const e=this.view;null!==e&&e.unbind();const t=this.behaviors;if(null!==t){const e=this.element;for(const[i]of t)i.unbind(e)}}onAttributeChangedCallback(e,t,i){const s=this.definition.attributeLookup[e];void 0!==s&&s.onAttributeChangedCallback(this.element,i)}emit(e,t,i){return!!this._isConnected&&this.element.dispatchEvent(new CustomEvent(e,Object.assign(Object.assign({detail:t},ce),i)))}finishInitialization(){const e=this.element,t=this.boundObservables;if(null!==t){const i=Object.keys(t);for(let s=0,o=i.length;s<o;++s){const o=i[s];e[o]=t[o]}this.boundObservables=null}const i=this.definition;null===this._template&&(this.element.resolveTemplate?this._template=this.element.resolveTemplate():i.template&&(this._template=i.template||null)),null!==this._template&&this.renderTemplate(this._template),null===this._styles&&(this.element.resolveStyles?this._styles=this.element.resolveStyles():i.styles&&(this._styles=i.styles||null)),null!==this._styles&&this.addStyles(this._styles),this.needsInitialization=!1}renderTemplate(e){const t=this.element,i=he(t)||t;null!==this.view?(this.view.dispose(),this.view=null):this.needsInitialization||h.removeChildNodes(i),e&&(this.view=e.render(t,i,t))}static forCustomElement(e){const t=e.$fastController;if(void 0!==t)return t;const i=le.forType(e.constructor);if(void 0===i)throw new Error("Missing FASTElement definition.");return e.$fastController=new ue(e,i)}}function pe(e){return class extends e{constructor(){super(),ue.forCustomElement(this)}$emit(e,t,i){return this.$fastController.emit(e,t,i)}connectedCallback(){this.$fastController.onConnectedCallback()}disconnectedCallback(){this.$fastController.onDisconnectedCallback()}attributeChangedCallback(e,t,i){this.$fastController.onAttributeChangedCallback(e,t,i)}}}const fe=Object.assign(pe(HTMLElement),{from:e=>pe(e),define:(e,t)=>new le(e,t).define().type});class be{createCSS(){return""}createBehavior(){}}function ge(e,...t){const{styles:i,behaviors:s}=function(e,t){const i=[];let s="";const o=[];for(let n=0,r=e.length-1;n<r;++n){s+=e[n];let r=t[n];if(r instanceof be){const e=r.createBehavior();r=r.createCSS(),e&&o.push(e)}r instanceof K||r instanceof CSSStyleSheet?(""!==s.trim()&&(i.push(s),s=""),i.push(r)):s+=r}return s+=e[e.length-1],""!==s.trim()&&i.push(s),{styles:i,behaviors:o}}(e,t),o=K.create(i);return s.length&&o.withBehaviors(...s),o}function me(e,t,i){return{index:e,removed:t,addedCount:i}}function ve(e,t,i,o,n,r){let a=0,l=0;const d=Math.min(i-t,r-n);if(0===t&&0===n&&(a=function(e,t,i){for(let s=0;s<i;++s)if(e[s]!==t[s])return s;return i}(e,o,d)),i===e.length&&r===o.length&&(l=function(e,t,i){let s=e.length,o=t.length,n=0;for(;n<i&&e[--s]===t[--o];)n++;return n}(e,o,d-a)),n+=a,r-=l,(i-=l)-(t+=a)==0&&r-n==0)return s;if(t===i){const e=me(t,[],0);for(;n<r;)e.removed.push(o[n++]);return[e]}if(n===r)return[me(t,[],i-t)];const c=function(e){let t=e.length-1,i=e[0].length-1,s=e[t][i];const o=[];for(;t>0||i>0;){if(0===t){o.push(2),i--;continue}if(0===i){o.push(3),t--;continue}const n=e[t-1][i-1],r=e[t-1][i],a=e[t][i-1];let l;l=r<a?r<n?r:n:a<n?a:n,l===n?(n===s?o.push(0):(o.push(1),s=n),t--,i--):l===r?(o.push(3),t--,s=r):(o.push(2),i--,s=a)}return o.reverse(),o}(function(e,t,i,s,o,n){const r=n-o+1,a=i-t+1,l=new Array(r);let d,c;for(let e=0;e<r;++e)l[e]=new Array(a),l[e][0]=e;for(let e=0;e<a;++e)l[0][e]=e;for(let i=1;i<r;++i)for(let n=1;n<a;++n)e[t+n-1]===s[o+i-1]?l[i][n]=l[i-1][n-1]:(d=l[i-1][n]+1,c=l[i][n-1]+1,l[i][n]=d<c?d:c);return l}(e,t,i,o,n,r)),h=[];let u,p=t,f=n;for(let e=0;e<c.length;++e)switch(c[e]){case 0:void 0!==u&&(h.push(u),u=void 0),p++,f++;break;case 1:void 0===u&&(u=me(p,[],0)),u.addedCount++,p++,u.removed.push(o[f]),f++;break;case 2:void 0===u&&(u=me(p,[],0)),u.addedCount++,p++;break;case 3:void 0===u&&(u=me(p,[],0)),u.removed.push(o[f]),f++}return void 0!==u&&h.push(u),h}const ye=Array.prototype.push;function xe(e,t,i,s){const o=me(t,i,s);let n=!1,r=0;for(let t=0;t<e.length;t++){const i=e[t];if(i.index+=r,n)continue;const s=(a=o.index,l=o.index+o.removed.length,d=i.index,c=i.index+i.addedCount,l<d||c<a?-1:l===d||c===a?0:a<d?l<c?l-d:c-d:c<l?c-a:l-a);if(s>=0){e.splice(t,1),t--,r-=i.addedCount-i.removed.length,o.addedCount+=i.addedCount-s;const a=o.removed.length+i.removed.length-s;if(o.addedCount||a){let e=i.removed;if(o.index<i.index){const t=o.removed.slice(0,i.index-o.index);ye.apply(t,e),e=t}if(o.index+o.removed.length>i.index+i.addedCount){const t=o.removed.slice(i.index+i.addedCount-o.index);ye.apply(e,t)}o.removed=e,i.index<o.index&&(o.index=i.index)}else n=!0}else if(o.index<i.index){n=!0,e.splice(t,0,o),t++;const s=o.addedCount-o.removed.length;i.index+=s,r+=s}}var a,l,d,c;n||e.push(o)}function Ce(e,t){let i=[];const s=function(e){const t=[];for(let i=0,s=e.length;i<s;i++){const s=e[i];xe(t,s.index,s.removed,s.addedCount)}return t}(t);for(let t=0,o=s.length;t<o;++t){const o=s[t];1!==o.addedCount||1!==o.removed.length?i=i.concat(ve(e,o.index,o.index+o.addedCount,o.removed,0,o.removed.length)):o.removed[0]!==e[o.index]&&i.push(o)}return i}let we=!1;function $e(e,t){let i=e.index;const s=t.length;return i>s?i=s-e.addedCount:i<0&&(i=s+e.removed.length+i-e.addedCount),i<0&&(i=0),e.index=i,e}class ke extends u{constructor(e){super(e),this.oldCollection=void 0,this.splices=void 0,this.needsQueue=!0,this.call=this.flush,Reflect.defineProperty(e,"$fastController",{value:this,enumerable:!1})}subscribe(e){this.flush(),super.subscribe(e)}addSplice(e){void 0===this.splices?this.splices=[e]:this.splices.push(e),this.needsQueue&&(this.needsQueue=!1,h.queueUpdate(this))}reset(e){this.oldCollection=e,this.needsQueue&&(this.needsQueue=!1,h.queueUpdate(this))}flush(){const e=this.splices,t=this.oldCollection;if(void 0===e&&void 0===t)return;this.needsQueue=!0,this.splices=void 0,this.oldCollection=void 0;const i=void 0===t?Ce(this.source,e):ve(this.source,0,this.source.length,t,0,t.length);this.notify(i)}}class Ie{constructor(e,t){this.target=e,this.propertyName=t}bind(e){e[this.propertyName]=this.target}unbind(){}}function Te(e){return new C("fast-ref",Ie,e)}const Oe=e=>"function"==typeof e,Se=()=>null;function Ae(e){return void 0===e?Se:Oe(e)?e:()=>e}function Re(e,t,i){const s=Oe(e)?e:()=>e,o=Ae(t),n=Ae(i);return(e,t)=>s(e,t)?o(e,t):n(e,t)}function Ee(e,t,i,s){e.bind(t[i],s)}function De(e,t,i,s){const o=Object.create(s);o.index=i,o.length=t.length,e.bind(t[i],o)}Object.freeze({positioning:!1,recycle:!0});class Pe{constructor(e,t,i,s,o,n){this.location=e,this.itemsBinding=t,this.templateBinding=s,this.options=n,this.source=null,this.views=[],this.items=null,this.itemsObserver=null,this.originalContext=void 0,this.childContext=void 0,this.bindView=Ee,this.itemsBindingObserver=f.binding(t,this,i),this.templateBindingObserver=f.binding(s,this,o),n.positioning&&(this.bindView=De)}bind(e,t){this.source=e,this.originalContext=t,this.childContext=Object.create(t),this.childContext.parent=e,this.childContext.parentContext=this.originalContext,this.items=this.itemsBindingObserver.observe(e,this.originalContext),this.template=this.templateBindingObserver.observe(e,this.originalContext),this.observeItems(!0),this.refreshAllViews()}unbind(){this.source=null,this.items=null,null!==this.itemsObserver&&this.itemsObserver.unsubscribe(this),this.unbindAllViews(),this.itemsBindingObserver.disconnect(),this.templateBindingObserver.disconnect()}handleChange(e,t){e===this.itemsBinding?(this.items=this.itemsBindingObserver.observe(this.source,this.originalContext),this.observeItems(),this.refreshAllViews()):e===this.templateBinding?(this.template=this.templateBindingObserver.observe(this.source,this.originalContext),this.refreshAllViews(!0)):this.updateViews(t)}observeItems(e=!1){if(!this.items)return void(this.items=s);const t=this.itemsObserver,i=this.itemsObserver=f.getNotifier(this.items),o=t!==i;o&&null!==t&&t.unsubscribe(this),(o||e)&&i.subscribe(this)}updateViews(e){const t=this.childContext,i=this.views,s=this.bindView,o=this.items,n=this.template,r=this.options.recycle,a=[];let l=0,d=0;for(let c=0,h=e.length;c<h;++c){const h=e[c],u=h.removed;let p=0,f=h.index;const b=f+h.addedCount,g=i.splice(h.index,u.length),m=d=a.length+g.length;for(;f<b;++f){const e=i[f],c=e?e.firstChild:this.location;let h;r&&d>0?(p<=m&&g.length>0?(h=g[p],p++):(h=a[l],l++),d--):h=n.create(),i.splice(f,0,h),s(h,o,f,t),h.insertBefore(c)}g[p]&&a.push(...g.slice(p))}for(let e=l,t=a.length;e<t;++e)a[e].dispose();if(this.options.positioning)for(let e=0,t=i.length;e<t;++e){const s=i[e].context;s.length=t,s.index=e}}refreshAllViews(e=!1){const t=this.items,i=this.childContext,s=this.template,o=this.location,n=this.bindView;let r=t.length,a=this.views,l=a.length;if(0!==r&&!e&&this.options.recycle||(j.disposeContiguousBatch(a),l=0),0===l){this.views=a=new Array(r);for(let e=0;e<r;++e){const r=s.create();n(r,t,e,i),a[e]=r,r.insertBefore(o)}}else{let e=0;for(;e<r;++e)if(e<l){n(a[e],t,e,i)}else{const r=s.create();n(r,t,e,i),a.push(r),r.insertBefore(o)}const d=a.splice(e,l-e);for(e=0,r=d.length;e<r;++e)d[e].dispose()}}unbindAllViews(){const e=this.views;for(let t=0,i=e.length;t<i;++t)e[t].unbind()}}class Be extends y{constructor(e,t,i){super(),this.itemsBinding=e,this.templateBinding=t,this.options=i,this.createPlaceholder=h.createBlockPlaceholder,function(){if(we)return;we=!0,f.setArrayObserverFactory((e=>new ke(e)));const e=Array.prototype;if(e.$fastPatch)return;Reflect.defineProperty(e,"$fastPatch",{value:1,enumerable:!1});const t=e.pop,i=e.push,s=e.reverse,o=e.shift,n=e.sort,r=e.splice,a=e.unshift;e.pop=function(){const e=this.length>0,i=t.apply(this,arguments),s=this.$fastController;return void 0!==s&&e&&s.addSplice(me(this.length,[i],0)),i},e.push=function(){const e=i.apply(this,arguments),t=this.$fastController;return void 0!==t&&t.addSplice($e(me(this.length-arguments.length,[],arguments.length),this)),e},e.reverse=function(){let e;const t=this.$fastController;void 0!==t&&(t.flush(),e=this.slice());const i=s.apply(this,arguments);return void 0!==t&&t.reset(e),i},e.shift=function(){const e=this.length>0,t=o.apply(this,arguments),i=this.$fastController;return void 0!==i&&e&&i.addSplice(me(0,[t],0)),t},e.sort=function(){let e;const t=this.$fastController;void 0!==t&&(t.flush(),e=this.slice());const i=n.apply(this,arguments);return void 0!==t&&t.reset(e),i},e.splice=function(){const e=r.apply(this,arguments),t=this.$fastController;return void 0!==t&&t.addSplice($e(me(+arguments[0],e,arguments.length>2?arguments.length-2:0),this)),e},e.unshift=function(){const e=a.apply(this,arguments),t=this.$fastController;return void 0!==t&&t.addSplice($e(me(0,[],arguments.length),this)),e}}(),this.isItemsBindingVolatile=f.isVolatileBinding(e),this.isTemplateBindingVolatile=f.isVolatileBinding(t)}createBehavior(e){return new Pe(e,this.itemsBinding,this.isItemsBindingVolatile,this.templateBinding,this.isTemplateBindingVolatile,this.options)}}function Fe(e){return e?function(t,i,s){return 1===t.nodeType&&t.matches(e)}:function(e,t,i){return 1===e.nodeType}}class Le{constructor(e,t){this.target=e,this.options=t,this.source=null}bind(e){const t=this.options.property;this.shouldUpdate=f.getAccessors(e).some((e=>e.name===t)),this.source=e,this.updateTarget(this.computeNodes()),this.shouldUpdate&&this.observe()}unbind(){this.updateTarget(s),this.source=null,this.shouldUpdate&&this.disconnect()}handleEvent(){this.updateTarget(this.computeNodes())}computeNodes(){let e=this.getNodes();return void 0!==this.options.filter&&(e=e.filter(this.options.filter)),e}updateTarget(e){this.source[this.options.property]=e}}class He extends Le{constructor(e,t){super(e,t)}observe(){this.target.addEventListener("slotchange",this)}disconnect(){this.target.removeEventListener("slotchange",this)}getNodes(){return this.target.assignedNodes(this.options)}}function Ve(e){return"string"==typeof e&&(e={property:e}),new C("fast-slotted",He,e)}class Me extends Le{constructor(e,t){super(e,t),this.observer=null,t.childList=!0}observe(){null===this.observer&&(this.observer=new MutationObserver(this.handleEvent.bind(this))),this.observer.observe(this.target,this.options)}disconnect(){this.observer.disconnect()}getNodes(){return"subtree"in this.options?Array.from(this.target.querySelectorAll(this.options.selector)):Array.from(this.target.childNodes)}}function Ne(e){return"string"==typeof e&&(e={property:e}),new C("fast-children",Me,e)}class ze{handleStartContentChange(){this.startContainer.classList.toggle("start",this.start.assignedNodes().length>0)}handleEndContentChange(){this.endContainer.classList.toggle("end",this.end.assignedNodes().length>0)}}const je=(e,t)=>U`<span part="end" ${Te("endContainer")} class=${e=>t.end?"end":void 0}><slot name="end" ${Te("end")} @slotchange="${e=>e.handleEndContentChange()}">${t.end||""}</slot></span>`,_e=(e,t)=>U`<span part="start" ${Te("startContainer")} class="${e=>t.start?"start":void 0}"><slot name="start" ${Te("start")} @slotchange="${e=>e.handleStartContentChange()}">${t.start||""}</slot></span>`;
/*! *****************************************************************************
Copyright (c) Microsoft Corporation.

Permission to use, copy, modify, and/or distribute this software for any
purpose with or without fee is hereby granted.

THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH
REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY
AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,
INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM
LOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR
OTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR
PERFORMANCE OF THIS SOFTWARE.
***************************************************************************** */
function qe(e,t,i,s){var o,n=arguments.length,r=n<3?t:null===s?s=Object.getOwnPropertyDescriptor(t,i):s;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)r=Reflect.decorate(e,t,i,s);else for(var a=e.length-1;a>=0;a--)(o=e[a])&&(r=(n<3?o(r):n>3?o(t,i,r):o(t,i))||r);return n>3&&r&&Object.defineProperty(t,i,r),r}U`<span part="end" ${Te("endContainer")}><slot name="end" ${Te("end")} @slotchange="${e=>e.handleEndContentChange()}"></slot></span>`,U`<span part="start" ${Te("startContainer")}><slot name="start" ${Te("start")} @slotchange="${e=>e.handleStartContentChange()}"></slot></span>`;const Ue=new Map;"metadata"in Reflect||(Reflect.metadata=function(e,t){return function(i){Reflect.defineMetadata(e,t,i)}},Reflect.defineMetadata=function(e,t,i){let s=Ue.get(i);void 0===s&&Ue.set(i,s=new Map),s.set(e,t)},Reflect.getOwnMetadata=function(e,t){const i=Ue.get(t);if(void 0!==i)return i.get(e)});class Ke{constructor(e,t){this.container=e,this.key=t}instance(e){return this.registerResolver(0,e)}singleton(e){return this.registerResolver(1,e)}transient(e){return this.registerResolver(2,e)}callback(e){return this.registerResolver(3,e)}cachedCallback(e){return this.registerResolver(3,ft(e))}aliasTo(e){return this.registerResolver(5,e)}registerResolver(e,t){const{container:i,key:s}=this;return this.container=this.key=void 0,i.registerResolver(s,new it(s,e,t))}}function We(e){const t=e.slice(),i=Object.keys(e),s=i.length;let o;for(let n=0;n<s;++n)o=i[n],wt(o)||(t[o]=e[o]);return t}const Ge=Object.freeze({none(e){throw Error(`${e.toString()} not registered, did you forget to add @singleton()?`)},singleton:e=>new it(e,1,e),transient:e=>new it(e,2,e)}),Qe=Object.freeze({default:Object.freeze({parentLocator:()=>null,responsibleForOwnerRequests:!1,defaultResolver:Ge.singleton})}),Xe=new Map;function Ye(e){return t=>Reflect.getOwnMetadata(e,t)}let Je=null;const Ze=Object.freeze({createContainer:e=>new ut(null,Object.assign({},Qe.default,e)),findResponsibleContainer(e){const t=e.$$container$$;return t&&t.responsibleForOwnerRequests?t:Ze.findParentContainer(e)},findParentContainer(e){const t=new CustomEvent(ct,{bubbles:!0,composed:!0,cancelable:!0,detail:{container:void 0}});return e.dispatchEvent(t),t.detail.container||Ze.getOrCreateDOMContainer()},getOrCreateDOMContainer:(e,t)=>e?e.$$container$$||new ut(e,Object.assign({},Qe.default,t,{parentLocator:Ze.findParentContainer})):Je||(Je=new ut(null,Object.assign({},Qe.default,t,{parentLocator:()=>null}))),getDesignParamtypes:Ye("design:paramtypes"),getAnnotationParamtypes:Ye("di:paramtypes"),getOrCreateAnnotationParamTypes(e){let t=this.getAnnotationParamtypes(e);return void 0===t&&Reflect.defineMetadata("di:paramtypes",t=[],e),t},getDependencies(e){let t=Xe.get(e);if(void 0===t){const i=e.inject;if(void 0===i){const i=Ze.getDesignParamtypes(e),s=Ze.getAnnotationParamtypes(e);if(void 0===i)if(void 0===s){const i=Object.getPrototypeOf(e);t="function"==typeof i&&i!==Function.prototype?We(Ze.getDependencies(i)):[]}else t=We(s);else if(void 0===s)t=We(i);else{t=We(i);let e,o=s.length;for(let i=0;i<o;++i)e=s[i],void 0!==e&&(t[i]=e);const n=Object.keys(s);let r;o=n.length;for(let e=0;e<o;++e)r=n[e],wt(r)||(t[r]=s[r])}}else t=We(i);Xe.set(e,t)}return t},defineProperty(e,t,i,s=!1){const o=`$di_${t}`;Reflect.defineProperty(e,t,{get:function(){let e=this[o];if(void 0===e){const n=this instanceof HTMLElement?Ze.findResponsibleContainer(this):Ze.getOrCreateDOMContainer();if(e=n.get(i),this[o]=e,s&&this instanceof fe){const s=this.$fastController,n=()=>{Ze.findResponsibleContainer(this).get(i)!==this[o]&&(this[o]=e,s.notify(t))};s.subscribe({handleChange:n},"isConnected")}}return e}})},createInterface(e,t){const i="function"==typeof e?e:t,s="string"==typeof e?e:e&&"friendlyName"in e&&e.friendlyName||vt,o="string"!=typeof e&&(e&&"respectConnection"in e&&e.respectConnection||!1),n=function(e,t,i){if(null==e||void 0!==new.target)throw new Error(`No registration for interface: '${n.friendlyName}'`);if(t)Ze.defineProperty(e,t,n,o);else{Ze.getOrCreateAnnotationParamTypes(e)[i]=n}};return n.$isInterface=!0,n.friendlyName=null==s?"(anonymous)":s,null!=i&&(n.register=function(e,t){return i(new Ke(e,null!=t?t:n))}),n.toString=function(){return`InterfaceSymbol<${n.friendlyName}>`},n},inject:(...e)=>function(t,i,s){if("number"==typeof s){const i=Ze.getOrCreateAnnotationParamTypes(t),o=e[0];void 0!==o&&(i[s]=o)}else if(i)Ze.defineProperty(t,i,e[0]);else{const i=s?Ze.getOrCreateAnnotationParamTypes(s.value):Ze.getOrCreateAnnotationParamTypes(t);let o;for(let t=0;t<e.length;++t)o=e[t],void 0!==o&&(i[t]=o)}},transient:e=>(e.register=function(t){return bt.transient(e,e).register(t)},e.registerInRequestor=!1,e),singleton:(e,t=tt)=>(e.register=function(t){return bt.singleton(e,e).register(t)},e.registerInRequestor=t.scoped,e)}),et=Ze.createInterface("Container");Ze.inject;const tt={scoped:!1};class it{constructor(e,t,i){this.key=e,this.strategy=t,this.state=i,this.resolving=!1}get $isResolver(){return!0}register(e){return e.registerResolver(this.key,this)}resolve(e,t){switch(this.strategy){case 0:return this.state;case 1:if(this.resolving)throw new Error(`Cyclic dependency found: ${this.state.name}`);return this.resolving=!0,this.state=e.getFactory(this.state).construct(t),this.strategy=0,this.resolving=!1,this.state;case 2:{const i=e.getFactory(this.state);if(null===i)throw new Error(`Resolver for ${String(this.key)} returned a null factory`);return i.construct(t)}case 3:return this.state(e,t,this);case 4:return this.state[0].resolve(e,t);case 5:return t.get(this.state);default:throw new Error(`Invalid resolver strategy specified: ${this.strategy}.`)}}getFactory(e){var t,i,s;switch(this.strategy){case 1:case 2:return e.getFactory(this.state);case 5:return null!==(s=null===(i=null===(t=e.getResolver(this.state))||void 0===t?void 0:t.getFactory)||void 0===i?void 0:i.call(t,e))&&void 0!==s?s:null;default:return null}}}function st(e){return this.get(e)}function ot(e,t){return t(e)}class nt{constructor(e,t){this.Type=e,this.dependencies=t,this.transformers=null}construct(e,t){let i;return i=void 0===t?new this.Type(...this.dependencies.map(st,e)):new this.Type(...this.dependencies.map(st,e),...t),null==this.transformers?i:this.transformers.reduce(ot,i)}registerTransformer(e){(this.transformers||(this.transformers=[])).push(e)}}const rt={$isResolver:!0,resolve:(e,t)=>t};function at(e){return"function"==typeof e.register}function lt(e){return function(e){return at(e)&&"boolean"==typeof e.registerInRequestor}(e)&&e.registerInRequestor}const dt=new Set(["Array","ArrayBuffer","Boolean","DataView","Date","Error","EvalError","Float32Array","Float64Array","Function","Int8Array","Int16Array","Int32Array","Map","Number","Object","Promise","RangeError","ReferenceError","RegExp","Set","SharedArrayBuffer","String","SyntaxError","TypeError","Uint8Array","Uint8ClampedArray","Uint16Array","Uint32Array","URIError","WeakMap","WeakSet"]),ct="__DI_LOCATE_PARENT__",ht=new Map;class ut{constructor(e,t){this.owner=e,this.config=t,this._parent=void 0,this.registerDepth=0,this.context=null,null!==e&&(e.$$container$$=this),this.resolvers=new Map,this.resolvers.set(et,rt),e instanceof Node&&e.addEventListener(ct,(e=>{e.composedPath()[0]!==this.owner&&(e.detail.container=this,e.stopImmediatePropagation())}))}get parent(){return void 0===this._parent&&(this._parent=this.config.parentLocator(this.owner)),this._parent}get depth(){return null===this.parent?0:this.parent.depth+1}get responsibleForOwnerRequests(){return this.config.responsibleForOwnerRequests}registerWithContext(e,...t){return this.context=e,this.register(...t),this.context=null,this}register(...e){if(100==++this.registerDepth)throw new Error("Unable to autoregister dependency");let t,i,s,o,n;const r=this.context;for(let a=0,l=e.length;a<l;++a)if(t=e[a],yt(t))if(at(t))t.register(this,r);else if(void 0!==t.prototype)bt.singleton(t,t).register(this);else for(i=Object.keys(t),o=0,n=i.length;o<n;++o)s=t[i[o]],yt(s)&&(at(s)?s.register(this,r):this.register(s));return--this.registerDepth,this}registerResolver(e,t){gt(e);const i=this.resolvers,s=i.get(e);return null==s?i.set(e,t):s instanceof it&&4===s.strategy?s.state.push(t):i.set(e,new it(e,4,[s,t])),t}registerTransformer(e,t){const i=this.getResolver(e);if(null==i)return!1;if(i.getFactory){const e=i.getFactory(this);return null!=e&&(e.registerTransformer(t),!0)}return!1}getResolver(e,t=!0){if(gt(e),void 0!==e.resolve)return e;let i,s=this;for(;null!=s;){if(i=s.resolvers.get(e),null!=i)return i;if(null==s.parent){const i=lt(e)?this:s;return t?this.jitRegister(e,i):null}s=s.parent}return null}has(e,t=!1){return!!this.resolvers.has(e)||!(!t||null==this.parent)&&this.parent.has(e,!0)}get(e){if(gt(e),e.$isResolver)return e.resolve(this,this);let t,i=this;for(;null!=i;){if(t=i.resolvers.get(e),null!=t)return t.resolve(i,this);if(null==i.parent){const s=lt(e)?this:i;return t=this.jitRegister(e,s),t.resolve(i,this)}i=i.parent}throw new Error(`Unable to resolve key: ${String(e)}`)}getAll(e,t=!1){gt(e);const i=this;let o,n=i;if(t){let t=s;for(;null!=n;)o=n.resolvers.get(e),null!=o&&(t=t.concat(mt(o,n,i))),n=n.parent;return t}for(;null!=n;){if(o=n.resolvers.get(e),null!=o)return mt(o,n,i);if(n=n.parent,null==n)return s}return s}getFactory(e){let t=ht.get(e);if(void 0===t){if(xt(e))throw new Error(`${e.name} is a native function and therefore cannot be safely constructed by DI. If this is intentional, please use a callback or cachedCallback resolver.`);ht.set(e,t=new nt(e,Ze.getDependencies(e)))}return t}registerFactory(e,t){ht.set(e,t)}createChild(e){return new ut(null,Object.assign({},this.config,e,{parentLocator:()=>this}))}jitRegister(e,t){if("function"!=typeof e)throw new Error(`Attempted to jitRegister something that is not a constructor: '${e}'. Did you forget to register this dependency?`);if(dt.has(e.name))throw new Error(`Attempted to jitRegister an intrinsic type: ${e.name}. Did you forget to add @inject(Key)`);if(at(e)){const i=e.register(t);if(!(i instanceof Object)||null==i.resolve){const i=t.resolvers.get(e);if(null!=i)return i;throw new Error("A valid resolver was not returned from the static register method")}return i}if(e.$isInterface)throw new Error(`Attempted to jitRegister an interface: ${e.friendlyName}`);{const i=this.config.defaultResolver(e,t);return t.resolvers.set(e,i),i}}}const pt=new WeakMap;function ft(e){return function(t,i,s){if(pt.has(s))return pt.get(s);const o=e(t,i,s);return pt.set(s,o),o}}const bt=Object.freeze({instance:(e,t)=>new it(e,0,t),singleton:(e,t)=>new it(e,1,t),transient:(e,t)=>new it(e,2,t),callback:(e,t)=>new it(e,3,t),cachedCallback:(e,t)=>new it(e,3,ft(t)),aliasTo:(e,t)=>new it(t,5,e)});function gt(e){if(null==e)throw new Error("key/value cannot be null or undefined. Are you trying to inject/register something that doesn't exist with DI?")}function mt(e,t,i){if(e instanceof it&&4===e.strategy){const s=e.state;let o=s.length;const n=new Array(o);for(;o--;)n[o]=s[o].resolve(t,i);return n}return[e.resolve(t,i)]}const vt="(anonymous)";function yt(e){return"object"==typeof e&&null!==e||"function"==typeof e}const xt=function(){const e=new WeakMap;let t=!1,i="",s=0;return function(o){return t=e.get(o),void 0===t&&(i=o.toString(),s=i.length,t=s>=29&&s<=100&&125===i.charCodeAt(s-1)&&i.charCodeAt(s-2)<=32&&93===i.charCodeAt(s-3)&&101===i.charCodeAt(s-4)&&100===i.charCodeAt(s-5)&&111===i.charCodeAt(s-6)&&99===i.charCodeAt(s-7)&&32===i.charCodeAt(s-8)&&101===i.charCodeAt(s-9)&&118===i.charCodeAt(s-10)&&105===i.charCodeAt(s-11)&&116===i.charCodeAt(s-12)&&97===i.charCodeAt(s-13)&&110===i.charCodeAt(s-14)&&88===i.charCodeAt(s-15),e.set(o,t)),t}}(),Ct={};function wt(e){switch(typeof e){case"number":return e>=0&&(0|e)===e;case"string":{const t=Ct[e];if(void 0!==t)return t;const i=e.length;if(0===i)return Ct[e]=!1;let s=0;for(let t=0;t<i;++t)if(s=e.charCodeAt(t),0===t&&48===s&&i>1||s<48||s>57)return Ct[e]=!1;return Ct[e]=!0}default:return!1}}function $t(e){return`${e.toLowerCase()}:presentation`}const kt=new Map,It=Object.freeze({define(e,t,i){const s=$t(e);void 0===kt.get(s)?kt.set(s,t):kt.set(s,!1),i.register(bt.instance(s,t))},forTag(e,t){const i=$t(e),s=kt.get(i);if(!1===s){return Ze.findResponsibleContainer(t).get(i)}return s||null}});class Tt{constructor(e,t){this.template=e||null,this.styles=void 0===t?null:Array.isArray(t)?K.create(t):t instanceof K?t:K.create([t])}applyTo(e){const t=e.$fastController;null===t.template&&(t.template=this.template),null===t.styles&&(t.styles=this.styles)}}class Ot extends fe{constructor(){super(...arguments),this._presentation=void 0}get $presentation(){return void 0===this._presentation&&(this._presentation=It.forTag(this.tagName,this)),this._presentation}templateChanged(){void 0!==this.template&&(this.$fastController.template=this.template)}stylesChanged(){void 0!==this.styles&&(this.$fastController.styles=this.styles)}connectedCallback(){null!==this.$presentation&&this.$presentation.applyTo(this),super.connectedCallback()}static compose(e){return(t={})=>new At(this===Ot?class extends Ot{}:this,e,t)}}function St(e,t,i){return"function"==typeof e?e(t,i):e}qe([b],Ot.prototype,"template",void 0),qe([b],Ot.prototype,"styles",void 0);class At{constructor(e,t,i){this.type=e,this.elementDefinition=t,this.overrideDefinition=i,this.definition=Object.assign(Object.assign({},this.elementDefinition),this.overrideDefinition)}register(e,t){const i=this.definition,s=this.overrideDefinition,o=`${i.prefix||t.elementPrefix}-${i.baseName}`;t.tryDefineElement({name:o,type:this.type,baseClass:this.elementDefinition.baseClass,callback:e=>{const t=new Tt(St(i.template,e,i),St(i.styles,e,i));e.definePresentation(t);let o=St(i.shadowOptions,e,i);e.shadowRootMode&&(o?s.shadowOptions||(o.mode=e.shadowRootMode):null!==o&&(o={mode:e.shadowRootMode})),e.defineElement({elementOptions:St(i.elementOptions,e,i),shadowOptions:o,attributes:St(i.attributes,e,i)})}})}}function Rt(e,...t){const i=ee.locate(e);t.forEach((t=>{Object.getOwnPropertyNames(t.prototype).forEach((i=>{"constructor"!==i&&Object.defineProperty(e.prototype,i,Object.getOwnPropertyDescriptor(t.prototype,i))}));ee.locate(t).forEach((e=>i.push(e)))}))}const Et={horizontal:"horizontal",vertical:"vertical"};let Dt;var Pt;!function(e){e[e.alt=18]="alt",e[e.arrowDown=40]="arrowDown",e[e.arrowLeft=37]="arrowLeft",e[e.arrowRight=39]="arrowRight",e[e.arrowUp=38]="arrowUp",e[e.back=8]="back",e[e.backSlash=220]="backSlash",e[e.break=19]="break",e[e.capsLock=20]="capsLock",e[e.closeBracket=221]="closeBracket",e[e.colon=186]="colon",e[e.colon2=59]="colon2",e[e.comma=188]="comma",e[e.ctrl=17]="ctrl",e[e.delete=46]="delete",e[e.end=35]="end",e[e.enter=13]="enter",e[e.equals=187]="equals",e[e.equals2=61]="equals2",e[e.equals3=107]="equals3",e[e.escape=27]="escape",e[e.forwardSlash=191]="forwardSlash",e[e.function1=112]="function1",e[e.function10=121]="function10",e[e.function11=122]="function11",e[e.function12=123]="function12",e[e.function2=113]="function2",e[e.function3=114]="function3",e[e.function4=115]="function4",e[e.function5=116]="function5",e[e.function6=117]="function6",e[e.function7=118]="function7",e[e.function8=119]="function8",e[e.function9=120]="function9",e[e.home=36]="home",e[e.insert=45]="insert",e[e.menu=93]="menu",e[e.minus=189]="minus",e[e.minus2=109]="minus2",e[e.numLock=144]="numLock",e[e.numPad0=96]="numPad0",e[e.numPad1=97]="numPad1",e[e.numPad2=98]="numPad2",e[e.numPad3=99]="numPad3",e[e.numPad4=100]="numPad4",e[e.numPad5=101]="numPad5",e[e.numPad6=102]="numPad6",e[e.numPad7=103]="numPad7",e[e.numPad8=104]="numPad8",e[e.numPad9=105]="numPad9",e[e.numPadDivide=111]="numPadDivide",e[e.numPadDot=110]="numPadDot",e[e.numPadMinus=109]="numPadMinus",e[e.numPadMultiply=106]="numPadMultiply",e[e.numPadPlus=107]="numPadPlus",e[e.openBracket=219]="openBracket",e[e.pageDown=34]="pageDown",e[e.pageUp=33]="pageUp",e[e.period=190]="period",e[e.print=44]="print",e[e.quote=222]="quote",e[e.scrollLock=145]="scrollLock",e[e.shift=16]="shift",e[e.space=32]="space",e[e.tab=9]="tab",e[e.tilde=192]="tilde",e[e.windowsLeft=91]="windowsLeft",e[e.windowsOpera=219]="windowsOpera",e[e.windowsRight=92]="windowsRight"}(Pt||(Pt={}));const Bt={ArrowDown:"ArrowDown",ArrowLeft:"ArrowLeft",ArrowRight:"ArrowRight",ArrowUp:"ArrowUp"};var Ft;function Lt(e,t,i=0){return[t,i]=[t,i].sort(((e,t)=>e-t)),t<=e&&e<i}!function(e){e.ltr="ltr",e.rtl="rtl"}(Ft||(Ft={}));let Ht=0;function Vt(e=""){return`${e}${Ht++}`}class Mt{}qe([oe({attribute:"aria-atomic"})],Mt.prototype,"ariaAtomic",void 0),qe([oe({attribute:"aria-busy"})],Mt.prototype,"ariaBusy",void 0),qe([oe({attribute:"aria-controls"})],Mt.prototype,"ariaControls",void 0),qe([oe({attribute:"aria-current"})],Mt.prototype,"ariaCurrent",void 0),qe([oe({attribute:"aria-describedby"})],Mt.prototype,"ariaDescribedby",void 0),qe([oe({attribute:"aria-details"})],Mt.prototype,"ariaDetails",void 0),qe([oe({attribute:"aria-disabled"})],Mt.prototype,"ariaDisabled",void 0),qe([oe({attribute:"aria-errormessage"})],Mt.prototype,"ariaErrormessage",void 0),qe([oe({attribute:"aria-flowto"})],Mt.prototype,"ariaFlowto",void 0),qe([oe({attribute:"aria-haspopup"})],Mt.prototype,"ariaHaspopup",void 0),qe([oe({attribute:"aria-hidden"})],Mt.prototype,"ariaHidden",void 0),qe([oe({attribute:"aria-invalid"})],Mt.prototype,"ariaInvalid",void 0),qe([oe({attribute:"aria-keyshortcuts"})],Mt.prototype,"ariaKeyshortcuts",void 0),qe([oe({attribute:"aria-label"})],Mt.prototype,"ariaLabel",void 0),qe([oe({attribute:"aria-labelledby"})],Mt.prototype,"ariaLabelledby",void 0),qe([oe({attribute:"aria-live"})],Mt.prototype,"ariaLive",void 0),qe([oe({attribute:"aria-owns"})],Mt.prototype,"ariaOwns",void 0),qe([oe({attribute:"aria-relevant"})],Mt.prototype,"ariaRelevant",void 0),qe([oe({attribute:"aria-roledescription"})],Mt.prototype,"ariaRoledescription",void 0);class Nt extends Ot{constructor(){super(...arguments),this.handleUnsupportedDelegatesFocus=()=>{var e;window.ShadowRoot&&!window.ShadowRoot.prototype.hasOwnProperty("delegatesFocus")&&(null===(e=this.$fastController.definition.shadowOptions)||void 0===e?void 0:e.delegatesFocus)&&(this.focus=()=>{var e;null===(e=this.control)||void 0===e||e.focus()})}}connectedCallback(){super.connectedCallback(),this.handleUnsupportedDelegatesFocus()}}qe([oe],Nt.prototype,"download",void 0),qe([oe],Nt.prototype,"href",void 0),qe([oe],Nt.prototype,"hreflang",void 0),qe([oe],Nt.prototype,"ping",void 0),qe([oe],Nt.prototype,"referrerpolicy",void 0),qe([oe],Nt.prototype,"rel",void 0),qe([oe],Nt.prototype,"target",void 0),qe([oe],Nt.prototype,"type",void 0),qe([b],Nt.prototype,"defaultSlottedContent",void 0);class zt{}qe([oe({attribute:"aria-expanded"})],zt.prototype,"ariaExpanded",void 0),Rt(zt,Mt),Rt(Nt,ze,zt);const jt=(e,t)=>U`<template class="${e=>e.circular?"circular":""}"><div class="control" part="control" style="${e=>e.generateBadgeStyle()}"><slot></slot></div></template>`;class _t extends Ot{constructor(){super(...arguments),this.generateBadgeStyle=()=>{if(!this.fill&&!this.color)return;const e=`background-color: var(--badge-fill-${this.fill});`,t=`color: var(--badge-color-${this.color});`;return this.fill&&!this.color?e:this.color&&!this.fill?t:`${t} ${e}`}}}qe([oe({attribute:"fill"})],_t.prototype,"fill",void 0),qe([oe({attribute:"color"})],_t.prototype,"color",void 0),qe([oe({mode:"boolean"})],_t.prototype,"circular",void 0);const qt="ElementInternals"in window&&"setFormValue"in window.ElementInternals.prototype,Ut=new WeakMap;function Kt(e){const t=class extends e{constructor(...e){super(...e),this.dirtyValue=!1,this.disabled=!1,this.proxyEventsToBlock=["change","click"],this.proxyInitialized=!1,this.required=!1,this.initialValue=this.initialValue||"",this.elementInternals||(this.formResetCallback=this.formResetCallback.bind(this))}static get formAssociated(){return qt}get validity(){return this.elementInternals?this.elementInternals.validity:this.proxy.validity}get form(){return this.elementInternals?this.elementInternals.form:this.proxy.form}get validationMessage(){return this.elementInternals?this.elementInternals.validationMessage:this.proxy.validationMessage}get willValidate(){return this.elementInternals?this.elementInternals.willValidate:this.proxy.willValidate}get labels(){if(this.elementInternals)return Object.freeze(Array.from(this.elementInternals.labels));if(this.proxy instanceof HTMLElement&&this.proxy.ownerDocument&&this.id){const e=this.proxy.labels,t=Array.from(this.proxy.getRootNode().querySelectorAll(`[for='${this.id}']`)),i=e?t.concat(Array.from(e)):t;return Object.freeze(i)}return s}valueChanged(e,t){this.dirtyValue=!0,this.proxy instanceof HTMLElement&&(this.proxy.value=this.value),this.currentValue=this.value,this.setFormValue(this.value),this.validate()}currentValueChanged(){this.value=this.currentValue}initialValueChanged(e,t){this.dirtyValue||(this.value=this.initialValue,this.dirtyValue=!1)}disabledChanged(e,t){this.proxy instanceof HTMLElement&&(this.proxy.disabled=this.disabled),h.queueUpdate((()=>this.classList.toggle("disabled",this.disabled)))}nameChanged(e,t){this.proxy instanceof HTMLElement&&(this.proxy.name=this.name)}requiredChanged(e,t){this.proxy instanceof HTMLElement&&(this.proxy.required=this.required),h.queueUpdate((()=>this.classList.toggle("required",this.required))),this.validate()}get elementInternals(){if(!qt)return null;let e=Ut.get(this);return e||(e=this.attachInternals(),Ut.set(this,e)),e}connectedCallback(){super.connectedCallback(),this.addEventListener("keypress",this._keypressHandler),this.value||(this.value=this.initialValue,this.dirtyValue=!1),this.elementInternals||(this.attachProxy(),this.form&&this.form.addEventListener("reset",this.formResetCallback))}disconnectedCallback(){super.disconnectedCallback(),this.proxyEventsToBlock.forEach((e=>this.proxy.removeEventListener(e,this.stopPropagation))),!this.elementInternals&&this.form&&this.form.removeEventListener("reset",this.formResetCallback)}checkValidity(){return this.elementInternals?this.elementInternals.checkValidity():this.proxy.checkValidity()}reportValidity(){return this.elementInternals?this.elementInternals.reportValidity():this.proxy.reportValidity()}setValidity(e,t,i){this.elementInternals?this.elementInternals.setValidity(e,t,i):"string"==typeof t&&this.proxy.setCustomValidity(t)}formDisabledCallback(e){this.disabled=e}formResetCallback(){this.value=this.initialValue,this.dirtyValue=!1}attachProxy(){var e;this.proxyInitialized||(this.proxyInitialized=!0,this.proxy.style.display="none",this.proxyEventsToBlock.forEach((e=>this.proxy.addEventListener(e,this.stopPropagation))),this.proxy.disabled=this.disabled,this.proxy.required=this.required,"string"==typeof this.name&&(this.proxy.name=this.name),"string"==typeof this.value&&(this.proxy.value=this.value),this.proxy.setAttribute("slot","form-associated-proxy"),this.proxySlot=document.createElement("slot"),this.proxySlot.setAttribute("name","form-associated-proxy")),null===(e=this.shadowRoot)||void 0===e||e.appendChild(this.proxySlot),this.appendChild(this.proxy)}detachProxy(){var e;this.removeChild(this.proxy),null===(e=this.shadowRoot)||void 0===e||e.removeChild(this.proxySlot)}validate(e){this.proxy instanceof HTMLElement&&this.setValidity(this.proxy.validity,this.proxy.validationMessage,e)}setFormValue(e,t){this.elementInternals&&this.elementInternals.setFormValue(e,t||e)}_keypressHandler(e){if("Enter"===e.key)if(this.form instanceof HTMLFormElement){const e=this.form.querySelector("[type=submit]");null==e||e.click()}}stopPropagation(e){e.stopPropagation()}};return oe({mode:"boolean"})(t.prototype,"disabled"),oe({mode:"fromView",attribute:"value"})(t.prototype,"initialValue"),oe({attribute:"current-value"})(t.prototype,"currentValue"),oe(t.prototype,"name"),oe({mode:"boolean"})(t.prototype,"required"),b(t.prototype,"value"),t}function Wt(e){class t extends(Kt(e)){}class i extends t{constructor(...e){super(e),this.dirtyChecked=!1,this.checkedAttribute=!1,this.checked=!1,this.dirtyChecked=!1}checkedAttributeChanged(){this.defaultChecked=this.checkedAttribute}defaultCheckedChanged(){this.dirtyChecked||(this.checked=this.defaultChecked,this.dirtyChecked=!1)}checkedChanged(e,t){this.dirtyChecked||(this.dirtyChecked=!0),this.currentChecked=this.checked,this.updateForm(),this.proxy instanceof HTMLInputElement&&(this.proxy.checked=this.checked),void 0!==e&&this.$emit("change"),this.validate()}currentCheckedChanged(e,t){this.checked=this.currentChecked}updateForm(){const e=this.checked?this.value:null;this.setFormValue(e,e)}connectedCallback(){super.connectedCallback(),this.updateForm()}formResetCallback(){super.formResetCallback(),this.checked=!!this.checkedAttribute,this.dirtyChecked=!1}}return oe({attribute:"checked",mode:"boolean"})(i.prototype,"checkedAttribute"),oe({attribute:"current-checked",converter:te})(i.prototype,"currentChecked"),b(i.prototype,"defaultChecked"),b(i.prototype,"checked"),i}class Gt extends Ot{}class Qt extends(Kt(Gt)){constructor(){super(...arguments),this.proxy=document.createElement("input")}}class Xt extends Qt{constructor(){super(...arguments),this.handleClick=e=>{var t;this.disabled&&(null===(t=this.defaultSlottedContent)||void 0===t?void 0:t.length)<=1&&e.stopPropagation()},this.handleSubmission=()=>{if(!this.form)return;const e=this.proxy.isConnected;e||this.attachProxy(),"function"==typeof this.form.requestSubmit?this.form.requestSubmit(this.proxy):this.proxy.click(),e||this.detachProxy()},this.handleFormReset=()=>{var e;null===(e=this.form)||void 0===e||e.reset()},this.handleUnsupportedDelegatesFocus=()=>{var e;window.ShadowRoot&&!window.ShadowRoot.prototype.hasOwnProperty("delegatesFocus")&&(null===(e=this.$fastController.definition.shadowOptions)||void 0===e?void 0:e.delegatesFocus)&&(this.focus=()=>{this.control.focus()})}}formactionChanged(){this.proxy instanceof HTMLInputElement&&(this.proxy.formAction=this.formaction)}formenctypeChanged(){this.proxy instanceof HTMLInputElement&&(this.proxy.formEnctype=this.formenctype)}formmethodChanged(){this.proxy instanceof HTMLInputElement&&(this.proxy.formMethod=this.formmethod)}formnovalidateChanged(){this.proxy instanceof HTMLInputElement&&(this.proxy.formNoValidate=this.formnovalidate)}formtargetChanged(){this.proxy instanceof HTMLInputElement&&(this.proxy.formTarget=this.formtarget)}typeChanged(e,t){this.proxy instanceof HTMLInputElement&&(this.proxy.type=this.type),"submit"===t&&this.addEventListener("click",this.handleSubmission),"submit"===e&&this.removeEventListener("click",this.handleSubmission),"reset"===t&&this.addEventListener("click",this.handleFormReset),"reset"===e&&this.removeEventListener("click",this.handleFormReset)}validate(){super.validate(this.control)}connectedCallback(){var e;super.connectedCallback(),this.proxy.setAttribute("type",this.type),this.handleUnsupportedDelegatesFocus();const t=Array.from(null===(e=this.control)||void 0===e?void 0:e.children);t&&t.forEach((e=>{e.addEventListener("click",this.handleClick)}))}disconnectedCallback(){var e;super.disconnectedCallback();const t=Array.from(null===(e=this.control)||void 0===e?void 0:e.children);t&&t.forEach((e=>{e.removeEventListener("click",this.handleClick)}))}}qe([oe({mode:"boolean"})],Xt.prototype,"autofocus",void 0),qe([oe({attribute:"form"})],Xt.prototype,"formId",void 0),qe([oe],Xt.prototype,"formaction",void 0),qe([oe],Xt.prototype,"formenctype",void 0),qe([oe],Xt.prototype,"formmethod",void 0),qe([oe({mode:"boolean"})],Xt.prototype,"formnovalidate",void 0),qe([oe],Xt.prototype,"formtarget",void 0),qe([oe],Xt.prototype,"type",void 0),qe([b],Xt.prototype,"defaultSlottedContent",void 0);class Yt{}qe([oe({attribute:"aria-expanded"})],Yt.prototype,"ariaExpanded",void 0),qe([oe({attribute:"aria-pressed"})],Yt.prototype,"ariaPressed",void 0),Rt(Yt,Mt),Rt(Xt,ze,Yt);const Jt={none:"none",default:"default",sticky:"sticky"},Zt={default:"default",columnHeader:"columnheader",rowHeader:"rowheader"},ei={default:"default",header:"header",stickyHeader:"sticky-header"};class ti extends Ot{constructor(){super(...arguments),this.rowType=ei.default,this.rowData=null,this.columnDefinitions=null,this.isActiveRow=!1,this.cellsRepeatBehavior=null,this.cellsPlaceholder=null,this.focusColumnIndex=0,this.refocusOnLoad=!1,this.updateRowStyle=()=>{this.style.gridTemplateColumns=this.gridTemplateColumns}}gridTemplateColumnsChanged(){this.$fastController.isConnected&&this.updateRowStyle()}rowTypeChanged(){this.$fastController.isConnected&&this.updateItemTemplate()}rowDataChanged(){null!==this.rowData&&this.isActiveRow&&(this.refocusOnLoad=!0)}cellItemTemplateChanged(){this.updateItemTemplate()}headerCellItemTemplateChanged(){this.updateItemTemplate()}connectedCallback(){super.connectedCallback(),null===this.cellsRepeatBehavior&&(this.cellsPlaceholder=document.createComment(""),this.appendChild(this.cellsPlaceholder),this.updateItemTemplate(),this.cellsRepeatBehavior=new Be((e=>e.columnDefinitions),(e=>e.activeCellItemTemplate),{positioning:!0}).createBehavior(this.cellsPlaceholder),this.$fastController.addBehaviors([this.cellsRepeatBehavior])),this.addEventListener("cell-focused",this.handleCellFocus),this.addEventListener("focusout",this.handleFocusout),this.addEventListener("keydown",this.handleKeydown),this.updateRowStyle(),this.refocusOnLoad&&(this.refocusOnLoad=!1,this.cellElements.length>this.focusColumnIndex&&this.cellElements[this.focusColumnIndex].focus())}disconnectedCallback(){super.disconnectedCallback(),this.removeEventListener("cell-focused",this.handleCellFocus),this.removeEventListener("focusout",this.handleFocusout),this.removeEventListener("keydown",this.handleKeydown)}handleFocusout(e){this.contains(e.target)||(this.isActiveRow=!1,this.focusColumnIndex=0)}handleCellFocus(e){this.isActiveRow=!0,this.focusColumnIndex=this.cellElements.indexOf(e.target),this.$emit("row-focused",this)}handleKeydown(e){if(e.defaultPrevented)return;let t=0;switch(e.key){case"ArrowLeft":t=Math.max(0,this.focusColumnIndex-1),this.cellElements[t].focus(),e.preventDefault();break;case"ArrowRight":t=Math.min(this.cellElements.length-1,this.focusColumnIndex+1),this.cellElements[t].focus(),e.preventDefault();break;case"Home":e.ctrlKey||(this.cellElements[0].focus(),e.preventDefault());break;case"End":e.ctrlKey||(this.cellElements[this.cellElements.length-1].focus(),e.preventDefault())}}updateItemTemplate(){this.activeCellItemTemplate=this.rowType===ei.default&&void 0!==this.cellItemTemplate?this.cellItemTemplate:this.rowType===ei.default&&void 0===this.cellItemTemplate?this.defaultCellItemTemplate:void 0!==this.headerCellItemTemplate?this.headerCellItemTemplate:this.defaultHeaderCellItemTemplate}}qe([oe({attribute:"grid-template-columns"})],ti.prototype,"gridTemplateColumns",void 0),qe([oe({attribute:"row-type"})],ti.prototype,"rowType",void 0),qe([b],ti.prototype,"rowData",void 0),qe([b],ti.prototype,"columnDefinitions",void 0),qe([b],ti.prototype,"cellItemTemplate",void 0),qe([b],ti.prototype,"headerCellItemTemplate",void 0),qe([b],ti.prototype,"rowIndex",void 0),qe([b],ti.prototype,"isActiveRow",void 0),qe([b],ti.prototype,"activeCellItemTemplate",void 0),qe([b],ti.prototype,"defaultCellItemTemplate",void 0),qe([b],ti.prototype,"defaultHeaderCellItemTemplate",void 0),qe([b],ti.prototype,"cellElements",void 0);class ii extends Ot{constructor(){super(),this.noTabbing=!1,this.generateHeader=Jt.default,this.rowsData=[],this.columnDefinitions=null,this.focusRowIndex=0,this.focusColumnIndex=0,this.rowsPlaceholder=null,this.generatedHeader=null,this.isUpdatingFocus=!1,this.pendingFocusUpdate=!1,this.rowindexUpdateQueued=!1,this.columnDefinitionsStale=!0,this.generatedGridTemplateColumns="",this.focusOnCell=(e,t,i)=>{if(0===this.rowElements.length)return this.focusRowIndex=0,void(this.focusColumnIndex=0);const s=Math.max(0,Math.min(this.rowElements.length-1,e)),o=this.rowElements[s].querySelectorAll('[role="cell"], [role="gridcell"], [role="columnheader"], [role="rowheader"]'),n=o[Math.max(0,Math.min(o.length-1,t))];i&&this.scrollHeight!==this.clientHeight&&(s<this.focusRowIndex&&this.scrollTop>0||s>this.focusRowIndex&&this.scrollTop<this.scrollHeight-this.clientHeight)&&n.scrollIntoView({block:"center",inline:"center"}),n.focus()},this.onChildListChange=(e,t)=>{e&&e.length&&(e.forEach((e=>{e.addedNodes.forEach((e=>{1===e.nodeType&&"row"===e.getAttribute("role")&&(e.columnDefinitions=this.columnDefinitions)}))})),this.queueRowIndexUpdate())},this.queueRowIndexUpdate=()=>{this.rowindexUpdateQueued||(this.rowindexUpdateQueued=!0,h.queueUpdate(this.updateRowIndexes))},this.updateRowIndexes=()=>{let e=this.gridTemplateColumns;if(void 0===e){if(""===this.generatedGridTemplateColumns&&this.rowElements.length>0){const e=this.rowElements[0];this.generatedGridTemplateColumns=new Array(e.cellElements.length).fill("1fr").join(" ")}e=this.generatedGridTemplateColumns}this.rowElements.forEach(((t,i)=>{const s=t;s.rowIndex=i,s.gridTemplateColumns=e,this.columnDefinitionsStale&&(s.columnDefinitions=this.columnDefinitions)})),this.rowindexUpdateQueued=!1,this.columnDefinitionsStale=!1}}static generateTemplateColumns(e){let t="";return e.forEach((e=>{t=`${t}${""===t?"":" "}1fr`})),t}noTabbingChanged(){this.$fastController.isConnected&&(this.noTabbing?this.setAttribute("tabIndex","-1"):this.setAttribute("tabIndex",this.contains(document.activeElement)||this===document.activeElement?"-1":"0"))}generateHeaderChanged(){this.$fastController.isConnected&&this.toggleGeneratedHeader()}gridTemplateColumnsChanged(){this.$fastController.isConnected&&this.updateRowIndexes()}rowsDataChanged(){null===this.columnDefinitions&&this.rowsData.length>0&&(this.columnDefinitions=ii.generateColumns(this.rowsData[0])),this.$fastController.isConnected&&this.toggleGeneratedHeader()}columnDefinitionsChanged(){null!==this.columnDefinitions?(this.generatedGridTemplateColumns=ii.generateTemplateColumns(this.columnDefinitions),this.$fastController.isConnected&&(this.columnDefinitionsStale=!0,this.queueRowIndexUpdate())):this.generatedGridTemplateColumns=""}headerCellItemTemplateChanged(){this.$fastController.isConnected&&null!==this.generatedHeader&&(this.generatedHeader.headerCellItemTemplate=this.headerCellItemTemplate)}focusRowIndexChanged(){this.$fastController.isConnected&&this.queueFocusUpdate()}focusColumnIndexChanged(){this.$fastController.isConnected&&this.queueFocusUpdate()}connectedCallback(){super.connectedCallback(),void 0===this.rowItemTemplate&&(this.rowItemTemplate=this.defaultRowItemTemplate),this.rowsPlaceholder=document.createComment(""),this.appendChild(this.rowsPlaceholder),this.toggleGeneratedHeader(),this.rowsRepeatBehavior=new Be((e=>e.rowsData),(e=>e.rowItemTemplate),{positioning:!0}).createBehavior(this.rowsPlaceholder),this.$fastController.addBehaviors([this.rowsRepeatBehavior]),this.addEventListener("row-focused",this.handleRowFocus),this.addEventListener("focus",this.handleFocus),this.addEventListener("keydown",this.handleKeydown),this.addEventListener("focusout",this.handleFocusOut),this.observer=new MutationObserver(this.onChildListChange),this.observer.observe(this,{childList:!0}),this.noTabbing&&this.setAttribute("tabindex","-1"),h.queueUpdate(this.queueRowIndexUpdate)}disconnectedCallback(){super.disconnectedCallback(),this.removeEventListener("row-focused",this.handleRowFocus),this.removeEventListener("focus",this.handleFocus),this.removeEventListener("keydown",this.handleKeydown),this.removeEventListener("focusout",this.handleFocusOut),this.observer.disconnect(),this.rowsPlaceholder=null,this.generatedHeader=null}handleRowFocus(e){this.isUpdatingFocus=!0;const t=e.target;this.focusRowIndex=this.rowElements.indexOf(t),this.focusColumnIndex=t.focusColumnIndex,this.setAttribute("tabIndex","-1"),this.isUpdatingFocus=!1}handleFocus(e){this.focusOnCell(this.focusRowIndex,this.focusColumnIndex,!0)}handleFocusOut(e){null!==e.relatedTarget&&this.contains(e.relatedTarget)||this.setAttribute("tabIndex",this.noTabbing?"-1":"0")}handleKeydown(e){if(e.defaultPrevented)return;let t;const i=this.rowElements.length-1,s=this.offsetHeight+this.scrollTop,o=this.rowElements[i];switch(e.key){case"ArrowUp":e.preventDefault(),this.focusOnCell(this.focusRowIndex-1,this.focusColumnIndex,!0);break;case"ArrowDown":e.preventDefault(),this.focusOnCell(this.focusRowIndex+1,this.focusColumnIndex,!0);break;case"PageUp":if(e.preventDefault(),0===this.rowElements.length){this.focusOnCell(0,0,!1);break}if(0===this.focusRowIndex)return void this.focusOnCell(0,this.focusColumnIndex,!1);for(t=this.focusRowIndex-1;t>=0;t--){const e=this.rowElements[t];if(e.offsetTop<this.scrollTop){this.scrollTop=e.offsetTop+e.clientHeight-this.clientHeight;break}}this.focusOnCell(t,this.focusColumnIndex,!1);break;case"PageDown":if(e.preventDefault(),0===this.rowElements.length){this.focusOnCell(0,0,!1);break}if(this.focusRowIndex>=i||o.offsetTop+o.offsetHeight<=s)return void this.focusOnCell(i,this.focusColumnIndex,!1);for(t=this.focusRowIndex+1;t<=i;t++){const e=this.rowElements[t];if(e.offsetTop+e.offsetHeight>s){let t=0;this.generateHeader===Jt.sticky&&null!==this.generatedHeader&&(t=this.generatedHeader.clientHeight),this.scrollTop=e.offsetTop-t;break}}this.focusOnCell(t,this.focusColumnIndex,!1);break;case"Home":e.ctrlKey&&(e.preventDefault(),this.focusOnCell(0,0,!0));break;case"End":e.ctrlKey&&null!==this.columnDefinitions&&(e.preventDefault(),this.focusOnCell(this.rowElements.length-1,this.columnDefinitions.length-1,!0))}}queueFocusUpdate(){this.isUpdatingFocus&&(this.contains(document.activeElement)||this===document.activeElement)||!1===this.pendingFocusUpdate&&(this.pendingFocusUpdate=!0,h.queueUpdate((()=>this.updateFocus())))}updateFocus(){this.pendingFocusUpdate=!1,this.focusOnCell(this.focusRowIndex,this.focusColumnIndex,!0)}toggleGeneratedHeader(){if(null!==this.generatedHeader&&(this.removeChild(this.generatedHeader),this.generatedHeader=null),this.generateHeader!==Jt.none&&this.rowsData.length>0){const e=document.createElement(this.rowElementTag);return this.generatedHeader=e,this.generatedHeader.columnDefinitions=this.columnDefinitions,this.generatedHeader.gridTemplateColumns=this.gridTemplateColumns,this.generatedHeader.rowType=this.generateHeader===Jt.sticky?ei.stickyHeader:ei.header,void(null===this.firstChild&&null===this.rowsPlaceholder||this.insertBefore(e,null!==this.firstChild?this.firstChild:this.rowsPlaceholder))}}}ii.generateColumns=e=>Object.getOwnPropertyNames(e).map(((e,t)=>({columnDataKey:e,gridColumn:`${t}`}))),qe([oe({attribute:"no-tabbing",mode:"boolean"})],ii.prototype,"noTabbing",void 0),qe([oe({attribute:"generate-header"})],ii.prototype,"generateHeader",void 0),qe([oe({attribute:"grid-template-columns"})],ii.prototype,"gridTemplateColumns",void 0),qe([b],ii.prototype,"rowsData",void 0),qe([b],ii.prototype,"columnDefinitions",void 0),qe([b],ii.prototype,"rowItemTemplate",void 0),qe([b],ii.prototype,"cellItemTemplate",void 0),qe([b],ii.prototype,"headerCellItemTemplate",void 0),qe([b],ii.prototype,"focusRowIndex",void 0),qe([b],ii.prototype,"focusColumnIndex",void 0),qe([b],ii.prototype,"defaultRowItemTemplate",void 0),qe([b],ii.prototype,"rowElementTag",void 0),qe([b],ii.prototype,"rowElements",void 0);const si=U`<template>${e=>null===e.rowData||null===e.columnDefinition||null===e.columnDefinition.columnDataKey?null:e.rowData[e.columnDefinition.columnDataKey]}</template>`,oi=U`<template>${e=>null===e.columnDefinition?null:void 0===e.columnDefinition.title?e.columnDefinition.columnDataKey:e.columnDefinition.title}</template>`;class ni extends Ot{constructor(){super(...arguments),this.cellType=Zt.default,this.rowData=null,this.columnDefinition=null,this.isActiveCell=!1,this.customCellView=null,this.updateCellStyle=()=>{this.style.gridColumn=this.gridColumn}}cellTypeChanged(){this.$fastController.isConnected&&this.updateCellView()}gridColumnChanged(){this.$fastController.isConnected&&this.updateCellStyle()}columnDefinitionChanged(e,t){this.$fastController.isConnected&&this.updateCellView()}connectedCallback(){var e;super.connectedCallback(),this.addEventListener("focusin",this.handleFocusin),this.addEventListener("focusout",this.handleFocusout),this.addEventListener("keydown",this.handleKeydown),this.style.gridColumn=`${void 0===(null===(e=this.columnDefinition)||void 0===e?void 0:e.gridColumn)?0:this.columnDefinition.gridColumn}`,this.updateCellView(),this.updateCellStyle()}disconnectedCallback(){super.disconnectedCallback(),this.removeEventListener("focusin",this.handleFocusin),this.removeEventListener("focusout",this.handleFocusout),this.removeEventListener("keydown",this.handleKeydown),this.disconnectCellView()}handleFocusin(e){if(!this.isActiveCell){if(this.isActiveCell=!0,this.cellType===Zt.columnHeader){if(null!==this.columnDefinition&&!0!==this.columnDefinition.headerCellInternalFocusQueue&&"function"==typeof this.columnDefinition.headerCellFocusTargetCallback){const e=this.columnDefinition.headerCellFocusTargetCallback(this);null!==e&&e.focus()}}else if(null!==this.columnDefinition&&!0!==this.columnDefinition.cellInternalFocusQueue&&"function"==typeof this.columnDefinition.cellFocusTargetCallback){const e=this.columnDefinition.cellFocusTargetCallback(this);null!==e&&e.focus()}this.$emit("cell-focused",this)}}handleFocusout(e){this===document.activeElement||this.contains(document.activeElement)||(this.isActiveCell=!1)}handleKeydown(e){if(!(e.defaultPrevented||null===this.columnDefinition||this.cellType===Zt.default&&!0!==this.columnDefinition.cellInternalFocusQueue||this.cellType===Zt.columnHeader&&!0!==this.columnDefinition.headerCellInternalFocusQueue))switch(e.key){case"Enter":case"F2":if(this.contains(document.activeElement)&&document.activeElement!==this)return;if(this.cellType===Zt.columnHeader){if(void 0!==this.columnDefinition.headerCellFocusTargetCallback){const t=this.columnDefinition.headerCellFocusTargetCallback(this);null!==t&&t.focus(),e.preventDefault()}}else if(void 0!==this.columnDefinition.cellFocusTargetCallback){const t=this.columnDefinition.cellFocusTargetCallback(this);null!==t&&t.focus(),e.preventDefault()}break;case"Escape":this.contains(document.activeElement)&&document.activeElement!==this&&(this.focus(),e.preventDefault())}}updateCellView(){if(this.disconnectCellView(),null!==this.columnDefinition)switch(this.cellType){case Zt.columnHeader:void 0!==this.columnDefinition.headerCellTemplate?this.customCellView=this.columnDefinition.headerCellTemplate.render(this,this):this.customCellView=oi.render(this,this);break;case void 0:case Zt.rowHeader:case Zt.default:void 0!==this.columnDefinition.cellTemplate?this.customCellView=this.columnDefinition.cellTemplate.render(this,this):this.customCellView=si.render(this,this)}}disconnectCellView(){null!==this.customCellView&&(this.customCellView.dispose(),this.customCellView=null)}}qe([oe({attribute:"cell-type"})],ni.prototype,"cellType",void 0),qe([oe({attribute:"grid-column"})],ni.prototype,"gridColumn",void 0),qe([b],ni.prototype,"rowData",void 0),qe([b],ni.prototype,"columnDefinition",void 0);class ri extends Ot{}class ai extends(Wt(ri)){constructor(){super(...arguments),this.proxy=document.createElement("input")}}class li extends ai{constructor(){super(),this.initialValue="on",this.indeterminate=!1,this.keypressHandler=e=>{if(!this.readOnly&&" "===e.key)this.indeterminate&&(this.indeterminate=!1),this.checked=!this.checked},this.clickHandler=e=>{this.disabled||this.readOnly||(this.indeterminate&&(this.indeterminate=!1),this.checked=!this.checked)},this.proxy.setAttribute("type","checkbox")}readOnlyChanged(){this.proxy instanceof HTMLInputElement&&(this.proxy.readOnly=this.readOnly)}}function di(e){return function(...e){return e.every((e=>e instanceof HTMLElement))}(e)&&("option"===e.getAttribute("role")||e instanceof HTMLOptionElement)}qe([oe({attribute:"readonly",mode:"boolean"})],li.prototype,"readOnly",void 0),qe([b],li.prototype,"defaultSlottedNodes",void 0),qe([b],li.prototype,"indeterminate",void 0);class ci extends Ot{constructor(e,t,i,s){super(),this.defaultSelected=!1,this.dirtySelected=!1,this.selected=this.defaultSelected,this.dirtyValue=!1,e&&(this.textContent=e),t&&(this.initialValue=t),i&&(this.defaultSelected=i),s&&(this.selected=s),this.proxy=new Option(`${this.textContent}`,this.initialValue,this.defaultSelected,this.selected),this.proxy.disabled=this.disabled}checkedChanged(e,t){this.ariaChecked="boolean"!=typeof t?null:t?"true":"false"}contentChanged(e,t){this.proxy instanceof HTMLOptionElement&&(this.proxy.textContent=this.textContent),this.$emit("contentchange",null,{bubbles:!0})}defaultSelectedChanged(){this.dirtySelected||(this.selected=this.defaultSelected,this.proxy instanceof HTMLOptionElement&&(this.proxy.selected=this.defaultSelected))}disabledChanged(e,t){this.ariaDisabled=this.disabled?"true":"false",this.proxy instanceof HTMLOptionElement&&(this.proxy.disabled=this.disabled)}selectedAttributeChanged(){this.defaultSelected=this.selectedAttribute,this.proxy instanceof HTMLOptionElement&&(this.proxy.defaultSelected=this.defaultSelected)}selectedChanged(){this.ariaSelected=this.selected?"true":"false",this.dirtySelected||(this.dirtySelected=!0),this.proxy instanceof HTMLOptionElement&&(this.proxy.selected=this.selected)}initialValueChanged(e,t){this.dirtyValue||(this.value=this.initialValue,this.dirtyValue=!1)}get label(){var e;return null!==(e=this.value)&&void 0!==e?e:this.text}get text(){var e,t;return null!==(t=null===(e=this.textContent)||void 0===e?void 0:e.replace(/\s+/g," ").trim())&&void 0!==t?t:""}set value(e){const t=`${null!=e?e:""}`;this._value=t,this.dirtyValue=!0,this.proxy instanceof HTMLOptionElement&&(this.proxy.value=t),f.notify(this,"value")}get value(){var e;return f.track(this,"value"),null!==(e=this._value)&&void 0!==e?e:this.text}get form(){return this.proxy?this.proxy.form:null}}qe([b],ci.prototype,"checked",void 0),qe([b],ci.prototype,"content",void 0),qe([b],ci.prototype,"defaultSelected",void 0),qe([oe({mode:"boolean"})],ci.prototype,"disabled",void 0),qe([oe({attribute:"selected",mode:"boolean"})],ci.prototype,"selectedAttribute",void 0),qe([b],ci.prototype,"selected",void 0),qe([oe({attribute:"value",mode:"fromView"})],ci.prototype,"initialValue",void 0);class hi{}qe([b],hi.prototype,"ariaChecked",void 0),qe([b],hi.prototype,"ariaPosInSet",void 0),qe([b],hi.prototype,"ariaSelected",void 0),qe([b],hi.prototype,"ariaSetSize",void 0),Rt(hi,Mt),Rt(ci,ze,hi);class ui extends Ot{constructor(){super(...arguments),this._options=[],this.selectedIndex=-1,this.selectedOptions=[],this.shouldSkipFocus=!1,this.typeaheadBuffer="",this.typeaheadExpired=!0,this.typeaheadTimeout=-1}get firstSelectedOption(){var e;return null!==(e=this.selectedOptions[0])&&void 0!==e?e:null}get hasSelectableOptions(){return this.options.length>0&&!this.options.every((e=>e.disabled))}get length(){var e,t;return null!==(t=null===(e=this.options)||void 0===e?void 0:e.length)&&void 0!==t?t:0}get options(){return f.track(this,"options"),this._options}set options(e){this._options=e,f.notify(this,"options")}get typeAheadExpired(){return this.typeaheadExpired}set typeAheadExpired(e){this.typeaheadExpired=e}clickHandler(e){const t=e.target.closest("option,[role=option]");if(t&&!t.disabled)return this.selectedIndex=this.options.indexOf(t),!0}focusAndScrollOptionIntoView(e=this.firstSelectedOption){this.contains(document.activeElement)&&null!==e&&(e.focus(),requestAnimationFrame((()=>{e.scrollIntoView({block:"nearest"})})))}focusinHandler(e){this.shouldSkipFocus||e.target!==e.currentTarget||(this.setSelectedOptions(),this.focusAndScrollOptionIntoView()),this.shouldSkipFocus=!1}getTypeaheadMatches(){const e=this.typeaheadBuffer.replace(/[.*+\-?^${}()|[\]\\]/g,"\\$&"),t=new RegExp(`^${e}`,"gi");return this.options.filter((e=>e.text.trim().match(t)))}getSelectableIndex(e=this.selectedIndex,t){const i=e>t?-1:e<t?1:0,s=e+i;let o=null;switch(i){case-1:o=this.options.reduceRight(((e,t,i)=>!e&&!t.disabled&&i<s?t:e),o);break;case 1:o=this.options.reduce(((e,t,i)=>!e&&!t.disabled&&i>s?t:e),o)}return this.options.indexOf(o)}handleChange(e,t){if("selected"===t)ui.slottedOptionFilter(e)&&(this.selectedIndex=this.options.indexOf(e)),this.setSelectedOptions()}handleTypeAhead(e){this.typeaheadTimeout&&window.clearTimeout(this.typeaheadTimeout),this.typeaheadTimeout=window.setTimeout((()=>this.typeaheadExpired=!0),ui.TYPE_AHEAD_TIMEOUT_MS),e.length>1||(this.typeaheadBuffer=`${this.typeaheadExpired?"":this.typeaheadBuffer}${e}`)}keydownHandler(e){if(this.disabled)return!0;this.shouldSkipFocus=!1;const t=e.key;switch(t){case"Home":e.shiftKey||(e.preventDefault(),this.selectFirstOption());break;case"ArrowDown":e.shiftKey||(e.preventDefault(),this.selectNextOption());break;case"ArrowUp":e.shiftKey||(e.preventDefault(),this.selectPreviousOption());break;case"End":e.preventDefault(),this.selectLastOption();break;case"Tab":return this.focusAndScrollOptionIntoView(),!0;case"Enter":case"Escape":return!0;case" ":if(this.typeaheadExpired)return!0;default:return 1===t.length&&this.handleTypeAhead(`${t}`),!0}}mousedownHandler(e){return this.shouldSkipFocus=!this.contains(document.activeElement),!0}multipleChanged(e,t){this.ariaMultiSelectable=t?"true":null}selectedIndexChanged(e,t){var i;if(this.hasSelectableOptions){if((null===(i=this.options[this.selectedIndex])||void 0===i?void 0:i.disabled)&&"number"==typeof e){const i=this.getSelectableIndex(e,t),s=i>-1?i:e;return this.selectedIndex=s,void(t===s&&this.selectedIndexChanged(t,s))}this.setSelectedOptions()}else this.selectedIndex=-1}selectedOptionsChanged(e,t){var i;const s=t.filter(ui.slottedOptionFilter);null===(i=this.options)||void 0===i||i.forEach((e=>{const t=f.getNotifier(e);t.unsubscribe(this,"selected"),e.selected=s.includes(e),t.subscribe(this,"selected")}))}selectFirstOption(){var e,t;this.disabled||(this.selectedIndex=null!==(t=null===(e=this.options)||void 0===e?void 0:e.findIndex((e=>!e.disabled)))&&void 0!==t?t:-1)}selectLastOption(){this.disabled||(this.selectedIndex=function(e,t){let i=e.length;for(;i--;)if(t(e[i],i,e))return i;return-1}(this.options,(e=>!e.disabled)))}selectNextOption(){!this.disabled&&this.selectedIndex<this.options.length-1&&(this.selectedIndex+=1)}selectPreviousOption(){!this.disabled&&this.selectedIndex>0&&(this.selectedIndex=this.selectedIndex-1)}setDefaultSelectedOption(){var e,t;this.selectedIndex=null!==(t=null===(e=this.options)||void 0===e?void 0:e.findIndex((e=>e.defaultSelected)))&&void 0!==t?t:-1}setSelectedOptions(){var e,t,i;(null===(e=this.options)||void 0===e?void 0:e.length)&&(this.selectedOptions=[this.options[this.selectedIndex]],this.ariaActiveDescendant=null!==(i=null===(t=this.firstSelectedOption)||void 0===t?void 0:t.id)&&void 0!==i?i:"",this.focusAndScrollOptionIntoView())}slottedOptionsChanged(e,t){this.options=t.reduce(((e,t)=>(di(t)&&e.push(t),e)),[]);const i=`${this.options.length}`;this.options.forEach(((e,t)=>{e.id||(e.id=Vt("option-")),e.ariaPosInSet=`${t+1}`,e.ariaSetSize=i})),this.$fastController.isConnected&&(this.setSelectedOptions(),this.setDefaultSelectedOption())}typeaheadBufferChanged(e,t){if(this.$fastController.isConnected){const e=this.getTypeaheadMatches();if(e.length){const t=this.options.indexOf(e[0]);t>-1&&(this.selectedIndex=t)}this.typeaheadExpired=!1}}}ui.slottedOptionFilter=e=>di(e)&&!e.hidden,ui.TYPE_AHEAD_TIMEOUT_MS=1e3,qe([oe({mode:"boolean"})],ui.prototype,"disabled",void 0),qe([b],ui.prototype,"selectedIndex",void 0),qe([b],ui.prototype,"selectedOptions",void 0),qe([b],ui.prototype,"slottedOptions",void 0),qe([b],ui.prototype,"typeaheadBuffer",void 0);class pi{}qe([b],pi.prototype,"ariaActiveDescendant",void 0),qe([b],pi.prototype,"ariaDisabled",void 0),qe([b],pi.prototype,"ariaExpanded",void 0),qe([b],pi.prototype,"ariaMultiSelectable",void 0),Rt(pi,Mt),Rt(ui,pi);const fi={above:"above",below:"below"};function bi(e){const t=e.parentElement;if(t)return t;{const t=e.getRootNode();if(t.host instanceof HTMLElement)return t.host}return null}const gi=document.createElement("div");class mi{setProperty(e,t){h.queueUpdate((()=>this.target.setProperty(e,t)))}removeProperty(e){h.queueUpdate((()=>this.target.removeProperty(e)))}}class vi extends mi{constructor(){super();const e=new CSSStyleSheet;this.target=e.cssRules[e.insertRule(":root{}")].style,document.adoptedStyleSheets=[...document.adoptedStyleSheets,e]}}class yi extends mi{constructor(){super(),this.style=document.createElement("style"),document.head.appendChild(this.style);const{sheet:e}=this.style;if(e){const t=e.insertRule(":root{}",e.cssRules.length);this.target=e.cssRules[t].style}}}class xi{constructor(e){this.store=new Map,this.target=null;const t=e.$fastController;this.style=document.createElement("style"),t.addStyles(this.style),f.getNotifier(t).subscribe(this,"isConnected"),this.handleChange(t,"isConnected")}targetChanged(){if(null!==this.target)for(const[e,t]of this.store.entries())this.target.setProperty(e,t)}setProperty(e,t){this.store.set(e,t),h.queueUpdate((()=>{null!==this.target&&this.target.setProperty(e,t)}))}removeProperty(e){this.store.delete(e),h.queueUpdate((()=>{null!==this.target&&this.target.removeProperty(e)}))}handleChange(e,t){const{sheet:i}=this.style;if(i){const e=i.insertRule(":host{}",i.cssRules.length);this.target=i.cssRules[e].style}else this.target=null}}qe([b],xi.prototype,"target",void 0);class Ci{constructor(e){this.target=e.style}setProperty(e,t){h.queueUpdate((()=>this.target.setProperty(e,t)))}removeProperty(e){h.queueUpdate((()=>this.target.removeProperty(e)))}}class wi{setProperty(e,t){wi.properties[e]=t;for(const i of wi.roots.values())Ii.getOrCreate(wi.normalizeRoot(i)).setProperty(e,t)}removeProperty(e){delete wi.properties[e];for(const t of wi.roots.values())Ii.getOrCreate(wi.normalizeRoot(t)).removeProperty(e)}static registerRoot(e){const{roots:t}=wi;if(!t.has(e)){t.add(e);const i=Ii.getOrCreate(this.normalizeRoot(e));for(const e in wi.properties)i.setProperty(e,wi.properties[e])}}static unregisterRoot(e){const{roots:t}=wi;if(t.has(e)){t.delete(e);const i=Ii.getOrCreate(wi.normalizeRoot(e));for(const e in wi.properties)i.removeProperty(e)}}static normalizeRoot(e){return e===gi?document:e}}wi.roots=new Set,wi.properties={};const $i=new WeakMap,ki=h.supportsAdoptedStyleSheets?class extends mi{constructor(e){super();const t=new CSSStyleSheet;this.target=t.cssRules[t.insertRule(":host{}")].style,e.$fastController.addStyles(K.create([t]))}}:xi,Ii=Object.freeze({getOrCreate(e){if($i.has(e))return $i.get(e);let t;return e===gi?t=new wi:e instanceof Document?t=h.supportsAdoptedStyleSheets?new vi:new yi:t=e instanceof fe?new ki(e):new Ci(e),$i.set(e,t),t}});class Ti extends be{constructor(e){super(),this.subscribers=new WeakMap,this._appliedTo=new Set,this.name=e.name,null!==e.cssCustomPropertyName&&(this.cssCustomProperty=`--${e.cssCustomPropertyName}`,this.cssVar=`var(${this.cssCustomProperty})`),this.id=Ti.uniqueId(),Ti.tokensById.set(this.id,this)}get appliedTo(){return[...this._appliedTo]}static from(e){return new Ti({name:"string"==typeof e?e:e.name,cssCustomPropertyName:"string"==typeof e?e:void 0===e.cssCustomPropertyName?e.name:e.cssCustomPropertyName})}static isCSSDesignToken(e){return"string"==typeof e.cssCustomProperty}static isDerivedDesignTokenValue(e){return"function"==typeof e}static getTokenById(e){return Ti.tokensById.get(e)}getOrCreateSubscriberSet(e=this){return this.subscribers.get(e)||this.subscribers.set(e,new Set)&&this.subscribers.get(e)}createCSS(){return this.cssVar||""}getValueFor(e){const t=Ei.getOrCreate(e).get(this);if(void 0!==t)return t;throw new Error(`Value could not be retrieved for token named "${this.name}". Ensure the value is set for ${e} or an ancestor of ${e}.`)}setValueFor(e,t){return this._appliedTo.add(e),t instanceof Ti&&(t=this.alias(t)),Ei.getOrCreate(e).set(this,t),this}deleteValueFor(e){return this._appliedTo.delete(e),Ei.existsFor(e)&&Ei.getOrCreate(e).delete(this),this}withDefault(e){return this.setValueFor(gi,e),this}subscribe(e,t){const i=this.getOrCreateSubscriberSet(t);t&&!Ei.existsFor(t)&&Ei.getOrCreate(t),i.has(e)||i.add(e)}unsubscribe(e,t){const i=this.subscribers.get(t||this);i&&i.has(e)&&i.delete(e)}notify(e){const t=Object.freeze({token:this,target:e});this.subscribers.has(this)&&this.subscribers.get(this).forEach((e=>e.handleChange(t))),this.subscribers.has(e)&&this.subscribers.get(e).forEach((e=>e.handleChange(t)))}alias(e){return t=>e.getValueFor(t)}}Ti.uniqueId=(()=>{let e=0;return()=>(e++,e.toString(16))})(),Ti.tokensById=new Map;class Oi{constructor(e,t,i){this.source=e,this.token=t,this.node=i,this.dependencies=new Set,this.observer=f.binding(e,this,!1),this.observer.handleChange=this.observer.call,this.handleChange()}disconnect(){this.observer.disconnect()}handleChange(){this.node.store.set(this.token,this.observer.observe(this.node.target,v))}}class Si{constructor(){this.values=new Map}set(e,t){this.values.get(e)!==t&&(this.values.set(e,t),f.getNotifier(this).notify(e.id))}get(e){return f.track(this,e.id),this.values.get(e)}delete(e){this.values.delete(e)}all(){return this.values.entries()}}const Ai=new WeakMap,Ri=new WeakMap;class Ei{constructor(e){this.target=e,this.store=new Si,this.children=[],this.assignedValues=new Map,this.reflecting=new Set,this.bindingObservers=new Map,this.tokenValueChangeHandler={handleChange:(e,t)=>{const i=Ti.getTokenById(t);if(i&&(i.notify(this.target),Ti.isCSSDesignToken(i))){const t=this.parent,s=this.isReflecting(i);if(t){const o=t.get(i),n=e.get(i);o===n||s?o===n&&s&&this.stopReflectToCSS(i):this.reflectToCSS(i)}else s||this.reflectToCSS(i)}}},Ai.set(e,this),f.getNotifier(this.store).subscribe(this.tokenValueChangeHandler),e instanceof fe?e.$fastController.addBehaviors([this]):e.isConnected&&this.bind()}static getOrCreate(e){return Ai.get(e)||new Ei(e)}static existsFor(e){return Ai.has(e)}static findParent(e){if(gi!==e.target){let t=bi(e.target);for(;null!==t;){if(Ai.has(t))return Ai.get(t);t=bi(t)}return Ei.getOrCreate(gi)}return null}static findClosestAssignedNode(e,t){let i=t;do{if(i.has(e))return i;i=i.parent?i.parent:i.target!==gi?Ei.getOrCreate(gi):null}while(null!==i);return null}get parent(){return Ri.get(this)||null}has(e){return this.assignedValues.has(e)}get(e){const t=this.store.get(e);if(void 0!==t)return t;const i=this.getRaw(e);return void 0!==i?(this.hydrate(e,i),this.get(e)):void 0}getRaw(e){var t;return this.assignedValues.has(e)?this.assignedValues.get(e):null===(t=Ei.findClosestAssignedNode(e,this))||void 0===t?void 0:t.getRaw(e)}set(e,t){Ti.isDerivedDesignTokenValue(this.assignedValues.get(e))&&this.tearDownBindingObserver(e),this.assignedValues.set(e,t),Ti.isDerivedDesignTokenValue(t)?this.setupBindingObserver(e,t):this.store.set(e,t)}delete(e){this.assignedValues.delete(e),this.tearDownBindingObserver(e);const t=this.getRaw(e);t?this.hydrate(e,t):this.store.delete(e)}bind(){const e=Ei.findParent(this);e&&e.appendChild(this);for(const e of this.assignedValues.keys())e.notify(this.target)}unbind(){if(this.parent){Ri.get(this).removeChild(this)}}appendChild(e){e.parent&&Ri.get(e).removeChild(e);const t=this.children.filter((t=>e.contains(t)));Ri.set(e,this),this.children.push(e),t.forEach((t=>e.appendChild(t))),f.getNotifier(this.store).subscribe(e);for(const[t,i]of this.store.all())e.hydrate(t,this.bindingObservers.has(t)?this.getRaw(t):i)}removeChild(e){const t=this.children.indexOf(e);return-1!==t&&this.children.splice(t,1),f.getNotifier(this.store).unsubscribe(e),e.parent===this&&Ri.delete(e)}contains(e){return function(e,t){let i=t;for(;null!==i;){if(i===e)return!0;i=bi(i)}return!1}(this.target,e.target)}reflectToCSS(e){this.isReflecting(e)||(this.reflecting.add(e),Ei.cssCustomPropertyReflector.startReflection(e,this.target))}stopReflectToCSS(e){this.isReflecting(e)&&(this.reflecting.delete(e),Ei.cssCustomPropertyReflector.stopReflection(e,this.target))}isReflecting(e){return this.reflecting.has(e)}handleChange(e,t){const i=Ti.getTokenById(t);i&&this.hydrate(i,this.getRaw(i))}hydrate(e,t){if(!this.has(e)){const i=this.bindingObservers.get(e);Ti.isDerivedDesignTokenValue(t)?i?i.source!==t&&(this.tearDownBindingObserver(e),this.setupBindingObserver(e,t)):this.setupBindingObserver(e,t):(i&&this.tearDownBindingObserver(e),this.store.set(e,t))}}setupBindingObserver(e,t){const i=new Oi(t,e,this);return this.bindingObservers.set(e,i),i}tearDownBindingObserver(e){return!!this.bindingObservers.has(e)&&(this.bindingObservers.get(e).disconnect(),this.bindingObservers.delete(e),!0)}}Ei.cssCustomPropertyReflector=new class{startReflection(e,t){e.subscribe(this,t),this.handleChange({token:e,target:t})}stopReflection(e,t){e.unsubscribe(this,t),this.remove(e,t)}handleChange(e){const{token:t,target:i}=e;this.add(t,i)}add(e,t){Ii.getOrCreate(t).setProperty(e.cssCustomProperty,this.resolveCSSValue(Ei.getOrCreate(t).get(e)))}remove(e,t){Ii.getOrCreate(t).removeProperty(e.cssCustomProperty)}resolveCSSValue(e){return e&&"function"==typeof e.createCSS?e.createCSS():e}},qe([b],Ei.prototype,"children",void 0);const Di=Object.freeze({create:function(e){return Ti.from(e)},notifyConnection:e=>!(!e.isConnected||!Ei.existsFor(e))&&(Ei.getOrCreate(e).bind(),!0),notifyDisconnection:e=>!(e.isConnected||!Ei.existsFor(e))&&(Ei.getOrCreate(e).unbind(),!0),registerRoot(e=gi){wi.registerRoot(e)},unregisterRoot(e=gi){wi.unregisterRoot(e)}}),Pi=Object.freeze({definitionCallbackOnly:null,ignoreDuplicate:Symbol()}),Bi=new Map,Fi=new Map;let Li=null;const Hi=Ze.createInterface((e=>e.cachedCallback((e=>(null===Li&&(Li=new Mi(null,e)),Li))))),Vi=Object.freeze({tagFor:e=>Fi.get(e),responsibleFor(e){const t=e.$$designSystem$$;if(t)return t;return Ze.findResponsibleContainer(e).get(Hi)},getOrCreate(e){if(!e)return null===Li&&(Li=Ze.getOrCreateDOMContainer().get(Hi)),Li;const t=e.$$designSystem$$;if(t)return t;const i=Ze.getOrCreateDOMContainer(e);if(i.has(Hi,!1))return i.get(Hi);{const t=new Mi(e,i);return i.register(bt.instance(Hi,t)),t}}});class Mi{constructor(e,t){this.owner=e,this.container=t,this.designTokensInitialized=!1,this.prefix="fast",this.shadowRootMode=void 0,this.disambiguate=()=>Pi.definitionCallbackOnly,null!==e&&(e.$$designSystem$$=this)}withPrefix(e){return this.prefix=e,this}withShadowRootMode(e){return this.shadowRootMode=e,this}withElementDisambiguation(e){return this.disambiguate=e,this}withDesignTokenRoot(e){return this.designTokenRoot=e,this}register(...e){const t=this.container,i=[],s=this.disambiguate,o=this.shadowRootMode,n={elementPrefix:this.prefix,tryDefineElement(e,n,r){const a=function(e,t,i){return"string"==typeof e?{name:e,type:t,callback:i}:e}(e,n,r),{name:l,callback:d,baseClass:c}=a;let{type:h}=a,u=l,p=Bi.get(u),f=!0;for(;p;){const e=s(u,h,p);switch(e){case Pi.ignoreDuplicate:return;case Pi.definitionCallbackOnly:f=!1,p=void 0;break;default:u=e,p=Bi.get(u)}}f&&((Fi.has(h)||h===Ot)&&(h=class extends h{}),Bi.set(u,h),Fi.set(h,u),c&&Fi.set(c,u)),i.push(new Ni(t,u,h,o,d,f))}};this.designTokensInitialized||(this.designTokensInitialized=!0,null!==this.designTokenRoot&&Di.registerRoot(this.designTokenRoot)),t.registerWithContext(n,...e);for(const e of i)e.callback(e),e.willDefine&&null!==e.definition&&e.definition.define();return this}}class Ni{constructor(e,t,i,s,o,n){this.container=e,this.name=t,this.type=i,this.shadowRootMode=s,this.callback=o,this.willDefine=n,this.definition=null}definePresentation(e){It.define(this.name,e,this.container)}defineElement(e){this.definition=new le(this.type,Object.assign(Object.assign({},e),{name:this.name}))}tagFor(e){return Vi.tagFor(e)}}const zi={separator:"separator",presentation:"presentation"};class ji extends Ot{constructor(){super(...arguments),this.role=zi.separator,this.orientation=Et.horizontal}}qe([oe],ji.prototype,"role",void 0),qe([oe],ji.prototype,"orientation",void 0);class _i extends ui{constructor(){super(...arguments),this.activeIndex=-1,this.rangeStartIndex=-1}get activeOption(){return this.options[this.activeIndex]}get checkedOptions(){var e;return null===(e=this.options)||void 0===e?void 0:e.filter((e=>e.checked))}get firstSelectedOptionIndex(){return this.options.indexOf(this.firstSelectedOption)}activeIndexChanged(e,t){var i,s;this.ariaActiveDescendant=null!==(s=null===(i=this.options[t])||void 0===i?void 0:i.id)&&void 0!==s?s:"",this.focusAndScrollOptionIntoView()}checkActiveIndex(){if(!this.multiple)return;const e=this.activeOption;e&&(e.checked=!0)}checkFirstOption(e=!1){e?(-1===this.rangeStartIndex&&(this.rangeStartIndex=this.activeIndex+1),this.options.forEach(((e,t)=>{e.checked=Lt(t,this.rangeStartIndex)}))):this.uncheckAllOptions(),this.activeIndex=0,this.checkActiveIndex()}checkLastOption(e=!1){e?(-1===this.rangeStartIndex&&(this.rangeStartIndex=this.activeIndex),this.options.forEach(((e,t)=>{e.checked=Lt(t,this.rangeStartIndex,this.options.length)}))):this.uncheckAllOptions(),this.activeIndex=this.options.length-1,this.checkActiveIndex()}connectedCallback(){super.connectedCallback(),this.addEventListener("focusout",this.focusoutHandler)}disconnectedCallback(){this.removeEventListener("focusout",this.focusoutHandler),super.disconnectedCallback()}checkNextOption(e=!1){e?(-1===this.rangeStartIndex&&(this.rangeStartIndex=this.activeIndex),this.options.forEach(((e,t)=>{e.checked=Lt(t,this.rangeStartIndex,this.activeIndex+1)}))):this.uncheckAllOptions(),this.activeIndex+=this.activeIndex<this.options.length-1?1:0,this.checkActiveIndex()}checkPreviousOption(e=!1){e?(-1===this.rangeStartIndex&&(this.rangeStartIndex=this.activeIndex),1===this.checkedOptions.length&&(this.rangeStartIndex+=1),this.options.forEach(((e,t)=>{e.checked=Lt(t,this.activeIndex,this.rangeStartIndex)}))):this.uncheckAllOptions(),this.activeIndex-=this.activeIndex>0?1:0,this.checkActiveIndex()}clickHandler(e){var t;if(!this.multiple)return super.clickHandler(e);const i=null===(t=e.target)||void 0===t?void 0:t.closest("[role=option]");return i&&!i.disabled?(this.uncheckAllOptions(),this.activeIndex=this.options.indexOf(i),this.checkActiveIndex(),this.toggleSelectedForAllCheckedOptions(),!0):void 0}focusAndScrollOptionIntoView(){super.focusAndScrollOptionIntoView(this.activeOption)}focusinHandler(e){if(!this.multiple)return super.focusinHandler(e);this.shouldSkipFocus||e.target!==e.currentTarget||(this.uncheckAllOptions(),-1===this.activeIndex&&(this.activeIndex=-1!==this.firstSelectedOptionIndex?this.firstSelectedOptionIndex:0),this.checkActiveIndex(),this.setSelectedOptions(),this.focusAndScrollOptionIntoView()),this.shouldSkipFocus=!1}focusoutHandler(e){this.multiple&&this.uncheckAllOptions()}keydownHandler(e){if(!this.multiple)return super.keydownHandler(e);if(this.disabled)return!0;const{key:t,shiftKey:i}=e;switch(this.shouldSkipFocus=!1,t){case"Home":return void this.checkFirstOption(i);case"ArrowDown":return void this.checkNextOption(i);case"ArrowUp":return void this.checkPreviousOption(i);case"End":return void this.checkLastOption(i);case"Tab":return this.focusAndScrollOptionIntoView(),!0;case"Escape":return this.uncheckAllOptions(),this.checkActiveIndex(),!0;case" ":if(e.preventDefault(),this.typeAheadExpired)return void this.toggleSelectedForAllCheckedOptions();default:return 1===t.length&&this.handleTypeAhead(`${t}`),!0}}mousedownHandler(e){if(e.offsetX>=0&&e.offsetX<=this.scrollWidth)return super.mousedownHandler(e)}multipleChanged(e,t){var i;this.ariaMultiSelectable=t?"true":null,null===(i=this.options)||void 0===i||i.forEach((e=>{e.checked=!t&&void 0})),this.setSelectedOptions()}setSelectedOptions(){this.multiple?this.$fastController.isConnected&&this.options&&(this.selectedOptions=this.options.filter((e=>e.selected)),this.focusAndScrollOptionIntoView()):super.setSelectedOptions()}sizeChanged(e,t){var i;const s=Math.max(0,parseInt(null!==(i=null==t?void 0:t.toFixed())&&void 0!==i?i:"",10));s!==t&&h.queueUpdate((()=>{this.size=s}))}toggleSelectedForAllCheckedOptions(){const e=this.checkedOptions.filter((e=>!e.disabled)),t=!e.every((e=>e.selected));e.forEach((e=>e.selected=t)),this.selectedIndex=this.options.indexOf(e[e.length-1]),this.setSelectedOptions()}typeaheadBufferChanged(e,t){if(this.multiple){if(this.$fastController.isConnected){const e=this.getTypeaheadMatches(),t=this.options.indexOf(e[0]);t>-1&&(this.activeIndex=t,this.uncheckAllOptions(),this.checkActiveIndex()),this.typeAheadExpired=!1}}else super.typeaheadBufferChanged(e,t)}uncheckAllOptions(e=!1){this.options.forEach((e=>e.checked=!this.multiple&&void 0)),e||(this.rangeStartIndex=-1)}}qe([b],_i.prototype,"activeIndex",void 0),qe([oe({mode:"boolean"})],_i.prototype,"multiple",void 0),qe([oe({converter:ie})],_i.prototype,"size",void 0);class qi extends Ot{}class Ui extends(Kt(qi)){constructor(){super(...arguments),this.proxy=document.createElement("input")}}const Ki={email:"email",password:"password",tel:"tel",text:"text",url:"url"};class Wi extends Ui{constructor(){super(...arguments),this.type=Ki.text}readOnlyChanged(){this.proxy instanceof HTMLInputElement&&(this.proxy.readOnly=this.readOnly,this.validate())}autofocusChanged(){this.proxy instanceof HTMLInputElement&&(this.proxy.autofocus=this.autofocus,this.validate())}placeholderChanged(){this.proxy instanceof HTMLInputElement&&(this.proxy.placeholder=this.placeholder)}typeChanged(){this.proxy instanceof HTMLInputElement&&(this.proxy.type=this.type,this.validate())}listChanged(){this.proxy instanceof HTMLInputElement&&(this.proxy.setAttribute("list",this.list),this.validate())}maxlengthChanged(){this.proxy instanceof HTMLInputElement&&(this.proxy.maxLength=this.maxlength,this.validate())}minlengthChanged(){this.proxy instanceof HTMLInputElement&&(this.proxy.minLength=this.minlength,this.validate())}patternChanged(){this.proxy instanceof HTMLInputElement&&(this.proxy.pattern=this.pattern,this.validate())}sizeChanged(){this.proxy instanceof HTMLInputElement&&(this.proxy.size=this.size)}spellcheckChanged(){this.proxy instanceof HTMLInputElement&&(this.proxy.spellcheck=this.spellcheck)}connectedCallback(){super.connectedCallback(),this.proxy.setAttribute("type",this.type),this.validate(),this.autofocus&&h.queueUpdate((()=>{this.focus()}))}select(){this.control.select(),this.$emit("select")}handleTextInput(){this.value=this.control.value}handleChange(){this.$emit("change")}validate(){super.validate(this.control)}}qe([oe({attribute:"readonly",mode:"boolean"})],Wi.prototype,"readOnly",void 0),qe([oe({mode:"boolean"})],Wi.prototype,"autofocus",void 0),qe([oe],Wi.prototype,"placeholder",void 0),qe([oe],Wi.prototype,"type",void 0),qe([oe],Wi.prototype,"list",void 0),qe([oe({converter:ie})],Wi.prototype,"maxlength",void 0),qe([oe({converter:ie})],Wi.prototype,"minlength",void 0),qe([oe],Wi.prototype,"pattern",void 0),qe([oe({converter:ie})],Wi.prototype,"size",void 0),qe([oe({mode:"boolean"})],Wi.prototype,"spellcheck",void 0),qe([b],Wi.prototype,"defaultSlottedNodes",void 0);class Gi{}Rt(Gi,Mt),Rt(Wi,ze,Gi);class Qi extends Ot{constructor(){super(...arguments),this.percentComplete=0}valueChanged(){this.$fastController.isConnected&&this.updatePercentComplete()}minChanged(){this.$fastController.isConnected&&this.updatePercentComplete()}maxChanged(){this.$fastController.isConnected&&this.updatePercentComplete()}connectedCallback(){super.connectedCallback(),this.updatePercentComplete()}updatePercentComplete(){const e="number"==typeof this.min?this.min:0,t="number"==typeof this.max?this.max:100,i="number"==typeof this.value?this.value:0,s=t-e;this.percentComplete=0===s?0:Math.fround((i-e)/s*100)}}qe([oe({converter:ie})],Qi.prototype,"value",void 0),qe([oe({converter:ie})],Qi.prototype,"min",void 0),qe([oe({converter:ie})],Qi.prototype,"max",void 0),qe([oe({mode:"boolean"})],Qi.prototype,"paused",void 0),qe([b],Qi.prototype,"percentComplete",void 0);class Xi extends Ot{constructor(){super(...arguments),this.orientation=Et.horizontal,this.radioChangeHandler=e=>{const t=e.target;t.checked&&(this.slottedRadioButtons.forEach((e=>{e!==t&&(e.checked=!1,this.isInsideFoundationToolbar||e.setAttribute("tabindex","-1"))})),this.selectedRadio=t,this.value=t.value,t.setAttribute("tabindex","0"),this.focusedRadio=t),e.stopPropagation()},this.moveToRadioByIndex=(e,t)=>{const i=e[t];this.isInsideToolbar||(i.setAttribute("tabindex","0"),i.readOnly?this.slottedRadioButtons.forEach((e=>{e!==i&&e.setAttribute("tabindex","-1")})):(i.checked=!0,this.selectedRadio=i)),this.focusedRadio=i,i.focus()},this.moveRightOffGroup=()=>{var e;null===(e=this.nextElementSibling)||void 0===e||e.focus()},this.moveLeftOffGroup=()=>{var e;null===(e=this.previousElementSibling)||void 0===e||e.focus()},this.focusOutHandler=e=>{const t=this.slottedRadioButtons,i=e.target,s=null!==i?t.indexOf(i):0,o=this.focusedRadio?t.indexOf(this.focusedRadio):-1;return(0===o&&s===o||o===t.length-1&&o===s)&&(this.selectedRadio?(this.focusedRadio=this.selectedRadio,this.isInsideFoundationToolbar||(this.selectedRadio.setAttribute("tabindex","0"),t.forEach((e=>{e!==this.selectedRadio&&e.setAttribute("tabindex","-1")})))):(this.focusedRadio=t[0],this.focusedRadio.setAttribute("tabindex","0"),t.forEach((e=>{e!==this.focusedRadio&&e.setAttribute("tabindex","-1")})))),!0},this.clickHandler=e=>{const t=e.target;if(t){const e=this.slottedRadioButtons;t.checked||0===e.indexOf(t)?(t.setAttribute("tabindex","0"),this.selectedRadio=t):(t.setAttribute("tabindex","-1"),this.selectedRadio=null),this.focusedRadio=t}e.preventDefault()},this.shouldMoveOffGroupToTheRight=(e,t,i)=>e===t.length&&this.isInsideToolbar&&"ArrowRight"===i,this.shouldMoveOffGroupToTheLeft=(e,t)=>(this.focusedRadio?e.indexOf(this.focusedRadio)-1:0)<0&&this.isInsideToolbar&&"ArrowLeft"===t,this.checkFocusedRadio=()=>{null===this.focusedRadio||this.focusedRadio.readOnly||this.focusedRadio.checked||(this.focusedRadio.checked=!0,this.focusedRadio.setAttribute("tabindex","0"),this.focusedRadio.focus(),this.selectedRadio=this.focusedRadio)},this.moveRight=e=>{const t=this.slottedRadioButtons;let i=0;if(i=this.focusedRadio?t.indexOf(this.focusedRadio)+1:1,this.shouldMoveOffGroupToTheRight(i,t,e.key))this.moveRightOffGroup();else for(i===t.length&&(i=0);i<t.length&&t.length>1;){if(!t[i].disabled){this.moveToRadioByIndex(t,i);break}if(this.focusedRadio&&i===t.indexOf(this.focusedRadio))break;if(i+1>=t.length){if(this.isInsideToolbar)break;i=0}else i+=1}},this.moveLeft=e=>{const t=this.slottedRadioButtons;let i=0;if(i=this.focusedRadio?t.indexOf(this.focusedRadio)-1:0,i=i<0?t.length-1:i,this.shouldMoveOffGroupToTheLeft(t,e.key))this.moveLeftOffGroup();else for(;i>=0&&t.length>1;){if(!t[i].disabled){this.moveToRadioByIndex(t,i);break}if(this.focusedRadio&&i===t.indexOf(this.focusedRadio))break;i-1<0?i=t.length-1:i-=1}},this.keydownHandler=e=>{const t=e.key;if(t in Bt&&this.isInsideFoundationToolbar)return!0;switch(t){case"Enter":this.checkFocusedRadio();break;case"ArrowRight":case"ArrowDown":this.direction===Ft.ltr?this.moveRight(e):this.moveLeft(e);break;case"ArrowLeft":case"ArrowUp":this.direction===Ft.ltr?this.moveLeft(e):this.moveRight(e);break;default:return!0}}}readOnlyChanged(){void 0!==this.slottedRadioButtons&&this.slottedRadioButtons.forEach((e=>{this.readOnly?e.readOnly=!0:e.readOnly=!1}))}disabledChanged(){void 0!==this.slottedRadioButtons&&this.slottedRadioButtons.forEach((e=>{this.disabled?e.disabled=!0:e.disabled=!1}))}nameChanged(){this.slottedRadioButtons&&this.slottedRadioButtons.forEach((e=>{e.setAttribute("name",this.name)}))}valueChanged(){this.slottedRadioButtons&&this.slottedRadioButtons.forEach((e=>{e.value===this.value&&(e.checked=!0,this.selectedRadio=e)})),this.$emit("change")}slottedRadioButtonsChanged(e,t){this.slottedRadioButtons&&this.slottedRadioButtons.length>0&&this.setupRadioButtons()}get parentToolbar(){return this.closest('[role="toolbar"]')}get isInsideToolbar(){var e;return null!==(e=this.parentToolbar)&&void 0!==e&&e}get isInsideFoundationToolbar(){var e;return!!(null===(e=this.parentToolbar)||void 0===e?void 0:e.$fastController)}connectedCallback(){super.connectedCallback(),this.direction=(e=>{const t=e.closest("[dir]");return null!==t&&"rtl"===t.dir?Ft.rtl:Ft.ltr})(this),this.setupRadioButtons()}disconnectedCallback(){this.slottedRadioButtons.forEach((e=>{e.removeEventListener("change",this.radioChangeHandler)}))}setupRadioButtons(){const e=this.slottedRadioButtons.filter((e=>e.hasAttribute("checked"))),t=e?e.length:0;if(t>1){e[t-1].checked=!0}let i=!1;if(this.slottedRadioButtons.forEach((e=>{void 0!==this.name&&e.setAttribute("name",this.name),this.disabled&&(e.disabled=!0),this.readOnly&&(e.readOnly=!0),this.value&&this.value===e.value?(this.selectedRadio=e,this.focusedRadio=e,e.checked=!0,e.setAttribute("tabindex","0"),i=!0):(this.isInsideFoundationToolbar||e.setAttribute("tabindex","-1"),e.checked=!1),e.addEventListener("change",this.radioChangeHandler)})),void 0===this.value&&this.slottedRadioButtons.length>0){const e=this.slottedRadioButtons.filter((e=>e.hasAttribute("checked"))),t=null!==e?e.length:0;if(t>0&&!i){const i=e[t-1];i.checked=!0,this.focusedRadio=i,i.setAttribute("tabindex","0")}else this.slottedRadioButtons[0].setAttribute("tabindex","0"),this.focusedRadio=this.slottedRadioButtons[0]}}}qe([oe({attribute:"readonly",mode:"boolean"})],Xi.prototype,"readOnly",void 0),qe([oe({attribute:"disabled",mode:"boolean"})],Xi.prototype,"disabled",void 0),qe([oe],Xi.prototype,"name",void 0),qe([oe],Xi.prototype,"value",void 0),qe([oe],Xi.prototype,"orientation",void 0),qe([b],Xi.prototype,"childItems",void 0),qe([b],Xi.prototype,"slottedRadioButtons",void 0);class Yi extends Ot{}class Ji extends(Wt(Yi)){constructor(){super(...arguments),this.proxy=document.createElement("input")}}class Zi extends Ji{constructor(){super(),this.initialValue="on",this.keypressHandler=e=>{if(" "!==e.key)return!0;this.checked||this.readOnly||(this.checked=!0)},this.proxy.setAttribute("type","radio")}readOnlyChanged(){this.proxy instanceof HTMLInputElement&&(this.proxy.readOnly=this.readOnly)}defaultCheckedChanged(){var e;this.$fastController.isConnected&&!this.dirtyChecked&&(this.isInsideRadioGroup()||(this.checked=null!==(e=this.defaultChecked)&&void 0!==e&&e,this.dirtyChecked=!1))}connectedCallback(){var e,t;super.connectedCallback(),this.validate(),"radiogroup"!==(null===(e=this.parentElement)||void 0===e?void 0:e.getAttribute("role"))&&null===this.getAttribute("tabindex")&&(this.disabled||this.setAttribute("tabindex","0")),this.checkedAttribute&&(this.dirtyChecked||this.isInsideRadioGroup()||(this.checked=null!==(t=this.defaultChecked)&&void 0!==t&&t,this.dirtyChecked=!1))}isInsideRadioGroup(){return null!==this.closest("[role=radiogroup]")}clickHandler(e){this.disabled||this.readOnly||this.checked||(this.checked=!0)}}function es(e,t,i){return e.nodeType!==Node.TEXT_NODE||"string"==typeof e.nodeValue&&!!e.nodeValue.trim().length}qe([oe({attribute:"readonly",mode:"boolean"})],Zi.prototype,"readOnly",void 0),qe([b],Zi.prototype,"name",void 0),qe([b],Zi.prototype,"defaultSlottedNodes",void 0);class ts extends _i{}class is extends(Kt(ts)){constructor(){super(...arguments),this.proxy=document.createElement("select")}}class ss extends is{constructor(){super(...arguments),this.open=!1,this.forcedPosition=!1,this.listboxId=Vt("listbox-"),this.maxHeight=0}openChanged(e,t){if(this.collapsible){if(this.open)return this.ariaControls=this.listboxId,this.ariaExpanded="true",this.setPositioning(),this.focusAndScrollOptionIntoView(),this.indexWhenOpened=this.selectedIndex,void h.queueUpdate((()=>this.focus()));this.ariaControls="",this.ariaExpanded="false"}}get collapsible(){return!(this.multiple||"number"==typeof this.size)}get value(){return f.track(this,"value"),this._value}set value(e){var t,i,s,o,n,r,a;const l=`${this._value}`;if(null===(t=this._options)||void 0===t?void 0:t.length){const t=this._options.findIndex((t=>t.value===e)),l=null!==(s=null===(i=this._options[this.selectedIndex])||void 0===i?void 0:i.value)&&void 0!==s?s:null,d=null!==(n=null===(o=this._options[t])||void 0===o?void 0:o.value)&&void 0!==n?n:null;-1!==t&&l===d||(e="",this.selectedIndex=t),e=null!==(a=null===(r=this.firstSelectedOption)||void 0===r?void 0:r.value)&&void 0!==a?a:e}l!==e&&(this._value=e,super.valueChanged(l,e),f.notify(this,"value"),this.updateDisplayValue())}updateValue(e){var t,i;this.$fastController.isConnected&&(this.value=null!==(i=null===(t=this.firstSelectedOption)||void 0===t?void 0:t.value)&&void 0!==i?i:""),e&&(this.$emit("input"),this.$emit("change",this,{bubbles:!0,composed:void 0}))}selectedIndexChanged(e,t){super.selectedIndexChanged(e,t),this.updateValue()}positionChanged(e,t){this.positionAttribute=t,this.setPositioning()}setPositioning(){const e=this.getBoundingClientRect(),t=window.innerHeight-e.bottom;this.position=this.forcedPosition?this.positionAttribute:e.top>t?fi.above:fi.below,this.positionAttribute=this.forcedPosition?this.positionAttribute:this.position,this.maxHeight=this.position===fi.above?~~e.top:~~t}get displayValue(){var e,t;return f.track(this,"displayValue"),null!==(t=null===(e=this.firstSelectedOption)||void 0===e?void 0:e.text)&&void 0!==t?t:""}disabledChanged(e,t){super.disabledChanged&&super.disabledChanged(e,t),this.ariaDisabled=this.disabled?"true":"false"}formResetCallback(){this.setProxyOptions(),super.setDefaultSelectedOption(),-1===this.selectedIndex&&(this.selectedIndex=0)}clickHandler(e){if(!this.disabled){if(this.open){const t=e.target.closest("option,[role=option]");if(t&&t.disabled)return}return super.clickHandler(e),this.open=this.collapsible&&!this.open,this.open||this.indexWhenOpened===this.selectedIndex||this.updateValue(!0),!0}}focusoutHandler(e){var t;if(super.focusoutHandler(e),!this.open)return!0;const i=e.relatedTarget;this.isSameNode(i)?this.focus():(null===(t=this.options)||void 0===t?void 0:t.includes(i))||(this.open=!1,this.indexWhenOpened!==this.selectedIndex&&this.updateValue(!0))}handleChange(e,t){super.handleChange(e,t),"value"===t&&this.updateValue()}slottedOptionsChanged(e,t){this.options.forEach((e=>{f.getNotifier(e).unsubscribe(this,"value")})),super.slottedOptionsChanged(e,t),this.options.forEach((e=>{f.getNotifier(e).subscribe(this,"value")})),this.setProxyOptions(),this.updateValue()}mousedownHandler(e){var t;return e.offsetX>=0&&e.offsetX<=(null===(t=this.listbox)||void 0===t?void 0:t.scrollWidth)?super.mousedownHandler(e):this.collapsible}multipleChanged(e,t){super.multipleChanged(e,t),this.proxy&&(this.proxy.multiple=t)}selectedOptionsChanged(e,t){var i;super.selectedOptionsChanged(e,t),null===(i=this.options)||void 0===i||i.forEach(((e,t)=>{var i;const s=null===(i=this.proxy)||void 0===i?void 0:i.options.item(t);s&&(s.selected=e.selected)}))}setDefaultSelectedOption(){var e;const t=null!==(e=this.options)&&void 0!==e?e:Array.from(this.children).filter(ui.slottedOptionFilter),i=null==t?void 0:t.findIndex((e=>e.hasAttribute("selected")||e.selected||e.value===this.value));this.selectedIndex=-1===i?0:i}setProxyOptions(){this.proxy instanceof HTMLSelectElement&&this.options&&(this.proxy.options.length=0,this.options.forEach((e=>{const t=e.proxy||(e instanceof HTMLOptionElement?e.cloneNode():null);t&&this.proxy.options.add(t)})))}keydownHandler(e){super.keydownHandler(e);const t=e.key||e.key.charCodeAt(0);switch(t){case" ":e.preventDefault(),this.collapsible&&this.typeAheadExpired&&(this.open=!this.open);break;case"Home":case"End":e.preventDefault();break;case"Enter":e.preventDefault(),this.open=!this.open;break;case"Escape":this.collapsible&&this.open&&(e.preventDefault(),this.open=!1);break;case"Tab":return this.collapsible&&this.open&&(e.preventDefault(),this.open=!1),!0}return this.open||this.indexWhenOpened===this.selectedIndex||(this.updateValue(!0),this.indexWhenOpened=this.selectedIndex),!("ArrowDown"===t||"ArrowUp"===t)}connectedCallback(){super.connectedCallback(),this.forcedPosition=!!this.positionAttribute,this.addEventListener("contentchange",this.updateDisplayValue)}disconnectedCallback(){this.removeEventListener("contentchange",this.updateDisplayValue),super.disconnectedCallback()}sizeChanged(e,t){super.sizeChanged(e,t),this.proxy&&(this.proxy.size=t)}updateDisplayValue(){this.collapsible&&f.notify(this,"displayValue")}}qe([oe({attribute:"open",mode:"boolean"})],ss.prototype,"open",void 0),qe([function(e,t,i){return Object.assign({},i,{get:function(){return f.trackVolatile(),i.get.apply(this)}})}],ss.prototype,"collapsible",null),qe([b],ss.prototype,"control",void 0),qe([oe({attribute:"position"})],ss.prototype,"positionAttribute",void 0),qe([b],ss.prototype,"position",void 0),qe([b],ss.prototype,"maxHeight",void 0);class os{}qe([b],os.prototype,"ariaControls",void 0),Rt(os,pi),Rt(ss,ze,os);class ns extends Ot{}class rs extends Ot{}qe([oe({mode:"boolean"})],rs.prototype,"disabled",void 0);const as="horizontal";class ls extends Ot{constructor(){super(...arguments),this.orientation=as,this.activeindicator=!0,this.showActiveIndicator=!0,this.prevActiveTabIndex=0,this.activeTabIndex=0,this.ticking=!1,this.change=()=>{this.$emit("change",this.activetab)},this.isDisabledElement=e=>"true"===e.getAttribute("aria-disabled"),this.isHiddenElement=e=>e.hasAttribute("hidden"),this.isFocusableElement=e=>!this.isDisabledElement(e)&&!this.isHiddenElement(e),this.setTabs=()=>{const e="gridColumn",t="gridRow",i=this.isHorizontal()?e:t;this.activeTabIndex=this.getActiveIndex(),this.showActiveIndicator=!1,this.tabs.forEach(((e,t)=>{if("tab"===e.slot){const i=this.activeTabIndex===t&&this.isFocusableElement(e);this.activeindicator&&this.isFocusableElement(e)&&(this.showActiveIndicator=!0);const s=this.tabIds[t],o=this.tabpanelIds[t];e.setAttribute("id",s),e.setAttribute("aria-selected",i?"true":"false"),e.setAttribute("aria-controls",o),e.addEventListener("click",this.handleTabClick),e.addEventListener("keydown",this.handleTabKeyDown),e.setAttribute("tabindex",i?"0":"-1"),i&&(this.activetab=e,this.activeid=s)}e.style.gridColumn="",e.style.gridRow="",e.style[i]=`${t+1}`,this.isHorizontal()?e.classList.remove("vertical"):e.classList.add("vertical")}))},this.setTabPanels=()=>{this.tabpanels.forEach(((e,t)=>{const i=this.tabIds[t],s=this.tabpanelIds[t];e.setAttribute("id",s),e.setAttribute("aria-labelledby",i),this.activeTabIndex!==t?e.setAttribute("hidden",""):e.removeAttribute("hidden")}))},this.handleTabClick=e=>{const t=e.currentTarget;1===t.nodeType&&this.isFocusableElement(t)&&(this.prevActiveTabIndex=this.activeTabIndex,this.activeTabIndex=this.tabs.indexOf(t),this.setComponent())},this.handleTabKeyDown=e=>{if(this.isHorizontal())switch(e.key){case"ArrowLeft":e.preventDefault(),this.adjustBackward(e);break;case"ArrowRight":e.preventDefault(),this.adjustForward(e)}else switch(e.key){case"ArrowUp":e.preventDefault(),this.adjustBackward(e);break;case"ArrowDown":e.preventDefault(),this.adjustForward(e)}switch(e.key){case"Home":e.preventDefault(),this.adjust(-this.activeTabIndex);break;case"End":e.preventDefault(),this.adjust(this.tabs.length-this.activeTabIndex-1)}},this.adjustForward=e=>{const t=this.tabs;let i=0;for(i=this.activetab?t.indexOf(this.activetab)+1:1,i===t.length&&(i=0);i<t.length&&t.length>1;){if(this.isFocusableElement(t[i])){this.moveToTabByIndex(t,i);break}if(this.activetab&&i===t.indexOf(this.activetab))break;i+1>=t.length?i=0:i+=1}},this.adjustBackward=e=>{const t=this.tabs;let i=0;for(i=this.activetab?t.indexOf(this.activetab)-1:0,i=i<0?t.length-1:i;i>=0&&t.length>1;){if(this.isFocusableElement(t[i])){this.moveToTabByIndex(t,i);break}i-1<0?i=t.length-1:i-=1}},this.moveToTabByIndex=(e,t)=>{const i=e[t];this.activetab=i,this.prevActiveTabIndex=this.activeTabIndex,this.activeTabIndex=t,i.focus(),this.setComponent()}}orientationChanged(){this.$fastController.isConnected&&(this.setTabs(),this.setTabPanels(),this.handleActiveIndicatorPosition())}activeidChanged(e,t){this.$fastController.isConnected&&this.tabs.length<=this.tabpanels.length&&(this.prevActiveTabIndex=this.tabs.findIndex((t=>t.id===e)),this.setTabs(),this.setTabPanels(),this.handleActiveIndicatorPosition())}tabsChanged(){this.$fastController.isConnected&&this.tabs.length<=this.tabpanels.length&&(this.tabIds=this.getTabIds(),this.tabpanelIds=this.getTabPanelIds(),this.setTabs(),this.setTabPanels(),this.handleActiveIndicatorPosition())}tabpanelsChanged(){this.$fastController.isConnected&&this.tabpanels.length<=this.tabs.length&&(this.tabIds=this.getTabIds(),this.tabpanelIds=this.getTabPanelIds(),this.setTabs(),this.setTabPanels(),this.handleActiveIndicatorPosition())}getActiveIndex(){return void 0!==this.activeid?-1===this.tabIds.indexOf(this.activeid)?0:this.tabIds.indexOf(this.activeid):0}getTabIds(){return this.tabs.map((e=>{var t;return null!==(t=e.getAttribute("id"))&&void 0!==t?t:`tab-${Vt()}`}))}getTabPanelIds(){return this.tabpanels.map((e=>{var t;return null!==(t=e.getAttribute("id"))&&void 0!==t?t:`panel-${Vt()}`}))}setComponent(){this.activeTabIndex!==this.prevActiveTabIndex&&(this.activeid=this.tabIds[this.activeTabIndex],this.focusTab(),this.change())}isHorizontal(){return this.orientation===as}handleActiveIndicatorPosition(){this.showActiveIndicator&&this.activeindicator&&this.activeTabIndex!==this.prevActiveTabIndex&&(this.ticking?this.ticking=!1:(this.ticking=!0,this.animateActiveIndicator()))}animateActiveIndicator(){this.ticking=!0;const e=this.isHorizontal()?"gridColumn":"gridRow",t=this.isHorizontal()?"translateX":"translateY",i=this.isHorizontal()?"offsetLeft":"offsetTop",s=this.activeIndicatorRef[i];this.activeIndicatorRef.style[e]=`${this.activeTabIndex+1}`;const o=this.activeIndicatorRef[i];this.activeIndicatorRef.style[e]=`${this.prevActiveTabIndex+1}`;const n=o-s;this.activeIndicatorRef.style.transform=`${t}(${n}px)`,this.activeIndicatorRef.classList.add("activeIndicatorTransition"),this.activeIndicatorRef.addEventListener("transitionend",(()=>{this.ticking=!1,this.activeIndicatorRef.style[e]=`${this.activeTabIndex+1}`,this.activeIndicatorRef.style.transform=`${t}(0px)`,this.activeIndicatorRef.classList.remove("activeIndicatorTransition")}))}adjust(e){const t=this.tabs.filter((e=>this.isFocusableElement(e))),i=t.indexOf(this.activetab),s=(o=0,n=t.length-1,r=i+e,Math.min(Math.max(r,o),n));var o,n,r;const a=this.tabs.indexOf(t[s]);a>-1&&this.moveToTabByIndex(this.tabs,a)}focusTab(){this.tabs[this.activeTabIndex].focus()}connectedCallback(){super.connectedCallback(),this.tabIds=this.getTabIds(),this.tabpanelIds=this.getTabPanelIds(),this.activeTabIndex=this.getActiveIndex()}}qe([oe],ls.prototype,"orientation",void 0),qe([oe],ls.prototype,"activeid",void 0),qe([b],ls.prototype,"tabs",void 0),qe([b],ls.prototype,"tabpanels",void 0),qe([oe({mode:"boolean"})],ls.prototype,"activeindicator",void 0),qe([b],ls.prototype,"activeIndicatorRef",void 0),qe([b],ls.prototype,"showActiveIndicator",void 0),Rt(ls,ze);class ds extends Ot{}class cs extends(Kt(ds)){constructor(){super(...arguments),this.proxy=document.createElement("textarea")}}const hs={none:"none",both:"both",horizontal:"horizontal",vertical:"vertical"};class us extends cs{constructor(){super(...arguments),this.resize=hs.none,this.cols=20,this.handleTextInput=()=>{this.value=this.control.value}}readOnlyChanged(){this.proxy instanceof HTMLTextAreaElement&&(this.proxy.readOnly=this.readOnly)}autofocusChanged(){this.proxy instanceof HTMLTextAreaElement&&(this.proxy.autofocus=this.autofocus)}listChanged(){this.proxy instanceof HTMLTextAreaElement&&this.proxy.setAttribute("list",this.list)}maxlengthChanged(){this.proxy instanceof HTMLTextAreaElement&&(this.proxy.maxLength=this.maxlength)}minlengthChanged(){this.proxy instanceof HTMLTextAreaElement&&(this.proxy.minLength=this.minlength)}spellcheckChanged(){this.proxy instanceof HTMLTextAreaElement&&(this.proxy.spellcheck=this.spellcheck)}select(){this.control.select(),this.$emit("select")}handleChange(){this.$emit("change")}validate(){super.validate(this.control)}}qe([oe({mode:"boolean"})],us.prototype,"readOnly",void 0),qe([oe],us.prototype,"resize",void 0),qe([oe({mode:"boolean"})],us.prototype,"autofocus",void 0),qe([oe({attribute:"form"})],us.prototype,"formId",void 0),qe([oe],us.prototype,"list",void 0),qe([oe({converter:ie})],us.prototype,"maxlength",void 0),qe([oe({converter:ie})],us.prototype,"minlength",void 0),qe([oe],us.prototype,"name",void 0),qe([oe],us.prototype,"placeholder",void 0),qe([oe({converter:ie,mode:"fromView"})],us.prototype,"cols",void 0),qe([oe({converter:ie,mode:"fromView"})],us.prototype,"rows",void 0),qe([oe({mode:"boolean"})],us.prototype,"spellcheck",void 0),qe([b],us.prototype,"defaultSlottedNodes",void 0),Rt(us,Gi);function ps(e){return`:host([hidden]){display:none}:host{display:${e}}`}const fs=function(){if("boolean"==typeof Dt)return Dt;if("undefined"==typeof window||!window.document||!window.document.createElement)return Dt=!1,Dt;const e=document.createElement("style"),t=function(){const e=document.querySelector('meta[property="csp-nonce"]');return e?e.getAttribute("content"):null}();null!==t&&e.setAttribute("nonce",t),document.head.appendChild(e);try{e.sheet.insertRule("foo:focus-visible {color:inherit}",0),Dt=!0}catch(e){Dt=!1}finally{document.head.removeChild(e)}return Dt}()?"focus-visible":"focus";function bs(e){const t=getComputedStyle(document.body),i=document.querySelector("body");if(i){const s=i.getAttribute("data-vscode-theme-kind");for(const[o,n]of e){let e=t.getPropertyValue(o).toString();if("vscode-high-contrast"===s)0===e.length&&n.name.includes("background")&&(e="transparent"),"button-icon-hover-background"===n.name&&(e="transparent");else if("vscode-high-contrast-light"===s){if(0===e.length&&n.name.includes("background"))switch(n.name){case"button-primary-hover-background":e="#0F4A85";break;case"button-secondary-hover-background":case"button-icon-hover-background":e="transparent"}}else"contrast-active-border"===n.name&&(e="transparent");n.setValueFor(i,e)}}}const gs=new Map;let ms=!1;function vs(e,t){const i=Di.create(e);if(t){if(t.includes("--fake-vscode-token")){t=`${t}-${"id"+Math.random().toString(16).slice(2)}`}gs.set(t,i)}return ms||(!function(e){window.addEventListener("load",(()=>{new MutationObserver((()=>{bs(e)})).observe(document.body,{attributes:!0,attributeFilter:["class"]}),bs(e)}))}(gs),ms=!0),i}const ys=vs("background","--vscode-editor-background").withDefault("#1e1e1e"),xs=vs("border-width").withDefault(1),Cs=vs("contrast-active-border","--vscode-contrastActiveBorder").withDefault("#f38518");vs("contrast-border","--vscode-contrastBorder").withDefault("#6fc3df");const ws=vs("corner-radius").withDefault(0),$s=vs("corner-radius-round").withDefault(2),ks=vs("design-unit").withDefault(4),Is=vs("disabled-opacity").withDefault(.4),Ts=vs("focus-border","--vscode-focusBorder").withDefault("#007fd4"),Os=vs("font-family","--vscode-font-family").withDefault("-apple-system, BlinkMacSystemFont, Segoe UI, Roboto, Helvetica, Arial, sans-serif, Apple Color Emoji, Segoe UI Emoji, Segoe UI Symbol");vs("font-weight","--vscode-font-weight").withDefault("400");const Ss=vs("foreground","--vscode-foreground").withDefault("#cccccc"),As=vs("input-height").withDefault("26"),Rs=vs("input-min-width").withDefault("100px"),Es=vs("type-ramp-base-font-size","--vscode-font-size").withDefault("13px"),Ds=vs("type-ramp-base-line-height").withDefault("normal"),Ps=vs("type-ramp-minus1-font-size").withDefault("11px"),Bs=vs("type-ramp-minus1-line-height").withDefault("16px");vs("type-ramp-minus2-font-size").withDefault("9px"),vs("type-ramp-minus2-line-height").withDefault("16px"),vs("type-ramp-plus1-font-size").withDefault("16px"),vs("type-ramp-plus1-line-height").withDefault("24px");const Fs=vs("scrollbarWidth").withDefault("10px"),Ls=vs("scrollbarHeight").withDefault("10px"),Hs=vs("scrollbar-slider-background","--vscode-scrollbarSlider-background").withDefault("#79797966"),Vs=vs("scrollbar-slider-hover-background","--vscode-scrollbarSlider-hoverBackground").withDefault("#646464b3"),Ms=vs("scrollbar-slider-active-background","--vscode-scrollbarSlider-activeBackground").withDefault("#bfbfbf66"),Ns=vs("badge-background","--vscode-badge-background").withDefault("#4d4d4d"),zs=vs("badge-foreground","--vscode-badge-foreground").withDefault("#ffffff"),js=vs("button-border","--vscode-button-border").withDefault("transparent"),_s=vs("button-icon-background").withDefault("transparent"),qs=vs("button-icon-corner-radius").withDefault("5px"),Us=vs("button-icon-outline-offset").withDefault(0),Ks=vs("button-icon-hover-background","--fake-vscode-token").withDefault("rgba(90, 93, 94, 0.31)"),Ws=vs("button-icon-padding").withDefault("3px"),Gs=vs("button-primary-background","--vscode-button-background").withDefault("#0e639c"),Qs=vs("button-primary-foreground","--vscode-button-foreground").withDefault("#ffffff"),Xs=vs("button-primary-hover-background","--vscode-button-hoverBackground").withDefault("#1177bb"),Ys=vs("button-secondary-background","--vscode-button-secondaryBackground").withDefault("#3a3d41"),Js=vs("button-secondary-foreground","--vscode-button-secondaryForeground").withDefault("#ffffff"),Zs=vs("button-secondary-hover-background","--vscode-button-secondaryHoverBackground").withDefault("#45494e"),eo=vs("button-padding-horizontal").withDefault("11px"),to=vs("button-padding-vertical").withDefault("4px"),io=vs("checkbox-background","--vscode-checkbox-background").withDefault("#3c3c3c"),so=vs("checkbox-border","--vscode-checkbox-border").withDefault("#3c3c3c"),oo=vs("checkbox-corner-radius").withDefault(3);vs("checkbox-foreground","--vscode-checkbox-foreground").withDefault("#f0f0f0");const no=vs("list-active-selection-background","--vscode-list-activeSelectionBackground").withDefault("#094771"),ro=vs("list-active-selection-foreground","--vscode-list-activeSelectionForeground").withDefault("#ffffff"),ao=vs("list-hover-background","--vscode-list-hoverBackground").withDefault("#2a2d2e"),lo=vs("divider-background","--vscode-settings-dropdownListBorder").withDefault("#454545"),co=vs("dropdown-background","--vscode-dropdown-background").withDefault("#3c3c3c"),ho=vs("dropdown-border","--vscode-dropdown-border").withDefault("#3c3c3c");vs("dropdown-foreground","--vscode-dropdown-foreground").withDefault("#f0f0f0");const uo=vs("dropdown-list-max-height").withDefault("200px"),po=vs("input-background","--vscode-input-background").withDefault("#3c3c3c"),fo=vs("input-foreground","--vscode-input-foreground").withDefault("#cccccc");vs("input-placeholder-foreground","--vscode-input-placeholderForeground").withDefault("#cccccc");const bo=vs("link-active-foreground","--vscode-textLink-activeForeground").withDefault("#3794ff"),go=vs("link-foreground","--vscode-textLink-foreground").withDefault("#3794ff"),mo=vs("progress-background","--vscode-progressBar-background").withDefault("#0e70c0"),vo=vs("panel-tab-active-border","--vscode-panelTitle-activeBorder").withDefault("#e7e7e7"),yo=vs("panel-tab-active-foreground","--vscode-panelTitle-activeForeground").withDefault("#e7e7e7"),xo=vs("panel-tab-foreground","--vscode-panelTitle-inactiveForeground").withDefault("#e7e7e799");vs("panel-view-background","--vscode-panel-background").withDefault("#1e1e1e"),vs("panel-view-border","--vscode-panel-border").withDefault("#80808059");const Co=vs("tag-corner-radius").withDefault("2px");class wo extends _t{connectedCallback(){super.connectedCallback(),this.circular||(this.circular=!0)}}const $o=wo.compose({baseName:"badge",template:jt,styles:(e,t)=>ge` ${ps("inline-block")} :host{box-sizing: border-box;font-family: ${Os};font-size: ${Ps};line-height: ${Bs};text-align: center}.control{align-items: center;background-color: ${Ns};border: calc(${xs} * 1px) solid ${js};border-radius: 11px;box-sizing: border-box;color: ${zs};display: flex;height: calc(${ks} * 4px);justify-content: center;min-width: calc(${ks} * 4px + 2px);min-height: calc(${ks} * 4px + 2px);padding: 3px 6px}`});"function"==typeof SuppressedError&&SuppressedError;const ko=ge` ${ps("inline-flex")} :host{outline: none;font-family: ${Os};font-size: ${Es};line-height: ${Ds};color: ${Qs};background: ${Gs};border-radius: calc(${$s} * 1px);fill: currentColor;cursor: pointer}.control{background: transparent;height: inherit;flex-grow: 1;box-sizing: border-box;display: inline-flex;justify-content: center;align-items: center;padding: ${to} ${eo};white-space: wrap;outline: none;text-decoration: none;border: calc(${xs} * 1px) solid ${js};color: inherit;border-radius: inherit;fill: inherit;cursor: inherit;font-family: inherit}:host(:hover){background: ${Xs}}:host(:active){background: ${Gs}}.control:${fs}{outline: calc(${xs} * 1px) solid ${Ts};outline-offset: calc(${xs} * 2px)}.control::-moz-focus-inner{border: 0}:host([disabled]){opacity: ${Is};background: ${Gs};cursor: ${"not-allowed"}}.content{display: flex}.start{display: flex}::slotted(svg),	::slotted(span){width: calc(${ks} * 4px);height: calc(${ks} * 4px)}.start{margin-inline-end: 8px}`,Io=ge`	:host([appearance='primary']){background: ${Gs};color: ${Qs}}:host([appearance='primary']:hover){background: ${Xs}}:host([appearance='primary']:active) .control:active{background: ${Gs}}:host([appearance='primary']) .control:${fs}{outline: calc(${xs} * 1px) solid ${Ts};outline-offset: calc(${xs} * 2px)}:host([appearance='primary'][disabled]){background: ${Gs}}`,To=ge`	:host([appearance='secondary']){background: ${Ys};color: ${Js}}:host([appearance='secondary']:hover){background: ${Zs}}:host([appearance='secondary']:active) .control:active{background: ${Ys}}:host([appearance='secondary']) .control:${fs}{outline: calc(${xs} * 1px) solid ${Ts};outline-offset: calc(${xs} * 2px)}:host([appearance='secondary'][disabled]){background: ${Ys}}`,Oo=ge`	:host([appearance='icon']){background: ${_s};border-radius: ${qs};color: ${Ss}}:host([appearance='icon']:hover){background: ${Ks};outline: 1px dotted ${Cs};outline-offset: -1px}:host([appearance='icon']) .control{padding: ${Ws};border: none}:host([appearance='icon']:active) .control:active{background: ${Ks}}:host([appearance='icon']) .control:${fs}{outline: calc(${xs} * 1px) solid ${Ts};outline-offset: ${Us}}:host([appearance='icon'][disabled]){background: ${_s}}`;class So extends Xt{connectedCallback(){if(super.connectedCallback(),!this.appearance){const e=this.getAttribute("appearance");this.appearance=e}}attributeChangedCallback(e,t,i){if("appearance"===e&&"icon"===i){this.getAttribute("aria-label")||(this.ariaLabel="Icon Button")}"aria-label"===e&&(this.ariaLabel=i),"disabled"===e&&(this.disabled=null!==i)}}!function(e,t,i,s){var o,n=arguments.length,r=n<3?t:null===s?s=Object.getOwnPropertyDescriptor(t,i):s;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)r=Reflect.decorate(e,t,i,s);else for(var a=e.length-1;a>=0;a--)(o=e[a])&&(r=(n<3?o(r):n>3?o(t,i,r):o(t,i))||r);n>3&&r&&Object.defineProperty(t,i,r)}([oe],So.prototype,"appearance",void 0);const Ao=So.compose({baseName:"button",template:(e,t)=>U`<button class="control" part="control" ?autofocus="${e=>e.autofocus}" ?disabled="${e=>e.disabled}" form="${e=>e.formId}" formaction="${e=>e.formaction}" formenctype="${e=>e.formenctype}" formmethod="${e=>e.formmethod}" formnovalidate="${e=>e.formnovalidate}" formtarget="${e=>e.formtarget}" name="${e=>e.name}" type="${e=>e.type}" value="${e=>e.value}" aria-atomic="${e=>e.ariaAtomic}" aria-busy="${e=>e.ariaBusy}" aria-controls="${e=>e.ariaControls}" aria-current="${e=>e.ariaCurrent}" aria-describedby="${e=>e.ariaDescribedby}" aria-details="${e=>e.ariaDetails}" aria-disabled="${e=>e.ariaDisabled}" aria-errormessage="${e=>e.ariaErrormessage}" aria-expanded="${e=>e.ariaExpanded}" aria-flowto="${e=>e.ariaFlowto}" aria-haspopup="${e=>e.ariaHaspopup}" aria-hidden="${e=>e.ariaHidden}" aria-invalid="${e=>e.ariaInvalid}" aria-keyshortcuts="${e=>e.ariaKeyshortcuts}" aria-label="${e=>e.ariaLabel}" aria-labelledby="${e=>e.ariaLabelledby}" aria-live="${e=>e.ariaLive}" aria-owns="${e=>e.ariaOwns}" aria-pressed="${e=>e.ariaPressed}" aria-relevant="${e=>e.ariaRelevant}" aria-roledescription="${e=>e.ariaRoledescription}" ${Te("control")}>${_e(0,t)}<span class="content" part="content"><slot ${Ve("defaultSlottedContent")}></slot></span>${je(0,t)}</button>`,styles:(e,t)=>ge` ${ko} ${Io} ${To} ${Oo}`,shadowOptions:{delegatesFocus:!0}});class Ro extends li{connectedCallback(){super.connectedCallback(),this.textContent?this.setAttribute("aria-label",this.textContent):this.setAttribute("aria-label","Checkbox")}}const Eo=Ro.compose({baseName:"checkbox",template:(e,t)=>U`<template role="checkbox" aria-checked="${e=>e.checked}" aria-required="${e=>e.required}" aria-disabled="${e=>e.disabled}" aria-readonly="${e=>e.readOnly}" tabindex="${e=>e.disabled?null:0}" @keypress="${(e,t)=>e.keypressHandler(t.event)}" @click="${(e,t)=>e.clickHandler(t.event)}" class="${e=>e.readOnly?"readonly":""} ${e=>e.checked?"checked":""} ${e=>e.indeterminate?"indeterminate":""}"><div part="control" class="control"><slot name="checked-indicator">${t.checkedIndicator||""}</slot><slot name="indeterminate-indicator">${t.indeterminateIndicator||""}</slot></div><label part="label" class="${e=>e.defaultSlottedNodes&&e.defaultSlottedNodes.length?"label":"label label__hidden"}"><slot ${Ve("defaultSlottedNodes")}></slot></label></template>`,styles:(e,t)=>ge` ${ps("inline-flex")} :host{align-items: center;outline: none;margin: calc(${ks} * 1px) 0;user-select: none;font-size: ${Es};line-height: ${Ds}}.control{position: relative;width: calc(${ks} * 4px + 2px);height: calc(${ks} * 4px + 2px);box-sizing: border-box;border-radius: calc(${oo} * 1px);border: calc(${xs} * 1px) solid ${so};background: ${io};outline: none;cursor: pointer}.label{font-family: ${Os};color: ${Ss};padding-inline-start: calc(${ks} * 2px + 2px);margin-inline-end: calc(${ks} * 2px + 2px);cursor: pointer}.label__hidden{display: none;visibility: hidden}.checked-indicator{width: 100%;height: 100%;display: block;fill: ${Ss};opacity: 0;pointer-events: none}.indeterminate-indicator{border-radius: 2px;background: ${Ss};position: absolute;top: 50%;left: 50%;width: 50%;height: 50%;transform: translate(-50%, -50%);opacity: 0}:host(:enabled) .control:hover{background: ${io};border-color: ${so}}:host(:enabled) .control:active{background: ${io};border-color: ${Ts}}:host(:${fs}) .control{border: calc(${xs} * 1px) solid ${Ts}}:host(.disabled) .label,	:host(.readonly) .label,	:host(.readonly) .control,	:host(.disabled) .control{cursor: ${"not-allowed"}}:host(.checked:not(.indeterminate)) .checked-indicator,	:host(.indeterminate) .indeterminate-indicator{opacity: 1}:host(.disabled){opacity: ${Is}}`,checkedIndicator:'\n\t\t<svg \n\t\t\tpart="checked-indicator"\n\t\t\tclass="checked-indicator"\n\t\t\twidth="16" \n\t\t\theight="16" \n\t\t\tviewBox="0 0 16 16" \n\t\t\txmlns="http://www.w3.org/2000/svg" \n\t\t\tfill="currentColor"\n\t\t>\n\t\t\t<path \n\t\t\t\tfill-rule="evenodd" \n\t\t\t\tclip-rule="evenodd" \n\t\t\t\td="M14.431 3.323l-8.47 10-.79-.036-3.35-4.77.818-.574 2.978 4.24 8.051-9.506.764.646z"\n\t\t\t/>\n\t\t</svg>\n\t',indeterminateIndicator:'\n\t\t<div part="indeterminate-indicator" class="indeterminate-indicator"></div>\n\t'});class Do extends ii{connectedCallback(){super.connectedCallback();this.getAttribute("aria-label")||this.setAttribute("aria-label","Data Grid")}}const Po=Do.compose({baseName:"data-grid",baseClass:ii,template:(e,t)=>{const i=function(e){const t=e.tagFor(ti);return U`<${t} :rowData="${e=>e}" :cellItemTemplate="${(e,t)=>t.parent.cellItemTemplate}" :headerCellItemTemplate="${(e,t)=>t.parent.headerCellItemTemplate}"></${t}>`}(e),s=e.tagFor(ti);return U`<template role="grid" tabindex="0" :rowElementTag="${()=>s}" :defaultRowItemTemplate="${i}" ${Ne({property:"rowElements",filter:Fe("[role=row]")})}><slot></slot></template>`},styles:(e,t)=>ge`	:host{display: flex;position: relative;flex-direction: column;width: 100%}`});class Bo extends ti{}const Fo=Bo.compose({baseName:"data-grid-row",baseClass:ti,template:(e,t)=>{const i=function(e){const t=e.tagFor(ni);return U`<${t} cell-type="${e=>e.isRowHeader?"rowheader":void 0}" grid-column="${(e,t)=>t.index+1}" :rowData="${(e,t)=>t.parent.rowData}" :columnDefinition="${e=>e}"></${t}>`}(e),s=function(e){const t=e.tagFor(ni);return U`<${t} cell-type="columnheader" grid-column="${(e,t)=>t.index+1}" :columnDefinition="${e=>e}"></${t}>`}(e);return U`<template role="row" class="${e=>"default"!==e.rowType?e.rowType:""}" :defaultCellItemTemplate="${i}" :defaultHeaderCellItemTemplate="${s}" ${Ne({property:"cellElements",filter:Fe('[role="cell"],[role="gridcell"],[role="columnheader"],[role="rowheader"]')})}><slot ${Ve("slottedCellElements")}></slot></template>`},styles:(e,t)=>ge`	:host{display: grid;padding: calc((${ks} / 4) * 1px) 0;box-sizing: border-box;width: 100%;background: transparent}:host(.header){}:host(.sticky-header){background: ${ys};position: sticky;top: 0}:host(:hover){background: ${ao};outline: 1px dotted ${Cs};outline-offset: -1px}`});class Lo extends ni{}const Ho=Lo.compose({baseName:"data-grid-cell",baseClass:ni,template:(e,t)=>U`<template tabindex="-1" role="${e=>e.cellType&&"default"!==e.cellType?e.cellType:"gridcell"}" class=" ${e=>"columnheader"===e.cellType?"column-header":"rowheader"===e.cellType?"row-header":""} "><slot></slot></template>`,styles:(e,t)=>ge`	:host{padding: calc(${ks} * 1px) calc(${ks} * 3px);color: ${Ss};opacity: 1;box-sizing: border-box;font-family: ${Os};font-size: ${Es};line-height: ${Ds};font-weight: 400;border: solid calc(${xs} * 1px) transparent;border-radius: calc(${ws} * 1px);white-space: wrap;overflow-wrap: anywhere}:host(.column-header){font-weight: 600}:host(:${fs}),	:host(:focus),	:host(:active){background: ${no};border: solid calc(${xs} * 1px) ${Ts};color: ${ro};outline: none}:host(:${fs}) ::slotted(*),	:host(:focus) ::slotted(*),	:host(:active) ::slotted(*){color: ${ro} !important}`});class Vo extends ji{}const Mo=Vo.compose({baseName:"divider",template:(e,t)=>U`<template role="${e=>e.role}" aria-orientation="${e=>e.orientation}"></template>`,styles:(e,t)=>ge` ${ps("block")} :host{border: none;border-top: calc(${xs} * 1px) solid ${lo};box-sizing: content-box;height: 0;margin: calc(${ks} * 1px) 0;width: 100%}`});class No extends ss{}const zo=No.compose({baseName:"dropdown",template:(e,t)=>U`<template class="${e=>[e.collapsible&&"collapsible",e.collapsible&&e.open&&"open",e.disabled&&"disabled",e.collapsible&&e.position].filter(Boolean).join(" ")}" aria-activedescendant="${e=>e.ariaActiveDescendant}" aria-controls="${e=>e.ariaControls}" aria-disabled="${e=>e.ariaDisabled}" aria-expanded="${e=>e.ariaExpanded}" aria-haspopup="${e=>e.collapsible?"listbox":null}" aria-multiselectable="${e=>e.ariaMultiSelectable}" ?open="${e=>e.open}" role="combobox" tabindex="${e=>e.disabled?null:"0"}" @click="${(e,t)=>e.clickHandler(t.event)}" @focusin="${(e,t)=>e.focusinHandler(t.event)}" @focusout="${(e,t)=>e.focusoutHandler(t.event)}" @keydown="${(e,t)=>e.keydownHandler(t.event)}" @mousedown="${(e,t)=>e.mousedownHandler(t.event)}">${Re((e=>e.collapsible),U`<div class="control" part="control" ?disabled="${e=>e.disabled}" ${Te("control")}>${_e(0,t)}<slot name="button-container"><div class="selected-value" part="selected-value"><slot name="selected-value">${e=>e.displayValue}</slot></div><div aria-hidden="true" class="indicator" part="indicator"><slot name="indicator">${t.indicator||""}</slot></div></slot>${je(0,t)}</div>`)}<div class="listbox" id="${e=>e.listboxId}" part="listbox" role="listbox" ?disabled="${e=>e.disabled}" ?hidden="${e=>!!e.collapsible&&!e.open}" ${Te("listbox")}><slot ${Ve({filter:ui.slottedOptionFilter,flatten:!0,property:"slottedOptions"})}></slot></div></template>`,styles:(e,t)=>ge` ${ps("inline-flex")} :host{background: ${co};border-radius: calc(${$s} * 1px);box-sizing: border-box;color: ${Ss};contain: contents;font-family: ${Os};height: calc(${As} * 1px);position: relative;user-select: none;min-width: ${Rs};outline: none;vertical-align: top}.control{align-items: center;box-sizing: border-box;border: calc(${xs} * 1px) solid ${ho};border-radius: calc(${$s} * 1px);cursor: pointer;display: flex;font-family: inherit;font-size: ${Es};line-height: ${Ds};min-height: 100%;padding: 2px 6px 2px 8px;width: 100%}.listbox{background: ${co};border: calc(${xs} * 1px) solid ${Ts};border-radius: calc(${$s} * 1px);box-sizing: border-box;display: inline-flex;flex-direction: column;left: 0;max-height: ${uo};padding: 0;overflow-y: auto;position: absolute;width: 100%;z-index: 1}.listbox[hidden]{display: none}:host(:${fs}) .control{border-color: ${Ts}}:host(:not([disabled]):hover){background: ${co};border-color: ${ho}}:host(:${fs}) ::slotted([aria-selected="true"][role="option"]:not([disabled])){background: ${no};border: calc(${xs} * 1px) solid transparent;color: ${ro}}:host([disabled]){cursor: ${"not-allowed"};opacity: ${Is}}:host([disabled]) .control{cursor: ${"not-allowed"};user-select: none}:host([disabled]:hover){background: ${co};color: ${Ss};fill: currentcolor}:host(:not([disabled])) .control:active{border-color: ${Ts}}:host(:empty) .listbox{display: none}:host([open]) .control{border-color: ${Ts}}:host([open][position='above']) .listbox{border-bottom-left-radius: 0;border-bottom-right-radius: 0}:host([open][position='below']) .listbox{border-top-left-radius: 0;border-top-right-radius: 0}:host([open][position='above']) .listbox{bottom: calc(${As} * 1px)}:host([open][position='below']) .listbox{top: calc(${As} * 1px)}.selected-value{flex: 1 1 auto;font-family: inherit;overflow: hidden;text-align: start;text-overflow: ellipsis;white-space: nowrap}.indicator{flex: 0 0 auto;margin-inline-start: 1em}slot[name='listbox']{display: none;width: 100%}:host([open]) slot[name='listbox']{display: flex;position: absolute}.end{margin-inline-start: auto}.start,	.end,	.indicator,	.select-indicator,	::slotted(svg),	::slotted(span){fill: currentcolor;height: 1em;min-height: calc(${ks} * 4px);min-width: calc(${ks} * 4px);width: 1em}::slotted([role='option']),	::slotted(option){flex: 0 0 auto}`,indicator:'\n\t\t<svg \n\t\t\tclass="select-indicator"\n\t\t\tpart="select-indicator"\n\t\t\twidth="16" \n\t\t\theight="16" \n\t\t\tviewBox="0 0 16 16" \n\t\t\txmlns="http://www.w3.org/2000/svg" \n\t\t\tfill="currentColor"\n\t\t>\n\t\t\t<path \n\t\t\t\tfill-rule="evenodd" \n\t\t\t\tclip-rule="evenodd" \n\t\t\t\td="M7.976 10.072l4.357-4.357.62.618L8.284 11h-.618L3 6.333l.619-.618 4.357 4.357z"\n\t\t\t/>\n\t\t</svg>\n\t'});class jo extends Nt{}const _o=jo.compose({baseName:"link",template:(e,t)=>U`<a class="control" part="control" download="${e=>e.download}" href="${e=>e.href}" hreflang="${e=>e.hreflang}" ping="${e=>e.ping}" referrerpolicy="${e=>e.referrerpolicy}" rel="${e=>e.rel}" target="${e=>e.target}" type="${e=>e.type}" aria-atomic="${e=>e.ariaAtomic}" aria-busy="${e=>e.ariaBusy}" aria-controls="${e=>e.ariaControls}" aria-current="${e=>e.ariaCurrent}" aria-describedby="${e=>e.ariaDescribedby}" aria-details="${e=>e.ariaDetails}" aria-disabled="${e=>e.ariaDisabled}" aria-errormessage="${e=>e.ariaErrormessage}" aria-expanded="${e=>e.ariaExpanded}" aria-flowto="${e=>e.ariaFlowto}" aria-haspopup="${e=>e.ariaHaspopup}" aria-hidden="${e=>e.ariaHidden}" aria-invalid="${e=>e.ariaInvalid}" aria-keyshortcuts="${e=>e.ariaKeyshortcuts}" aria-label="${e=>e.ariaLabel}" aria-labelledby="${e=>e.ariaLabelledby}" aria-live="${e=>e.ariaLive}" aria-owns="${e=>e.ariaOwns}" aria-relevant="${e=>e.ariaRelevant}" aria-roledescription="${e=>e.ariaRoledescription}" ${Te("control")}>${_e(0,t)}<span class="content" part="content"><slot ${Ve("defaultSlottedContent")}></slot></span>${je(0,t)}</a>`,styles:(e,t)=>ge` ${ps("inline-flex")} :host{background: transparent;box-sizing: border-box;color: ${go};cursor: pointer;fill: currentcolor;font-family: ${Os};font-size: ${Es};line-height: ${Ds};outline: none}.control{background: transparent;border: calc(${xs} * 1px) solid transparent;border-radius: calc(${ws} * 1px);box-sizing: border-box;color: inherit;cursor: inherit;fill: inherit;font-family: inherit;height: inherit;padding: 0;outline: none;text-decoration: none;word-break: break-word}.control::-moz-focus-inner{border: 0}:host(:hover){color: ${bo}}:host(:hover) .content{text-decoration: underline}:host(:active){background: transparent;color: ${bo}}:host(:${fs}) .control,	:host(:focus) .control{border: calc(${xs} * 1px) solid ${Ts}}`,shadowOptions:{delegatesFocus:!0}});class qo extends ci{connectedCallback(){super.connectedCallback(),this.textContent?this.setAttribute("aria-label",this.textContent):this.setAttribute("aria-label","Option")}}const Uo=qo.compose({baseName:"option",template:(e,t)=>U`<template aria-checked="${e=>e.ariaChecked}" aria-disabled="${e=>e.ariaDisabled}" aria-posinset="${e=>e.ariaPosInSet}" aria-selected="${e=>e.ariaSelected}" aria-setsize="${e=>e.ariaSetSize}" class="${e=>[e.checked&&"checked",e.selected&&"selected",e.disabled&&"disabled"].filter(Boolean).join(" ")}" role="option">${_e(0,t)}<span class="content" part="content"><slot ${Ve("content")}></slot></span>${je(0,t)}</template>`,styles:(e,t)=>ge` ${ps("inline-flex")} :host{font-family: var(--body-font);border-radius: ${ws};border: calc(${xs} * 1px) solid transparent;box-sizing: border-box;color: ${Ss};cursor: pointer;fill: currentcolor;font-size: ${Es};line-height: ${Ds};margin: 0;outline: none;overflow: hidden;padding: 0 calc((${ks} / 2) * 1px) calc((${ks} / 4) * 1px);user-select: none;white-space: nowrap}:host(:${fs}){border-color: ${Ts};background: ${no};color: ${Ss}}:host([aria-selected='true']){background: ${no};border: calc(${xs} * 1px) solid transparent;color: ${ro}}:host(:active){background: ${no};color: ${ro}}:host(:not([aria-selected='true']):hover){background: ${no};border: calc(${xs} * 1px) solid transparent;color: ${ro}}:host(:not([aria-selected='true']):active){background: ${no};color: ${Ss}}:host([disabled]){cursor: ${"not-allowed"};opacity: ${Is}}:host([disabled]:hover){background-color: inherit}.content{grid-column-start: 2;justify-self: start;overflow: hidden;text-overflow: ellipsis}`});class Ko extends ls{connectedCallback(){super.connectedCallback(),this.orientation&&(this.orientation=as);this.getAttribute("aria-label")||this.setAttribute("aria-label","Panels")}}const Wo=Ko.compose({baseName:"panels",template:(e,t)=>U`<template class="${e=>e.orientation}">${_e(0,t)}<div class="tablist" part="tablist" role="tablist"><slot class="tab" name="tab" part="tab" ${Ve("tabs")}></slot>${Re((e=>e.showActiveIndicator),U`<div ${Te("activeIndicatorRef")} class="activeIndicator" part="activeIndicator"></div>`)}</div>${je(0,t)}<div class="tabpanel" part="tabpanel"><slot name="tabpanel" ${Ve("tabpanels")}></slot></div></template>`,styles:(e,t)=>ge` ${ps("grid")} :host{box-sizing: border-box;font-family: ${Os};font-size: ${Es};line-height: ${Ds};color: ${Ss};grid-template-columns: auto 1fr auto;grid-template-rows: auto 1fr;overflow-x: auto}.tablist{display: grid;grid-template-rows: auto auto;grid-template-columns: auto;column-gap: calc(${ks} * 8px);position: relative;width: max-content;align-self: end;padding: calc(${ks} * 1px) calc(${ks} * 1px) 0;box-sizing: border-box}.start,	.end{align-self: center}.activeIndicator{grid-row: 2;grid-column: 1;width: 100%;height: calc((${ks} / 4) * 1px);justify-self: center;background: ${yo};margin: 0;border-radius: calc(${ws} * 1px)}.activeIndicatorTransition{transition: transform 0.01s linear}.tabpanel{grid-row: 2;grid-column-start: 1;grid-column-end: 4;position: relative}`});class Go extends rs{connectedCallback(){super.connectedCallback(),this.disabled&&(this.disabled=!1),this.textContent&&this.setAttribute("aria-label",this.textContent)}}const Qo=Go.compose({baseName:"panel-tab",template:(e,t)=>U`<template slot="tab" role="tab" aria-disabled="${e=>e.disabled}"><slot></slot></template>`,styles:(e,t)=>ge` ${ps("inline-flex")} :host{box-sizing: border-box;font-family: ${Os};font-size: ${Es};line-height: ${Ds};height: calc(${ks} * 7px);padding: calc(${ks} * 1px) 0;color: ${xo};fill: currentcolor;border-radius: calc(${ws} * 1px);border: solid calc(${xs} * 1px) transparent;align-items: center;justify-content: center;grid-row: 1;cursor: pointer}:host(:hover){color: ${yo};fill: currentcolor}:host(:active){color: ${yo};fill: currentcolor}:host([aria-selected='true']){background: transparent;color: ${yo};fill: currentcolor}:host([aria-selected='true']:hover){background: transparent;color: ${yo};fill: currentcolor}:host([aria-selected='true']:active){background: transparent;color: ${yo};fill: currentcolor}:host(:${fs}){outline: none;border: solid calc(${xs} * 1px) ${vo}}:host(:focus){outline: none}::slotted(vscode-badge){margin-inline-start: calc(${ks} * 2px)}`});class Xo extends ns{}const Yo=Xo.compose({baseName:"panel-view",template:(e,t)=>U`<template slot="tabpanel" role="tabpanel"><slot></slot></template>`,styles:(e,t)=>ge` ${ps("flex")} :host{color: inherit;background-color: transparent;border: solid calc(${xs} * 1px) transparent;box-sizing: border-box;font-size: ${Es};line-height: ${Ds};padding: 10px calc((${ks} + 2) * 1px)}`});class Jo extends Qi{connectedCallback(){super.connectedCallback(),this.paused&&(this.paused=!1),this.setAttribute("aria-label","Loading"),this.setAttribute("aria-live","assertive"),this.setAttribute("role","alert")}attributeChangedCallback(e,t,i){"value"===e&&this.removeAttribute("value")}}const Zo=Jo.compose({baseName:"progress-ring",template:(e,t)=>U`<template role="progressbar" aria-valuenow="${e=>e.value}" aria-valuemin="${e=>e.min}" aria-valuemax="${e=>e.max}" class="${e=>e.paused?"paused":""}">${Re((e=>"number"==typeof e.value),U`<svg class="progress" part="progress" viewBox="0 0 16 16" slot="determinate"><circle class="background" part="background" cx="8px" cy="8px" r="7px"></circle><circle class="determinate" part="determinate" style="stroke-dasharray: ${e=>44*e.percentComplete/100}px ${44}px" cx="8px" cy="8px" r="7px"></circle></svg>`,U`<slot name="indeterminate" slot="indeterminate">${t.indeterminateIndicator||""}</slot>`)}</template>`,styles:(e,t)=>ge` ${ps("flex")} :host{align-items: center;outline: none;height: calc(${ks} * 7px);width: calc(${ks} * 7px);margin: 0}.progress{height: 100%;width: 100%}.background{fill: none;stroke: transparent;stroke-width: calc(${ks} / 2 * 1px)}.indeterminate-indicator-1{fill: none;stroke: ${mo};stroke-width: calc(${ks} / 2 * 1px);stroke-linecap: square;transform-origin: 50% 50%;transform: rotate(-90deg);transition: all 0.2s ease-in-out;animation: spin-infinite 2s linear infinite}@keyframes spin-infinite{0%{stroke-dasharray: 0.01px 43.97px;transform: rotate(0deg)}50%{stroke-dasharray: 21.99px 21.99px;transform: rotate(450deg)}100%{stroke-dasharray: 0.01px 43.97px;transform: rotate(1080deg)}}`,indeterminateIndicator:'\n\t\t<svg class="progress" part="progress" viewBox="0 0 16 16">\n\t\t\t<circle\n\t\t\t\tclass="background"\n\t\t\t\tpart="background"\n\t\t\t\tcx="8px"\n\t\t\t\tcy="8px"\n\t\t\t\tr="7px"\n\t\t\t></circle>\n\t\t\t<circle\n\t\t\t\tclass="indeterminate-indicator-1"\n\t\t\t\tpart="indeterminate-indicator-1"\n\t\t\t\tcx="8px"\n\t\t\t\tcy="8px"\n\t\t\t\tr="7px"\n\t\t\t></circle>\n\t\t</svg>\n\t'});class en extends Xi{connectedCallback(){super.connectedCallback();const e=this.querySelector("label");if(e){const t="radio-group-"+Math.random().toString(16).slice(2);e.setAttribute("id",t),this.setAttribute("aria-labelledby",t)}}}const tn=en.compose({baseName:"radio-group",template:(e,t)=>U`<template role="radiogroup" aria-disabled="${e=>e.disabled}" aria-readonly="${e=>e.readOnly}" @click="${(e,t)=>e.clickHandler(t.event)}" @keydown="${(e,t)=>e.keydownHandler(t.event)}" @focusout="${(e,t)=>e.focusOutHandler(t.event)}"><slot name="label"></slot><div class="positioning-region ${e=>e.orientation===Et.horizontal?"horizontal":"vertical"}" part="positioning-region"><slot ${Ve({property:"slottedRadioButtons",filter:Fe("[role=radio]")})}></slot></div></template>`,styles:(e,t)=>ge` ${ps("flex")} :host{align-items: flex-start;margin: calc(${ks} * 1px) 0;flex-direction: column}.positioning-region{display: flex;flex-wrap: wrap}:host([orientation='vertical']) .positioning-region{flex-direction: column}:host([orientation='horizontal']) .positioning-region{flex-direction: row}::slotted([slot='label']){color: ${Ss};font-size: ${Es};margin: calc(${ks} * 1px) 0}`});class sn extends Zi{connectedCallback(){super.connectedCallback(),this.textContent?this.setAttribute("aria-label",this.textContent):this.setAttribute("aria-label","Radio")}}const on=sn.compose({baseName:"radio",template:(e,t)=>U`<template role="radio" class="${e=>e.checked?"checked":""} ${e=>e.readOnly?"readonly":""}" aria-checked="${e=>e.checked}" aria-required="${e=>e.required}" aria-disabled="${e=>e.disabled}" aria-readonly="${e=>e.readOnly}" @keypress="${(e,t)=>e.keypressHandler(t.event)}" @click="${(e,t)=>e.clickHandler(t.event)}"><div part="control" class="control"><slot name="checked-indicator">${t.checkedIndicator||""}</slot></div><label part="label" class="${e=>e.defaultSlottedNodes&&e.defaultSlottedNodes.length?"label":"label label__hidden"}"><slot ${Ve("defaultSlottedNodes")}></slot></label></template>`,styles:(e,t)=>ge` ${ps("inline-flex")} :host{align-items: center;flex-direction: row;font-size: ${Es};line-height: ${Ds};margin: calc(${ks} * 1px) 0;outline: none;position: relative;transition: all 0.2s ease-in-out;user-select: none}.control{background: ${io};border-radius: 999px;border: calc(${xs} * 1px) solid ${so};box-sizing: border-box;cursor: pointer;height: calc(${ks} * 4px);position: relative;outline: none;width: calc(${ks} * 4px)}.label{color: ${Ss};cursor: pointer;font-family: ${Os};margin-inline-end: calc(${ks} * 2px + 2px);padding-inline-start: calc(${ks} * 2px + 2px)}.label__hidden{display: none;visibility: hidden}.control,	.checked-indicator{flex-shrink: 0}.checked-indicator{background: ${Ss};border-radius: 999px;display: inline-block;inset: calc(${ks} * 1px);opacity: 0;pointer-events: none;position: absolute}:host(:not([disabled])) .control:hover{background: ${io};border-color: ${so}}:host(:not([disabled])) .control:active{background: ${io};border-color: ${Ts}}:host(:${fs}) .control{border: calc(${xs} * 1px) solid ${Ts}}:host([aria-checked='true']) .control{background: ${io};border: calc(${xs} * 1px) solid ${so}}:host([aria-checked='true']:not([disabled])) .control:hover{background: ${io};border: calc(${xs} * 1px) solid ${so}}:host([aria-checked='true']:not([disabled])) .control:active{background: ${io};border: calc(${xs} * 1px) solid ${Ts}}:host([aria-checked="true"]:${fs}:not([disabled])) .control{border: calc(${xs} * 1px) solid ${Ts}}:host([disabled]) .label,	:host([readonly]) .label,	:host([readonly]) .control,	:host([disabled]) .control{cursor: ${"not-allowed"}}:host([aria-checked='true']) .checked-indicator{opacity: 1}:host([disabled]){opacity: ${Is}}`,checkedIndicator:'\n\t\t<div part="checked-indicator" class="checked-indicator"></div>\n\t'});class nn extends _t{connectedCallback(){super.connectedCallback(),this.circular&&(this.circular=!1)}}const rn=nn.compose({baseName:"tag",template:jt,styles:(e,t)=>ge` ${ps("inline-block")} :host{box-sizing: border-box;font-family: ${Os};font-size: ${Ps};line-height: ${Bs}}.control{background-color: ${Ns};border: calc(${xs} * 1px) solid ${js};border-radius: ${Co};color: ${zs};padding: calc(${ks} * 0.5px) calc(${ks} * 1px);text-transform: uppercase}`});class an extends us{connectedCallback(){super.connectedCallback(),this.textContent?this.setAttribute("aria-label",this.textContent):this.setAttribute("aria-label","Text area")}}const ln=an.compose({baseName:"text-area",template:(e,t)=>U`<template class=" ${e=>e.readOnly?"readonly":""} ${e=>e.resize!==hs.none?`resize-${e.resize}`:""}"><label part="label" for="control" class="${e=>e.defaultSlottedNodes&&e.defaultSlottedNodes.length?"label":"label label__hidden"}"><slot ${Ve("defaultSlottedNodes")}></slot></label><textarea part="control" class="control" id="control" ?autofocus="${e=>e.autofocus}" cols="${e=>e.cols}" ?disabled="${e=>e.disabled}" form="${e=>e.form}" list="${e=>e.list}" maxlength="${e=>e.maxlength}" minlength="${e=>e.minlength}" name="${e=>e.name}" placeholder="${e=>e.placeholder}" ?readonly="${e=>e.readOnly}" ?required="${e=>e.required}" rows="${e=>e.rows}" ?spellcheck="${e=>e.spellcheck}" :value="${e=>e.value}" aria-atomic="${e=>e.ariaAtomic}" aria-busy="${e=>e.ariaBusy}" aria-controls="${e=>e.ariaControls}" aria-current="${e=>e.ariaCurrent}" aria-describedby="${e=>e.ariaDescribedby}" aria-details="${e=>e.ariaDetails}" aria-disabled="${e=>e.ariaDisabled}" aria-errormessage="${e=>e.ariaErrormessage}" aria-flowto="${e=>e.ariaFlowto}" aria-haspopup="${e=>e.ariaHaspopup}" aria-hidden="${e=>e.ariaHidden}" aria-invalid="${e=>e.ariaInvalid}" aria-keyshortcuts="${e=>e.ariaKeyshortcuts}" aria-label="${e=>e.ariaLabel}" aria-labelledby="${e=>e.ariaLabelledby}" aria-live="${e=>e.ariaLive}" aria-owns="${e=>e.ariaOwns}" aria-relevant="${e=>e.ariaRelevant}" aria-roledescription="${e=>e.ariaRoledescription}" @input="${(e,t)=>e.handleTextInput()}" @change="${e=>e.handleChange()}" ${Te("control")}></textarea></template>`,styles:(e,t)=>ge` ${ps("inline-block")} :host{font-family: ${Os};outline: none;user-select: none}.control{box-sizing: border-box;position: relative;color: ${fo};background: ${po};border-radius: calc(${$s} * 1px);border: calc(${xs} * 1px) solid ${ho};font: inherit;font-size: ${Es};line-height: ${Ds};padding: calc(${ks} * 2px + 1px);width: 100%;min-width: ${Rs};resize: none}.control:hover:enabled{background: ${po};border-color: ${ho}}.control:active:enabled{background: ${po};border-color: ${Ts}}.control:hover,	.control:${fs},	.control:disabled,	.control:active{outline: none}.control::-webkit-scrollbar{width: ${Fs};height: ${Ls}}.control::-webkit-scrollbar-corner{background: ${po}}.control::-webkit-scrollbar-thumb{background: ${Hs}}.control::-webkit-scrollbar-thumb:hover{background: ${Vs}}.control::-webkit-scrollbar-thumb:active{background: ${Ms}}:host(:focus-within:not([disabled])) .control{border-color: ${Ts}}:host([resize='both']) .control{resize: both}:host([resize='horizontal']) .control{resize: horizontal}:host([resize='vertical']) .control{resize: vertical}.label{display: block;color: ${Ss};cursor: pointer;font-size: ${Es};line-height: ${Ds};margin-bottom: 2px}.label__hidden{display: none;visibility: hidden}:host([disabled]) .label,	:host([readonly]) .label,	:host([readonly]) .control,	:host([disabled]) .control{cursor: ${"not-allowed"}}:host([disabled]){opacity: ${Is}}:host([disabled]) .control{border-color: ${ho}}`,shadowOptions:{delegatesFocus:!0}});class dn extends Wi{connectedCallback(){super.connectedCallback(),this.textContent?this.setAttribute("aria-label",this.textContent):this.setAttribute("aria-label","Text field")}}const cn=dn.compose({baseName:"text-field",template:(e,t)=>U`<template class=" ${e=>e.readOnly?"readonly":""} "><label part="label" for="control" class="${e=>e.defaultSlottedNodes&&e.defaultSlottedNodes.length?"label":"label label__hidden"}"><slot ${Ve({property:"defaultSlottedNodes",filter:es})}></slot></label><div class="root" part="root">${_e(0,t)}<input class="control" part="control" id="control" @input="${e=>e.handleTextInput()}" @change="${e=>e.handleChange()}" ?autofocus="${e=>e.autofocus}" ?disabled="${e=>e.disabled}" list="${e=>e.list}" maxlength="${e=>e.maxlength}" minlength="${e=>e.minlength}" pattern="${e=>e.pattern}" placeholder="${e=>e.placeholder}" ?readonly="${e=>e.readOnly}" ?required="${e=>e.required}" size="${e=>e.size}" ?spellcheck="${e=>e.spellcheck}" :value="${e=>e.value}" type="${e=>e.type}" aria-atomic="${e=>e.ariaAtomic}" aria-busy="${e=>e.ariaBusy}" aria-controls="${e=>e.ariaControls}" aria-current="${e=>e.ariaCurrent}" aria-describedby="${e=>e.ariaDescribedby}" aria-details="${e=>e.ariaDetails}" aria-disabled="${e=>e.ariaDisabled}" aria-errormessage="${e=>e.ariaErrormessage}" aria-flowto="${e=>e.ariaFlowto}" aria-haspopup="${e=>e.ariaHaspopup}" aria-hidden="${e=>e.ariaHidden}" aria-invalid="${e=>e.ariaInvalid}" aria-keyshortcuts="${e=>e.ariaKeyshortcuts}" aria-label="${e=>e.ariaLabel}" aria-labelledby="${e=>e.ariaLabelledby}" aria-live="${e=>e.ariaLive}" aria-owns="${e=>e.ariaOwns}" aria-relevant="${e=>e.ariaRelevant}" aria-roledescription="${e=>e.ariaRoledescription}" ${Te("control")} />${je(0,t)}</div></template>`,styles:(e,t)=>ge` ${ps("inline-block")} :host{font-family: ${Os};outline: none;user-select: none}.root{box-sizing: border-box;position: relative;display: flex;flex-direction: row;color: ${fo};background: ${po};border-radius: calc(${$s} * 1px);border: calc(${xs} * 1px) solid ${ho};height: calc(${As} * 1px);min-width: ${Rs}}.control{-webkit-appearance: none;font: inherit;background: transparent;border: 0;color: inherit;height: calc(100% - (${ks} * 1px));width: 100%;margin-top: auto;margin-bottom: auto;border: none;padding: 0 calc(${ks} * 2px + 1px);font-size: ${Es};line-height: ${Ds}}.control:hover,	.control:${fs},	.control:disabled,	.control:active{outline: none}.label{display: block;color: ${Ss};cursor: pointer;font-size: ${Es};line-height: ${Ds};margin-bottom: 2px}.label__hidden{display: none;visibility: hidden}.start,	.end{display: flex;margin: auto;fill: currentcolor}::slotted(svg),	::slotted(span){width: calc(${ks} * 4px);height: calc(${ks} * 4px)}.start{margin-inline-start: calc(${ks} * 2px)}.end{margin-inline-end: calc(${ks} * 2px)}:host(:hover:not([disabled])) .root{background: ${po};border-color: ${ho}}:host(:active:not([disabled])) .root{background: ${po};border-color: ${Ts}}:host(:focus-within:not([disabled])) .root{border-color: ${Ts}}:host([disabled]) .label,	:host([readonly]) .label,	:host([readonly]) .control,	:host([disabled]) .control{cursor: ${"not-allowed"}}:host([disabled]){opacity: ${Is}}:host([disabled]) .control{border-color: ${ho}}`,shadowOptions:{delegatesFocus:!0}}),hn={vsCodeBadge:$o,vsCodeButton:Ao,vsCodeCheckbox:Eo,vsCodeDataGrid:Po,vsCodeDataGridCell:Ho,vsCodeDataGridRow:Fo,vsCodeDivider:Mo,vsCodeDropdown:zo,vsCodeLink:_o,vsCodeOption:Uo,vsCodePanels:Wo,vsCodePanelTab:Qo,vsCodePanelView:Yo,vsCodeProgressRing:Zo,vsCodeRadioGroup:tn,vsCodeRadio:on,vsCodeTag:rn,vsCodeTextArea:ln,vsCodeTextField:cn,register(e,...t){if(e)for(const i in this)"register"!==i&&this[i]().register(e,...t)}};function un(e){return Vi.getOrCreate(e).withPrefix("vscode")}const pn=un().register(hn);export{wo as Badge,So as Button,Ro as Checkbox,Do as DataGrid,Lo as DataGridCell,Zt as DataGridCellTypes,Bo as DataGridRow,ei as DataGridRowTypes,Vo as Divider,zi as DividerRole,No as Dropdown,fi as DropdownPosition,Jt as GenerateHeaderOptions,jo as Link,qo as Option,Go as PanelTab,Xo as PanelView,Ko as Panels,Jo as ProgressRing,sn as Radio,en as RadioGroup,Et as RadioGroupOrientation,nn as Tag,an as TextArea,hs as TextAreaResize,dn as TextField,Ki as TextFieldType,pn as VSCodeDesignSystem,hn as allComponents,un as provideVSCodeDesignSystem,$o as vsCodeBadge,Ao as vsCodeButton,Eo as vsCodeCheckbox,Po as vsCodeDataGrid,Ho as vsCodeDataGridCell,Fo as vsCodeDataGridRow,Mo as vsCodeDivider,zo as vsCodeDropdown,_o as vsCodeLink,Uo as vsCodeOption,Qo as vsCodePanelTab,Yo as vsCodePanelView,Wo as vsCodePanels,Zo as vsCodeProgressRing,on as vsCodeRadio,tn as vsCodeRadioGroup,rn as vsCodeTag,ln as vsCodeTextArea,cn as vsCodeTextField};
