body {
    padding: 20px;
    color: var(--vscode-descriptionForeground);
    font-family: var(--vscode-font-family);
    background-color: var(--vscode-editor-background);
    margin: 0;
}

.section {
    margin-bottom: 32px;
}
.section.local {
    display: none;
}

.section + .section {
    border-top: 1px solid var(--vscode-panel-border);
    padding-top: 32px;
}

.section-title {
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 16px;
    color: var(--vscode-foreground);
}

.info-grid {
    display: grid;
    grid-template-columns: auto 1fr;
    gap: 16px 8px;
    align-items: center;
}

.info-label {
    width: 80px;
    color: var(--vscode-descriptionForeground);
}

.info-value {
    color: var(--vscode-descriptionForeground);
    line-height: 28px;
    height: 28px;
    padding: 0 12px;
    border-radius: 4px;
    border: 1px solid var(--vscode-dropdown-background);
    min-width: 0;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.info-value a {
    color: var(--vscode-textLink-foreground);
    text-decoration: none;
}

.info-value a:hover {
    text-decoration: underline;
}

.table-container {
    border-radius: 4px;
}

.table {
    width: 100%;
    border-collapse: collapse;
    background-color: var(--vscode-editor-background);
}

.table-header {
    background-color: var(--vscode-list-inactiveSelectionBackground);
}

.table-header th {
    padding: 12px 16px;
    text-align: left;
    font-weight: 600;
    color: var(--vscode-descriptionForeground);
}
.table-cell {
    padding: 4px 16px;
    vertical-align: middle;
    text-align: left;
    color: var(--vscode-descriptionForeground);
}
.table td {
    border-bottom: 1px solid var(--vscode-panel-border);
}

.table tbody tr:hover {
    color: var(--vscode-list-inactiveSelectionForeground);
    background-color: var(--vscode-list-inactiveSelectionBackground);
}

.action-btn {
    background: none;
    border: none;
    color: var(--vscode-descriptionForeground);
    cursor: pointer;
    padding: 4px;
    margin: 0 2px;
    border-radius: 2px;
}

.action-btn:hover {
    background-color: var(--vscode-toolbar-hoverBackground);
}

.action-btn[disabled] {
    opacity: 0.5;
    cursor: not-allowed;
}

.no-data {
    text-align: center;
    color: var(--vscode-descriptionForeground);
    font-style: italic;
    padding: 24px;
}

.create-btn {
    float: right;
    background-color: transparent;
    color: var(--vscode-button-background);
    border: none;
    padding: 8px 16px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
}

.create-btn:hover {
    color: var(--vscode-button-hoverBackground);
}

.create-btn[disabled] {
    opacity: 0.5;
    cursor: not-allowed;
}

.loading {
    display: none;
    text-align: center;
    padding: 12px;
    color: var(--vscode-descriptionForeground);
}
