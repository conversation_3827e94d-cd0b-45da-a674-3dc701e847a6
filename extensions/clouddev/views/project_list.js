/**
 * Project List Webview Script
 * - 支持搜索过滤
 * - 渲染卡片、标签、操作按钮
 * - 选中/hover 效果
 * - 通过 vscode.postMessage 对接数据和交互
 */

const vscode = acquireVsCodeApi();
let projects = [];
let projectToDelete = null;
// 存储项目轮询状态
const projectPolls = new Map();

// 存储从扩展传递过来的本地化消息
let localizedMessages = {};

// HTML转义函数，防止XSS攻击
function escapeHtml(unsafe) {
    if (typeof unsafe !== 'string') {
        return String(unsafe || '');
    }
    return unsafe
        .replace(/&/g, "&amp;")
        .replace(/</g, "&lt;")
        .replace(/>/g, "&gt;")
        .replace(/"/g, "&quot;")
        .replace(/'/g, "&#039;");
}

// 验证消息来源和结构
function validateMessage(message) {
    if (!message || typeof message !== 'object') {
        return false;
    }

    // 验证命令字段
    if (typeof message.command !== 'string') {
        return false;
    }

    return true;
}

// 安全地清理和验证颜色值，防止CSS注入
function sanitizeColorValue(color) {
    if (!color || typeof color !== 'string') {
        return null;
    }

    // 移除多余的空白字符
    const trimmedColor = color.trim();

    // 严格验证十六进制颜色格式
    if (/^#[0-9A-Fa-f]{3}$|^#[0-9A-Fa-f]{6}$/.test(trimmedColor)) {
        return trimmedColor;
    }

    // 严格验证RGB格式
    const rgbMatch = trimmedColor.match(/^rgb\(\s*(\d{1,3})\s*,\s*(\d{1,3})\s*,\s*(\d{1,3})\s*\)$/);
    if (rgbMatch) {
        const [, r, g, b] = rgbMatch;
        // 验证RGB值范围
        if (parseInt(r) <= 255 && parseInt(g) <= 255 && parseInt(b) <= 255) {
            return `rgb(${r}, ${g}, ${b})`;
        }
    }

    // 严格验证RGBA格式
    const rgbaMatch = trimmedColor.match(/^rgba\(\s*(\d{1,3})\s*,\s*(\d{1,3})\s*,\s*(\d{1,3})\s*,\s*(0|1|0?\.\d+)\s*\)$/);
    if (rgbaMatch) {
        const [, r, g, b, a] = rgbaMatch;
        // 验证RGB值范围和alpha值范围
        if (parseInt(r) <= 255 && parseInt(g) <= 255 && parseInt(b) <= 255 && parseFloat(a) <= 1) {
            return `rgba(${r}, ${g}, ${b}, ${a})`;
        }
    }

    // 如果都不匹配，返回null（不设置颜色）
    console.warn('[SECURITY] Invalid color value rejected:', color);
    return null;
}

// 安全的本地化函数，使用从扩展传递过来的消息
function getLocalizedText(key) {
    if (typeof key !== 'string') {
        return '';
    }
    const value = localizedMessages[key];
    return typeof value === 'string' ? escapeHtml(value) : escapeHtml(key);
}

const typeIconMap = {
    Remote: "icon-daima",
    Local: "icon-bendi",
};

// 辅助函数：devMode 转友好文本
function devModeText(devMode) {
    if (devMode === 1) { return getLocalizedText('remote'); }
    if (devMode === 2) { return getLocalizedText('local'); }
    return devMode;
}

// 初始化时请求项目列表
window.addEventListener('DOMContentLoaded', () => {
    vscode.postMessage({ command: 'getProjects' });
});

// 停止项目状态轮询
function stopPolling(projectId) {
    const intervalId = projectPolls.get(projectId);
    if (intervalId) {
        clearInterval(intervalId);
        projectPolls.delete(projectId);
    }
}

// 开始项目状态轮询
function startPolling(project) {
    // 先清除可能存在的旧轮询
    stopPolling(project.id);
    if (!project.status) {
        return;
    }
    if (project.devMode === 1 && !['Running', 'Shutdown'].includes(project.status)) {
        // 开始新的轮询
        const intervalId = setInterval(() => {
            vscode.postMessage({
                command: 'pollProjectStatus',
                data: project
            });
        }, 3000); // 每3秒轮询一次

        projectPolls.set(project.id, intervalId);
    }
}

// 监听来自扩展的消息
window.addEventListener('message', event => {
    // 验证事件来源和数据结构
    if (!event || !event.data) {
        return;
    }

    const message = event.data;

    // 验证消息结构
    if (!validateMessage(message)) {
        console.warn('Invalid message received:', message);
        return;
    }

    switch (message.command) {
        case 'setProjects':
            // 验证项目数据
            if (Array.isArray(message.projects)) {
                projects = message.projects;
            }

            // 存储从扩展传递过来的本地化消息
            if (message.messages && typeof message.messages === 'object') {
                // 清理并验证本地化消息
                localizedMessages = {};
                for (const [key, value] of Object.entries(message.messages)) {
                    if (typeof key === 'string' && typeof value === 'string') {
                        localizedMessages[key] = value;
                    }
                }

                // 更新搜索框的placeholder
                const searchInput = document.getElementById('searchInput');
                if (searchInput) {
                    searchInput.placeholder = getLocalizedText('search');
                }
            }

            const highlightProjectName = typeof message.highlightProjectName === 'string'
                ? message.highlightProjectName
                : null;

            renderProjects(
                filterProjects(document.getElementById('searchInput').value),
                highlightProjectName
            );
            break;

        case 'updateProject':
            // 更新单个项目状态
            if (message.project && typeof message.project === 'object') {
                const updatedProject = message.project;
                const index = projects.findIndex(p => p.id === updatedProject.id);
                if (index !== -1) {
                    // 如果项目状态变为Running，停止轮询
                    if (['Running', 'Shutdown'].includes(updatedProject.status)) {
                        stopPolling(updatedProject.id);
                    }
                    projects[index] = updatedProject;
                    renderProjects(filterProjects(document.getElementById('searchInput').value));
                }
            }
            break;
    }
});

function getStatusColor({ status, devMode }) {
    // 开发模式, 1: Remote 2: Local
    if (devMode === 2) {
        return 'rgba(255, 255, 255, 0.3)';
    }
    if (status === 'Running') {
        return '#2EA043'; // 绿色
    } else if (status === 'Shutdown') {
        return 'rgba(255, 255, 255, 0.3)'; // 灰色
    }
    return '#FB9B15'; // 默认橙色
}

function getStatusClass({ status, devMode }) {
    // 开发模式, 1: Remote 2: Local
    if (devMode === 2) {
        return '';
    }
    // 为需要呼吸动画的状态添加CSS类
    if (status === 'Shutting' || status === 'Pending') {
        return 'breathing-animation';
    }
    return '';
}

// 安全创建DOM元素的辅助函数
function createElement(tag, className, textContent) {
    const element = document.createElement(tag);
    if (className) {
        element.className = className;
    }
    if (textContent !== undefined) {
        element.textContent = textContent;
    }
    return element;
}

// 安全设置元素属性
function setElementAttribute(element, name, value) {
    if (typeof value === 'string' || typeof value === 'number') {
        element.setAttribute(name, String(value));
    }
}

function renderProjects(list, highlightProjectName) {
    const container = document.getElementById("projectList");
    // 清空容器
    while (container.firstChild) {
        container.removeChild(container.firstChild);
    }

    if (!Array.isArray(list)) {
        return;
    }

    list.forEach((p) => {
        // 验证项目对象
        if (!p || typeof p !== 'object') {
            return;
        }

        startPolling(p);
        const selected = highlightProjectName && p.name === highlightProjectName;
        const card = createElement("div", "project-card");

        if (selected) {
            card.classList.add("selected");
        }

        // 安全设置数据属性
        if (p.id) {
            setElementAttribute(card, 'data-id', p.id);
        }

        // 创建项目头像
        const avatar = createElement("div", "project-avatar", escapeHtml(p.abbrName || ''));
        if (p.bgColor && typeof p.bgColor === 'string') {
            // 严格验证颜色值格式，防止CSS注入
            const sanitizedColor = sanitizeColorValue(p.bgColor);
            if (sanitizedColor) {
                avatar.style.backgroundColor = sanitizedColor;
            }
        }
        card.appendChild(avatar);

        // 创建标题行
        const titleRow = createElement("div", "project-title-row");

        // 添加状态点（仅对远程项目）
        if (p.devMode !== 2) {
            const statusDot = createElement("span", `project-status-dot ${getStatusClass(p)}`, "●");
            statusDot.style.color = getStatusColor(p);
            titleRow.appendChild(statusDot);
        }

        const title = createElement("span", "project-title", escapeHtml(p.name || ''));
        titleRow.appendChild(title);
        card.appendChild(titleRow);

        // 创建描述
        const desc = createElement("div", "project-desc", escapeHtml(p.description || '-'));
        setElementAttribute(desc, 'title', p.description || '');
        card.appendChild(desc);

        // 创建开发模式标签
        const devModeTag = createElement("div", "tag");
        const devModeTextValue = devModeText(p.devMode);
        const devModeIcon = createElement("i", `iconfont ${typeIconMap[devModeTextValue] || 'icon-yuanchengziyuanguanliqi'}`);
        const devModeSpan = createElement("span", "", escapeHtml(devModeTextValue));
        devModeTag.appendChild(devModeIcon);
        devModeTag.appendChild(devModeSpan);
        card.appendChild(devModeTag);

        // 创建项目模板标签
        const templateTag = createElement("div", "tag");
        if (p.projectTemplateIconUrl && typeof p.projectTemplateIconUrl === 'string') {
            const templateIcon = createElement("img", "project-template-icon");
            setElementAttribute(templateIcon, 'src', p.projectTemplateIconUrl);
            setElementAttribute(templateIcon, 'alt', p.projectTemplateName || '');
            templateTag.appendChild(templateIcon);
        } else {
            const templateIcon = createElement("i", "iconfont icon-jisuanjiyuyan");
            templateTag.appendChild(templateIcon);
        }
        const templateName = createElement("span", "", escapeHtml(p.projectTemplateName || '-'));
        templateTag.appendChild(templateName);
        card.appendChild(templateTag);

        // 创建操作按钮区域
        const actions = createElement("div", "project-actions");

        // 添加打开和外部链接按钮（条件性）
        if (!selected && !(p.devMode === 1 && p.status !== 'Running')) {
            const openBtn = createElement("button", "project-action-btn iconfont icon-dangqianyedakai");
            setElementAttribute(openBtn, 'title', getLocalizedText('open'));
            setElementAttribute(openBtn, 'data-action', 'open');
            actions.appendChild(openBtn);

            const externalBtn = createElement("button", "project-action-btn iconfont icon-tiaozhuan");
            setElementAttribute(externalBtn, 'title', getLocalizedText('external'));
            setElementAttribute(externalBtn, 'data-action', 'external');
            actions.appendChild(externalBtn);
        }

        // 添加启动/停止按钮（仅对远程项目）
        if (p.devMode !== 2) {
            const isRunning = p.status === 'Running';
            const isPending = ['Pending', 'Shutting'].includes(p.status);
            const startStopBtn = createElement("button",
                `project-action-btn iconfont ${['Pending', 'Running'].includes(p.status) ? 'icon-zanting' : 'icon-kaishi'}${isPending ? ' disabled' : ''}`
            );
            setElementAttribute(startStopBtn, 'title', isRunning ? getLocalizedText('stop') : getLocalizedText('start'));
            setElementAttribute(startStopBtn, 'data-action', isRunning ? 'stop' : 'start');
            actions.appendChild(startStopBtn);
        }

        // 添加设置按钮
        const settingsBtn = createElement("button", "project-action-btn iconfont icon-shezhi");
        setElementAttribute(settingsBtn, 'title', getLocalizedText('settings'));
        setElementAttribute(settingsBtn, 'data-action', 'settings');
        actions.appendChild(settingsBtn);

        // 添加删除按钮
        const deleteBtn = createElement("button", "project-action-btn iconfont icon-shanchu");
        setElementAttribute(deleteBtn, 'title', getLocalizedText('delete'));
        setElementAttribute(deleteBtn, 'data-action', 'delete');
        actions.appendChild(deleteBtn);

        card.appendChild(actions);

        // 添加卡片点击事件
        card.addEventListener("click", (e) => {
            const actionButton = e.target.closest(".project-action-btn");
            if (actionButton) {
                const action = actionButton.dataset.action;
                handleProjectAction(action, p);
            } else {
                // 高亮卡片并触发选择命令
                document.querySelectorAll(".project-card.selected").forEach(el => el.classList.remove("selected"));
                card.classList.add("selected");
                vscode.postMessage({ command: 'selectProject', data: p });
            }
        });

        container.appendChild(card);
    });
}

function filterProjects(searchValue) {
    // 验证输入参数
    if (typeof searchValue !== 'string') {
        searchValue = '';
    }

    const val = searchValue.trim().toLowerCase();

    if (!Array.isArray(projects)) {
        return [];
    }

    return projects.filter(p => {
        if (!p || typeof p !== 'object') {
            return false;
        }

        const name = typeof p.name === 'string' ? p.name.toLowerCase() : '';
        const desc = typeof p.desc === 'string' ? p.desc.toLowerCase() : '';
        const lang = typeof p.lang === 'string' ? p.lang.toLowerCase() : '';

        return name.includes(val) || desc.includes(val) || lang.includes(val);
    });
}

function handleProjectAction(action, project) {
    switch (action) {
        case 'open':
            vscode.postMessage({ command: 'openProject', data: project });
            break;
        case 'settings':
            vscode.postMessage({ command: 'settings', data: project });
            break;
        case 'stop':
            vscode.postMessage({ command: 'stop', data: project });
            break;
        case 'start':
            vscode.postMessage({ command: 'start', data: project });
            break;
        case 'delete':
            vscode.postMessage({ command: 'delete', data: project });
            stopPolling(project.id);
            break;
        case 'external':
            vscode.postMessage({ command: 'externalProject', data: project });
            break;
    }
}

// 搜索输入事件处理
document.getElementById('searchInput').addEventListener('input', (e) => {
    // 验证事件和目标元素
    if (!e || !e.target) {
        return;
    }

    const searchValue = typeof e.target.value === 'string' ? e.target.value : '';
    renderProjects(filterProjects(searchValue));
});
