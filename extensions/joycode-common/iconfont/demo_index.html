<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8"/>
  <title>iconfont Demo</title>
  <link rel="shortcut icon" href="//img.alicdn.com/imgextra/i4/O1CN01Z5paLz1O0zuCC7osS_!!6000000001644-55-tps-83-82.svg" type="image/x-icon"/>
  <link rel="icon" type="image/svg+xml" href="//img.alicdn.com/imgextra/i4/O1CN01Z5paLz1O0zuCC7osS_!!6000000001644-55-tps-83-82.svg"/>
  <link rel="stylesheet" href="https://g.alicdn.com/thx/cube/1.3.2/cube.min.css">
  <link rel="stylesheet" href="demo.css">
  <link rel="stylesheet" href="iconfont.css">
  <script src="iconfont.js"></script>
  <!-- jQuery -->
  <script src="https://a1.alicdn.com/oss/uploads/2018/12/26/7bfddb60-08e8-11e9-9b04-53e73bb6408b.js"></script>
  <!-- 代码高亮 -->
  <script src="https://a1.alicdn.com/oss/uploads/2018/12/26/a3f714d0-08e6-11e9-8a15-ebf944d7534c.js"></script>
  <style>
    .main .logo {
      margin-top: 0;
      height: auto;
    }

    .main .logo a {
      display: flex;
      align-items: center;
    }

    .main .logo .sub-title {
      margin-left: 0.5em;
      font-size: 22px;
      color: #fff;
      background: linear-gradient(-45deg, #3967FF, #B500FE);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
  </style>
</head>
<body>
  <div class="main">
    <h1 class="logo"><a href="https://www.iconfont.cn/" title="iconfont 首页" target="_blank">
      <img width="200" src="https://img.alicdn.com/imgextra/i3/O1CN01Mn65HV1FfSEzR6DKv_!!6000000000514-55-tps-228-59.svg">
      
    </a></h1>
    <div class="nav-tabs">
      <ul id="tabs" class="dib-box">
        <li class="dib active"><span>Unicode</span></li>
        <li class="dib"><span>Font class</span></li>
        <li class="dib"><span>Symbol</span></li>
      </ul>
      
      <a href="https://www.iconfont.cn/manage/index?manage_type=myprojects&projectId=4926664" target="_blank" class="nav-more">查看项目</a>
      
    </div>
    <div class="tab-container">
      <div class="content unicode" style="display: block;">
          <ul class="icon_lists dib-box">
          
            <li class="dib">
              <span class="icon iconfont">&#xe76a;</span>
                <div class="name">云控制台</div>
                <div class="code-name">&amp;#xe76a;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe769;</span>
                <div class="name">云控制台</div>
                <div class="code-name">&amp;#xe769;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe69e;</span>
                <div class="name">更多内容</div>
                <div class="code-name">&amp;#xe69e;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe768;</span>
                <div class="name">部署</div>
                <div class="code-name">&amp;#xe768;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe69d;</span>
                <div class="name">问题修复</div>
                <div class="code-name">&amp;#xe69d;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe69c;</span>
                <div class="name">Windows</div>
                <div class="code-name">&amp;#xe69c;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe69b;</span>
                <div class="name">苹果</div>
                <div class="code-name">&amp;#xe69b;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe69a;</span>
                <div class="name">UI</div>
                <div class="code-name">&amp;#xe69a;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe699;</span>
                <div class="name">闪电</div>
                <div class="code-name">&amp;#xe699;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe698;</span>
                <div class="name">测试</div>
                <div class="code-name">&amp;#xe698;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe697;</span>
                <div class="name">特性</div>
                <div class="code-name">&amp;#xe697;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe68d;</span>
                <div class="name">自定义智能体头像</div>
                <div class="code-name">&amp;#xe68d;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe693;</span>
                <div class="name">调度者</div>
                <div class="code-name">&amp;#xe693;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe694;</span>
                <div class="name">编码</div>
                <div class="code-name">&amp;#xe694;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe695;</span>
                <div class="name">规划</div>
                <div class="code-name">&amp;#xe695;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe696;</span>
                <div class="name">问答</div>
                <div class="code-name">&amp;#xe696;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe68c;</span>
                <div class="name">升级</div>
                <div class="code-name">&amp;#xe68c;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe68e;</span>
                <div class="name">MCP</div>
                <div class="code-name">&amp;#xe68e;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe68f;</span>
                <div class="name">登出</div>
                <div class="code-name">&amp;#xe68f;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe690;</span>
                <div class="name">键盘</div>
                <div class="code-name">&amp;#xe690;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe691;</span>
                <div class="name">标签</div>
                <div class="code-name">&amp;#xe691;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe692;</span>
                <div class="name">#</div>
                <div class="code-name">&amp;#xe692;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe67d;</span>
                <div class="name">主题</div>
                <div class="code-name">&amp;#xe67d;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe67c;</span>
                <div class="name">规则</div>
                <div class="code-name">&amp;#xe67c;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe67e;</span>
                <div class="name">JS</div>
                <div class="code-name">&amp;#xe67e;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe67f;</span>
                <div class="name">JoyCode</div>
                <div class="code-name">&amp;#xe67f;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe680;</span>
                <div class="name">收起 (上下)</div>
                <div class="code-name">&amp;#xe680;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe681;</span>
                <div class="name">通用设置</div>
                <div class="code-name">&amp;#xe681;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe682;</span>
                <div class="name">日志</div>
                <div class="code-name">&amp;#xe682;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe683;</span>
                <div class="name">联网搜索</div>
                <div class="code-name">&amp;#xe683;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe684;</span>
                <div class="name">语言</div>
                <div class="code-name">&amp;#xe684;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe685;</span>
                <div class="name">上下文</div>
                <div class="code-name">&amp;#xe685;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe686;</span>
                <div class="name">说明</div>
                <div class="code-name">&amp;#xe686;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe687;</span>
                <div class="name">java</div>
                <div class="code-name">&amp;#xe687;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe688;</span>
                <div class="name">智能体</div>
                <div class="code-name">&amp;#xe688;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe689;</span>
                <div class="name">深度思考</div>
                <div class="code-name">&amp;#xe689;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe68a;</span>
                <div class="name">html</div>
                <div class="code-name">&amp;#xe68a;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe68b;</span>
                <div class="name">展开 (上下)</div>
                <div class="code-name">&amp;#xe68b;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe67a;</span>
                <div class="name">举例</div>
                <div class="code-name">&amp;#xe67a;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe67b;</span>
                <div class="name">本地部署</div>
                <div class="code-name">&amp;#xe67b;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe767;</span>
                <div class="name">搜索</div>
                <div class="code-name">&amp;#xe767;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe766;</span>
                <div class="name">本地</div>
                <div class="code-name">&amp;#xe766;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe765;</span>
                <div class="name">管理项目</div>
                <div class="code-name">&amp;#xe765;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe760;</span>
                <div class="name">底栏(展开)</div>
                <div class="code-name">&amp;#xe760;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe761;</span>
                <div class="name">上箭头</div>
                <div class="code-name">&amp;#xe761;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe762;</span>
                <div class="name">图片</div>
                <div class="code-name">&amp;#xe762;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe763;</span>
                <div class="name">查询</div>
                <div class="code-name">&amp;#xe763;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe764;</span>
                <div class="name">设置</div>
                <div class="code-name">&amp;#xe764;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe74f;</span>
                <div class="name">下箭头</div>
                <div class="code-name">&amp;#xe74f;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe750;</span>
                <div class="name">时间</div>
                <div class="code-name">&amp;#xe750;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe751;</span>
                <div class="name">停止填充</div>
                <div class="code-name">&amp;#xe751;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe752;</span>
                <div class="name">刷新</div>
                <div class="code-name">&amp;#xe752;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe753;</span>
                <div class="name">右侧栏(展开)</div>
                <div class="code-name">&amp;#xe753;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe754;</span>
                <div class="name">代码</div>
                <div class="code-name">&amp;#xe754;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe755;</span>
                <div class="name">点赞</div>
                <div class="code-name">&amp;#xe755;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe756;</span>
                <div class="name">远程资源管理器</div>
                <div class="code-name">&amp;#xe756;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe757;</span>
                <div class="name">语音填充</div>
                <div class="code-name">&amp;#xe757;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe758;</span>
                <div class="name">发送</div>
                <div class="code-name">&amp;#xe758;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe759;</span>
                <div class="name">复制</div>
                <div class="code-name">&amp;#xe759;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe75a;</span>
                <div class="name">新增会话</div>
                <div class="code-name">&amp;#xe75a;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe75b;</span>
                <div class="name">关闭</div>
                <div class="code-name">&amp;#xe75b;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe75c;</span>
                <div class="name">头像</div>
                <div class="code-name">&amp;#xe75c;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe75d;</span>
                <div class="name">左侧栏(展开)</div>
                <div class="code-name">&amp;#xe75d;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe75e;</span>
                <div class="name">分享</div>
                <div class="code-name">&amp;#xe75e;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe75f;</span>
                <div class="name">跳转</div>
                <div class="code-name">&amp;#xe75f;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe73c;</span>
                <div class="name">当前页打开</div>
                <div class="code-name">&amp;#xe73c;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe73d;</span>
                <div class="name">解锁</div>
                <div class="code-name">&amp;#xe73d;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe73e;</span>
                <div class="name">开始</div>
                <div class="code-name">&amp;#xe73e;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe73f;</span>
                <div class="name">会话</div>
                <div class="code-name">&amp;#xe73f;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe740;</span>
                <div class="name">资源插件</div>
                <div class="code-name">&amp;#xe740;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe741;</span>
                <div class="name">底栏</div>
                <div class="code-name">&amp;#xe741;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe742;</span>
                <div class="name">暂停</div>
                <div class="code-name">&amp;#xe742;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe743;</span>
                <div class="name">下载</div>
                <div class="code-name">&amp;#xe743;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe744;</span>
                <div class="name">上传</div>
                <div class="code-name">&amp;#xe744;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe745;</span>
                <div class="name">链接</div>
                <div class="code-name">&amp;#xe745;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe746;</span>
                <div class="name">文档-下折角</div>
                <div class="code-name">&amp;#xe746;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe747;</span>
                <div class="name">新增（方形）</div>
                <div class="code-name">&amp;#xe747;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe748;</span>
                <div class="name">插件市场</div>
                <div class="code-name">&amp;#xe748;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe749;</span>
                <div class="name">代码管理器</div>
                <div class="code-name">&amp;#xe749;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe74a;</span>
                <div class="name">编辑</div>
                <div class="code-name">&amp;#xe74a;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe74b;</span>
                <div class="name">返回上一步</div>
                <div class="code-name">&amp;#xe74b;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe74c;</span>
                <div class="name">文件</div>
                <div class="code-name">&amp;#xe74c;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe74d;</span>
                <div class="name">失败填充</div>
                <div class="code-name">&amp;#xe74d;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe74e;</span>
                <div class="name">完成</div>
                <div class="code-name">&amp;#xe74e;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe72e;</span>
                <div class="name">附件</div>
                <div class="code-name">&amp;#xe72e;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe72f;</span>
                <div class="name">收藏</div>
                <div class="code-name">&amp;#xe72f;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe730;</span>
                <div class="name">上箭头填充</div>
                <div class="code-name">&amp;#xe730;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe731;</span>
                <div class="name">文档</div>
                <div class="code-name">&amp;#xe731;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe732;</span>
                <div class="name">新增</div>
                <div class="code-name">&amp;#xe732;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe733;</span>
                <div class="name">停止</div>
                <div class="code-name">&amp;#xe733;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe734;</span>
                <div class="name">问号</div>
                <div class="code-name">&amp;#xe734;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe735;</span>
                <div class="name">bug finder</div>
                <div class="code-name">&amp;#xe735;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe736;</span>
                <div class="name">发送填充</div>
                <div class="code-name">&amp;#xe736;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe737;</span>
                <div class="name">左侧栏</div>
                <div class="code-name">&amp;#xe737;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe738;</span>
                <div class="name">完成填充</div>
                <div class="code-name">&amp;#xe738;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe739;</span>
                <div class="name">失败</div>
                <div class="code-name">&amp;#xe739;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe73a;</span>
                <div class="name">叹号填充</div>
                <div class="code-name">&amp;#xe73a;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe73b;</span>
                <div class="name">日志</div>
                <div class="code-name">&amp;#xe73b;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe71c;</span>
                <div class="name">锁定</div>
                <div class="code-name">&amp;#xe71c;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe71d;</span>
                <div class="name">减</div>
                <div class="code-name">&amp;#xe71d;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe71e;</span>
                <div class="name">闭眼</div>
                <div class="code-name">&amp;#xe71e;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe71f;</span>
                <div class="name">更换</div>
                <div class="code-name">&amp;#xe71f;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe720;</span>
                <div class="name">新建箭头</div>
                <div class="code-name">&amp;#xe720;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe721;</span>
                <div class="name">编辑（Ai）</div>
                <div class="code-name">&amp;#xe721;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe722;</span>
                <div class="name">对勾</div>
                <div class="code-name">&amp;#xe722;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe723;</span>
                <div class="name">禁止</div>
                <div class="code-name">&amp;#xe723;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe724;</span>
                <div class="name">历史记录</div>
                <div class="code-name">&amp;#xe724;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe725;</span>
                <div class="name">下箭头填充</div>
                <div class="code-name">&amp;#xe725;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe726;</span>
                <div class="name">叹号</div>
                <div class="code-name">&amp;#xe726;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe727;</span>
                <div class="name">不赞成</div>
                <div class="code-name">&amp;#xe727;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe728;</span>
                <div class="name">问号填充</div>
                <div class="code-name">&amp;#xe728;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe729;</span>
                <div class="name">回车</div>
                <div class="code-name">&amp;#xe729;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe72a;</span>
                <div class="name">右箭头填充</div>
                <div class="code-name">&amp;#xe72a;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe72b;</span>
                <div class="name">新建文件夹</div>
                <div class="code-name">&amp;#xe72b;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe72c;</span>
                <div class="name">新增（圆环）</div>
                <div class="code-name">&amp;#xe72c;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe72d;</span>
                <div class="name">登录</div>
                <div class="code-name">&amp;#xe72d;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe70d;</span>
                <div class="name">收藏填充</div>
                <div class="code-name">&amp;#xe70d;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe70e;</span>
                <div class="name">排序</div>
                <div class="code-name">&amp;#xe70e;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe70f;</span>
                <div class="name">右箭头</div>
                <div class="code-name">&amp;#xe70f;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe710;</span>
                <div class="name">头像圆圈</div>
                <div class="code-name">&amp;#xe710;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe711;</span>
                <div class="name">睁眼</div>
                <div class="code-name">&amp;#xe711;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe712;</span>
                <div class="name">文件夹</div>
                <div class="code-name">&amp;#xe712;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe713;</span>
                <div class="name">计算机语言</div>
                <div class="code-name">&amp;#xe713;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe714;</span>
                <div class="name">文件夹打开</div>
                <div class="code-name">&amp;#xe714;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe715;</span>
                <div class="name">查看示例</div>
                <div class="code-name">&amp;#xe715;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe716;</span>
                <div class="name">右侧栏</div>
                <div class="code-name">&amp;#xe716;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe717;</span>
                <div class="name">删除</div>
                <div class="code-name">&amp;#xe717;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe718;</span>
                <div class="name">授权登录</div>
                <div class="code-name">&amp;#xe718;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe719;</span>
                <div class="name">语音</div>
                <div class="code-name">&amp;#xe719;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe71a;</span>
                <div class="name">左箭头</div>
                <div class="code-name">&amp;#xe71a;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe71b;</span>
                <div class="name">更多</div>
                <div class="code-name">&amp;#xe71b;</div>
              </li>
          
          </ul>
          <div class="article markdown">
          <h2 id="unicode-">Unicode 引用</h2>
          <hr>

          <p>Unicode 是字体在网页端最原始的应用方式，特点是：</p>
          <ul>
            <li>支持按字体的方式去动态调整图标大小，颜色等等。</li>
            <li>默认情况下不支持多色，直接添加多色图标会自动去色。</li>
          </ul>
          <blockquote>
            <p>注意：新版 iconfont 支持两种方式引用多色图标：SVG symbol 引用方式和彩色字体图标模式。（使用彩色字体图标需要在「编辑项目」中开启「彩色」选项后并重新生成。）</p>
          </blockquote>
          <p>Unicode 使用步骤如下：</p>
          <h3 id="-font-face">第一步：拷贝项目下面生成的 <code>@font-face</code></h3>
<pre><code class="language-css"
>@font-face {
  font-family: 'iconfont';
  src: url('iconfont.woff2?t=1752672610201') format('woff2'),
       url('iconfont.woff?t=1752672610201') format('woff'),
       url('iconfont.ttf?t=1752672610201') format('truetype'),
       url('iconfont.svg?t=1752672610201#iconfont') format('svg');
}
</code></pre>
          <h3 id="-iconfont-">第二步：定义使用 iconfont 的样式</h3>
<pre><code class="language-css"
>.iconfont {
  font-family: "iconfont" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
</code></pre>
          <h3 id="-">第三步：挑选相应图标并获取字体编码，应用于页面</h3>
<pre>
<code class="language-html"
>&lt;span class="iconfont"&gt;&amp;#x33;&lt;/span&gt;
</code></pre>
          <blockquote>
            <p>"iconfont" 是你项目下的 font-family。可以通过编辑项目查看，默认是 "iconfont"。</p>
          </blockquote>
          </div>
      </div>
      <div class="content font-class">
        <ul class="icon_lists dib-box">
          
          <li class="dib">
            <span class="icon iconfont icon-xingzhuangjiehe"></span>
            <div class="name">
              云控制台
            </div>
            <div class="code-name">.icon-xingzhuangjiehe
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-yunkongzhitai"></span>
            <div class="name">
              云控制台
            </div>
            <div class="code-name">.icon-yunkongzhitai
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-gengduoneirong"></span>
            <div class="name">
              更多内容
            </div>
            <div class="code-name">.icon-gengduoneirong
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-bushu"></span>
            <div class="name">
              部署
            </div>
            <div class="code-name">.icon-bushu
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-wentixiufu"></span>
            <div class="name">
              问题修复
            </div>
            <div class="code-name">.icon-wentixiufu
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-windows"></span>
            <div class="name">
              Windows
            </div>
            <div class="code-name">.icon-windows
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-pingguo"></span>
            <div class="name">
              苹果
            </div>
            <div class="code-name">.icon-pingguo
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-ui"></span>
            <div class="name">
              UI
            </div>
            <div class="code-name">.icon-ui
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-shandian"></span>
            <div class="name">
              闪电
            </div>
            <div class="code-name">.icon-shandian
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-ceshi"></span>
            <div class="name">
              测试
            </div>
            <div class="code-name">.icon-ceshi
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-texing"></span>
            <div class="name">
              特性
            </div>
            <div class="code-name">.icon-texing
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-zidingyizhinengtitouxiang"></span>
            <div class="name">
              自定义智能体头像
            </div>
            <div class="code-name">.icon-zidingyizhinengtitouxiang
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-tiaoduzhe"></span>
            <div class="name">
              调度者
            </div>
            <div class="code-name">.icon-tiaoduzhe
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-bianma"></span>
            <div class="name">
              编码
            </div>
            <div class="code-name">.icon-bianma
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-guihua"></span>
            <div class="name">
              规划
            </div>
            <div class="code-name">.icon-guihua
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-wenda"></span>
            <div class="name">
              问答
            </div>
            <div class="code-name">.icon-wenda
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-shengji"></span>
            <div class="name">
              升级
            </div>
            <div class="code-name">.icon-shengji
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-mcp"></span>
            <div class="name">
              MCP
            </div>
            <div class="code-name">.icon-mcp
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-dengchu"></span>
            <div class="name">
              登出
            </div>
            <div class="code-name">.icon-dengchu
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-jianpan"></span>
            <div class="name">
              键盘
            </div>
            <div class="code-name">.icon-jianpan
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-biaoqian"></span>
            <div class="name">
              标签
            </div>
            <div class="code-name">.icon-biaoqian
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-a-"></span>
            <div class="name">
              #
            </div>
            <div class="code-name">.icon-a-
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-zhuti"></span>
            <div class="name">
              主题
            </div>
            <div class="code-name">.icon-zhuti
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-guize"></span>
            <div class="name">
              规则
            </div>
            <div class="code-name">.icon-guize
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-js"></span>
            <div class="name">
              JS
            </div>
            <div class="code-name">.icon-js
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-JoyCode"></span>
            <div class="name">
              JoyCode
            </div>
            <div class="code-name">.icon-JoyCode
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-a-shouqishangxia"></span>
            <div class="name">
              收起 (上下)
            </div>
            <div class="code-name">.icon-a-shouqishangxia
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-tongyongshezhi"></span>
            <div class="name">
              通用设置
            </div>
            <div class="code-name">.icon-tongyongshezhi
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-rizhi1"></span>
            <div class="name">
              日志
            </div>
            <div class="code-name">.icon-rizhi1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-lianwangsousuo"></span>
            <div class="name">
              联网搜索
            </div>
            <div class="code-name">.icon-lianwangsousuo
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-yuyan"></span>
            <div class="name">
              语言
            </div>
            <div class="code-name">.icon-yuyan
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-shangxiawen"></span>
            <div class="name">
              上下文
            </div>
            <div class="code-name">.icon-shangxiawen
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-shuoming"></span>
            <div class="name">
              说明
            </div>
            <div class="code-name">.icon-shuoming
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-java"></span>
            <div class="name">
              java
            </div>
            <div class="code-name">.icon-java
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-zhinengti"></span>
            <div class="name">
              智能体
            </div>
            <div class="code-name">.icon-zhinengti
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-shendusikao"></span>
            <div class="name">
              深度思考
            </div>
            <div class="code-name">.icon-shendusikao
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-html"></span>
            <div class="name">
              html
            </div>
            <div class="code-name">.icon-html
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-a-zhankaishangxia"></span>
            <div class="name">
              展开 (上下)
            </div>
            <div class="code-name">.icon-a-zhankaishangxia
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-juli"></span>
            <div class="name">
              举例
            </div>
            <div class="code-name">.icon-juli
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-bendibushu"></span>
            <div class="name">
              本地部署
            </div>
            <div class="code-name">.icon-bendibushu
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-sousuo"></span>
            <div class="name">
              搜索
            </div>
            <div class="code-name">.icon-sousuo
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-bendi"></span>
            <div class="name">
              本地
            </div>
            <div class="code-name">.icon-bendi
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-guanlixiangmu"></span>
            <div class="name">
              管理项目
            </div>
            <div class="code-name">.icon-guanlixiangmu
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-a-dilanzhankai"></span>
            <div class="name">
              底栏(展开)
            </div>
            <div class="code-name">.icon-a-dilanzhankai
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-shangjiantou"></span>
            <div class="name">
              上箭头
            </div>
            <div class="code-name">.icon-shangjiantou
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-tupian"></span>
            <div class="name">
              图片
            </div>
            <div class="code-name">.icon-tupian
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-chaxun"></span>
            <div class="name">
              查询
            </div>
            <div class="code-name">.icon-chaxun
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-shezhi"></span>
            <div class="name">
              设置
            </div>
            <div class="code-name">.icon-shezhi
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-xiajiantou"></span>
            <div class="name">
              下箭头
            </div>
            <div class="code-name">.icon-xiajiantou
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-shijian"></span>
            <div class="name">
              时间
            </div>
            <div class="code-name">.icon-shijian
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-tingzhitianchong"></span>
            <div class="name">
              停止填充
            </div>
            <div class="code-name">.icon-tingzhitianchong
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-shuaxin"></span>
            <div class="name">
              刷新
            </div>
            <div class="code-name">.icon-shuaxin
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-a-youcelanzhankai"></span>
            <div class="name">
              右侧栏(展开)
            </div>
            <div class="code-name">.icon-a-youcelanzhankai
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-daima"></span>
            <div class="name">
              代码
            </div>
            <div class="code-name">.icon-daima
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-dianzan"></span>
            <div class="name">
              点赞
            </div>
            <div class="code-name">.icon-dianzan
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-yuanchengziyuanguanliqi"></span>
            <div class="name">
              远程资源管理器
            </div>
            <div class="code-name">.icon-yuanchengziyuanguanliqi
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-yuyintianchong"></span>
            <div class="name">
              语音填充
            </div>
            <div class="code-name">.icon-yuyintianchong
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-fasong"></span>
            <div class="name">
              发送
            </div>
            <div class="code-name">.icon-fasong
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-fuzhi"></span>
            <div class="name">
              复制
            </div>
            <div class="code-name">.icon-fuzhi
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-xinzenghuihua"></span>
            <div class="name">
              新增会话
            </div>
            <div class="code-name">.icon-xinzenghuihua
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-guanbi"></span>
            <div class="name">
              关闭
            </div>
            <div class="code-name">.icon-guanbi
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-touxiang"></span>
            <div class="name">
              头像
            </div>
            <div class="code-name">.icon-touxiang
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-a-zuocelanzhankai"></span>
            <div class="name">
              左侧栏(展开)
            </div>
            <div class="code-name">.icon-a-zuocelanzhankai
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-fenxiang"></span>
            <div class="name">
              分享
            </div>
            <div class="code-name">.icon-fenxiang
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-tiaozhuan"></span>
            <div class="name">
              跳转
            </div>
            <div class="code-name">.icon-tiaozhuan
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-dangqianyedakai"></span>
            <div class="name">
              当前页打开
            </div>
            <div class="code-name">.icon-dangqianyedakai
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-jiesuo"></span>
            <div class="name">
              解锁
            </div>
            <div class="code-name">.icon-jiesuo
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-kaishi"></span>
            <div class="name">
              开始
            </div>
            <div class="code-name">.icon-kaishi
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-huihua"></span>
            <div class="name">
              会话
            </div>
            <div class="code-name">.icon-huihua
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-ziyuanchajian"></span>
            <div class="name">
              资源插件
            </div>
            <div class="code-name">.icon-ziyuanchajian
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-dilan"></span>
            <div class="name">
              底栏
            </div>
            <div class="code-name">.icon-dilan
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-zanting"></span>
            <div class="name">
              暂停
            </div>
            <div class="code-name">.icon-zanting
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-xiazai"></span>
            <div class="name">
              下载
            </div>
            <div class="code-name">.icon-xiazai
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-shangchuan"></span>
            <div class="name">
              上传
            </div>
            <div class="code-name">.icon-shangchuan
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-lianjie"></span>
            <div class="name">
              链接
            </div>
            <div class="code-name">.icon-lianjie
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-wendang-xiazhejiao"></span>
            <div class="name">
              文档-下折角
            </div>
            <div class="code-name">.icon-wendang-xiazhejiao
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-a-xinzengfangxing"></span>
            <div class="name">
              新增（方形）
            </div>
            <div class="code-name">.icon-a-xinzengfangxing
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-chajianshichang"></span>
            <div class="name">
              插件市场
            </div>
            <div class="code-name">.icon-chajianshichang
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-daimaguanliqi"></span>
            <div class="name">
              代码管理器
            </div>
            <div class="code-name">.icon-daimaguanliqi
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-bianji"></span>
            <div class="name">
              编辑
            </div>
            <div class="code-name">.icon-bianji
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-fanhuishangyibu"></span>
            <div class="name">
              返回上一步
            </div>
            <div class="code-name">.icon-fanhuishangyibu
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-wenjian"></span>
            <div class="name">
              文件
            </div>
            <div class="code-name">.icon-wenjian
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-shibaitianchong"></span>
            <div class="name">
              失败填充
            </div>
            <div class="code-name">.icon-shibaitianchong
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-wancheng"></span>
            <div class="name">
              完成
            </div>
            <div class="code-name">.icon-wancheng
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-fujian"></span>
            <div class="name">
              附件
            </div>
            <div class="code-name">.icon-fujian
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-shoucang"></span>
            <div class="name">
              收藏
            </div>
            <div class="code-name">.icon-shoucang
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-shangjiantoutianchong"></span>
            <div class="name">
              上箭头填充
            </div>
            <div class="code-name">.icon-shangjiantoutianchong
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-wendang"></span>
            <div class="name">
              文档
            </div>
            <div class="code-name">.icon-wendang
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-xinzeng"></span>
            <div class="name">
              新增
            </div>
            <div class="code-name">.icon-xinzeng
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-tingzhi"></span>
            <div class="name">
              停止
            </div>
            <div class="code-name">.icon-tingzhi
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-wenhao"></span>
            <div class="name">
              问号
            </div>
            <div class="code-name">.icon-wenhao
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-a-bugfinder"></span>
            <div class="name">
              bug finder
            </div>
            <div class="code-name">.icon-a-bugfinder
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-fasongtianchong"></span>
            <div class="name">
              发送填充
            </div>
            <div class="code-name">.icon-fasongtianchong
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-zuocelan"></span>
            <div class="name">
              左侧栏
            </div>
            <div class="code-name">.icon-zuocelan
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-wanchengtianchong"></span>
            <div class="name">
              完成填充
            </div>
            <div class="code-name">.icon-wanchengtianchong
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-shibai"></span>
            <div class="name">
              失败
            </div>
            <div class="code-name">.icon-shibai
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-tanhaotianchong"></span>
            <div class="name">
              叹号填充
            </div>
            <div class="code-name">.icon-tanhaotianchong
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-rizhi"></span>
            <div class="name">
              日志
            </div>
            <div class="code-name">.icon-rizhi
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-suoding"></span>
            <div class="name">
              锁定
            </div>
            <div class="code-name">.icon-suoding
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-jian"></span>
            <div class="name">
              减
            </div>
            <div class="code-name">.icon-jian
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-biyan"></span>
            <div class="name">
              闭眼
            </div>
            <div class="code-name">.icon-biyan
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-genghuan"></span>
            <div class="name">
              更换
            </div>
            <div class="code-name">.icon-genghuan
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-xinjianjiantou"></span>
            <div class="name">
              新建箭头
            </div>
            <div class="code-name">.icon-xinjianjiantou
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-a-bianjiAi"></span>
            <div class="name">
              编辑（Ai）
            </div>
            <div class="code-name">.icon-a-bianjiAi
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-duigou"></span>
            <div class="name">
              对勾
            </div>
            <div class="code-name">.icon-duigou
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-jinzhi"></span>
            <div class="name">
              禁止
            </div>
            <div class="code-name">.icon-jinzhi
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-lishijilu"></span>
            <div class="name">
              历史记录
            </div>
            <div class="code-name">.icon-lishijilu
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-xiajiantoutianchong"></span>
            <div class="name">
              下箭头填充
            </div>
            <div class="code-name">.icon-xiajiantoutianchong
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-tanhao"></span>
            <div class="name">
              叹号
            </div>
            <div class="code-name">.icon-tanhao
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-buzancheng"></span>
            <div class="name">
              不赞成
            </div>
            <div class="code-name">.icon-buzancheng
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-wenhaotianchong"></span>
            <div class="name">
              问号填充
            </div>
            <div class="code-name">.icon-wenhaotianchong
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-huiche"></span>
            <div class="name">
              回车
            </div>
            <div class="code-name">.icon-huiche
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-youjiantoutianchong"></span>
            <div class="name">
              右箭头填充
            </div>
            <div class="code-name">.icon-youjiantoutianchong
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-xinjianwenjianjia"></span>
            <div class="name">
              新建文件夹
            </div>
            <div class="code-name">.icon-xinjianwenjianjia
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-a-xinzengyuanhuan"></span>
            <div class="name">
              新增（圆环）
            </div>
            <div class="code-name">.icon-a-xinzengyuanhuan
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-denglu"></span>
            <div class="name">
              登录
            </div>
            <div class="code-name">.icon-denglu
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-shoucangtianchong"></span>
            <div class="name">
              收藏填充
            </div>
            <div class="code-name">.icon-shoucangtianchong
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-paixu"></span>
            <div class="name">
              排序
            </div>
            <div class="code-name">.icon-paixu
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-youjiantou"></span>
            <div class="name">
              右箭头
            </div>
            <div class="code-name">.icon-youjiantou
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-touxiangyuanquan"></span>
            <div class="name">
              头像圆圈
            </div>
            <div class="code-name">.icon-touxiangyuanquan
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-zhengyan"></span>
            <div class="name">
              睁眼
            </div>
            <div class="code-name">.icon-zhengyan
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-wenjianjia"></span>
            <div class="name">
              文件夹
            </div>
            <div class="code-name">.icon-wenjianjia
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-jisuanjiyuyan"></span>
            <div class="name">
              计算机语言
            </div>
            <div class="code-name">.icon-jisuanjiyuyan
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-wenjianjiadakai"></span>
            <div class="name">
              文件夹打开
            </div>
            <div class="code-name">.icon-wenjianjiadakai
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-chakanshili"></span>
            <div class="name">
              查看示例
            </div>
            <div class="code-name">.icon-chakanshili
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-youcelan"></span>
            <div class="name">
              右侧栏
            </div>
            <div class="code-name">.icon-youcelan
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-shanchu"></span>
            <div class="name">
              删除
            </div>
            <div class="code-name">.icon-shanchu
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-shouquandenglu"></span>
            <div class="name">
              授权登录
            </div>
            <div class="code-name">.icon-shouquandenglu
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-yuyin"></span>
            <div class="name">
              语音
            </div>
            <div class="code-name">.icon-yuyin
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-zuojiantou"></span>
            <div class="name">
              左箭头
            </div>
            <div class="code-name">.icon-zuojiantou
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-gengduo"></span>
            <div class="name">
              更多
            </div>
            <div class="code-name">.icon-gengduo
            </div>
          </li>
          
        </ul>
        <div class="article markdown">
        <h2 id="font-class-">font-class 引用</h2>
        <hr>

        <p>font-class 是 Unicode 使用方式的一种变种，主要是解决 Unicode 书写不直观，语意不明确的问题。</p>
        <p>与 Unicode 使用方式相比，具有如下特点：</p>
        <ul>
          <li>相比于 Unicode 语意明确，书写更直观。可以很容易分辨这个 icon 是什么。</li>
          <li>因为使用 class 来定义图标，所以当要替换图标时，只需要修改 class 里面的 Unicode 引用。</li>
        </ul>
        <p>使用步骤如下：</p>
        <h3 id="-fontclass-">第一步：引入项目下面生成的 fontclass 代码：</h3>
<pre><code class="language-html">&lt;link rel="stylesheet" href="./iconfont.css"&gt;
</code></pre>
        <h3 id="-">第二步：挑选相应图标并获取类名，应用于页面：</h3>
<pre><code class="language-html">&lt;span class="iconfont icon-xxx"&gt;&lt;/span&gt;
</code></pre>
        <blockquote>
          <p>"
            iconfont" 是你项目下的 font-family。可以通过编辑项目查看，默认是 "iconfont"。</p>
        </blockquote>
      </div>
      </div>
      <div class="content symbol">
          <ul class="icon_lists dib-box">
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-xingzhuangjiehe"></use>
                </svg>
                <div class="name">云控制台</div>
                <div class="code-name">#icon-xingzhuangjiehe</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-yunkongzhitai"></use>
                </svg>
                <div class="name">云控制台</div>
                <div class="code-name">#icon-yunkongzhitai</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-gengduoneirong"></use>
                </svg>
                <div class="name">更多内容</div>
                <div class="code-name">#icon-gengduoneirong</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-bushu"></use>
                </svg>
                <div class="name">部署</div>
                <div class="code-name">#icon-bushu</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-wentixiufu"></use>
                </svg>
                <div class="name">问题修复</div>
                <div class="code-name">#icon-wentixiufu</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-windows"></use>
                </svg>
                <div class="name">Windows</div>
                <div class="code-name">#icon-windows</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-pingguo"></use>
                </svg>
                <div class="name">苹果</div>
                <div class="code-name">#icon-pingguo</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-ui"></use>
                </svg>
                <div class="name">UI</div>
                <div class="code-name">#icon-ui</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-shandian"></use>
                </svg>
                <div class="name">闪电</div>
                <div class="code-name">#icon-shandian</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-ceshi"></use>
                </svg>
                <div class="name">测试</div>
                <div class="code-name">#icon-ceshi</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-texing"></use>
                </svg>
                <div class="name">特性</div>
                <div class="code-name">#icon-texing</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-zidingyizhinengtitouxiang"></use>
                </svg>
                <div class="name">自定义智能体头像</div>
                <div class="code-name">#icon-zidingyizhinengtitouxiang</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-tiaoduzhe"></use>
                </svg>
                <div class="name">调度者</div>
                <div class="code-name">#icon-tiaoduzhe</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-bianma"></use>
                </svg>
                <div class="name">编码</div>
                <div class="code-name">#icon-bianma</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-guihua"></use>
                </svg>
                <div class="name">规划</div>
                <div class="code-name">#icon-guihua</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-wenda"></use>
                </svg>
                <div class="name">问答</div>
                <div class="code-name">#icon-wenda</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-shengji"></use>
                </svg>
                <div class="name">升级</div>
                <div class="code-name">#icon-shengji</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-mcp"></use>
                </svg>
                <div class="name">MCP</div>
                <div class="code-name">#icon-mcp</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-dengchu"></use>
                </svg>
                <div class="name">登出</div>
                <div class="code-name">#icon-dengchu</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-jianpan"></use>
                </svg>
                <div class="name">键盘</div>
                <div class="code-name">#icon-jianpan</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-biaoqian"></use>
                </svg>
                <div class="name">标签</div>
                <div class="code-name">#icon-biaoqian</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-a-"></use>
                </svg>
                <div class="name">#</div>
                <div class="code-name">#icon-a-</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-zhuti"></use>
                </svg>
                <div class="name">主题</div>
                <div class="code-name">#icon-zhuti</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-guize"></use>
                </svg>
                <div class="name">规则</div>
                <div class="code-name">#icon-guize</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-js"></use>
                </svg>
                <div class="name">JS</div>
                <div class="code-name">#icon-js</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-JoyCode"></use>
                </svg>
                <div class="name">JoyCode</div>
                <div class="code-name">#icon-JoyCode</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-a-shouqishangxia"></use>
                </svg>
                <div class="name">收起 (上下)</div>
                <div class="code-name">#icon-a-shouqishangxia</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-tongyongshezhi"></use>
                </svg>
                <div class="name">通用设置</div>
                <div class="code-name">#icon-tongyongshezhi</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-rizhi1"></use>
                </svg>
                <div class="name">日志</div>
                <div class="code-name">#icon-rizhi1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-lianwangsousuo"></use>
                </svg>
                <div class="name">联网搜索</div>
                <div class="code-name">#icon-lianwangsousuo</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-yuyan"></use>
                </svg>
                <div class="name">语言</div>
                <div class="code-name">#icon-yuyan</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-shangxiawen"></use>
                </svg>
                <div class="name">上下文</div>
                <div class="code-name">#icon-shangxiawen</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-shuoming"></use>
                </svg>
                <div class="name">说明</div>
                <div class="code-name">#icon-shuoming</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-java"></use>
                </svg>
                <div class="name">java</div>
                <div class="code-name">#icon-java</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-zhinengti"></use>
                </svg>
                <div class="name">智能体</div>
                <div class="code-name">#icon-zhinengti</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-shendusikao"></use>
                </svg>
                <div class="name">深度思考</div>
                <div class="code-name">#icon-shendusikao</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-html"></use>
                </svg>
                <div class="name">html</div>
                <div class="code-name">#icon-html</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-a-zhankaishangxia"></use>
                </svg>
                <div class="name">展开 (上下)</div>
                <div class="code-name">#icon-a-zhankaishangxia</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-juli"></use>
                </svg>
                <div class="name">举例</div>
                <div class="code-name">#icon-juli</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-bendibushu"></use>
                </svg>
                <div class="name">本地部署</div>
                <div class="code-name">#icon-bendibushu</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-sousuo"></use>
                </svg>
                <div class="name">搜索</div>
                <div class="code-name">#icon-sousuo</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-bendi"></use>
                </svg>
                <div class="name">本地</div>
                <div class="code-name">#icon-bendi</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-guanlixiangmu"></use>
                </svg>
                <div class="name">管理项目</div>
                <div class="code-name">#icon-guanlixiangmu</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-a-dilanzhankai"></use>
                </svg>
                <div class="name">底栏(展开)</div>
                <div class="code-name">#icon-a-dilanzhankai</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-shangjiantou"></use>
                </svg>
                <div class="name">上箭头</div>
                <div class="code-name">#icon-shangjiantou</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-tupian"></use>
                </svg>
                <div class="name">图片</div>
                <div class="code-name">#icon-tupian</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-chaxun"></use>
                </svg>
                <div class="name">查询</div>
                <div class="code-name">#icon-chaxun</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-shezhi"></use>
                </svg>
                <div class="name">设置</div>
                <div class="code-name">#icon-shezhi</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-xiajiantou"></use>
                </svg>
                <div class="name">下箭头</div>
                <div class="code-name">#icon-xiajiantou</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-shijian"></use>
                </svg>
                <div class="name">时间</div>
                <div class="code-name">#icon-shijian</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-tingzhitianchong"></use>
                </svg>
                <div class="name">停止填充</div>
                <div class="code-name">#icon-tingzhitianchong</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-shuaxin"></use>
                </svg>
                <div class="name">刷新</div>
                <div class="code-name">#icon-shuaxin</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-a-youcelanzhankai"></use>
                </svg>
                <div class="name">右侧栏(展开)</div>
                <div class="code-name">#icon-a-youcelanzhankai</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-daima"></use>
                </svg>
                <div class="name">代码</div>
                <div class="code-name">#icon-daima</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-dianzan"></use>
                </svg>
                <div class="name">点赞</div>
                <div class="code-name">#icon-dianzan</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-yuanchengziyuanguanliqi"></use>
                </svg>
                <div class="name">远程资源管理器</div>
                <div class="code-name">#icon-yuanchengziyuanguanliqi</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-yuyintianchong"></use>
                </svg>
                <div class="name">语音填充</div>
                <div class="code-name">#icon-yuyintianchong</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-fasong"></use>
                </svg>
                <div class="name">发送</div>
                <div class="code-name">#icon-fasong</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-fuzhi"></use>
                </svg>
                <div class="name">复制</div>
                <div class="code-name">#icon-fuzhi</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-xinzenghuihua"></use>
                </svg>
                <div class="name">新增会话</div>
                <div class="code-name">#icon-xinzenghuihua</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-guanbi"></use>
                </svg>
                <div class="name">关闭</div>
                <div class="code-name">#icon-guanbi</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-touxiang"></use>
                </svg>
                <div class="name">头像</div>
                <div class="code-name">#icon-touxiang</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-a-zuocelanzhankai"></use>
                </svg>
                <div class="name">左侧栏(展开)</div>
                <div class="code-name">#icon-a-zuocelanzhankai</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-fenxiang"></use>
                </svg>
                <div class="name">分享</div>
                <div class="code-name">#icon-fenxiang</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-tiaozhuan"></use>
                </svg>
                <div class="name">跳转</div>
                <div class="code-name">#icon-tiaozhuan</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-dangqianyedakai"></use>
                </svg>
                <div class="name">当前页打开</div>
                <div class="code-name">#icon-dangqianyedakai</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-jiesuo"></use>
                </svg>
                <div class="name">解锁</div>
                <div class="code-name">#icon-jiesuo</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-kaishi"></use>
                </svg>
                <div class="name">开始</div>
                <div class="code-name">#icon-kaishi</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-huihua"></use>
                </svg>
                <div class="name">会话</div>
                <div class="code-name">#icon-huihua</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-ziyuanchajian"></use>
                </svg>
                <div class="name">资源插件</div>
                <div class="code-name">#icon-ziyuanchajian</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-dilan"></use>
                </svg>
                <div class="name">底栏</div>
                <div class="code-name">#icon-dilan</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-zanting"></use>
                </svg>
                <div class="name">暂停</div>
                <div class="code-name">#icon-zanting</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-xiazai"></use>
                </svg>
                <div class="name">下载</div>
                <div class="code-name">#icon-xiazai</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-shangchuan"></use>
                </svg>
                <div class="name">上传</div>
                <div class="code-name">#icon-shangchuan</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-lianjie"></use>
                </svg>
                <div class="name">链接</div>
                <div class="code-name">#icon-lianjie</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-wendang-xiazhejiao"></use>
                </svg>
                <div class="name">文档-下折角</div>
                <div class="code-name">#icon-wendang-xiazhejiao</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-a-xinzengfangxing"></use>
                </svg>
                <div class="name">新增（方形）</div>
                <div class="code-name">#icon-a-xinzengfangxing</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-chajianshichang"></use>
                </svg>
                <div class="name">插件市场</div>
                <div class="code-name">#icon-chajianshichang</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-daimaguanliqi"></use>
                </svg>
                <div class="name">代码管理器</div>
                <div class="code-name">#icon-daimaguanliqi</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-bianji"></use>
                </svg>
                <div class="name">编辑</div>
                <div class="code-name">#icon-bianji</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-fanhuishangyibu"></use>
                </svg>
                <div class="name">返回上一步</div>
                <div class="code-name">#icon-fanhuishangyibu</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-wenjian"></use>
                </svg>
                <div class="name">文件</div>
                <div class="code-name">#icon-wenjian</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-shibaitianchong"></use>
                </svg>
                <div class="name">失败填充</div>
                <div class="code-name">#icon-shibaitianchong</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-wancheng"></use>
                </svg>
                <div class="name">完成</div>
                <div class="code-name">#icon-wancheng</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-fujian"></use>
                </svg>
                <div class="name">附件</div>
                <div class="code-name">#icon-fujian</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-shoucang"></use>
                </svg>
                <div class="name">收藏</div>
                <div class="code-name">#icon-shoucang</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-shangjiantoutianchong"></use>
                </svg>
                <div class="name">上箭头填充</div>
                <div class="code-name">#icon-shangjiantoutianchong</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-wendang"></use>
                </svg>
                <div class="name">文档</div>
                <div class="code-name">#icon-wendang</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-xinzeng"></use>
                </svg>
                <div class="name">新增</div>
                <div class="code-name">#icon-xinzeng</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-tingzhi"></use>
                </svg>
                <div class="name">停止</div>
                <div class="code-name">#icon-tingzhi</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-wenhao"></use>
                </svg>
                <div class="name">问号</div>
                <div class="code-name">#icon-wenhao</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-a-bugfinder"></use>
                </svg>
                <div class="name">bug finder</div>
                <div class="code-name">#icon-a-bugfinder</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-fasongtianchong"></use>
                </svg>
                <div class="name">发送填充</div>
                <div class="code-name">#icon-fasongtianchong</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-zuocelan"></use>
                </svg>
                <div class="name">左侧栏</div>
                <div class="code-name">#icon-zuocelan</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-wanchengtianchong"></use>
                </svg>
                <div class="name">完成填充</div>
                <div class="code-name">#icon-wanchengtianchong</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-shibai"></use>
                </svg>
                <div class="name">失败</div>
                <div class="code-name">#icon-shibai</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-tanhaotianchong"></use>
                </svg>
                <div class="name">叹号填充</div>
                <div class="code-name">#icon-tanhaotianchong</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-rizhi"></use>
                </svg>
                <div class="name">日志</div>
                <div class="code-name">#icon-rizhi</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-suoding"></use>
                </svg>
                <div class="name">锁定</div>
                <div class="code-name">#icon-suoding</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-jian"></use>
                </svg>
                <div class="name">减</div>
                <div class="code-name">#icon-jian</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-biyan"></use>
                </svg>
                <div class="name">闭眼</div>
                <div class="code-name">#icon-biyan</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-genghuan"></use>
                </svg>
                <div class="name">更换</div>
                <div class="code-name">#icon-genghuan</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-xinjianjiantou"></use>
                </svg>
                <div class="name">新建箭头</div>
                <div class="code-name">#icon-xinjianjiantou</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-a-bianjiAi"></use>
                </svg>
                <div class="name">编辑（Ai）</div>
                <div class="code-name">#icon-a-bianjiAi</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-duigou"></use>
                </svg>
                <div class="name">对勾</div>
                <div class="code-name">#icon-duigou</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-jinzhi"></use>
                </svg>
                <div class="name">禁止</div>
                <div class="code-name">#icon-jinzhi</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-lishijilu"></use>
                </svg>
                <div class="name">历史记录</div>
                <div class="code-name">#icon-lishijilu</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-xiajiantoutianchong"></use>
                </svg>
                <div class="name">下箭头填充</div>
                <div class="code-name">#icon-xiajiantoutianchong</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-tanhao"></use>
                </svg>
                <div class="name">叹号</div>
                <div class="code-name">#icon-tanhao</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-buzancheng"></use>
                </svg>
                <div class="name">不赞成</div>
                <div class="code-name">#icon-buzancheng</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-wenhaotianchong"></use>
                </svg>
                <div class="name">问号填充</div>
                <div class="code-name">#icon-wenhaotianchong</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-huiche"></use>
                </svg>
                <div class="name">回车</div>
                <div class="code-name">#icon-huiche</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-youjiantoutianchong"></use>
                </svg>
                <div class="name">右箭头填充</div>
                <div class="code-name">#icon-youjiantoutianchong</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-xinjianwenjianjia"></use>
                </svg>
                <div class="name">新建文件夹</div>
                <div class="code-name">#icon-xinjianwenjianjia</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-a-xinzengyuanhuan"></use>
                </svg>
                <div class="name">新增（圆环）</div>
                <div class="code-name">#icon-a-xinzengyuanhuan</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-denglu"></use>
                </svg>
                <div class="name">登录</div>
                <div class="code-name">#icon-denglu</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-shoucangtianchong"></use>
                </svg>
                <div class="name">收藏填充</div>
                <div class="code-name">#icon-shoucangtianchong</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-paixu"></use>
                </svg>
                <div class="name">排序</div>
                <div class="code-name">#icon-paixu</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-youjiantou"></use>
                </svg>
                <div class="name">右箭头</div>
                <div class="code-name">#icon-youjiantou</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-touxiangyuanquan"></use>
                </svg>
                <div class="name">头像圆圈</div>
                <div class="code-name">#icon-touxiangyuanquan</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-zhengyan"></use>
                </svg>
                <div class="name">睁眼</div>
                <div class="code-name">#icon-zhengyan</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-wenjianjia"></use>
                </svg>
                <div class="name">文件夹</div>
                <div class="code-name">#icon-wenjianjia</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-jisuanjiyuyan"></use>
                </svg>
                <div class="name">计算机语言</div>
                <div class="code-name">#icon-jisuanjiyuyan</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-wenjianjiadakai"></use>
                </svg>
                <div class="name">文件夹打开</div>
                <div class="code-name">#icon-wenjianjiadakai</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-chakanshili"></use>
                </svg>
                <div class="name">查看示例</div>
                <div class="code-name">#icon-chakanshili</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-youcelan"></use>
                </svg>
                <div class="name">右侧栏</div>
                <div class="code-name">#icon-youcelan</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-shanchu"></use>
                </svg>
                <div class="name">删除</div>
                <div class="code-name">#icon-shanchu</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-shouquandenglu"></use>
                </svg>
                <div class="name">授权登录</div>
                <div class="code-name">#icon-shouquandenglu</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-yuyin"></use>
                </svg>
                <div class="name">语音</div>
                <div class="code-name">#icon-yuyin</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-zuojiantou"></use>
                </svg>
                <div class="name">左箭头</div>
                <div class="code-name">#icon-zuojiantou</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-gengduo"></use>
                </svg>
                <div class="name">更多</div>
                <div class="code-name">#icon-gengduo</div>
            </li>
          
          </ul>
          <div class="article markdown">
          <h2 id="symbol-">Symbol 引用</h2>
          <hr>

          <p>这是一种全新的使用方式，应该说这才是未来的主流，也是平台目前推荐的用法。相关介绍可以参考这篇<a href="">文章</a>
            这种用法其实是做了一个 SVG 的集合，与另外两种相比具有如下特点：</p>
          <ul>
            <li>支持多色图标了，不再受单色限制。</li>
            <li>通过一些技巧，支持像字体那样，通过 <code>font-size</code>, <code>color</code> 来调整样式。</li>
            <li>兼容性较差，支持 IE9+，及现代浏览器。</li>
            <li>浏览器渲染 SVG 的性能一般，还不如 png。</li>
          </ul>
          <p>使用步骤如下：</p>
          <h3 id="-symbol-">第一步：引入项目下面生成的 symbol 代码：</h3>
<pre><code class="language-html">&lt;script src="./iconfont.js"&gt;&lt;/script&gt;
</code></pre>
          <h3 id="-css-">第二步：加入通用 CSS 代码（引入一次就行）：</h3>
<pre><code class="language-html">&lt;style&gt;
.icon {
  width: 1em;
  height: 1em;
  vertical-align: -0.15em;
  fill: currentColor;
  overflow: hidden;
}
&lt;/style&gt;
</code></pre>
          <h3 id="-">第三步：挑选相应图标并获取类名，应用于页面：</h3>
<pre><code class="language-html">&lt;svg class="icon" aria-hidden="true"&gt;
  &lt;use xlink:href="#icon-xxx"&gt;&lt;/use&gt;
&lt;/svg&gt;
</code></pre>
          </div>
      </div>

    </div>
  </div>
  <script>
  $(document).ready(function () {
      $('.tab-container .content:first').show()

      $('#tabs li').click(function (e) {
        var tabContent = $('.tab-container .content')
        var index = $(this).index()

        if ($(this).hasClass('active')) {
          return
        } else {
          $('#tabs li').removeClass('active')
          $(this).addClass('active')

          tabContent.hide().eq(index).fadeIn()
        }
      })
    })
  </script>
</body>
</html>
