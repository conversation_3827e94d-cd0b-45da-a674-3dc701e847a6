# JoyCode 主题色彩属性详解

本文件详细说明了 [`joycode-color-theme.json`](joycode-color-theme.json) 中 `colors` 字段下每一个属性的含义、用途及其视觉效果。
This document describes the meaning and usage of each property in the `colors` section of `joycode-color-theme.json`.

| 属性名<br>Property | 示例色值<br>Sample | 说明（中文）<br>Description (ZH) | 说明（英文）<br>Description (EN) |
|---|---|---|---|
| activityBar.activeBorder | #222325 | 活动栏激活项边框色 | Border color for active activity bar items |
| activityBar.border | #222325 | 活动栏边框色 | Border color for the activity bar |
| activityBar.background | #141414 | 侧边栏顶部活动栏背景色 | Background color of the activity bar (top sidebar) |
| activityBar.activeBackground | #2B2B2EFF | 活动栏激活项背景色 | Background color for active activity bar items |
| activityBar.foreground | #ffffff | 侧边栏顶部活动栏图标/文字颜色 | Foreground color for the activity bar items |
| activityBar.inactiveForeground | #ffffff5b | 活动栏非激活项前景色 | Foreground color for inactive activity bar items |
| activityBarBadge.background | #88C0D0 | 活动栏徽章背景色 | Background color for badges on the activity bar |
| activityBarBadge.foreground | #000000 | 活动栏徽章文字颜色 | Foreground color for badges on the activity bar |
| badge.background | #88C0D0 | 通用徽章背景色 | General badge background color |
| badge.foreground | #141414 | 通用徽章文字颜色 | General badge text color |
| breadcrumb.activeSelectionForeground | #FFFFFF | 面包屑导航激活项颜色 | Foreground color for the active breadcrumb item |
| breadcrumb.background | #1a1a1a | 面包屑导航背景色 | Breadcrumb navigation background color |
| breadcrumb.foreground | #CCCCCC99 | 面包屑导航文字颜色 | Breadcrumb navigation text color |
| breadcrumbPicker.background | #141414 | 面包屑下拉选择器背景色 | Background color of the breadcrumb picker |
| button.background | #f3FBFB | 主按钮背景色 | Background color for primary buttons |
| button.foreground | #191c22 | 主按钮文字颜色 | Foreground color for primary buttons |
| button.hoverBackground | #66aefa | 主按钮悬停背景色 | Background color for primary buttons when hovered |
| button.secondaryBackground | #303035FF | 次按钮背景色 | Background color for secondary buttons |
| button.secondaryForeground | #ececec | 次按钮文字颜色 | Foreground color for secondary buttons |
| button.secondaryHoverBackground | #767676 | 次按钮悬停背景色 | Background color for secondary buttons when hovered |
| debugExceptionWidget.background | #505050 | 调试异常小部件背景色 | Background color for the debug exception widget |
| debugExceptionWidget.border | #141414 | 调试异常小部件边框色 | Border color for the debug exception widget |
| debugToolBar.background | #1A1A1A | 调试工具栏背景色 | Background color for the debug toolbar |
| diffEditor.insertedTextBackground | #FFFFFF22 | diff 编辑器插入文本背景色 | Background color for inserted text in the diff editor |
| diffEditor.removedTextBackground | #FFFFFF22 | diff 编辑器删除文本背景色 | Background color for removed text in the diff editor |
| dropdown.background | #1a1a1a | 下拉菜单背景色 | Dropdown background color |
| dropdown.border | #2A2A2A | 下拉菜单边框色 | Dropdown border color |
| dropdown.foreground | #FFFFFF80 | 下拉菜单文字颜色 | Dropdown text color |
| editor.background | #18181BFF | 编辑器背景色 | Editor background color |
| editor.foreground | #FFFFFFCC | 编辑器默认文字颜色 | Default foreground color for editor text |
| editor.findMatchBackground | #88C0D066 | 查找匹配项背景色 | Background color of find match highlight |
| editor.findMatchHighlightBackground | #88C0D044 | 查找高亮匹配项背景色 | Background color for other find matches |
| editor.findRangeHighlightBackground | #FFFFFF33 | 查找范围高亮背景色 | Background color for range highlighted during find |
| editor.hoverHighlightBackground | #29292905 | 鼠标悬停高亮背景色 | Background color for hover highlight |
| editor.inactiveSelectionBackground | #40404077 | 非激活选择区域背景色 | Background color for inactive selection |
| editor.lineHighlightBackground | #292929 | 当前行高亮背景色 | Background color for the current line highlight |
| editor.lineHighlightBorder | #292929 | 当前行高亮边框色 | Border color for the current line highlight |
| editor.rangeHighlightBackground | #40404052 | 区域高亮背景色 | Background color for range highlight |
| editor.selectionBackground | #40404099 | 选中文本背景色 | Background color for selected text |
| editor.selectionHighlightBackground | #404040CC | 选中高亮文本背景色 | Background color for selection highlight |
| editor.snippetFinalTabstopHighlightBorder | #CCCCCC | 代码片段最后 tabstop 边框色 | Border color for the final tabstop of a snippet |
| editor.snippetTabstopHighlightBackground | #CCCCCC55 | 代码片段 tabstop 高亮背景色 | Background color for snippet tabstops |
| editor.wordHighlightBackground | #ffffff21 | 单词高亮背景色 | Background color for word highlight |
| editor.wordHighlightStrongBackground | #ffffff2d | 强单词高亮背景色 | Background color for strong word highlight |
| editorBracketMatch.background | #14141400 | 括号匹配背景色 | Background color for bracket match |
| editorBracketMatch.border | #FFFFFF55 | 括号匹配边框色 | Border color for bracket match |
| editorCodeLens.foreground | #505050 | CodeLens 文字颜色 | Foreground color for CodeLens |
| editorCursor.foreground | #FFFFFF | 编辑器光标颜色 | Editor cursor color |
| editorError.border | #BF616A00 | 编辑器错误边框色 | Border color for editor errors |
| editorError.foreground | #BF616A | 编辑器错误前景色 | Foreground color for editor errors |
| editorGroup.border | #ffffff0d | 编辑器分组边框色 | Border color for editor groups |
| editorGroup.dropBackground | #2A2A2A99 | 拖拽时分组背景色 | Background color when dragging editors |
| editorGroup.emptyBackground | #141414 | 空编辑器组背景色 | Background color for empty editor groups |
| editorGroupHeader.noTabsBackground | #141414 | 无标签页时组头背景色 | Background color for editor group header (no tabs) |
| editorGroupHeader.tabsBackground | #141414 | 标签页组头背景色 | Background color for editor group header (tabs) |
| editorGroupHeader.tabsBorder | #FFFFFF0D | 标签页组头边框色 | Border color for editor group header (tabs) |
| editorGutter.addedBackground | #A3BE8C | 行号栏新增行背景色 | Background color for added lines in the gutter |
| editorGutter.background | #1a1a1a | 行号栏背景色 | Background color of the editor gutter |
| editorGutter.deletedBackground | #BF616A | 行号栏删除行背景色 | Background color for deleted lines in the gutter |
| editorGutter.modifiedBackground | #EBCB8B | 行号栏修改行背景色 | Background color for modified lines in the gutter |
| editorHoverWidget.background | #1A1A1A | 编辑器悬浮窗背景色 | Background color for editor hover widget |
| editorHoverWidget.border | #2A2A2A | 编辑器悬浮窗边框色 | Border color for editor hover widget |
| editorIndentGuide.activeBackground1 | #505050 | 主缩进线颜色 | Color for active indent guides |
| editorIndentGuide.background1 | #404040B3 | 缩进线颜色 | Color of indent guides |
| editorInlayHint.background | #00000000 | 内嵌提示背景色 | Background color for inlay hints |
| editorInlayHint.foreground | #505050 | 内嵌提示文字颜色 | Foreground color for inlay hints |
| editorInlayHint.parameterBackground | #00000000 | 参数内嵌提示背景色 | Background color for parameter inlay hints |
| editorInlayHint.parameterForeground | #505050 | 参数内嵌提示文字颜色 | Foreground color for parameter inlay hints |
| editorInlayHint.typeBackground | #00000000 | 类型内嵌提示背景色 | Background color for type inlay hints |
| editorInlayHint.typeForeground | #505050 | 类型内嵌提示文字颜色 | Foreground color for type inlay hints |
| editorLineNumber.activeForeground | #FFFFFF | 当前行号颜色 | Foreground color for the active line number |
| editorLineNumber.foreground | #505050 | 行号颜色 | Foreground color for line numbers |
| editorLink.activeForeground | #FFFFFF | 编辑器内链接激活色 | Active foreground color for links in the editor |
| editorMarkerNavigation.background | #ffffff70 | 标记导航条背景色 | Background color for marker navigation bar |
| editorMarkerNavigationError.background | #BF616AC0 | 错误标记导航条背景色 | Background color for error marker navigation |
| editorMarkerNavigationWarning.background | #CCCCCC | 警告标记导航条背景色 | Background color for warning marker navigation |
| editorOverviewRuler.border | #00000000 | 概览标尺边框色 | Border color for the overview ruler |
| editorRuler.foreground | #494949 | 标尺线颜色 | Color of the editor rulers |
| editorSuggestWidget.background | #141414 | 智能提示背景色 | Background color for suggest widget |
| editorSuggestWidget.border | #2A2A2A | 智能提示边框色 | Border color for suggest widget |
| editorSuggestWidget.foreground | #FFFFFF | 智能提示文字颜色 | Foreground color for suggest widget |
| editorSuggestWidget.highlightForeground | #FFFFFF | 智能提示高亮文字颜色 | Highlight color for matched text in suggest widget |
| editorSuggestWidget.selectedBackground | #404040 | 智能提示选中项背景色 | Background color for selected item in suggest widget |
| editorWarning.border | #CCCCCC00 | 编辑器警告边框色 | Border color for editor warnings |
| editorWarning.foreground | #EBCB8B | 编辑器警告前景色 | Foreground color for editor warnings |
| editorWhitespace.foreground | #505050B3 | 空白字符颜色 | Foreground color for whitespace characters |
| editorWidget.background | #141414 | 编辑器小部件背景色 | Background color for editor widgets |
| editorWidget.resizeBorder | #FFFFFF | 编辑器小部件调整边框色 | Border color for resizing editor widgets |
| errorForeground | #bf616a | 全局错误前景色 | Foreground color for error messages |
| extensionButton.prominentBackground | #565656 | 扩展按钮高亮背景色 | Background color for prominent extension buttons |
| extensionButton.prominentForeground | #FFFFFF | 扩展按钮高亮文字色 | Foreground color for prominent extension buttons |
| extensionButton.prominentHoverBackground | #767676 | 扩展按钮高亮悬停背景色 | Hover background for prominent extension buttons |
| focusBorder | #30373a | 聚焦边框色 | Border color for focused elements |
| foreground | #CCCCCCdd | 全局前景色 | Default foreground color |
| gitDecoration.addedResourceForeground | #A3BE8C | Git 新增文件前景色 | Foreground color for added resources in Git |
| gitDecoration.deletedResourceForeground | #BF616A | Git 删除文件前景色 | Foreground color for deleted resources in Git |
| gitDecoration.ignoredResourceForeground | #505050 | Git 忽略文件前景色 | Foreground color for ignored resources in Git |
| gitDecoration.modifiedResourceForeground | #EBCB8B | Git 修改文件前景色 | Foreground color for modified resources in Git |
| gitDecoration.untrackedResourceForeground | #88C0D0 | Git 未跟踪文件前景色 | Foreground color for untracked resources in Git |
| input.background | #2A2A2A55 | 输入框背景色 | Background color for input fields |
| input.border | #303035FF | 输入框边框色 | Border color for input fields |
| input.foreground | #FFFFFFCC | 输入框文字颜色 | Foreground color for input fields |
| input.placeholderForeground | #FFFFFF33 | 输入框占位符颜色 | Foreground color for input placeholder text |
| inputOption.activeBorder | #FFFFFF | 输入选项激活边框色 | Border color for active input options |
| inputValidation.errorBackground | #BF616A | 输入校验错误背景色 | Background color for input validation errors |
| inputValidation.errorBorder | #BF616A | 输入校验错误边框色 | Border color for input validation errors |
| inputValidation.infoBackground | #88C0D0 | 输入校验信息背景色 | Background color for input validation info |
| inputValidation.infoBorder | #88C0D0 | 输入校验信息边框色 | Border color for input validation info |
| inputValidation.infoForeground | #141414 | 输入校验信息文字色 | Foreground color for input validation info |
| inputValidation.warningBackground | #EBCB8B | 输入校验警告背景色 | Background color for input validation warnings |
| inputValidation.warningBorder | #EBCB8B | 输入校验警告边框色 | Border color for input validation warnings |
| list.activeSelectionBackground | #ffffff1d | 列表激活选中项背景色 | Background color for active selection in lists |
| list.activeSelectionForeground | #FFFFFF | 列表激活选中项文字色 | Foreground color for active selection in lists |
| list.inactiveSelectionBackground | #ffffff10 | 列表非激活选中项背景色 | Background color for inactive selection in lists |
| list.inactiveSelectionForeground | #ffffffd7 | 列表非激活选中项文字色 | Foreground color for inactive selection in lists |
| list.deemphasizedForeground | #CCCCCC | 列表弱化项文字色 | Foreground color for deemphasized list items |
| list.dropBackground | #FFFFFF99 | 列表拖拽背景色 | Background color for list drop targets |
| list.errorForeground | #BF616A | 列表错误项文字色 | Foreground color for error items in lists |
| list.focusBackground | #434C5E | 列表聚焦项背景色 | Background color for focused list items |
| list.focusForeground | #ECEFF4 | 列表聚焦项文字色 | Foreground color for focused list items |
| list.highlightForeground | #88C0D0 | 列表高亮项文字色 | Foreground color for highlighted list items |
| list.hoverBackground | #2A2A2A99 | 列表悬停项背景色 | Background color for hovered list items |
| list.hoverForeground | #FFFFFF | 列表悬停项文字色 | Foreground color for hovered list items |
| list.invalidItemForeground | #CCCCCC | 列表无效项文字色 | Foreground color for invalid list items |
| list.warningForeground | #EBCB8B | 列表警告项文字色 | Foreground color for warning items in lists |
| menu.background | #141414 | 菜单背景色 | Menu background color |
| menu.border | #222325 | 菜单边框色 | Menu border color |
| menu.foreground | #CCCCCC | 菜单文字颜色 | Menu foreground color |
| menu.separatorBackground | #454545 | 菜单分隔线颜色 | Menu separator color |
| menubar.selectionBackground | #CCCCCC33 | 菜单栏选中项背景色 | Background color for selected menubar items |
| merge.border | #2A2A2A00 | 合并边框色 | Border color for merge conflicts |
| merge.currentContentBackground | #88C0D04D | 当前内容合并背景色 | Background color for current content in merge conflicts |
| merge.currentHeaderBackground | #88C0D066 | 当前内容合并头部背景色 | Background color for current header in merge conflicts |
| merge.incomingContentBackground | #A3BE8C4D | 传入内容合并背景色 | Background color for incoming content in merge conflicts |
| merge.incomingHeaderBackground | #A3BE8C66 | 传入内容合并头部背景色 | Background color for incoming header in merge conflicts |
| notificationLink.foreground | #88C0D0 | 通知链接文字色 | Foreground color for links in notifications |
| notifications.background | #141414 | 通知背景色 | Notification background color |
| notifications.foreground | #FFFFFF | 通知文字颜色 | Notification foreground color |
| panel.background | #141414 | 面板背景色 | Panel background color |
| panel.border | #FFFFFF0D | 面板边框色 | Panel border color |
| panelTitle.activeBorder | #FFFFFF00 | 激活面板标题边框色 | Border color for active panel title |
| panelTitle.activeForeground | #FFFFFF | 激活面板标题文字色 | Foreground color for active panel title |
| panelTitle.inactiveForeground | #CCCCCC99 | 非激活面板标题文字色 | Foreground color for inactive panel title |
| peekView.border | #505050 | 代码预览边框色 | Border color for peek view |
| peekViewEditor.background | #141414 | 代码预览编辑器背景色 | Background color for peek view editor |
| peekViewEditor.matchHighlightBackground | #FFFFFF66 | 代码预览高亮背景色 | Background color for match highlight in peek view editor |
| peekViewEditorGutter.background | #141414 | 代码预览编辑器行号栏背景色 | Background color for peek view editor gutter |
| peekViewResult.background | #141414 | 代码预览结果背景色 | Background color for peek view result |
| peekViewResult.fileForeground | #FFFFFF | 代码预览结果文件名颜色 | Foreground color for file names in peek view result |
| peekViewResult.lineForeground | #FFFFFF66 | 代码预览结果行号颜色 | Foreground color for line numbers in peek view result |
| peekViewResult.matchHighlightBackground | #FFFFFF66 | 代码预览结果高亮背景色 | Background color for match highlight in peek view result |
| peekViewResult.selectionBackground | #404040 | 代码预览结果选中项背景色 | Background color for selected result in peek view |
| peekViewResult.selectionForeground | #FFFFFF | 代码预览结果选中项文字色 | Foreground color for selected result in peek view |
| peekViewTitle.background | #2A2A2A | 代码预览标题背景色 | Background color for peek view title |
| peekViewTitleDescription.foreground | #FFFFFF | 代码预览标题描述文字色 | Foreground color for peek view title description |
| peekViewTitleLabel.foreground | #FFFFFF | 代码预览标题标签文字色 | Foreground color for peek view title label |
| pickerGroup.border | #2A2A2A00 | 选择器分组边框色 | Border color for picker group |
| pickerGroup.foreground | #FFFFFF | 选择器分组文字色 | Foreground color for picker group |
| progressBar.background | #A3BE8C | 进度条背景色 | Background color for progress bar |
| scrollbar.shadow | #00000000 | 滚动条阴影色 | Shadow color for scrollbar |
| scrollbarSlider.activeBackground | #60606055 | 滚动条滑块激活背景色 | Background color for active scrollbar slider |
| scrollbarSlider.background | #40404055 | 滚动条滑块背景色 | Background color for scrollbar slider |
| scrollbarSlider.hoverBackground | #40404055 | 滚动条滑块悬停背景色 | Background color for hovered scrollbar slider |
| selection.background | #FFFFFF33 | 选中区域背景色 | Background color for selected regions |
| sideBar.background | #141414 | 侧边栏背景色 | Sidebar background color |
| sideBar.border | #FFFFFF0D | 侧边栏边框色 | Sidebar border color |
| sideBar.foreground | #CCCCCC99 | 侧边栏文字颜色 | Sidebar foreground color |
| sideBarSectionHeader.background | #141414 | 侧边栏分区头背景色 | Sidebar section header background color |
| sideBarSectionHeader.border | #222325 | 侧边栏分区头边框色 | Sidebar section header border color |
| sideBarSectionHeader.foreground | #505050 | 侧边栏分区头文字色 | Sidebar section header foreground color |
| sideBarTitle.foreground | #CCCCCC | 侧边栏标题文字色 | Sidebar title foreground color |
| statusBar.foreground | #cccccc82 | 状态栏文字颜色 | Status bar foreground color |
| statusBar.background | #141414 | 状态栏背景色 | Status bar background color |
| statusBar.border | #FFFFFF0D | 状态栏边框色 | Status bar border color |
| statusBar.debuggingBackground | #434C5E | 调试状态栏背景色 | Status bar background color when debugging |
| statusBar.debuggingForeground | #D8DEE9 | 调试状态栏文字色 | Status bar foreground color when debugging |
| statusBar.noFolderBackground | #141414 | 无文件夹状态栏背景色 | Status bar background color when no folder is open |
| statusBar.noFolderForeground | #FFFFFF | 无文件夹状态栏文字色 | Status bar foreground color when no folder is open |
| statusBarItem.activeBackground | #505050 | 状态栏激活项背景色 | Background color for active status bar items |
| statusBarItem.hoverBackground | #404040 | 状态栏悬停项背景色 | Background color for hovered status bar items |
| statusBarItem.prominentBackground | #2A2A2A | 状态栏高亮项背景色 | Background color for prominent status bar items |
| statusBarItem.prominentHoverBackground | #404040 | 状态栏高亮项悬停背景色 | Background color for hovered prominent status bar items |
| tab.activeBackground | #1a1a1a | 活动标签页背景色 | Background color for active tab |
| tab.activeBorder | #1a1a1a | 活动标签页边框色 | Border color for active tab |
| tab.activeBorderTop | #FFFFFF00 | 活动标签页顶部边框色 | Top border color for active tab |
| tab.activeForeground | #FFFFFF | 活动标签页文字色 | Foreground color for active tab |
| tab.border | #FFFFFF0D | 标签页边框色 | Border color for tabs |
| tab.lastPinnedBorder | #222325 | 最后固定标签页边框色 | Border color for the last pinned tab |
| tab.hoverBackground | #FFFFFF00 | 标签页悬停背景色 | Background color for hovered tab |
| tab.inactiveBackground | #141414 | 非活动标签页背景色 | Background color for inactive tab |
| tab.inactiveForeground | #505050 | 非活动标签页文字色 | Foreground color for inactive tab |
| tab.unfocusedActiveBorder | #88C0D000 | 非聚焦活动标签页边框色 | Border color for unfocused active tab |
| tab.unfocusedActiveForeground | #FFFFFF99 | 非聚焦活动标签页文字色 | Foreground color for unfocused active tab |
| tab.unfocusedHoverBackground | #2A2A2AB3 | 非聚焦标签页悬停背景色 | Background color for hovered unfocused tab |
| tab.unfocusedHoverBorder | #88C0D000 | 非聚焦标签页悬停边框色 | Border color for hovered unfocused tab |
| tab.unfocusedInactiveForeground | #FFFFFF66 | 非聚焦非活动标签页文字色 | Foreground color for unfocused inactive tab |
| terminal.ansiBlack | #2A2A2A | 终端 ANSI 黑色 | ANSI black in terminal |
| terminal.ansiBlue | #81A1C1 | 终端 ANSI 蓝色 | ANSI blue in terminal |
| terminal.ansiBrightBlack | #505050 | 终端 ANSI 亮黑色 | ANSI bright black in terminal |
| terminal.ansiBrightBlue | #81A1C1 | 终端 ANSI 亮蓝色 | ANSI bright blue in terminal |
| terminal.ansiBrightCyan | #88C0D0 | 终端 ANSI 亮青色 | ANSI bright cyan in terminal |
| terminal.ansiBrightGreen | #A3BE8C | 终端 ANSI 亮绿色 | ANSI bright green in terminal |
| terminal.ansiBrightMagenta | #B48EAD | 终端 ANSI 亮洋红色 | ANSI bright magenta in terminal |
| terminal.ansiBrightRed | #BF616A | 终端 ANSI 亮红色 | ANSI bright red in terminal |
| terminal.ansiBrightWhite | #FFFFFF | 终端 ANSI 亮白色 | ANSI bright white in terminal |
| terminal.ansiBrightYellow | #EBCB8B | 终端 ANSI 亮黄色 | ANSI bright yellow in terminal |
| terminal.ansiCyan | #88C0D0 | 终端 ANSI 青色 | ANSI cyan in terminal |
| terminal.ansiGreen | #A3BE8C | 终端 ANSI 绿色 | ANSI green in terminal |
| terminal.ansiMagenta | #B48EAD | 终端 ANSI 洋红色 | ANSI magenta in terminal |
| terminal.ansiRed | #BF616A | 终端 ANSI 红色 | ANSI red in terminal |
| terminal.ansiWhite | #FFFFFF | 终端 ANSI 白色 | ANSI white in terminal |
| terminal.ansiYellow | #EBCB8B | 终端 ANSI 黄色 | ANSI yellow in terminal |
| terminal.background | #141414 | 终端背景色 | Terminal background color |
| terminal.foreground | #FFFFFFcc | 终端文字颜色 | Terminal foreground color |
| terminal.selectionBackground | #636262dd | 终端选中区域背景色 | Terminal selection background color |
| terminalCursor.background | #FFFFFF22 | 终端光标背景色 | Terminal cursor background color |
| terminalCursor.foreground | #FFFFFF | 终端光标前景色 | Terminal cursor foreground color |
| textLink.activeForeground | #4c9df3 | 活动文本链接颜色 | Active text link color |
| textLink.foreground | #4c9df3 | 文本链接颜色 | Text link color |
| textPreformat.foreground | #88C0D0 | 预格式化文本颜色 | Preformatted text color |
| textSeparator.foreground | #88C0D0 | 文本分隔符颜色 | Text separator color |
| titleBar.activeBackground | #141414 | 活动标题栏背景色 | Active title bar background color |
| titleBar.activeForeground | #ffffffcb | 活动标题栏文字色 | Active title bar foreground color |
| titleBar.border | #FFFFFF0D | 标题栏边框色 | Title bar border color |
| titleBar.inactiveBackground | #141414 | 非活动标题栏背景色 | Inactive title bar background color |
| titleBar.inactiveForeground | #ffffff98 | 非活动标题栏文字色 | Inactive title bar foreground color |
| tree.indentGuidesStroke | #CCCCCC55 | 树形控件缩进线颜色 | Indent guide color in tree views |
| walkThrough.embeddedEditorBackground | #141414 | 嵌入式编辑器背景色 | Embedded editor background color |
| widget.border | #222325 | 小部件边框色 | Widget border color |
| widget.shadow | #00000066 | 小部件阴影色 | Widget shadow color |
| minimapGutter.addedBackground | #15ac91 | 小地图新增行背景色 | Minimap gutter added line color |
| minimapGutter.modifiedBackground | #e5b95c | 小地图修改行背景色 | Minimap gutter modified line color |
| minimapGutter.deletedBackground | #f14c4c | 小地图删除行背景色 | Minimap gutter deleted line color |
| minimap.findMatchHighlight | #15ac9170 | 小地图查找高亮色 | Minimap find match highlight color |
| minimap.errorHighlight | #f14c4c | 小地图错误高亮色 | Minimap error highlight color |
| minimap.warningHighlight | #ea7620 | 小地图警告高亮色 | Minimap warning highlight color |
| minimap.background | #181818 | 小地图背景色 | Minimap background color |

> 以上为 JoyCode 主题 colors 字段全部属性的详细说明。
> For more details, see the [VSCode Theme Color Reference](https://code.visualstudio.com/api/references/theme-color).

---
