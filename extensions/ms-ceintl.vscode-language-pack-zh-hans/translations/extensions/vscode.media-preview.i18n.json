{"": ["--------------------------------------------------------------------------------------------", "Copyright (c) Microsoft Corporation. All rights reserved.", "Licensed under the MIT License. See License.txt in the project root for license information.", "--------------------------------------------------------------------------------------------", "Do not edit this file. It is machine generated."], "version": "1.0.0", "contents": {"bundle": {"An error occurred while loading the audio file.": "在加载视频文件时出错。", "An error occurred while loading the image.": "加载图片出错。", "An error occurred while loading the video file.": "在加载视频文件时出错。", "Image Binary Size": "图像二进制文件大小", "Image Size": "图像大小", "Image Zoom": "图像缩放", "Open file using VS Code's standard text/binary editor?": "使用 VS Code 的标准文本/二进制编辑器打开文件？", "Select zoom level": "选择缩放级别", "Whole Image": "整张图片", "{0}B": "{0} B", "{0}GB": "{0} GB", "{0}KB": "{0} KB", "{0}MB": "{0} MB", "{0}TB": "{0} TB"}, "package": {"command.copyImage": "复制", "command.reopenAsPreview": "作为图像预览重新打开", "command.reopenAsText": "作为源文本重新打开", "command.zoomIn": "放大", "command.zoomOut": "缩小", "customEditor.audioPreview.displayName": "音频预览", "customEditor.imagePreview.displayName": "图像预览", "customEditor.videoPreview.displayName": "视频预览", "description": "为图像、音频和视频提供 VS Code 的内置预览", "displayName": "媒体预览", "videoPreviewerAutoPlay": "开始自动静音播放视频。", "videoPreviewerLoop": "自动再次循环播放视频。"}}}