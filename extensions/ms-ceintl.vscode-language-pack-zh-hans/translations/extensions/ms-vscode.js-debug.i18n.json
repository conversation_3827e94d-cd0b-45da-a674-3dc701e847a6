{"": ["--------------------------------------------------------------------------------------------", "Copyright (c) Microsoft Corporation. All rights reserved.", "Licensed under the MIT License. See License.txt in the project root for license information.", "--------------------------------------------------------------------------------------------", "Do not edit this file. It is machine generated."], "version": "1.0.0", "contents": {"package": {"add.eventListener.breakpoint": "切换事件侦听器断点", "add.xhr.breakpoint": "添加 XHR/提取断点", "attach.node.process": "附加到 Node 进程", "base.cascadeTerminateToConfigurations.label": "当终止此调试会话时，也将停止的调试会话的列表。", "base.enableDWARF.label": "切换调试程序是否尝试从 WebAssembly 读取 DWARF 调试符号，这可能会占用大量资源。需要“ms-vscode.wasm-dwarf-debugging”扩展才能运行。", "breakpoint.xhr.any": "任何 XHR/提取", "breakpoint.xhr.contains": "URL 包含以下内容时中断:", "browser.address.description": "调试的浏览器正在侦听的 IP 地址或主机名。", "browser.attach.port.description": "用于远程调试浏览器的端口，在启动浏览器时通过 `--remote-debugging-port` 指定。", "browser.baseUrl.description": "用于解析路径 baseUrl 的基本 URL。 将 URL 映射到磁盘上的文件时，将修剪 baseURL。 默认为启动 URL 域。", "browser.browserAttachLocation.description": "强制在一个位置连接浏览器。在远程工作区中(例如通过 ssh 或 WSL)，这可用于在远程计算机上而不是在本地连接浏览器。", "browser.browserLaunchLocation.description": "强制在一个位置启动浏览器。在远程工作区中(例如通过 ssh 或 WSL)，这可用于在远程计算机上而不是在本地打开浏览器。", "browser.cleanUp.description": "调试会话完成后的清理操作:“仅关闭正在调试的选项卡”和“关闭整个浏览器”。", "browser.cwd.description": "运行时可执行文件的可选工作目录。", "browser.disableNetworkCache.description": "控制是否跳过每个请求的网络缓存", "browser.env.description": "浏览器的环境键/值对的可选字典。", "browser.file.description": "要在浏览器中打开的本地 HTML 文件", "browser.includeDefaultArgs.description": "启动中是否包括默认浏览器启动参数(以禁用可能使调试更加困难的功能)。", "browser.includeLaunchArgs.description": "高级: 是否在浏览器上设置任何默认启动/调试参数。调试器将假定浏览器将使用管道调试，如`--remote-debugging-pipe`提供的管道调试。", "browser.inspectUri.description": "用于重写 inspectUri 的格式: 这是一个模板字符串，可插入 \"{curlyBraces}\" 中的键。可用的键包括:\r\n - \"url.*\" 是正在运行的应用程序的解析地址，例如 \"{url.port}\" 和 \"{url.hostname}\"\r\n - \"port\" 是 Chrome 正在侦听的调试端口。\r\n - \"browserInspectUri\" 是启动的浏览器上的检查器 URI\r\n - \"browserInspectUriPath\" 是启动的浏览器上的检查器 URI 的路径部分(例如 \"/devtools/browser/e9ec0098-306e-472a-8133-5e42488929c2\")。\r\n - \"wsProtocol\" 是提示的 websocket 协议。如果原始 URL 为 \"https\"，则设置为 \"wss\"，否则为 \"ws\"。\r\n", "browser.launch.port.description": "浏览器侦听的端口。默认值为“0”，这将导致浏览器通过管道进行调试，这通常更安全，除非需要从其他工具连接到浏览器，否则应选择该值。", "browser.pathMapping.description": "将 URL/路径映射到本地文件夹，以将浏览器中的脚本解析为磁盘上的脚本", "browser.perScriptSourcemaps.description": "是否使用包含源文件基本名称的唯一源映射单独加载脚本。处理大量小型脚本时，可以设置此项来优化源映射处理。如果设置为“自动”，将在可以检测已知用例时进行检测。", "browser.profileStartup.description": "如果为 true，则将在进程启动后立即开始分析", "browser.restart": "是否在浏览器连接关闭时重新连接", "browser.revealPage": "焦点选项卡", "browser.runtimeArgs.description": "传递给运行时可执行文件的可选参数。", "browser.runtimeExecutable.description": "\"canary\"、\"stable\"、\"custom\" 或浏览器可执行文件的路径。 Custom 表示自定义包装器、自定义生成或 CHROME_PATH 环境变量。", "browser.runtimeExecutable.edge.description": "\"canary\"、\"stable\"、\"dev'\"、\"custom\" 或浏览器可执行文件的路径。custom 表示自定义包装器、自定义生成或 EDGE_PATH 环境变量。", "browser.server.description": "配置要启动的 Web 服务器。采用与 \"node\" 启动任务相同的配置。", "browser.skipFiles.description": "调试时要跳过的文件或文件夹名称或路径 glob 的数组。允许星型模式和否定式，例如`[\"**/node_modules/**\", \"!**/node_modules/my-module/**\"]`", "browser.smartStep.description": "自动逐句通过源映射文件中未映射的行。例如，下编译 async/await 或其他功能时 TypeScript 自动生成的代码。", "browser.sourceMapPathOverrides.description": "一组用于重写源映射中所述的源文件位置的映射，映射到磁盘上的相应位置。有关详细信息，请参见自述文件。", "browser.sourceMapRenames.description": "是否在 sourcemap 使用“名称”映射。这需要请求源内容，后者在使用某些调试程序时，速度会很慢。", "browser.sourceMaps.description": "使用 JavaScript 源映射(如存在)。", "browser.targetSelection": "是附加到与 URL 筛选器匹配的所有目标(“自动”)还是要求选择一个(“选择”)。", "browser.timeout.description": "重试此毫秒数以连接到浏览器。默认值为 10000 毫秒。", "browser.url.description": "将搜索具有此确切网址的标签并附加到该标签(若找到)", "browser.urlFilter.description": "将使用此 URL 搜索页面，找到后将连接到该页面。可使用 * 通配符。", "browser.userDataDir.description": "默认情况下，在临时文件夹中使用单独的用户配置文件启动浏览器。使用此选项可进行替代。设置为 false 以使用默认用户配置文件启动。如果实例已从 `userDataDir` 运行，则无法启动新的浏览器。", "browser.vueComponentPaths": "用于查找 \"*.vue\" 组件的文件 glob 模式的列表。默认搜索整个工作区。需要指定此项，因为 Vue 的源映射需要在 Vue CLI 4 中进行额外查找。可通过将此项设置为空数组来禁用此特殊处理。", "browser.webRoot.description": "此设置指定 Web 服务器根的工作区绝对路径。用于将 `/app.js` 等路径解析为磁盘上的文件。pathMapping 的速记方式为 \"/\"", "chrome.attach.description": "附加到已处于调试模式的 Chrome 实例", "chrome.attach.label": "Chrome: 附加", "chrome.label": "Web 应用(Chrome)", "chrome.launch.description": "启动 Chrome 以调试 URL", "chrome.launch.label": "Chrome: 启动", "commands.callersAdd.label": "排除调用方", "commands.callersAdd.paletteLabel": "排除调用方在当前位置中暂停", "commands.callersGoToCaller.label": "转到调用方位置", "commands.callersGoToTarget.label": "转到目标位置", "commands.callersRemove.label": "移除排除的调用方", "commands.callersRemoveAll.label": "删除所有排除的调用方", "commands.disableSourceMapStepping.label": "禁用源映射单步执行", "commands.enableSourceMapStepping.label": "启用源映射单步执行", "commands.networkClear.label": "清除网络日志", "commands.networkCopyURI.label": "复制请求 URL", "commands.networkOpenBody.label": "打开响应正文", "commands.networkOpenBodyInHexEditor.label": "在十六进制编辑器中打开响应正文", "commands.networkReplayXHR.label": "重播请求", "commands.networkViewRequest.label": "以 cURL 形式查看请求", "configuration.autoAttachMode": "配置在 \"#debug.node.autoAttach#\" 处于启用状态时自动附加和调试的进程。无论此设置如何，都始终附加到启动的带有 \"--inspect\" 标志的节点进程。", "configuration.autoAttachMode.always": "自动附加到终端中启动的每个 Node.js 进程。", "configuration.autoAttachMode.disabled": "自动附加被禁用，且不在状态栏中显示。", "configuration.autoAttachMode.explicit": "仅在给定 \"--inspect\" 时自动附加。", "configuration.autoAttachMode.smart": "运行不在 node_modules 文件夹中的脚本时自动附加。", "configuration.autoAttachSmartPatterns": "配置 glob 模式，以确定何时附加智能 `#debug.javascript.autoAttachFilter#` 模式。`$KNOWN_TOOLS$` 被替换为常见测试和代码运行器的名称的列表。[在 VS Code 文档中阅读更多内容](https://code.visualstudio.com/docs/nodejs/nodejs-debugging#_auto-attach-smart-patterns)。", "configuration.automaticallyTunnelRemoteServer": "调试远程 Web 应用时，配置是否自动将远程服务器通过隧道传输到本地计算机。", "configuration.breakOnConditionalError": "在条件断点引发错误时是否停止。", "configuration.debugByLinkOptions": "调试时从调试终端内部单击链接使用的选项。可设置为\"false\"以禁用此行为。", "configuration.defaultRuntimeExecutables": "用于启动配置的默认 \"runtimeExecutable\" (如果未指定)。这可用于配置 Node.js 或浏览器安装项的自定义路径。", "configuration.enableNetworkView": "为支持它的目标启用实验性网络视图。", "configuration.npmScriptLensLocation": "在 npm 脚本中应显示“运行”和“调试”代码的位置。 它可以在脚本部分的“全部”、脚本、脚本部分的“顶部”或“从不”上面。", "configuration.pickAndAttachOptions": "通过 `Debug: Attach to Node.js Process` 命令调试进程时使用的默认选项", "configuration.resourceRequestOptions": "在调试器中加载资源(如源映射)时可使用的请求选项。例如，如果你的源映射需要身份验证或使用自签名证书，则可能需要配置此设置。选项用于创建使用 [`got`](https://github.com/sindresorhus/got) 库的请求。\r\n\r\n可通过传递 `{ \"https\": { \"rejectUnauthorized\": false } }` 来实现禁用证书验证的常见情况。", "configuration.terminalOptions": "JavaScript 调试终端和 npm 脚本的默认启动选项。", "configuration.unmapMissingSources": "配置是否会自动取消映射无法读取源文件的源映射文件。如果这是 false (默认)，系统会显示提示。", "createDiagnostics.label": "诊断断点问题", "customDescriptionGenerator.description": "自定义调试程序为对象(本地变量等)显示的文本说明。示例:\r\n      1. this.toString() // 将调用 toString 来打印所有对象\r\n      2. this.customDescription ? this.customDescription() : defaultValue // 如果未返回 defaultValue，则使用 customDescription 方法(若可用)\r\n      3. function (def) { return this.customDescription ? this.customDescription() : def } // 如果未返回 defaultValue，则使用 customDescription 方法(若可用)\r\n      ", "customPropertiesGenerator.description": "自定义为调试程序中的对象显示的属性(本地变量等)。示例:\r\n    1. { ...this, extraProperty: '12345' } // 向所有对象添加 extraProperty 12345\r\n    2. this.customProperties ? this.customProperties() : this // 如果不使用此(默认属性)中的属性，请使用 customProperties 方法(若可用)\r\n    3. function () { return this.customProperties ? this.customProperties() : this } // 如果不返回默认属性，请使用 customDescription 方法(若可用)\r\n\r\n    已弃用: 这是此功能的临时实现，直到我们有时间按此处所示方法实现它为止: https://github.com/microsoft/vscode/issues/102181", "debug.npm.edit": "编辑 package.json", "debug.npm.noScripts": "在 package.json 中找不到 npm 脚本", "debug.npm.noWorkspaceFolder": "需要打开工作区文件夹来调试 npm 脚本。", "debug.npm.parseError": "无法读取 {0}: {1}", "debug.npm.script": "调试 npm 脚本", "debug.terminal.attach": "附加到 Node.js 终端进程", "debug.terminal.label": "JavaScript 调试终端", "debug.terminal.program.description": "在启动的终端中运行命令。如果未提供命令，终端将在不启动程序的情况下打开。", "debug.terminal.snippet.label": "在调试终端中运行 \"npm start\"", "debug.terminal.toggleAuto": "切换终端 Node.js 自动附加", "debug.terminal.welcome": "[JavaScript 调试终端](command:extension.js-debug.createDebuggerTerminal)\r\n\r\n可使用 JavaScript 调试终端调试在命令行上运行的 Node.js 进程。", "debug.terminal.welcomeWithLink": "[JavaScript 调试终端](command:extension.js-debug.createDebuggerTerminal)\r\n\r\n可使用 JavaScript 调试终端调试在命令行上运行的 Node.js 进程。\r\n\r\n[调试 URL](command:extension.js-debug.debugLink)", "debug.unverifiedBreakpoints": "无法设置某些断点。如果遇到问题，可以 [对启动配置进行故障排除](command:extension.js-debug.createDiagnostics)。", "debugLink.label": "打开链接", "edge.address.description": "调试 Web 视图时，Web 视图正在侦听的 IP 地址或主机名。如果未设置，则自动发现。", "edge.attach.description": "附加到已在调试模式下的 Edge 实例", "edge.attach.label": "Edge: 附加", "edge.label": "Web 应用(Edge)", "edge.launch.description": "启动 Microsoft Edge 以调试 URL", "edge.launch.label": "Edge: 启动", "edge.port.description": "调试 Web 视图时，Web 视图调试程序正在侦听的端口。如果未设置，则自动发现。", "edge.useWebView.attach.description": "包含 UWP 托管 Webview2 的调试管道“pipeName”的对象。这是创建管道“\\\\.\\pipe\\LOCAL\\MyTestSharedMemory”时的“MyTestSharedMemory”", "edge.useWebView.launch.description": "如果设置为“true”，则调试器会将运行时可执行文件视为包含 WebView 的主机应用程序，以允许你调试 WebView 脚本内容。", "edit.xhr.breakpoint": "编辑 XHR/提取断点", "enableContentValidation.description": "切换是否要验证确定磁盘上的文件内容与运行时中加载的内容相匹配。这在各种情况下都很有用，在一些情况下还是必需操作，但是如果你具有脚本的服务器端转换，则可能会导致出现问题。", "errors.timeout": "{0}: {1} 毫秒后超时", "extension.description": "用于调试 Node.js 程序和 Chrome 的扩展。", "extensionHost.label": "VS Code 扩展开发", "extensionHost.launch.config.name": "启动扩展", "extensionHost.launch.debugWebWorkerHost": "配置是否应尝试附加到 Web 辅助进程扩展主机。", "extensionHost.launch.debugWebviews": "配置是否应尝试附加到已启动 VS Code 实例中的 Web 视图。此操作仅适用于桌面 VS Code。", "extensionHost.launch.env.description": "传递给扩展主机的环境变量。", "extensionHost.launch.rendererDebugOptions": "附加到呈现器进程时使用的 Chrome 启动选项，具有 \"debugWebviews\" 或 \"debugWebWorkerHost\"。", "extensionHost.launch.runtimeExecutable.description": "VS Code 的绝对路径。", "extensionHost.launch.stopOnEntry.description": "启动后自动停止扩展主机。", "extensionHost.launch.testConfiguration": "[测试 CLI](https://code.visualstudio.com/api/working-with-extensions/testing-extension#quick-setup-the-test-cli) 的测试配置文件的路径。", "extensionHost.launch.testConfigurationLabel": "要从文件中运行的单个配置。如果未指定，系统可能会要求你选取。", "extensionHost.snippet.launch.description": "在调试模式下启动 VS Code 扩展", "extensionHost.snippet.launch.label": "VS Code 扩展开发", "getDiagnosticLogs.label": "保存诊断 JS 调试日志", "longPredictionWarning.disable": "不再显示", "longPredictionWarning.message": "配置断点需要一段时间。你可通过更新 launch.json 中的 \"outFiles\" 来加快速度。", "longPredictionWarning.noFolder": "未打开工作区文件夹。", "longPredictionWarning.open": "打开 launch.json", "node.address.description": "要调试的进程的 TCP/IP 地址。默认值为 \"localhost\"。", "node.attach.attachExistingChildren.description": "是否尝试附加到已生成的子进程。", "node.attach.attachSpawnedProcesses.description": "是否在附加过程中设置环境变量以跟踪生成的子级。", "node.attach.config.name": "附加", "node.attach.continueOnAttach": "如果为 true，我们将自动恢复启动的程序并等待 \"--inspect-brk\"", "node.attach.processId.description": "要附加到的进程 ID。", "node.attach.restart.description": "如果连接断开，请尝试重新连接到该程序。如果设置为 \"true\"，将始终每秒重试一次。可通过在对象中指定 \"delay\" 和 \"maxAttempts\" 来自定义时间间隔和最大尝试次数。", "node.attachSimplePort.description": "如果设置，则通过给定端口附加到进程。Node.js 程序通常不再需要该设置，而且它没法再调试子进程，但在使用 Deno 和 Docker 启动等更复杂的场景中，它可能很有用。如果设置为 0，则将选择随机端口，并自动向启动参数添加 --inspect-brk。", "node.console.title": "Node 调试控制台", "node.disableOptimisticBPs.description": "请勿在任何文件中设置断点，除非该文件已加载源映射。", "node.enableTurboSourcemaps.description": "配置是否使用速度更快的全新源映射发现机制", "node.experimentalNetworking.description": "在 Node.js 中启用实验性检查。如果设置为 `自动`，则会为支持它的 Node.js 版本启用此功能。可以将其设置为 `开` 或 `关` 以显式启用或禁用它。", "node.killBehavior.description": "配置在停止会话时如何终止调试进程。可以是:\r\n\r\n- forceful (default): 强制关闭进程树。在 posix 上发送 SIGKILL，在 Windows 上发送 \"taskkill.exe /F\"。\r\n- polite: 正常关闭进程树。可能出现按此方式关闭后继续运行行为出错的进程的情况。在 posix 上发送 SIGTERM，在 Windows 上发送 \"taskkill.exe\" 但不带 \"/F\" (强制)标志。\r\n- 无: 将不终止。", "node.label": "Node.js", "node.launch.args.description": "传递给程序的命令行参数。\r\n\r\n可以是字符串数组或单个字符串。在终端中启动程序时，将此属性设置为单个字符串将导致 shell 的参数无法转义。", "node.launch.autoAttachChildProcesses.description": "自动将调试器附加到新的子进程。", "node.launch.config.name": "启动", "node.launch.console.description": "启动调试目标的位置。", "node.launch.console.externalTerminal.description": "可通过用户设置来配置的外部终端", "node.launch.console.integratedTerminal.description": "VS Code 的集成终端", "node.launch.console.internalConsole.description": "VS Code 调试控制台(不支持从程序读取输入)", "node.launch.cwd.description": "正在调试程序工作目录的绝对路径。如果已设置 localRoot，则 cwd 将与该值匹配，否则它将回退到 workspaceFolder", "node.launch.env.description": "传递到程序的环境变量。`null` 值从环境中删除该变量。", "node.launch.envFile.description": "包含环境变量定义的文件的绝对路径。", "node.launch.logging": "事件日志配置", "node.launch.logging.cdp": "Chrome DevTools 协议消息的日志文件路径", "node.launch.logging.dap": "调试适配器协议消息的日志文件的路径", "node.launch.outputCapture.description": "捕获输出消息的位置: 如果设置为 `console`，则为默认调试 API，如果设置为 `std`，则为 stdout/stderr 流。", "node.launch.program.description": "程序的绝对路径。通过查看 package.json 和打开的文件猜测所生成的值。编辑此属性。", "node.launch.restart.description": "如果程序退出时带有非零的退出码，则尝试重启该程序。", "node.launch.runtimeArgs.description": "传递给运行时可执行文件的可选参数。", "node.launch.runtimeExecutable.description": "要使用的运行时。应为绝对路径或在 PATH 上可用的运行时名称。默认值为 \"node\"。", "node.launch.runtimeSourcemapPausePatterns": "手动插入入口点断点的模式列表。在使用不存在或启动前无法检测到的源映射时，这有助于让调试程序设置断点，例如[使用无服务器框架](https://github.com/microsoft/vscode-js-debug/issues/492)。", "node.launch.runtimeVersion.description": "要使用的 \"node\" 运行时版本。需要 \"nvm\"。", "node.launch.useWSL.deprecation": "已弃用 \"useWSL\" 并将停止对它的支持。请改用 \"Remote - WSL\" 扩展。", "node.launch.useWSL.description": "使用适用于 Linux 的 Windows 子系统。", "node.localRoot.description": "包含该程序的本地目录的路径。", "node.pauseForSourceMap.description": "是否等待每个传入脚本的源映射加载。 这会产生性能开销，只要没有禁用 rootPath，就可在磁盘空间不足时安全地禁用它。", "node.port.description": "要附加到的调试端口。默认值为 9229。", "node.processattach.config.name": "附加到进程", "node.profileStartup.description": "如果为 true，则将在进程启动后立即开始分析", "node.remote.host.header.description": "连接到检查器的 Websocket 时要使用的显式主机标头。如果未指定，主机标头将设置为 \"localhost\"。当检查器在仅接受特定主机标头的代理后面运行时，这非常有用。", "node.remoteRoot.description": "包含该程序的远程目录的绝对路径。", "node.resolveSourceMapLocations.description": "可用源映射来解析本地文件的位置(文件夹和 URL)的小型匹配模式列表。这可用于避免造成外部源映射代码中错误地出现中断。使用前缀为 \"!\" 的模式可将这些中断排除。也可将其设置为空数组或 null 以避免限制。", "node.showAsyncStacks.description": "显示导致当前调用堆栈的异步调用。", "node.snippet.attach.description": "附加到正在运行的 node 程序", "node.snippet.attach.label": "Node.js: 附加", "node.snippet.attachProcess.description": "打开进程选取器并选择附加到的 node 进程", "node.snippet.attachProcess.label": "Node.js: 附加到进程", "node.snippet.electron.description": "调试 Electron 主进程", "node.snippet.electron.label": "Node.js: Electron 主进程", "node.snippet.gulp.description": "调试 Gulp 任务(确保项目中已安装本地 Gulp)", "node.snippet.gulp.label": "Node.js: <PERSON><PERSON> 任务", "node.snippet.launch.description": "在调试模式下启动节点计划", "node.snippet.launch.label": "Node.js: 启动程序", "node.snippet.mocha.description": "调试 mocha 测试", "node.snippet.mocha.label": "Node.js: <PERSON><PERSON> 测试", "node.snippet.nodemon.description": "使用 nodemon 以在源更改时重新启动调试会话", "node.snippet.nodemon.label": "Node.js: <PERSON><PERSON><PERSON> 安装程序", "node.snippet.npm.description": "通过 npm \"debug\" 脚本启动 node 程序", "node.snippet.npm.label": "Node.js: 通过 npm 启动", "node.snippet.remoteattach.description": "附加到远程节点计划的调试端口", "node.snippet.remoteattach.label": "Node.js: 附加到远程程序", "node.snippet.yo.description": "调试 yeoman 生成器 (通过在项目文件夹中运行 \"npm link\" 进行安装)", "node.snippet.yo.label": "Node.js: <PERSON><PERSON> 生成器", "node.sourceMapPathOverrides.description": "一组重写源映射中源文件的位置为磁盘上所处位置的映射。", "node.sourceMaps.description": "使用 JavaScript 源映射(如存在)。", "node.stopOnEntry.description": "启动后自动停止程序。", "node.timeout.description": "重试此毫秒数以连接到 Node.js。默认值为 10000 毫秒。", "node.versionHint.description": "允许显式指定正在运行的节点版本，这可用于在自动版本检测不可用的情况下禁用或启用某些行为。", "node.websocket.address.description": "要附加到的确切 websocket 地址。如果未指定，将从地址和端口中发现它。", "openEdgeDevTools.label": "打开浏览器开发工具", "outFiles.description": "如果启用了源映射，这些 glob 模式会指定生成的 JavaScript 文件。如果模式以 `!` 开头，则会排除这些文件。如果未指定，生成的代码应位于与其源相同的目录。", "pretty.print.script": "用于调试的美观格式打印", "profile.start": "获取性能配置文件", "profile.stop": "停止性能配置文件", "remove.eventListener.breakpoint.all": "移除所有事件侦听器断点", "remove.xhr.breakpoint": "删除 XHR/提取断点", "remove.xhr.breakpoint.all": "删除所有 XHR/提取断点", "requestCDPProxy.label": "为调试会话请求 CDP 代理", "skipFiles.description": "调试时要跳过的文件的 glob 模式数组。模式 \"<node_internals>/**\" 与所有内部 Node.js 模块相匹配。", "smartStep.description": "通过单步执行自动生成的代码不能映射回原始源。", "start.with.stop.on.entry": "开始调试并在输入时停止", "startWithStopOnEntry.label": "开始调试并在输入时停止", "timeouts.generalDescription": "多个调试程序操作的超时。", "timeouts.generalDescription.markdown": "多个调试程序操作的超时。", "timeouts.hoverEvaluation.description": "悬停符号的值计算中止之前的时间。如果设置为 0，则悬停计算永远不会超时。", "timeouts.sourceMaps.description": "与源映射操作相关的超时。", "timeouts.sourceMaps.sourceMapCumulativePause.description": "在最小时间(sourceMapMinPause)耗尽后，每个会话等待源映射被处理的额外时间(以毫秒为单位)", "timeouts.sourceMaps.sourceMapMinPause.description": "分析脚本时等待每个源映射被处理的最小时间(以毫秒为单位)", "toggle.skipping.this.file": "跳过此文件的开关", "trace.boolean.description": "跟踪可设置为 \"true\"，以将诊断日志写入磁盘。", "trace.description": "配置生成哪些诊断输出。", "trace.logFile.description": "配置磁盘日志的写入位置。", "trace.stdio.description": "是否从启动的应用程序或浏览器返回跟踪数据。", "workspaceTrust.description": "必须有信任才能在此工作区中调试代码。"}, "bundle": {"A profiling session is already running, would you like to stop it and start a new session?": "分析会话已在运行，是否要停止它并开始新会话?", "Add XHR Breakpoint": "添加 XHR 断点", "Add new URL...": "添加新 URL...", "Adjust glob pattern(s) in the 'outFiles' attribute so that they cover the generated JavaScript.": "在“outFiles”属性中调整glob模式，以包含生成的JavaScript。", "Always": "始终", "Always in this Workspace": "始终在此“工作区”中", "An error occurred taking a profile from the target.": "从目标获取配置文件时出错。", "Animation Frame Fired": "已触发动画帧", "Any XHR or fetch": "任何 XHR 或提取", "Assertion failed": "断言失败", "Attach to process: '{0}' doesn't look like a process id.": "附加到进程:“{0}”不像是进程 ID。", "Attach to process: cannot enable debug mode for process '{0}' ({1}).": "附加到进程: 无法对进程 \"{0}\" 启用调试模式 ({1})。", "Attribute 'runtimeVersion' requires Node.js version manager 'nvm-windows' or 'nvs'.": "属性 \"runtimeVersion\" 需要 Node.js 版本管理器 \"nvm-windows\" 或 \"nvs\"。", "Attribute 'runtimeVersion' requires Node.js version manager 'nvs', 'nvm' or 'fnm' to be installed.": "属性“runtimeVersion”需要安装 Node.js 版本管理器“nvs”、“nvm”或“fnm”。", "Attribute 'runtimeVersion' with a flavor/architecture requires 'nvs' to be installed.": "具有风格/体系结构的属性 \"runtimeVersion\" 需要安装 \"nvs\"。", "Bidder Bidding Phase Start": "竞标者出价阶段开始", "Bidder Reporting Phase Start": "投标人报告阶段开始", "Block": "块", "Break when URL Contains": "URL 包含以下内容时中断:", "Breaks on all throw errors, even if they're caught later.": "出现任何引发错误时中断，即使这些错误稍后才被捕获也是如此。", "Breaks only on errors or promise rejections that are not handled.": "仅对未处理的错误或承诺拒绝进行中断。", "Browser connection failed, will retry: {0}": "浏览器连接失败，将重试: {0}", "CPU Profile": "CPU 配置文件", "CPU profile saved as \"{0}\" in your workspace folder": "在工作区文件夹中保存为“{0}”的 CPU 配置文件", "CSP violation \"{0}\"": "CSP 违规“{0}”", "Can't find Node.js binary \"{0}\": {1}. Make sure Node.js is installed and in your PATH, or set the \"runtimeExecutable\" in your launch.json": "找不到 Node.js 二进制文件“{0}”: {1}。请确保 Node.js 已安装且位于你的路径中，或者在 launch.json 中设置 \"runtimeExecutable\"", "Can't load environment variables from file ({0}).": "无法从文件加载环境变量({0})。", "Cancel Animation Frame": "取消动画帧", "Cannot connect to the target at {0}: {1}": "无法连接到 {0} 处的目标: {1}", "Cannot find `{0}` installed in {1}": "找不到在 {1} 中安装的 `{0}`", "Cannot find a program to debug": "找不到要调试的程序", "Cannot find test configuration with label `{0}`, got: {1}": "找不到标签为“{0}”的测试配置，已获取: {1}", "Cannot launch debug target in terminal ({0}).": "无法在终端启动调试目标({0})。", "Cannot restart asynchronous frame": "无法重启异步帧", "Cannot set an empty value": "无法设置空值", "Catch Block": "Catch 块", "Caught Exceptions": "捕获的异常", "Close AudioContext": "关闭 AudioContext", "Closure": "闭包", "Closure ({0})": "闭包({0})", "Console profile started": "已启动控制台配置文件", "Could not connect to any UWP Webview pipe. Make sure your webview is hosted in debug mode, and that the `pipeName` in your `launch.json` is correct.": "无法连接到任何 UWP Webview 管道。确保在调试模式下托管 Web 视图，并且 `launch.json` 中的 `pipeName` 正确无误。", "Could not find a location for the variable": "找不到变量的位置", "Could not query the provided object": "无法查询提供的对象", "Could not read source map for {0}: {1}": "无法读取 {0} 的源映射: {1}", "Could not read {0}: {1}": "无法读取 {0}: {1}", "Create AudioContext": "创建 AudioContext", "Create canvas context": "创建画布上下文", "Debug Anyway": "仍然调试", "Debug URL": "调试 URL", "Details": "详细信息", "Don't show again": "不再显示", "Duration": "持续时间", "Duration of Profile": "配置文件的持续时间", "Edit XHR Breakpoint": "编辑 XHR 断点", "Edit package.json": "编辑 package.json", "Enables Node.js [auto attach]({0}) debugging in \"{1}\" mode/{Locked='[auto attach]({0})'}the 2nd placeholder is the setting value": "启用 Node.js [auto attach]({0})在“{1}”模式中调试", "Enter a URL or a pattern to match": "输入要匹配的 URL 或模式", "Eval": "Eval", "Frame could not be restarted": "无法重启框架", "Generates a .cpuprofile file you can open in VS Code or the Edge/Chrome devtools": "生成可以在 VS Code 或 Edge/Chrome 开发工具中打开的 .cpuprofile 文件", "Generates a .heapprofile file you can open in VS Code or the Edge/Chrome devtools": "生成可以在 VS Code 或 Edge/Chrome 开发工具中打开的 .heapprofile 文件", "Generates a .heapsnapshot file you can open in VS Code or the Edge/Chrome devtools": "生成可以在 VS Code 或 Edge/Chrome 开发工具中打开的 .heapsnapshot 文件", "Global": "全局", "Globals": "全局", "Got it!": "知道了!", "Heap Profile": "堆配置文件", "Heap Snapshot": "堆快照", "How long to run the profile": "配置文件的运行时长", "Ignore": "忽略", "Installation complete! The extension will be used after you restart your debug session.": "安装完成! 重新启动调试会话后，将使用该扩展。", "Installing the DWARF debugger...": "正在安装 DWARF 调试程序...", "Invalid expression": "表达式无效", "Invalid hit condition \"{0}\". Expected an expression like \"> 42\" or \"== 2\".": "命中条件“{0}”无效。应输入表达式，如 \"> 42\" 或 \"== 2\"。", "It looks like a browser is already running from {0}. Please close it before trying to debug, otherwise VS Code may not be able to connect to it.": "似乎已从 {0} 运行了浏览器。请在尝试调试前关闭它，否则 VS Code 可能无法连接它。", "It looks like your debug session has already ended. Try debugging again, then run the \"Debug: Diagnose Breakpoint Problems\" command.": "你的调试会话似乎已结束。请尝试重新调试，然后运行“调试: 诊断断点问题”命令。", "It's taking a while to configure your breakpoints. You can speed this up by updating the 'outFiles' in your launch.json.": "配置断点需要一段时间。你可通过更新 launch.json 中的 \"outFiles\" 来加快速度。", "JavaScript Debug Terminal": "JavaScript 调试终端", "JavaScript debug adapter": "JavaScript 调试适配器", "Launch Chrome against localhost": "针对 localhost 启动 Chrome", "Launch Edge against localhost": "针对 localhost 启动 Edge", "Launch Program": "启动程序", "Launch configuration created based on 'package.json'.": "已根据 \"package.json\" 生成启动配置。", "Launch configuration for '{0}' project created.": "已创建“{0}”项目的启动配置。", "Local": "本地", "Locals": "本地", "Lost connection to debugee, reconnecting in {0}ms\r\n": "已断开与调试对象的连接，将在 {0} 毫秒后重新连接\r\n", "Manual": "手动", "Missing source information. Did you set \"originalUrl\" or \"source\"?": "缺少源信息。是否设置了 “originalUrl” 或 “source”？", "Module": "模块", "Networking not available.": "网络不可用。", "Never": "从不", "No": "否", "No npm scripts found in the workspace folder.": "在工作区文件夹中找不到 npm 脚本。", "No npm scripts found in your package.json": "在 package.json 中找不到 npm 脚本", "No package.json files found in your workspace.": "在工作区中找不到 package.json 文件。", "No workspace folder open.": "未打开工作区文件夹。", "Node Attributes": "节点特性", "Node.js version '{0}' not installed using version manager {1}.": "未使用版本管理器 {1} 安装 Node.js 版本“{0}”。", "Not Now": "以后再说", "Only objects can be queried": "只能查询对象", "Open launch.json": "打开 launch.json", "Output has been truncated to the first {0} characters. Run `{1}` to copy the full output.": "输出被截断为前 {0} 个字符。运行“{1}”以复制完整输出。", "Parameters": "参数", "Paused": "已暂停", "Paused before Out Of Memory exception": "在出现内存不足异常之前暂停", "Paused on Content Security Policy violation instrumentation breakpoint, directive \"{0}\"": "因内容安全策略违规检测断点而暂停，指令“{0}”", "Paused on DOM breakpoint": "因 DOM 断点暂停", "Paused on WebGL Error instrumentation breakpoint, error \"{0}\"": "因 WebGL 错误检测断点而暂停，错误为“{0}”", "Paused on XMLHttpRequest or fetch": "在 XMLHttpRequest 或提取时暂停", "Paused on assert": "已在断言时暂停", "Paused on breakpoint": "已在断点处暂停", "Paused on debug() call": "在 debug() 调用时暂停", "Paused on debugger statement": "暂停于调试器语句", "Paused on event listener": "因事件侦听器暂停", "Paused on event listener breakpoint \"{0}\", triggered on \"{1}\"": "因事件侦听器断点“{0}”而暂停，于“{1}”触发", "Paused on exception": "已在异常时暂停", "Paused on frame entry": "暂停于框架条目", "Paused on instrumentation breakpoint": "因检测断点暂停", "Paused on instrumentation breakpoint \"{0}\"": "因检测断点“{0}”而暂停", "Paused on {0}": "因 {0} 已暂停", "Pick Breakpoint": "选取断点", "Pick the node.js process to attach to": "选择要附加到的 Node.js 进程", "Please enter a number": "请输入数字", "Please enter a number greater than 1": "请输入一个大于 1 的数字", "Please stop the running profile before starting a new one.": "请在启动新的配置文件之前停止正在运行的配置文件。", "Process picker failed ({0})": "进程选取器失败 ({0})", "Profile duration in seconds, e.g \"5\"": "配置文件持续时间(以秒为单位)，例如 \"5\"", "Profiling": "分析", "Profiling with breakpoints enabled can change the performance of your code. It can be useful to validate your findings with the \"duration\" or \"manual\" termination conditions.": "在启用断点的情况下分析可能会更改代码的性能。使用“持续时间”或“手动”终止条件验证发现的结果可能很有用。", "Read More": "阅读更多", "Request Animation Frame": "请求动画帧", "Resume AudioContext": "恢复 AudioContext", "Return value": "返回值", "Run Current File": "运行当前文件", "Run Node.js tool": "运行 Node.js 工具", "Run Script: {0}": "运行脚本: {0}", "Run for a specific amount of time": "运行特定时间", "Run until a specific breakpoint is hit": "运行直到命中特定断点为止", "Run until manually stopped": "运行直到手动停止为止", "Runs a Node.js command-line installed in the workspace node_modules.": "运行工作区 node_modules 中安装的 Node.js 命令行。", "Saving": "正在保存", "Script": "脚本", "Script Blocked by Content Security Policy": "脚本受到内容安全策略阻止", "Script First Statement": "编写第一条语句的脚本", "Select a tab": "选择选项卡", "Select a tool to run": "选择要运行的工具", "Select current working directory for new terminal": "选择当前工作目录新建终端", "Select test configuration to run": "选择要运行的测试配置", "Select the page where you want to open the devtools": "选择要在其中打开开发工具的页面", "Select the session you want to inspect:": "选择要检查的会话:", "Seller Reporting Phase Start": "卖家报告阶段开始", "Seller Scoring Phase Start": "卖家评分阶段开始", "Set innerHTML": "设置 innerHTML", "Skipped by skipFiles": "已由 skipFiles 跳过", "Skipped by smartStep": "已由 smartStep 跳过", "Some breakpoints might not work in your version of Node.js. We recommend upgrading for the latest bug, performance, and security fixes. Details: https://aka.ms/AAcsvqm": "在你的 Node.js 版本中，某些断点可能不起作用。我们建议升级以获取最新的 bug、性能和安全修复。详细信息: https://aka.ms/AAcsvqm", "Source not a source map": "源不是源映射", "Source not found": "未找到源", "Stack frame not found": "未找到堆栈帧", "Starting profile...": "正在启动配置文件...", "Stopping profile...": "正在停止配置文件...", "Suspend AudioContext": "暂停 AudioContext", "Syntax error setting breakpoint with condition {0} on line {1}: {2}": "在第 {1} 行上设置带条件 {0} 的断点时出现语法错误: {2}", "Target page not found. You may need to update your \"urlFilter\" to match the page you want to debug.": "找不到目标页。若要匹配想要调试的页面，可能需要更新 \"urlFilter\"。", "The Node version in \"{0}\" is outdated (version {1}), we require at least Node 8.x.": "“{0}”中的 Node 版本已过时(版本 {1})，我们至少需要 Node 8.x。", "The URL provided is invalid": "提供的 URL 无效", "The browser process exited with code {0} before connecting to the debug server. Make sure the `runtimeExecutable` is configured correctly and that it can run without errors.": "在连接到调试服务器之前，浏览器进程已退出，代码为 {0}。请确保 `runtimeExecutable` 配置正确，并且可以运行而不发生错误。", "The configured `cwd` {0} does not exist.": "配置的 “cwd” {0} 不存在。", "The configured `cwd` {0} is not a folder.": "配置的 \"cwd\" {0} 不是文件夹。", "This is a missing file path referenced by a sourcemap. Would you like to debug the compiled version instead?": "这是由源映射引用的缺失的文件路径。是否要改为调试编译版本?", "Thread is not paused": "线程未暂停", "Thread is not paused on exception": "出现异常时线程未暂停", "Thread not found": "找不到线程", "Type of profile": "配置文件的类型", "URL contains \"{0}\"": "URL 包含“{0}”", "UWP webview debugging is not available on your platform.": "UWP Webview 调试在平台上不可用。", "Unable to attach to browser": "无法附加到浏览器", "Unable to evaluate": "无法计算", "Unable to evaluate on async stack frame": "无法计算异步堆栈帧", "Unable to find an installation of the browser on your system. Try installing it, or providing an absolute path to the browser in the \"runtimeExecutable\" in your launch.json.": "在你的系统上找不到浏览器的安装。请尝试安装它，或者在 launch.json 的 “runtimeExecutable” 中提供浏览器的绝对路径。", "Unable to find {0} version {1}. Available auto-discovered versions are: {2}. You can set the \"runtimeExecutable\" in your launch.json to one of these, or provide an absolute path to the browser executable.": "找不到 {0} 版本 {1}。可用的自动发现的版本包括: {2}。你可将 launch.json 中的 \"runtimeExecutable\" 设置为其中一个，或者提供浏览器可执行文件的绝对路径。", "Unable to launch browser: \"{0}\"": "无法启动浏览器:“{0}”", "Unable to pause": "无法暂停", "Unable to pretty print": "无法优质打印", "Unable to resume": "无法恢复", "Unable to retrieve source content": "无法检索源内容", "Unable to set variable value": "无法设置变量值", "Unable to step in": "无法进入子函数", "Unable to step next": "无法越过子函数", "Unable to step out": "无法跳出子函数", "Unbound breakpoint": "未绑定断点", "Uncaught Exceptions": "未捕获的异常", "Unknown error": "未知错误", "VS Code can provide better debugging experience for WebAssembly via \"DWARF Debugging\" extension. Would you like to install it?/\"DWARF Debugging\" is the extension name and should not be localized.": "VS Code 可以通过 \"DWARF 调试\" 为 WEBAssembly 提供更好的调试体验。是否要安装它?", "Variable not found": "找不到变量", "Variables not available in async stacks": "变量在异步堆栈中不可用", "WARNING: Processing source-maps of {0} took longer than {1} ms so we continued execution without waiting for all the breakpoints for the script to be set.": "警告: 处理 {0} 的源映射耗时超过 {1} 毫秒，因此我们继续执行，而不是等到脚本的所有断点都设置好。", "We can't launch a browser in debug mode from here. If you want to debug this webpage, open this workspace from VS Code on your desktop.": "无法从此处启动调试模式下的浏览器。若要调试此网页，请从桌面上的 VS Code 打开此工作区。", "We can't launch a browser in debug mode from here. Open this workspace in VS Code on your desktop to enable debugging.": "无法从此处启动调试模式下的浏览器。要启用调试，请在桌面上的 VS Code 中打开此工作区。", "WebGL Error Fired": "已触发 WebGL 错误", "WebGL Warning Fired": "已触发 WebGL 警告", "With Block": "包含块", "Would you like to save a configuration in your launch.json for easy access later?": "是否要将配置保存在 launch.json 中以便日后访问?", "XHR/Fetch URLs": "XHR/提取 URL", "Yes": "是", "You may install the `{}` module via npm for enhanced WebAssembly debugging": "可以通过 npm 安装 `{}` 模块以进行增强的 WebAssembly 调试", "You need to open a workspace folder to debug npm scripts.": "需要打开工作区文件夹来调试 npm 脚本。", "You're running an outdated version of Node.js. We recommend upgrading for the latest bug, performance, and security fixes.": "你正在运行的 Node.js 版本已过期。我们建议升级以获取最新的针对 bug、性能和安全的修复。", "an old debug session": "旧调试会话", "breakpoint.provisionalBreakpoint": "breakpoint.provisionalBreakpoint", "path does not exist": "路径不存在", "process id: {0} ({1})": "进程 ID: {0} ({1})", "process id: {0}, debug port: {1} ({2})": "进程 ID: {0}，调试端口: {1} ({2})", "setInterval fired": "已触发 setInterval", "setTimeout fired": "已触发 setTimeout", "the configured userDataDir": "已配置的 userDataDir", "{0} (couldn't describe: {1})": "{0} (无法描述: {1})", "{0} Click to Stop Profiling": "{0} 单击可停止分析", "{0} Click to Stop Profiling ({1} sessions)": "{0}单 击以停止分析({1} 会话)", "{0} Click to Stop Profiling ({1})": "{0} 单击可停止分析 ({1})"}}}