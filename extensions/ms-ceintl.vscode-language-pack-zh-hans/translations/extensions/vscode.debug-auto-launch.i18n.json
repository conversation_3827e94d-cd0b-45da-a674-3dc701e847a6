{"": ["--------------------------------------------------------------------------------------------", "Copyright (c) Microsoft Corporation. All rights reserved.", "Licensed under the MIT License. See License.txt in the project root for license information.", "--------------------------------------------------------------------------------------------", "Do not edit this file. It is machine generated."], "version": "1.0.0", "contents": {"bundle": {"Always": "总是", "Auto Attach: Always": "自动附加: 始终", "Auto Attach: Disabled": "自动附加: 已禁用", "Auto Attach: Smart": "自动附加: 智能", "Auto Attach: With Flag": "自动附加: 带标志", "Auto attach is disabled and not shown in status bar": "自动附加被禁用，且不在状态栏中显示", "Auto attach to every Node.js process launched in the terminal": "自动附加到终端中启动的每个 Node.js 进程", "Auto attach when running scripts that aren't in a node_modules folder": "运行 node_modules 文件夹中未包含的脚本时自动附加", "Automatically attach to node.js processes in debug mode": "在调试模式下自动附加到 Node.js 进程", "Debug Auto Attach": "调试自动附加", "Disabled": "已禁用", "Only With Flag": "仅带标志", "Only auto attach when the `--inspect` flag is given": "仅在给定 \"--inspect\" 标志时自动附加", "Re-enable auto attach": "重新启用自动附加", "Smart": "智能", "Temporarily disable auto attach in this session": "在此会话中暂时禁用自动附加", "Toggle auto attach in this workspace": "在此工作区中切换自动附加", "Toggle auto attach on this machine": "在此计算机上切换自动附加"}, "package": {"description": "当 node-debug 扩展未启用时提供自动附加的辅助程序。", "displayName": "Node 调试自动附加", "toggle.auto.attach": "切换开关自动附加"}}}