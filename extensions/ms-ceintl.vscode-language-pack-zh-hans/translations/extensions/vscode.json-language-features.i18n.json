{"": ["--------------------------------------------------------------------------------------------", "Copyright (c) Microsoft Corporation. All rights reserved.", "Licensed under the MIT License. See License.txt in the project root for license information.", "--------------------------------------------------------------------------------------------", "Do not edit this file. It is machine generated."], "version": "1.0.0", "contents": {"bundle": {"$ref '{0}' in '{1}' can not be resolved.": "无法解析“{1}”中的 $ref“{0}”。", "<empty>": "<empty>", "A default value. Used by suggestions.": "默认值。由建议使用。", "A descriptive title of the schema.": "架构的描述性标题。", "A long description of the schema. Used in hover menus and suggestions.": "对架构较长的描述。在悬停菜单和建议中使用。", "A map of property names to either an array of property names or a schema. An array of property names means the property named in the key depends on the properties in the array being present in the object in order to be valid. If the value is a schema, then the schema is only applied to the object if the property in the key exists on the object.": "属性名称到属性名称数组或架构的映射。属性名称数组表示键中命名的属性需要对象中存在该数组中的属性，它才能有效。如果该值是架构，则仅当键中的属性存在于对象上时，架构才会应用于对象。", "A map of property names to schemas for each property.": "属性名称到每个属性的架构的映射。", "A map of regular expressions on property names to schemas for matching properties.": "属性名的常规表达式映射到匹配属性的架构。", "A number that should cleanly divide the current value (i.e. have no remainder).": "应可被当前值完全整除的数字(即没有余数)。", "A regular expression to match the string against. It is not implicitly anchored.": "要与字符串匹配的正则表达式。它未隐式定位。", "A schema which must not match.": "不允许匹配的架构。", "A unique identifier for the schema.": "架构的唯一标识符。", "An array instance is valid against \"contains\" if at least one of its elements is valid against the given schema.": "如果数组实例中至少有一个元素对给定架构有效，则该实例对“contains”有效。", "An array of schemas, all of which must match.": "架构的数组，所有架构都必须匹配。", "An array of schemas, exactly one of which must match.": "架构的数组，其中必须只有一个匹配。", "An array of schemas, where at least one must match.": "架构数组，其中必须至少有一个匹配。", "An array of strings that lists the names of all properties required on this object.": "一个字符串数组，其中列出了此对象上所需的所有属性的名称。", "An instance validates successfully against this keyword if its value is equal to the value of the keyword.": "如果实例的值等于关键字的值，则此实例对此关键字的验证成功。", "Array does not contain required item.": "数组未包含所需项。", "Array has duplicate items.": "数组具有重复项。", "Array has too few items that match the contains contraint. Expected {0} or more.": "数组中有太少项与 contains 约束匹配。预期为 {0} 或更多。", "Array has too few items. Expected {0} or more.": "数组包含的项太少。预期为 {0} 或更多。", "Array has too many items according to schema. Expected {0} or fewer.": "根据架构，数组包含的项太多。预期为 {0} 或更少。", "Array has too many items that match the contains contraint. Expected {0} or less.": "数组中有太多项与 contains 约束匹配。预期为 {0} 或更少。", "Array has too many items. Expected {0} or fewer.": "数组包含的项太多。预期为 {0} 或更少。", "Colon expected": "需要冒号", "Comments are not permitted in JSON.": "JSON 中不允许有注释。", "Comments from schema authors to readers or maintainers of the schema.": "架构作者给架构的读者或维护者的注释。", "Configure": "配置", "Configured by extension: {0}": "已由扩展配置: {0}", "Configured in user settings": "已在用户设置中配置", "Configured in workspace settings": "已在工作区设置中配置", "Default value": "默认值", "Describes the content encoding of a string property.": "描述字符串属性的内容编码。", "Describes the format expected for the value. By default, not used for validation": "描述值预期的格式。默认情况下，不用于验证", "Describes the media type of a string property.": "描述字符串属性的媒体类型。", "Downloading schemas is disabled through setting '{0}'": "已通过设置“{0}”禁用下载架构", "Downloading schemas is disabled. Click to configure.": "已禁用下载架构。单击以进行配置。", "Draft-03 schemas are not supported.": "不支持 Draft-03 架构。", "Duplicate anchor declaration: '{0}'": "重复的定位点声明：“{0}”", "Duplicate object key": "重复的对象键", "Either a schema or a boolean. If a schema, used to validate all properties not matched by 'properties', 'propertyNames', or 'patternProperties'. If false, any properties not defined by the adajacent keywords will cause this schema to fail.": "架构或布尔值。如果是架构，则用于验证与“properties”、“propertyNames”或“patternProperties”不匹配的所有属性。如果为 false，则 adajacent 关键字未定义的任何属性都将导致此架构失败。", "Either a string of one of the basic schema types (number, integer, null, array, object, boolean, string) or an array of strings specifying a subset of those types.": "基本架构类型之一的字符串(数字、整数、null、数组、对象、布尔值、字符串)或指定这些类型的子集的字符串数组。", "End of file expected.": "预期为文件结尾。", "Expected a JSON object, array or literal.": "应为 JSON 对象、数组或文本。", "Expected comma": "缺少逗号", "Expected comma or closing brace": "预期为逗号或右大括号", "Expected comma or closing bracket": "应有逗号或右中括号", "Failed to sort the JSONC document, please consider opening an issue.": "无法对 JSONC 文档进行排序，请考虑打开问题。", "For arrays, only when items is set as an array. If items are a schema, this schema validates items after the ones specified by the items schema. If false, additional items will cause validation to fail.": "对于数组，仅当项设置为数组时。如果项是架构，则此架构将在项架构指定的项之后验证项。如果为 false，则其他项将导致验证失败。", "For arrays. Can either be a schema to validate every element against or an array of schemas to validate each item against in order (the first schema will validate the first element, the second schema will validate the second element, and so on.": "对于数组。可以是用于验证每个元素的架构，也可以是要根据顺序验证每个项的架构数组(第一个架构将验证第一个元素，第二个架构将验证第二个元素，依此类推)。", "If all of the items in the array must be unique. Defaults to false.": "数组中的所有项是否都必须是唯一的。默认为 false。", "If the instance is an object, this keyword validates if every property name in the instance validates against the provided schema.": "如果实例是对象，则此关键字将验证实例中的每个属性名称是否针对提供的架构进行了验证。", "Incorrect type. Expected \"{0}\".": "类型不正确。预期为 \"{0}\"", "Incorrect type. Expected one of {0}.": "类型不正确。应为 {0} 之一。", "Indicates that the value of the instance is managed exclusively by the owning authority.": "指示实例的值由拥有机构独占管理。", "Invalid characters in string. Control characters must be escaped.": "字符串中的字符无效。必须转义控制字符。", "Invalid color format. Use #RGB, #RGBA, #RRGGBB or #RRGGBBAA.": "颜色格式无效。请使用 #RGB、#RGBA、#RRGGBB 或 #RRGGBBAA。", "Invalid escape character in string.": "字符串中的转义字符无效。", "Invalid number format.": "数字格式无效。", "Invalid unicode sequence in string.": "字符串中的 unicode 序列无效。", "Item does not match any validation rule from the array.": "项与数组中的任何验证规则都不匹配。", "JSON Language Server": "JSON 语言服务器", "JSON Outline Status": "JSON 大纲状态", "JSON Validation Status": "JSON 验证状态", "JSON schema cache cleared.": "已清除 JSON 架构缓存。", "JSON schema configured": "已配置 JSON 架构", "JSON: Schema Resolution Error": "JSON: 架构解析错误", "Learn more about JSON schema configuration...": "详细了解 JSON 架构配置...", "Loading JSON info": "正在加载 JSON 信息", "Makes the maximum property exclusive.": "将最大属性设置为独占。", "Makes the minimum property exclusive.": "将最小属性设置为独占。", "Matches a schema that is not allowed.": "匹配不允许的架构。", "Matches multiple schemas when only one must validate.": "当只有一个架构必须验证时，匹配多个架构。", "Missing property \"{0}\".": "缺少属性 \"{0}\"。", "New array": "新数组", "New object": "新建对象", "No schema configured for this file": "未配置此文件的架构", "No schema validation": "无架构验证", "Not used for validation. Place subschemas here that you wish to reference inline with $ref.": "不用于验证。将要使用 $ref 内联引用的子架构放在此处。", "Object has fewer properties than the required number of {0}": "对象的属性少于所需的 {0}", "Object has more properties than limit of {0}.": "对象的属性数超过了 {0} 的限制。", "Object is missing property {0} required by property {1}.": "对象缺少属性 {0}，它是属性 {1} 必须的。", "Open Extension": "打开扩展", "Open Settings": "打开设置", "Outline": "大纲", "Problem reading content from '{0}': UTF-8 with BOM detected, only UTF 8 is allowed.": "从“{0}”读取内容时出现问题: 检测到带 BOM 的 UTF-8，只允许 UTF 8。", "Problems loading reference '{0}': {1}": "加载引用“{0}”时出现问题: {1}", "Property expected": "应为属性", "Property keys must be doublequoted": "属性键必须带双引号", "Property {0} is not allowed.": "不允许属性 {0}。", "Reference a definition hosted on any location.": "引用托管在任何位置的定义。", "Sample JSON values associated with a particular schema, for the purpose of illustrating usage.": "与特定架构关联的示例 JSON 值，用于说明用法。", "Schema not found: {0}": "找不到架构： {0}", "Schema validated": "已验证架构", "Select the schema to use for {0}": "选择要用于 {0} 的架构", "Show Schemas": "显示架构", "Sort JSON": "Sort JSON", "String does not match the pattern of \"{0}\".": "字符串与 \"{0}\" 的模式不匹配。", "String is longer than the maximum length of {0}.": "字符串长度超过 {0} 的最大长度。", "String is not a RFC3339 date-time.": "字符串不是 RFC3339 日期时间。", "String is not a RFC3339 date.": "字符串不是 RFC3339 日期。", "String is not a RFC3339 time.": "字符串不是 RFC3339 时间。", "String is not a URI: {0}": "字符串不是 URI: {0}", "String is not a hostname.": "字符串不是主机名。", "String is not an IPv4 address.": "字符串不是 IPv4 地址。", "String is not an IPv6 address.": "字符串不是 IPv6 地址。", "String is not an e-mail address.": "字符串不是电子邮件地址。", "String is shorter than the minimum length of {0}.": "字符串短于最小长度 {0}。", "The \"else\" subschema is used for validation when the \"if\" subschema fails.": "当“if”子架构失败时，使用“else”子架构进行验证。", "The \"then\" subschema is used for validation when the \"if\" subschema succeeds.": "当“if”子架构成功时，“then”子架构会用于验证。", "The maximum length of a string.": "字符串的最大长度。", "The maximum number of items that can be inside an array. Inclusive.": "可以位于数组内的最大项数(包含)。", "The maximum number of properties an object can have. Inclusive.": "对象可以具有的最大属性数(包含)。", "The maximum numerical value, inclusive by default.": "最大数值，默认为包含。", "The minimum length of a string.": "字符串的最小长度。", "The minimum number of items that can be inside an array. Inclusive.": "可以位于数组内的最小项数(包含)。", "The minimum number of properties an object can have. Inclusive.": "对象可以具有的最小属性数(包含)。", "The minimum numerical value, inclusive by default.": "最小数值，默认情况下包含。", "The schema to verify this document against.": "要针对其验证此文档的架构。", "The schema uses meta-schema features ({0}) that are not yet supported by the validator.": "该架构使用验证程序尚不支持的元架构功能({0})。", "The set of literal values that are valid.": "有效的文本值集。", "The validation outcome of the \"if\" subschema controls which of the \"then\" or \"else\" keywords are evaluated.": "“if”子架构的验证结果控制要评估哪一个“then”或“else”关键字。", "Trailing comma": "尾随逗号", "URI expected.": "预期为 URI。", "URI is expected.": "需要 URI。", "URI with a scheme is expected.": "需要包含架构的 URI。", "Unable to compute used schemas: No document": "无法计算使用的架构: 无文档", "Unable to compute used schemas: {0}": "无法计算使用的架构: {0}", "Unable to load schema from '{0}'. No schema request service available": "无法从“{0}”加载架构。没有可用的架构请求服务", "Unable to load schema from '{0}': No content.": "无法从“{0}”加载架构: 没有内容。", "Unable to load schema from '{0}': {1}.": "无法从“{0}”加载架构: {1}。", "Unable to load {0}": "无法加载 {0}", "Unable to parse content from '{0}': Parse error at offset {1}.": "无法分析“{0}”中的内容: 在偏移 {1} 处发生分析错误。", "Unable to resolve schema. Click to retry.": "无法解析架构。单击以重试。", "Unexpected end of comment.": "意外的注释结尾。", "Unexpected end of number.": "意外的数字结尾。", "Unexpected end of string.": "意外的字符串结尾。", "Value expected": "需要值", "Value is above the exclusive maximum of {0}.": "值高于独占最大值 {0}。", "Value is above the maximum of {0}.": "值大于最大值 {0}。", "Value is below the exclusive minimum of {0}.": "值低于 {0} 的独占最小值。", "Value is below the minimum of {0}.": "值低于最小值 {0}。", "Value is deprecated": "值已弃用", "Value is not accepted. Valid values: {0}.": "值不被接受。有效值: {0}。", "Value is not divisible by {0}.": "值不能被 {0} 整除。", "Value must be {0}.": "值必须为 {0}", "multiple JSON schemas configured": "已配置多个 JSON 架构", "no JSON schema configured": "未配置任何 JSON 架构", "only {0} document symbols shown for performance reasons": "仅 {0} 出于性能原因而显示的文档符号", "{0} is a directory, not a file": "{0} 是目录，而不是文件"}, "package": {"description": "为 JSON 文件提供丰富的语言支持", "displayName": "JSON 语言功能", "json.clickToRetry": "单击以重试。", "json.colorDecorators.enable.deprecationMessage": "已弃用设置 \"json.colorDecorators.enable\"，请改用 \"editor.colorDecorators\"。", "json.colorDecorators.enable.desc": "启用或禁用颜色修饰器", "json.command.clearCache": "清除架构缓存", "json.command.sort": "排序文档", "json.enableSchemaDownload.desc": "启用后，可以从 http 和 https 位置提取 JSON 架构。", "json.format.enable.desc": "启用或禁用默认 JSON 格式化程序。", "json.format.keepLines.desc": "设置格式时保留所有现有新行。", "json.maxItemsComputed.desc": "计算的大纲符号和折叠区域的最大数量(因性能原因而受限)。", "json.maxItemsExceededInformation.desc": "当超出分级显示符号和折叠区域的最大数目时显示通知。", "json.schemaResolutionErrorMessage": "无法解析架构。", "json.schemas.desc": "将架构关联到当前项目中的 JSON 文件。", "json.schemas.fileMatch.desc": "将 JSON 文件解析为架构时要与之匹配的文件模式数组。\"*\" 和 '**' 可用作通配符。也可定义排除模式，并以 \"!\" 开头。当至少有一个匹配模式，且最后一个匹配模式不是排除模式时，文件匹配。", "json.schemas.fileMatch.item.desc": "将 JSON 文件解析为架构时，可以包含要与之匹配的 “*” 和 “**” 的文件模式。以 '!' 开头时，它定义排除模式。", "json.schemas.schema.desc": "给定 URL 的架构定义。仅当要避免访问架构 URL 时需要提供架构。", "json.schemas.url.desc": "架构的 URL 或绝对文件路径。可以是工作区和工作区文件夹设置中的相对路径(以“./”开头)。", "json.tracing.desc": "跟踪 VS Code 和 JSON 语言服务器之间的通信。", "json.validate.enable.desc": "启用/禁用 JSON 验证。"}}}