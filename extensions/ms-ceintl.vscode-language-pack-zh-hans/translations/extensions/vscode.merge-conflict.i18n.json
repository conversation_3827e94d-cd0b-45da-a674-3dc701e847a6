{"": ["--------------------------------------------------------------------------------------------", "Copyright (c) Microsoft Corporation. All rights reserved.", "Licensed under the MIT License. See License.txt in the project root for license information.", "--------------------------------------------------------------------------------------------", "Do not edit this file. It is machine generated."], "version": "1.0.0", "contents": {"bundle": {"(Current Change)": "(当前更改)", "(Incoming Change)": "(传入的更改)", "Accept Both Changes": "保留双方更改", "Accept Current Change": "采用当前更改", "Accept Incoming Change": "采用传入的更改", "Compare Changes": "比较变更", "Editor cursor is not within a merge conflict": "编辑器光标不在合并冲突内", "Editor cursor is within the common ancestors block, please move it to either the \"current\" or \"incoming\" block": "编辑器光标在共同来源块上，请将其移动至“当前”或“传入”区域中", "Editor cursor is within the merge conflict splitter, please move it to either the \"current\" or \"incoming\" block": "编辑器光标在合并冲突分割线上，请将其移动至“当前”或“传入”区域中", "No merge conflicts found in this file": "没有在此文件中找到合并冲突", "No other merge conflicts within this file": "此文件中没有其他合并冲突了", "{0}: Current Changes ↔ Incoming Changes": "{0}: 当前更改 ↔ 传入的更改"}, "package": {"command.accept.all-both": "全部保留两者", "command.accept.all-current": "全部采用当前内容", "command.accept.all-incoming": "全部采用传入版本", "command.accept.both": "保留两者", "command.accept.current": "采用当前内容", "command.accept.incoming": "采用传入内容", "command.accept.selection": "采用选中版本", "command.category": "合并冲突", "command.compare": "比较当前冲突", "command.next": "下一个冲突", "command.previous": "上一个冲突", "config.autoNavigateNextConflictEnabled": "是否在解决合并冲突后自动转到下一个合并冲突。", "config.codeLensEnabled": "为编辑器中的合并冲突区域创建 CodeLens。", "config.decoratorsEnabled": "为编辑器中的合并冲突区域创建提示小标。", "config.diffViewPosition": "控件在比较合并冲突中的更改时应在何处打开差异视图。", "config.diffViewPosition.below": "在当前编辑器组下方打开差异视图。", "config.diffViewPosition.beside": "在当前编辑器组旁边打开差异视图。", "config.diffViewPosition.current": "在当前的编辑器组中打开差异视图。", "config.title": "合并冲突", "description": "为内联合并冲突提供高亮和命令。", "displayName": "合并冲突"}}}