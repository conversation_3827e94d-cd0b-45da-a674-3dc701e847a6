{"": ["--------------------------------------------------------------------------------------------", "Copyright (c) Microsoft Corporation. All rights reserved.", "Licensed under the MIT License. See License.txt in the project root for license information.", "--------------------------------------------------------------------------------------------", "Do not edit this file. It is machine generated."], "version": "1.0.0", "contents": {"bundle": {"Auto detecting Jake for folder {0} failed with error: {1}', this.workspaceFolder.name, err.error ? err.error.toString() : 'unknown": "自动检测文件夹 {0} 的 Jake 失败，错误为: {1}', this.workspaceFolder.name, err.error ? err.error.toString() : 'unknown", "Go to output": "转到输出", "Problem finding jake tasks. See the output for more information.": "查找 jake 任务时出现问题。有关详细信息，请查看输出。"}, "package": {"config.jake.autoDetect": "Jake 任务检测的控制启用。Jake 任务检测可能会导致执行任何打开的工作区中的文件。", "description": "向 VS Code 提供 Jake 功能的扩展。", "displayName": "适用于 VS Code 的 Jake 支持", "jake.taskDefinition.file.description": "提供任务的 Jake 文件。可以省略。", "jake.taskDefinition.type.description": "要自定义的 Jake 任务。"}}}