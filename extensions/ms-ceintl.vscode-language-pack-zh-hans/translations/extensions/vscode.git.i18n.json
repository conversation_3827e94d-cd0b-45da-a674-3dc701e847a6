{"": ["--------------------------------------------------------------------------------------------", "Copyright (c) Microsoft Corporation. All rights reserved.", "Licensed under the MIT License. See License.txt in the project root for license information.", "--------------------------------------------------------------------------------------------", "Do not edit this file. It is machine generated."], "version": "1.0.0", "contents": {"bundle": {"\n\nAre you sure you want to discard ALL changes in {0} files?": "是否确实要放弃 {0} 文件中的所有更改？", "\n\nAre you sure you want to discard changes in '{0}'?": "\n\n是否确实要放弃“{0}”中的更改?", "\"{0}\" has fingerprint \"{1}\"": "\"{0}\" 具有指纹 \"{1}\"", "$(info) Remote \"{0}\" has no tags.": "$(info)远程库“{0}”没有标记。", "$(info) This repository has no stashes.": "$(info) 此存储库没有储藏条目。", "$(info) This repository has no tags.": "$(info) 此仓库没有标记。", "A branch named \"{0}\" already exists": "已存在名为“{0}”的分支", "A git repository was found in the parent folders of the workspace or the open file(s). Would you like to open the repository?": "在工作区的父文件夹或打开的文件中找到了 git 存储库。是否要打开存储库?", "Absolute paths not supported in \"git.scanRepositories\" setting.": "“git.scanRepositories” 设置中不支持绝对路径。", "Add Remote": "添加远程存储库", "Add a new remote...": "添加一个新远程...", "Add remote from URL": "从 URL 添加远程存储库", "Add remote from {0}": "从 {0} 添加远程存储库", "Add to Workspace": "添加到工作区", "All Repositories": "所有存储库", "Always": "始终", "Always Pull": "始终拉取", "Always Replace Local Tag(s)": "始终替换本地标记", "Are you sure you want to DELETE the following untracked file: '{0}'?{1}": "是否确实要删除以下未跟踪的文件： '{0}'？{1}", "Are you sure you want to DELETE the {0} untracked files?{1}": "是否确实要删除 {0} 个未跟踪的文件? {1}", "Are you sure you want to continue connecting?": "确定要继续连接吗?", "Are you sure you want to create an empty commit?": "是否确定要创建空提交?", "Are you sure you want to discard ALL changes in {0} files?\n\nThis is IRREVERSIBLE!\nYour current working set will be FOREVER LOST if you proceed.": "是否确实要放弃 {0} 个文件中的全部更改?\n\n此操作不可撤消!\n如果继续操作，你当前的工作集将永久丢失。", "Are you sure you want to discard changes in '{0}'?": "是否确实要放弃“{0}”中的更改?", "Are you sure you want to drop ALL stashes? There are {0} stashes that will be subject to pruning, and MAY BE IMPOSSIBLE TO RECOVER.": "是否确实要删除所有储藏? 其中有 {0} 个储藏将会受到修剪，并且可能无法恢复。", "Are you sure you want to drop ALL stashes? There is 1 stash that will be subject to pruning, and MAY BE IMPOSSIBLE TO RECOVER.": "是否确实要删除所有储藏? 其中有 1 个储藏将会受到修剪，并且可能无法恢复。", "Are you sure you want to drop the stash: {0}?": "确定要删除储藏 {0} 吗?", "Are you sure you want to restore '{0}'?": "是否确实要还原“{0}”?", "Are you sure you want to restore ALL {0} files?": "是否确定要还原全部 {0} 个文件?", "Are you sure you want to stage {0} files with merge conflicts?": "确定要暂存含有合并冲突的 {0} 个文件吗?", "Are you sure you want to stage {0} with merge conflicts?": "确定要暂存含有合并冲突的 {0} 吗?", "Ask Me Later": "稍后询问", "Branch \"{0}\" already exists": "分支“{0}”已存在", "Branch name": "分支名称", "Branch name needs to match regex: {0}": "分支名称必须匹配正则表达式: {0}", "Can't force push refs to remote. The tip of the remote-tracking branch has been updated since the last checkout. Try running \"Pull\" first to pull the latest changes from the remote branch first.": "无法将引用强制推送到远程。自上次签出后，远程跟踪分支的提示已更新。首先尝试运行“拉取”以从远程分支中拉取最新更改。", "Can't push refs to remote. Try running \"Pull\" first to integrate your changes.": "无法推送 refs 到远端。您可以试着运行“拉取”功能，整合您的更改。", "Can't undo because HEAD doesn't point to any commit.": "无法撤消，因为 HEAD 不指向任何提交。", "Changes": "更改", "Checking Out Branch/Tag...": "正在签出分支/标记...", "Checking Out Changes...": "正在签出更改...", "Checkout Branch/Tag...": "签出分支/标记...", "Choose Folder...": "选择文件夹...", "Choose a folder to clone {0} into": "选择文件夹以将 {0} 克隆到", "Choose a repository": "选择仓库", "Choose which repository to clone": "选择要克隆的存储库", "Choose which repository to publish": "选择要发布的存储库", "Clear whitespace characters": "清除空格字符", "Clone from URL": "存储库 URL", "Clone from {0}": "从 {0} 克隆", "Cloning git repository \"{0}\"...": "正在克隆 Git 仓库“{0}”...", "Commit": "提交", "Commit & Push Changes": "提交和推送更改", "Commit & Sync Changes": "提交和同步更改", "Commit Anyway": "仍要提交", "Commit Changes": "提交更改", "Commit Changes on \"{0}\"": "提交对“{0}”的更改", "Commit Changes to New Branch": "将更改提交到新分支", "Commit Hash": "提交哈希", "Commit message": "提交消息", "Commit operation was cancelled due to empty commit message.": "由于提交消息为空，已取消提交操作。", "Commit to New Branch & Push Changes": "提交到新分支并推送更改", "Commit to New Branch & Synchronize Changes": "提交到新分支并同步更改", "Commit to a New Branch": "提交到新分支", "Commits without verification are not allowed, please enable them with the \"git.allowNoVerifyCommit\" setting.": "不允许在未验证的情况下提交，请使用 \"git.allowNoVerifyCommit\" 设置启用这些提交。", "Committing & Pushing Changes...": "正在提交和推送更改...", "Committing & Synchronizing Changes...": "正在提交和同步更改...", "Committing Changes to New Branch...": "正在将更改提交到新分支...", "Committing Changes...": "正在提交更改...", "Committing to New Branch & Pushing Changes...": "正在提交到新分支并推送更改...", "Committing to New Branch & Synchronizing Changes...": "正在提交到新分支并同步更改...", "Conflict: Added By Them": "冲突: 已由他们添加", "Conflict: Added By Us": "冲突: 已由我们添加", "Conflict: Both Added": "冲突: 两个都已添加", "Conflict: Both Deleted": "冲突: 两个都已删除", "Conflict: Both Modified": "冲突: 两个都已修改", "Conflict: Deleted By Them": "冲突: 已由他们删除", "Conflict: Deleted By Us": "冲突: 已由我们删除", "Continue Merge": "继续合并", "Continue Rebase": "继续变基", "Continuing Merge...": "正在继续合并...", "Continuing Rebase...": "正在继续变基...", "Copy Commit Hash": "复制提交哈希", "Could not clone your repository as Git is not installed.": "无法克隆存储库，因为未安装 Git。", "Create Empty Commit": "创建空提交", "Current": "当前", "Current commit message only contains whitespace characters": "当前提交消息仅包含空白字符", "Delete All {0} Files": "删除所有 {0} 文件", "Delete Branch": "删除分支", "Delete File": "删除文件", "Deleted": "已删除", "Discard 1 Tracked File": "放弃 1 个已跟踪的文件", "Discard All {0} Files": "放弃所有 {0} 个文件", "Discard All {0} Tracked Files": "放弃所有 {0} 个跟踪的文件", "Discard File": "放弃文件", "Don't Pull": "不拉取", "Don't Show Again": "不再显示", "Download Git": "下载 Git", "Email": "电子邮件", "Enables the following features: {0}": "启用以下功能: {0}", "Failed to authenticate to git remote.": "未能对 git remote 进行身份验证。", "Failed to authenticate to git remote:\n\n{0}": "未能对 git remote 进行身份验证:\n\n{0}", "File \"{0}\" was deleted by them and modified by us.\n\nWhat would you like to do?": "文件“{0}”已被他们删除且已经过我们修改。\n\n你想要执行什么操作?", "File \"{0}\" was deleted by us and modified by them.\n\nWhat would you like to do?": "文件“{0}”已被我们删除且已经过他们修改。\n\n你想要执行什么操作?", "Force Checkout": "强制签出", "Force push is not allowed, please enable it with the \"git.allowForcePush\" setting.": "不允许强制推送，请启用 \"git. allowForcePush\" 设置。", "Git Blame Information": "Git 责任信息", "Git History": "Git 历史记录", "Git Local Changes (Index)": "Git 本地更改(索引)", "Git Local Changes (Working Tree)": "Git 本地更改(工作树)", "Git error": "Git 错误", "Git not found. Install it or configure it using the \"git.path\" setting.": "未找到 Git。请安装 Git，或在 \"git.path\" 设置中配置。", "Git repositories were found in the parent folders of the workspace or the open file(s). Would you like to open the repositories?": "在工作区的父文件夹或打开的文件中找到了 Git 存储库。是否要打开存储库?", "Git: {0}": "Git: {0}", "HEAD version of \"{0}\" is not available.": "“{0}”的 HEAD 版本不可用。", "Hard wrap all lines": "所有行硬换行", "Hard wrap line": "硬换行", "Ignored": "已忽略", "Incoming": "传入", "Incoming Changes": "传入的更改", "Incoming Changes (added)": "传入的更改(已添加)", "Incoming Changes (deleted)": "传入的更改(已删除)", "Incoming Changes (modified)": "传入的更改(已修改)", "Incoming Changes (renamed)": "传入的更改(已重命名)", "Index Added": "已添加索引", "Index Copied": "已复制索引", "Index Deleted": "已删除索引", "Index Modified": "已修改索引", "Index Renamed": "已重命名索引", "Initialize Repository": "初始化仓库", "Intent to Add": "打算添加", "Intent to Rename": "打算重命名", "Invalid branch name": "分支名称无效", "It looks like the current branch \"{0}\" might have been rebased. Are you sure you still want to pull into it?": "当前分支“{0}”似乎已变基。确定仍要拉取到其中吗?", "It looks like the current branch might have been rebased. Are you sure you still want to pull into it?": "当前分支似乎已变基。确定仍要拉取到其中吗?", "It's not possible to change the commit message in the middle of a rebase. Please complete the rebase operation and use interactive rebase instead.": "无法在变基过程中修改提交消息。请完成变基操作，并改用交互式变基。", "Keep Our Version": "保留“我们”的版本", "Keep Their Version": "保留“他们”的版本", "Learn More": "了解详细信息", "Make sure you configure your \"user.name\" and \"user.email\" in git.": "请确保已在 Git 中配置你的 \"user.name\" 和 \"user.email\"。", "Manage Unsafe Repositories": "管理不安全的存储库", "Merge Changes": "合并更改", "Message": "消息", "Message (commit on \"{0}\")": "消息(在 \"{0}\" 上提交)", "Message ({0} to commit on \"{1}\")": "消息({0} 在“{1}”提交)", "Message ({0} to commit)": "消息({0} 待提交)", "Migrate Changes": "迁移更改", "Modified": "已修改", "Move to Recycle Bin": "移动到回收站", "Move to Trash": "移动到回收站", "Never": "从不", "No": "否", "No rebase in progress.": "没有正在进行的变基。", "Not Committed Yet": "尚未提交", "Not Committed Yet (Staged)": "尚未提交(暂存)", "OK": "确定", "OK, Don't Ask Again": "确定，且不再询问", "OK, Don't Show Again": "确定，且不再显示", "Open": "打开", "Open Commit": "打开提交", "Open Comparison": "打开比较", "Open File": "打开文件", "Open Git Log": "打开 GIT 日志", "Open Merge": "打开合并", "Open Repositories In Parent Folders": "在父文件夹中打开存储库", "Open Repository": "打开仓库", "Open Settings": "打开“设置”", "Open in New Window": "在新窗口中打开", "Optionally provide a stash message": "提供储藏消息(可选)", "Passphrase": "密码", "Pick a branch to pull from": "选择拉取的来源分支", "Pick a provider to publish the branch \"{0}\" to:": "选取提供程序以将分支“{0}”发布到:", "Pick a remote to publish the branch \"{0}\" to:": "选取要将分支“{0}”发布到的远程:", "Pick a remote to pull the branch from": "选择要拉取的源远程分支", "Pick a remote to remove": "选择要删除的远程库", "Pick a repository to mark as safe and open": "选择要标记为安全并打开的存储库", "Pick a repository to open": "选择要打开的存储库", "Pick a repository to reopen": "选择要重新打开的存储库", "Pick a stash to apply": "选择要应用的储藏", "Pick a stash to drop": "选择要删除的储藏", "Pick a stash to pop": "选择要弹出的储藏", "Pick a stash to view": "选择要查看的储藏条目", "Pick workspace folder to initialize git repo in": "选择用于初始化 Git 储存库的工作区文件夹", "Please check out a branch to push to a remote.": "请签出一个分支以推送到远程。", "Please clean your repository working tree before checkout.": "在签出前，请清理仓库工作树。", "Please provide a commit message": "请提供提交消息", "Please provide a message to annotate the tag": "请提供消息以对标记进行注释", "Please provide a new branch name": "请提供新的分支名称", "Please provide a remote name": "请提供远程存储库名称", "Please provide a tag name": "已成功带标记进行推送。", "Please provide the commit hash": "请提供提交哈希", "Publish Branch": "发布分支", "Publish Branch \"{0}\"/{Locked=\"Branch\"}Do not translate \"Branch\" as it is a git term": "发布 Branch“{0}”", "Publish Branch/{Locked=\"Branch\"}Do not translate \"Branch\" as it is a git term": "发布 Branch", "Publish to {0}": "发布到 {0}", "Publish to...": "发布到...", "Publishing Branch \"{0}\".../{Locked=\"Branch\"}Do not translate \"Branch\" as it is a git term": "正在发布 Branch“{0}”...", "Publishing Branch.../{Locked=\"Branch\"}Do not translate \"Branch\" as it is a git term": "正在发布 Branch...", "Pull": "拉取", "Pull {0} and push {1} commits between {2}/{3}": "在 {2}/{3} 之间拉取 {0} 个提交并推送 {1} 个提交", "Pull {0} commits from {1}/{2}": "从 {1}/{2} 拉取 {0} 个提交", "Push {0} commits to {1}/{2}": "将 {0} 个提交推送到 {1}/{2}", "Rebasing": "正在变基", "Regenerate Branch Name": "重新生成分支名称", "Remote \"{0}\" already exists.": "远程存储库“{0}”已存在。", "Remote branch at {0}": "{0} 处的远程分支", "Remote name": "远程存储库名称", "Remote name format invalid": "远程仓库名称格式无效", "Remote tag at {0}": "{0} 处的远程标记", "Reopen Closed Repositories": "重新打开已关闭的存储库", "Replace Local Tag(s)": "替换本地标记", "Restore All {0} Files": "还原所有 {0} 文件", "Restore File": "还原文件", "Save All & Commit Changes": "保存所有和提交更改", "Save All & Stash": "全部保存并储藏", "Select a branch or tag to checkout": "选择要签出的分支或标记", "Select a branch or tag to merge from": "选择要合并的分支或标记", "Select a branch to checkout in detached mode": "选择要在分离模式下签出的分支", "Select a branch to delete": "选择要删除的分支", "Select a branch to rebase onto": "选择要变基到的分支", "Select a ref to create the branch from": "选择一个 ref 以从中创建分支", "Select a remote branch to delete": "选择要删除的远程分支", "Select a remote tag to delete": "选择要删除的标记", "Select a remote to delete a tag from": "选择要从中删除标记的远程库", "Select a remote to fetch": "选择要提取的远程", "Select a tag to delete": "选择要删除的标记", "Select as Repository Destination": "选择为存储库目标", "Show Changes": "显示更改", "Show Command Output": "显示命令输出", "Staged Changes": "暂存的更改", "Stash & Checkout": "储藏并签出", "Stash Anyway": "仍要储藏", "Stash message": "储藏消息", "Stashed Changes": "隐藏的更改", "Successfully pushed.": "已成功推送。", "Synchronize Changes": "同步更改", "Synchronizing Changes...": "正在同步更改...", "Syncing. Cancelling may cause serious damages to the repository": "正在同步。取消可能会导致仓库出现严重损坏", "Tag at {0}": "{0} 处的标记", "Tag name": "标记名称", "The \"{0}\" repository has {1} submodules which won't be opened automatically. You can still open each one individually by opening a file within.": "“{0}”仓库中的 {1} 个子模块将不会自动打开。您仍可以通过打开其中的文件来单独打开每个子模块。", "The active branch cannot be deleted.": "无法删除活动分支。", "The branch \"{0}\" has no remote branch. Would you like to publish this branch?": "分支“{0}”没有远程分支。是否要发布此分支?", "The branch \"{0}\" is not fully merged. Delete anyway?": "“{0}”分支未被完全合并。是否仍要删除?", "The changes are already present in the current branch.": "当前频道中已存在更改。", "The current branch is not published to the remote. Would you like to publish it to access your changes elsewhere?": "当前分支未发布到远程。要发布该分支以访问其他位置的更改吗?", "The following file has unresolved diagnostics: '{0}'.\n\nHow would you like to proceed?": "以下文件未解析诊断： '{0}'。\n\n你希望如何继续？", "The following file has unsaved changes which won't be included in the commit if you proceed: {0}.\n\nWould you like to save it before committing?": "以下文件具有未保存的更改；如果继续，则提交内容将不包含这些更改: {0}。\n\n你想在提交之前保存它吗?", "The following file has unsaved changes which won't be included in the stash if you proceed: {0}.\n\nWould you like to save it before stashing?": "以下文件具有未保存的更改；如果继续，则储藏时不会包含这些更改: {0}。\n\n要在储藏之前保存吗?", "The git repositories in the current folder are potentially unsafe as the folders are owned by someone other than the current user.": "当前文件夹中的 git 存储库可能不安全，因为这些文件夹由当前用户以外的其他人所有。", "The git repository at \"{0}\" has too many active changes, only a subset of Git features will be enabled.": "位于“{0}”的 Git 仓库中存在太多活动更改，将仅启用部分 Git 功能。", "The git repository in the current folder is potentially unsafe as the folder is owned by someone other than the current user.": "当前文件夹中的 git 存储库可能不安全，因为该文件夹由当前用户以外的其他人所有。", "The last commit was a merge commit. Are you sure you want to undo it?": "最后一个提交是合并提交。是否确实要撤消它？", "The new branch will be \"{0}\"": "新分支将为“{0}”", "The remote branch of the active branch cannot be deleted.": "无法删除活动分支的远程分支。", "The repository does not have any changes.": "存储库没有任何更改。", "The repository does not have any commits. Please make an initial commit before creating a stash.": "存储库没有任何提交。请在创建储藏条目之前进行初始提交。", "The repository does not have any staged changes.": "存储库没有任何暂存更改。", "The repository does not have any untracked changes.": "存储库没有任何未跟踪的更改。", "The selection range does not contain any changes.": "选择范围未包含任何更改。", "There are known issues with the installed Git \"{0}\". Please update to Git >= 2.27 for the git features to work correctly.": "安装的 Git \"{0}\" 存在已知问题。要使 Git 功能正常工作，请至少将 Git 更新到 2.27 版本。", "There are merge conflicts while applying the stash. Please resolve them before committing your changes.": "应用储藏时存在合并冲突。请先解决这些问题，然后再提交更改。", "There are merge conflicts. Please resolve them before committing your changes.": "存在合并冲突。请先解决这些问题，然后再提交更改。", "There are no available repositories": "没有可用存储库", "There are no changes to commit.": "没有要提交的更改。", "There are no changes to stash.": "没有要储藏的更改。", "There are no staged changes to commit.\n\nWould you like to stage all your changes and commit them directly?": "没有可提交的暂存更改。\n\n是否要暂存所有更改并直接提交?", "There are no staged changes to stash.": "没有要储藏的暂存更改。", "There are no stashes in the repository.": "此仓库中没有储藏。", "There are {0} files that have unresolved diagnostics.\n\nHow would you like to proceed?": "有 {0} 个文件未解析诊断。\n\n你希望如何继续？", "There are {0} unsaved files.\n\nWould you like to save them before committing?": "当前有 {0} 个文件尚未保存。\n\n您要在提交之前保存吗?", "There are {0} unsaved files.\n\nWould you like to save them before stashing?": "有 {0} 个文件尚未保存。\n\n要在储藏之前保存吗?", "There were merge conflicts while cherry picking the changes. Resolve the conflicts before committing them.": "挑拣更改时出现合并冲突。在提交冲突之前解决冲突。", "This action will pull and push commits from and to \"{0}/{1}\".": "此操作将从“{0}/{1}”中拉取并向其推送提交。", "This repository has no remotes configured to fetch from.": "此仓库未配置可以从中抓取的远程仓库。", "This will create a Git repository in \"{0}\". Are you sure you want to continue?": "将在“{0}”中创建 Git 仓库。确定要继续吗?", "Too many changes were detected. Only the first {0} changes will be shown below.": "检测到过多更改。下面将仅显示第一个 {0} 更改。", "Type Changed": "类型已更改", "Unable to pull from remote repository due to conflicting tag(s): {0}. Would you like to resolve the conflict by replacing the local tag(s)?": "无法从远程存储库中拉取，因为标记冲突: {0}。是否要通过替换本地标记来解决冲突?", "Uncommitted Changes": "未提交的更改", "Undo merge commit": "撤消合并提交", "Untracked": "未跟踪的", "Untracked Changes": "未跟踪的更改", "Update Git": "更新 GIT", "View Problems": "查看问题", "Would you like to add \"{0}\" to .gitignore?": "是否要将“{0}”添加到 .gitignore?", "Would you like to open the cloned repository, or add it to the current workspace?": "您是希望打开克隆的仓库，还是将其添加到当前工作区?", "Would you like to open the cloned repository?": "是否要打开已克隆仓库?", "Would you like to open the initialized repository, or add it to the current workspace?": "您是希望打开初始化的仓库，还是将其添加到当前工作区?", "Would you like to open the initialized repository?": "是否打开初始化的仓库?", "Would you like to publish this repository to continue working on it elsewhere?": "是否要发布此存储库以在其他位置继续处理它?", "Would you like {0} to [periodically run \"git fetch\"]({1})?": "你希望 {0} [定期运行 \"git fetch\"]({1})吗?", "Yes": "是", "Yes, Don't Show Again": "确定，且不再显示", "You": "你", "You are about to commit your changes without verification, this skips pre-commit hooks and can be undesirable.\n\nAre you sure to continue?": "你即将在未验证的情况下提交更改，这会跳过 pre-commit 挂钩，可能导致不理想的结果。\n\n确定要继续吗?", "You are about to force push your changes, this can be destructive and could inadvertently overwrite changes made by others.\n\nAre you sure to continue?": "即将强制推送更改，此操作可能具有破坏性并可能在无意中覆盖其他人的更改。\n\n确定要继续吗?", "You are trying to commit to a protected branch and you might not have permission to push your commits to the remote.\n\nHow would you like to proceed?": "你正在尝试提交到受保护的分支，并且你可能无权将提交推送到远程库。\n\n你希望如何继续?", "You seem to have git \"{0}\" installed. Code works best with git >= 2": "似乎已安装 GIT \"{0}\"。Code 非常适合 GIT >= 2", "Your local changes would be overwritten by checkout.": "签出会覆盖本地更改。", "Your repository has no remotes configured to publish to.": "仓库未配置任何要发布到的远程仓库。", "Your repository has no remotes configured to pull from.": "仓库未配置任何从其中进行拉取的远程仓库。", "Your repository has no remotes configured to push to.": "仓库未配置任何要推送到的远程仓库。", "Your repository has no remotes.": "您的仓库没有远程仓库。", "[main] Log level: {0}": "[main] 日志级别: {0}", "[main] Skipped found git in: \"{0}\"": "[main] 已跳过在以下位置找到的 git: \"{0}\"", "[main] Using git \"{0}\" from \"{1}\"": "[main] 使用来自 \"{1}\" 的 git \"{0}\"", "[main] Validating found git in: \"{0}\"": "[main] 正在验证在以下位置找到的 git: \"{0}\"", "branches": "分支", "in {0}": "{0} 后", "no": "否", "now": "现在", "remote branches": "远程分支", "tags": "标记", "yes": "是", "{0} (Deleted)": "{0} (已删除)", "{0} (Index)": "{0} (索引)", "{0} (Intent to add)": "{0} (打算添加)", "{0} (Ours)": "{0} (我们的)", "{0} (Theirs)": "{0} (他们的)", "{0} (Type changed)": "{0} (类型已更改)", "{0} (Untracked)": "{0} (未跟踪)", "{0} (Working Tree)": "{0} (工作树)", "{0} ({1})": "{0} ({1})", "{0} ({1}) ↔ {0} ({2})": "{0} ({1}) ↔ {0} ({2})", "{0} Checkout detached...": "{0} 签出已分离…", "{0} Commit": "{0} 提交", "{0} Commit & Push": "{0} 提交和推送", "{0} Commit & Sync": "{0} 提交和同步", "{0} Commit (Amend)": "{0} 提交(修改)", "{0} Continue": "{0} 继续", "{0} Create new branch from...": "{0} 创建新分支依据...", "{0} Create new branch...": "{0} 创建新分支...", "{0} Fetch all remotes": "{0} 提取所有远程", "{0} Publish Branch/{Locked=\"Branch\"}Do not translate \"Branch\" as it is a git term": "{0} 发布 Branch", "{0} Sync Changes{1}{2}": "{0} 同步更改 {1}{2}", "{0} characters over {1} in current line": "当前行比 {1} 超出 {0} 个字符", "{0} day": "{0} 天", "{0} day ago": "{0} 天前", "{0} days": "{0} 天", "{0} days ago": "{0} 天前", "{0} deletions{1}": "{0} 行删除{1}", "{0} deletion{1}": "{0} 个删除{1}", "{0} file changed": "已更改 {0} 个文件", "{0} files changed": "已更改 {0} 个文件", "{0} hour": "{0} 小时", "{0} hour ago": "{0} 小时前", "{0} hours": "{0} 小时", "{0} hours ago": "{0} 小时前", "{0} hr": "{0} 小时", "{0} hr ago": "{0} 小时前", "{0} hrs": "{0} 小时", "{0} hrs ago": "{0} 小时前", "{0} insertions{1}": "{0} 行插入{1}", "{0} insertion{1}": "{0} 个插入{1}", "{0} min": "{0} 分钟", "{0} min ago": "{0} 分钟前", "{0} mins": "{0} 分钟", "{0} mins ago": "{0} 分钟前", "{0} minute": "{0} 分钟", "{0} minute ago": "{0} 分钟前", "{0} minutes": "{0} 分钟", "{0} minutes ago": "{0} 分钟前", "{0} mo": "{0} 个月", "{0} mo ago": "{0} 个月前", "{0} month": "{0} 个月", "{0} month ago": "{0} 个月前", "{0} months": "{0} 个月", "{0} months ago": "{0} 个月前", "{0} mos": "{0} 个月", "{0} mos ago": "{0} 个月前", "{0} sec": "{0} 秒", "{0} sec ago": "{0} 秒前", "{0} second": "{0} 秒", "{0} second ago": "{0} 秒前", "{0} seconds": "{0} 秒", "{0} seconds ago": "{0} 秒前", "{0} secs": "{0} 秒", "{0} secs ago": "{0} 秒前", "{0} week": "{0} 周", "{0} week ago": "{0} 周前", "{0} weeks": "{0} 周", "{0} weeks ago": "{0} 周前", "{0} wk": "{0} 周", "{0} wk ago": "{0} 周前", "{0} wks": "{0} 周", "{0} wks ago": "{0} 周前", "{0} year": "{0} 年", "{0} year ago": "{0} 年前", "{0} years": "{0} 年", "{0} years ago": "{0} 年前", "{0} yr": "{0} 年", "{0} yr ago": "{0} 年前", "{0} yrs": "{0} 年", "{0} yrs ago": "{0} 年前", "{0} ↔ {1}": "{0} ↔ {1}"}, "package": {"colors.added": "已添加资源的颜色。", "colors.blameEditorDecoration": "追溯编辑器修饰的颜色。", "colors.conflict": "存在冲突的资源的颜色。", "colors.deleted": "已删除资源的颜色。", "colors.ignored": "已忽略资源的颜色。", "colors.incomingAdded": "已添加的传入资源的颜色。", "colors.incomingDeleted": "已删除传入资源的颜色。", "colors.incomingModified": "已修改的传入资源的颜色。", "colors.incomingRenamed": "已重命名的传入资源的颜色。", "colors.modified": "已修改资源的颜色。", "colors.renamed": "重命名或复制的资源的颜色。", "colors.stageDeleted": "已暂存的已删除资源的颜色。", "colors.stageModified": "已暂存的已修改资源的颜色。", "colors.submodule": "子模块资源的颜色。", "colors.untracked": "未跟踪资源的颜色。", "command.addRemote": "添加远程存储库…", "command.api.getRemoteSources": "获取远程源", "command.api.getRepositories": "获取存储库", "command.api.getRepositoryState": "获取仓库状态", "command.blameToggleEditorDecoration": "切换 Git Blame 编辑器效果", "command.blameToggleStatusBarItem": "切换 Git Blame 状态栏项", "command.branch": "创建分支...", "command.branchFrom": "从现有来源创建新的分支...", "command.checkout": "签出到...", "command.checkoutDetached": "签出到(已分离)…", "command.cherryPick": "挑拣…", "command.cherryPickAbort": "中止挑拣", "command.clean": "放弃更改", "command.cleanAll": "放弃所有更改", "command.cleanAllTracked": "放弃所有跟踪的更改", "command.cleanAllUntracked": "放弃所有未跟踪的更改", "command.clone": "克隆", "command.cloneRecursive": "克隆(递归)", "command.close": "关闭仓库", "command.closeAllDiffEditors": "关闭所有差异编辑器", "command.closeAllUnmodifiedEditors": "关闭所有未修改的编辑器", "command.closeOtherRepositories": "关闭其他存储库", "command.commit": "提交", "command.commitAll": "全部提交", "command.commitAllAmend": "全部提交(修改)", "command.commitAllAmendNoVerify": "全部提交(修正，不验证)", "command.commitAllNoVerify": "全部提交(不验证)", "command.commitAllSigned": "全部提交(已署名)", "command.commitAllSignedNoVerify": "全部提交(已签收，不验证)", "command.commitAmend": "提交(修改)", "command.commitAmendNoVerify": "提交(修改，不验证)", "command.commitEmpty": "创建空提交", "command.commitEmptyNoVerify": "空提交(不验证)", "command.commitMessageAccept": "接受提交消息", "command.commitMessageDiscard": "放弃提交消息", "command.commitNoVerify": "提交(不验证)", "command.commitSigned": "提交(已签收)", "command.commitSignedNoVerify": "提交(已签收，不验证)", "command.commitStaged": "提交已暂存文件", "command.commitStagedAmend": "提交已暂存文件(修改)", "command.commitStagedAmendNoVerify": "提交已暂存内容(修正，不验证)", "command.commitStagedNoVerify": "提交已暂存内容(不验证)", "command.commitStagedSigned": "提交已暂存文件(已署名)", "command.commitStagedSignedNoVerify": "提交已暂存内容(已签收，不验证)", "command.continueInLocalClone": "在本地克隆存储库并在桌面上打开...", "command.continueInLocalClone.qualifiedName": "继续在新的本地克隆中工作", "command.createTag": "创建标记...", "command.deleteBranch": "删除分支...", "command.deleteRemoteBranch": "删除远程分支...", "command.deleteRemoteTag": "删除远程标记...", "command.deleteTag": "删除标签...", "command.fetch": "抓取", "command.fetchAll": "从所有远程存储库中抓取", "command.fetchPrune": "抓取(删除)", "command.git.acceptMerge": "完成合并", "command.git.openMergeEditor": "在合并编辑器中解析", "command.git.runGitMerge": "计算与 Git 冲突", "command.git.runGitMergeDiff3": "计算与 Git 冲突(Diff3)", "command.graphCheckout": "签出", "command.graphCheckoutDetached": "签出(已分离)", "command.graphCherryPick": "挑拣", "command.graphDeleteBranch": "删除分支", "command.graphDeleteTag": "删除标签", "command.ignore": "添加到 .gitignore", "command.init": "初始化仓库", "command.manageUnsafeRepositories": "管理不安全的存储库", "command.merge": "合并...", "command.mergeAbort": "中止合并", "command.openAllChanges": "打开所有更改", "command.openChange": "打开更改", "command.openFile": "打开文件", "command.openHEADFile": "打开文件 (HEAD)", "command.openRepositoriesInParentFolders": "在父文件夹中打开存储库", "command.openRepository": "打开仓库", "command.publish": "发布分支...", "command.pull": "拉取", "command.pullFrom": "拉取自...", "command.pullRebase": "拉取(变基)", "command.push": "推送", "command.pushFollowTags": "推送(“关注”标记)", "command.pushFollowTagsForce": "推送(“关注”标记，强制)", "command.pushForce": "推送(强制)", "command.pushTags": "推送标记", "command.pushTo": "推送到...", "command.pushToForce": "推送到...(强制)", "command.rebase": "变基分支…", "command.rebaseAbort": "中止变基", "command.refresh": "刷新", "command.removeRemote": "删除远程存储库", "command.rename": "重命名", "command.renameBranch": "重命名分支...", "command.reopenClosedRepositories": "重新打开已关闭的存储库...", "command.restoreCommitTemplate": "还原提交模板", "command.revealFileInOS.linux": "打开包含的文件夹", "command.revealFileInOS.mac": "在查找器中显示", "command.revealFileInOS.windows": "在文件资源管理器中显示", "command.revealInExplorer": "在资源管理器视图中显示", "command.revertChange": "还原更改", "command.revertSelectedRanges": "还原所选更改", "command.showOutput": "显示 GIT 输出", "command.stage": "暂存更改", "command.stageAll": "暂存所有更改", "command.stageAllMerge": "暂存所有合并更改", "command.stageAllTracked": "暂存所有跟踪的更改", "command.stageAllUntracked": "暂存所有未跟踪的更改", "command.stageBlock": "暂存块", "command.stageChange": "暂存更改", "command.stageSelectedRanges": "暂存所选范围", "command.stageSelection": "暂存选择", "command.stash": "储藏", "command.stashApply": "应用储藏...", "command.stashApplyEditor": "应用储藏条目", "command.stashApplyLatest": "应用最新储藏", "command.stashDrop": "删除储藏...", "command.stashDropAll": "删除所有储藏...", "command.stashDropEditor": "删除储藏条目", "command.stashIncludeUntracked": "储藏(包含未跟踪)", "command.stashPop": "弹出储藏...", "command.stashPopEditor": "弹出储藏条目", "command.stashPopLatest": "弹出最新储藏", "command.stashStaged": "储藏暂存", "command.stashView": "查看储藏条目...", "command.sync": "同步", "command.syncRebase": "同步(变基)", "command.timelineCompareWithSelected": "与已选项目进行比较", "command.timelineCopyCommitId": "复制提交 ID", "command.timelineCopyCommitMessage": "复制提交消息", "command.timelineOpenDiff": "打开更改", "command.timelineSelectForCompare": "选择以进行比较", "command.undoCommit": "撤消上次提交", "command.unstage": "取消暂存更改", "command.unstageAll": "取消暂存所有更改", "command.unstageChange": "取消暂存更改", "command.unstageSelectedRanges": "取消暂存所选范围", "command.viewChanges": "打开更改", "command.viewCommit": "打开提交", "command.viewStagedChanges": "打开暂存更改", "command.viewUntrackedChanges": "打开未跟踪的更改", "config.allowForcePush": "控制是否启用强制推送 (不论 force 还是 force-with-lease)。", "config.allowNoVerifyCommit": "控制是否允许没有运行 pre-commit 和 commit-msg 挂钩的提交。", "config.alwaysShowStagedChangesResourceGroup": "始终显示“暂存的更改”资源组。", "config.alwaysSignOff": "控制所有提交的 signoff 标志。", "config.autoRepositoryDetection": "配置何时自动检测存储库。", "config.autoRepositoryDetection.false": "禁止自动扫描仓库。", "config.autoRepositoryDetection.openEditors": "扫描当前打开文件的父文件夹。", "config.autoRepositoryDetection.subFolders": "扫描当前打开文件夹的子文件夹。", "config.autoRepositoryDetection.true": "扫描当前打开文件夹与当前打开文件所在文件夹的子文件夹。", "config.autoStash": "在拉取前暂存所有更改，在成功拉取后还原这些更改。", "config.autofetch": "若设置为 true，则自动从当前 Git 仓库的默认远程仓库抓取提交。若设置为“全部”，则从所有远程仓库进行抓取。", "config.autofetchPeriod": "在启用“#git.autofetch#”情况下每次自动 git fetch 之间的间隔时间(以秒为单位)。", "config.autorefresh": "是否启用自动刷新。", "config.blameEditorDecoration.enabled": "控制是否使用编辑器修饰在编辑器中显示追溯信息。", "config.blameEditorDecoration.template": "追溯信息编辑器修饰的模板。支持的变量:\r\n\r\n*“hash”: 提交哈希\r\n\r\n*“hashShort”：根据“#git.commitShortHashLength#”的提交哈希的前 N 个字符\r\n\r\n*“subject”: 提交消息的第一行\r\n\r\n*“authorName”: 作者姓名\r\n\r\n*“authorEmail”: 作者电子邮件地址\r\n\r\n*“authorDate”: 作者日期\r\n\r\n*“authorDateAgo”: 现在和作者日期之间的时间差\r\n\r\n", "config.blameStatusBarItem.enabled": "控制是否在状态栏中显示追溯信息。", "config.blameStatusBarItem.template": "追溯信息状态栏项的模板。支持的变量:\r\n\r\n*“hash”: 提交哈希\r\n\r\n*“hashShort”：根据“#git.commitShortHashLength#”的提交哈希的前 N 个字符\r\n\r\n*“subject”: 提交消息的第一行\r\n\r\n*“authorName”: 作者姓名\r\n\r\n*“authorEmail”: 作者电子邮件地址\r\n\r\n*“authorDate”: 作者日期\r\n\r\n*“authorDateAgo”: 现在和作者日期之间的时间差\r\n\r\n", "config.branchPrefix": "创建新分支时使用的前缀。", "config.branchProtection": "受保护分支的列表。默认情况下，在将更改提交到受保护分支之前会显示提示。可以使用 `#git.branchProtectionPrompt#` 设置控制提示。", "config.branchProtectionPrompt": "控制是否在将更改提交到受保护的分支之前显示提示。", "config.branchProtectionPrompt.alwaysCommit": "始终将更改提交到受保护分支。", "config.branchProtectionPrompt.alwaysCommitToNewBranch": "始终将更改提交到新的分支。", "config.branchProtectionPrompt.alwaysPrompt": "始终在将更改提交到受保护分支之前进行提示。", "config.branchRandomNameDictionary": "用于随机生成的分支名称的字典列表。每个值都表示用于生成分支名称段的字典。支持的词典:“形容词”、“动物”、“颜色”和“数字”。", "config.branchRandomNameDictionary.adjectives": "随机形容词", "config.branchRandomNameDictionary.animals": "随机动物名称", "config.branchRandomNameDictionary.colors": "随机颜色名称", "config.branchRandomNameDictionary.numbers": "100 和 999 之间的一个随机数", "config.branchRandomNameEnable": "控制在创建新分支时是否生成随机名称。", "config.branchSortOrder": "控制分支的排列顺序。", "config.branchValidationRegex": "用于验证新分支名称的正则表达式。", "config.branchWhitespaceChar": "用于替换新分支名称中的空格，以及用于分隔随机生成的分支名称区段的字符。", "config.checkoutType": "控制在运行“签出到…”时列出的 Git 参考类型。", "config.checkoutType.local": "本地分支", "config.checkoutType.remote": "远程分支", "config.checkoutType.tags": "标记", "config.closeDiffOnOperation": "控制在储藏、提交、放弃、暂存或取消暂存更改时，是否应自动关闭差异编辑器。", "config.commandsToLog": "GIT 命令列表 (例如: commit、push)，这些命令的 `stdout` 将被记录到 [git 输出](command:git.showOutput)。如果 GIT 命令配置了客户端挂钩，那么客户端挂钩的 `stdout` 也将被记录到 [git 输出](command:git.showOutput)。", "config.commitShortHashLength": "控制提交短哈希的长度。", "config.confirmEmptyCommits": "始终确认为 \"Git: Commit Empty\" 命令创建空提交。", "config.confirmForcePush": "控制在强制推送前是否进行确认。", "config.confirmNoVerifyCommit": "控制是否在提交前要求确认而不进行验证。", "config.confirmSync": "同步 Git 存储库前请先进行确认。", "config.countBadge": "控制 Git 计数徽章。", "config.countBadge.all": "对所有更改计数。", "config.countBadge.off": "关闭计数器。", "config.countBadge.tracked": "仅对跟踪的更改计数。", "config.decorations.enabled": "控制 Git 是否在资源管理器和“打开编辑器”视图中添加颜色和小标。", "config.defaultBranchName": "初始化新的 Git 存储库时默认分支的名称(例如: main、trunk、development)。设置为空时，将使用在 Git 中配置的默认分支名称。**注意:** 需要 Git 版本“2.28.0”或更高版本。", "config.defaultCloneDirectory": "克隆 Git 存储库的默认位置。", "config.detectSubmodules": "控制是否自动检测 Git 子模块。", "config.detectSubmodulesLimit": "控制可检测到的 Git 子模块的限制。", "config.diagnosticsCommitHook.enabled": "控制是否在提交之前检查未解决的诊断。", "config.diagnosticsCommitHook.sources": "控制在提交之前要考虑的源(项)和最小严重性(值)的列表。注意: 若要忽略特定源的诊断，请将该源添加到列表中，并将最小严重性设置为“无”。", "config.discardAllScope": "控制运行“放弃所有更改”命令时放弃的更改类型。\"all\" 放弃所有更改。\"tracked\" 只放弃跟踪的文件。\"prompt\" 表示在每次运行此操作时显示提示对话框。", "config.discardUntrackedChangesToTrash": "控制放弃未跟踪的更改是否将文件移动到回收站(Windows)、垃圾桶(macOS、Linux)，而不是永久删除它们。**注意:** 连接到远程或在 Linux 中作为快照包运行时，此设置不起作用。", "config.enableCommitSigning": "允许使用 GPG、X.509 或 SSH 提交签名。", "config.enableSmartCommit": "在没有暂存的更改时提交所有更改。", "config.enableStatusBarSync": "控制Git Sync命令是否出现在状态栏中。", "config.enabled": "是否启用 Git。", "config.experimental.installGuide": "Git 安装流程的试验性改进。", "config.fetchOnPull": "启用后，在拉取时获取所有分支。否则，仅获取当前。", "config.followTagsWhenSync": "运行同步命令时推送附注标签。", "config.ignoreLegacyWarning": "忽略“旧版 Git”警告。", "config.ignoreLimitWarning": "忽略“仓库中存在大量更改”的警告。", "config.ignoreMissingGitWarning": "忽略“缺失 Git”的警告。", "config.ignoreRebaseWarning": "忽略拉取时发出的分支似乎已变基的警告。", "config.ignoreSubmodules": "忽略对文件树中子模块的修改。", "config.ignoreWindowsGit27Warning": "如果 Windows 上安装了 Git 2.25 - 2.26，则忽略警告。", "config.ignoredRepositories": "要忽略的 Git 存储库列表。", "config.inputValidation": "控制是否显示提交消息输入验证诊断。", "config.inputValidationLength": "控制显示提交消息长度警告的长度阈值。", "config.inputValidationSubjectLength": "控制用于显示警告的提交消息主题长度阈值。请取消设置它以继承 `#git.inputValidationLength#`的值。", "config.mergeEditor": "打开当前处于冲突状态的文件的合并编辑器。", "config.openAfterClone": "控制是否在克隆后自动打开仓库。", "config.openAfterClone.always": "始终在当前窗口中打开。", "config.openAfterClone.alwaysNewWindow": "始终在新窗口中打开。", "config.openAfterClone.prompt": "始终提示操作。", "config.openAfterClone.whenNoFolderOpen": "只有在没有打开任何文件夹时，才在当前窗口中打开。", "config.openDiffOnClick": "控制单击更改时是否应打开差异编辑器。否则将打开常规编辑器。", "config.openRepositoryInParentFolders": "控制是应打开工作区父文件夹中的存储库还是打开的文件。", "config.openRepositoryInParentFolders.always": "始终在工作区的父文件夹中打开存储库或打开文件。", "config.openRepositoryInParentFolders.never": "切勿在工作区的父文件夹中打开存储库或打开文件。", "config.openRepositoryInParentFolders.prompt": "在打开存储库之前提示工作区的父文件夹或打开文件。", "config.optimisticUpdate": "控制是否在运行 git 命令后乐观地更新源代码管理视图的状态。", "config.path": "Git 可执行文件的路径和文件名，例如 \"C:\\Program Files\\Git\\bin\\git.exe\" (Windows)。这也可以是一个包含多个要查找的路径的字符串值数组。", "config.postCommitCommand": "成功提交后运行 git 命令。", "config.postCommitCommand.none": "提交后不要运行任何命令。", "config.postCommitCommand.push": "成功提交后运行 'git push'。", "config.postCommitCommand.sync": "成功提交后运行 'git pull' 和 'git push' 命令。", "config.promptToSaveFilesBeforeCommit": "控制 Git 是否在提交之前检查未保存的文件。", "config.promptToSaveFilesBeforeCommit.always": "检查是否有任何未保存的文件。", "config.promptToSaveFilesBeforeCommit.never": "禁用此检查。", "config.promptToSaveFilesBeforeCommit.staged": "只检查未保存的已暂存文件。", "config.promptToSaveFilesBeforeStash": "控制 Git 是否在储藏更改之前检查未保存的文件。", "config.promptToSaveFilesBeforeStash.always": "检查是否有任何未保存的文件。", "config.promptToSaveFilesBeforeStash.never": "禁用此检查。", "config.promptToSaveFilesBeforeStash.staged": "只检查未保存的已暂存文件。", "config.pruneOnFetch": "提取时修剪。", "config.publishBeforeContinueOn": "控制是否要在从 Git 存储库使用“继续工作”时提示发布未发布的 Git 状态。", "config.publishBeforeContinueOn.always": "从 Git 存储库使用“继续工作”时始终发布未发布的 Git 状态", "config.publishBeforeContinueOn.never": "从 Git 存储库使用“继续工作”时切勿发布未发布的 Git 状态", "config.publishBeforeContinueOn.prompt": "从 Git 存储库使用“继续工作”时提示发布未发布的 Git 状态", "config.pullBeforeCheckout": "控制没有传出提交的分支在签出之前是否快速转发。", "config.pullTags": "拉取时提取所有标签。", "config.rebaseWhenSync": "在运行“同步”命令时，强制 Git 使用“变基”。", "config.rememberPostCommitCommand": "记住提交后运行的最后一个 git 命令。", "config.replaceTagsWhenPull": "如果在运行拉取命令时发生冲突，自动将本地标记替换为远程标记。", "config.repositoryScanIgnoredFolders": "当 `#git.autoRepositoryDetection#` 设置为 `true` 或 `subFolders` 时扫描 Git 仓库时忽略的文件夹列表。", "config.repositoryScanMaxDepth": "在将 `#git.autoRepositoryDetection#` 设置为 `true` 或 `subFolders` 时，控制扫描工作区文件夹以查找 Git 仓库时使用的深度。如果不进行限制，可以设置为 `-1`。", "config.requireGitUserConfig": "控制在是要求进行显式 Git 用户配置，还是允许 Git 在缺少配置时进行猜测。", "config.scanRepositories": "在其中搜索 Git 存储库的路径的列表。", "config.showActionButton": "控制操作按钮是否显示在“源代码管理”视图中。", "config.showActionButton.commit": "显示一个操作按钮，以便在本地分支已修改文件可供提交时提交更改。", "config.showActionButton.publish": "显示一个操作按钮，以便在本地分支没有跟踪远程分支时发布该分支。", "config.showActionButton.sync": "显示一个操作按钮，以便在本地分支位于远程分支前面或后面时同步更改。", "config.showCommitInput": "控制是否在 Git 源控制面板中显示提交输入。", "config.showInlineOpenFileAction": "控制是否在 Git 更改视图中显示内联“打开文件”操作。", "config.showProgress": "控制 Git 操作是否显示进度提示。", "config.showPushSuccessNotification": "控制在推送成功时是否显示通知。", "config.showReferenceDetails": "控制是否在签出、分支和标记选取器中显示 Git 引用的上次提交详细信息。", "config.similarityThreshold": "控制相似性索引(相比于文件大小添加/删除的量)的阈值，以便将一对已添加/删除的文件中的更改视为重命名。**注意:** 需要 Git“2.18.0”或更高版本。", "config.smartCommitChanges": "控制哪些更改由Smart Commit自动暂存。", "config.smartCommitChanges.all": "自动暂存所有更改。", "config.smartCommitChanges.tracked": "仅自动暂存跟踪的更改。", "config.statusLimit": "控制如何限制可从 Git 状态命令分析的更改数。可以设置为 0 表示无限制。", "config.suggestSmartCommit": "建议启用智能提交(在无暂存更改时提交所有更改)。", "config.supportCancellation": "控制在运行同步操作时是否出现通知，允许用户取消操作。", "config.terminalAuthentication": "控制是否启用 JoyCode 作为集成终端中生成的 Git 进程的身份验证处理程序。请注意: 需要重启终端才能选取此设置中的更改。", "config.terminalGitEditor": "控制是否使 JoyCode 成为集成终端中产生的 Git 进程的 Git 编辑器。请注意: 需要重启终端才能选择此设置中的更改。", "config.timeline.date": "控制在日程表视图中项目使用的日期。", "config.timeline.date.authored": "使用创作日期", "config.timeline.date.committed": "使用提交日期", "config.timeline.showAuthor": "控制是否在日程表视图中显示提交作者。", "config.timeline.showUncommitted": "控制是否在时间线视图中显示未提交的更改。", "config.untrackedChanges": "控制未跟踪的更改的行为。", "config.untrackedChanges.hidden": "未跟踪的更改被隐藏，并从多个操作中排除。", "config.untrackedChanges.mixed": "所有更改，无论是跟踪的还是未跟踪的，都会一起出现并表现出相同的行为。", "config.untrackedChanges.separate": "未跟踪的更改单独显示在“源代码管理”视图中。它们也被排除在几个操作之外。", "config.useCommitInputAsStashMessage": "控制是否将提交输入框中的消息用作默认储藏消息。", "config.useEditorAsCommitInput": "控制当提交输入框中未提供消息时，是否将使用全文编辑器来创作提交消息。", "config.useForcePushIfIncludes": "控制是否使用更安全的 force-if-includes 进行强制推送。注意: 此设置要求启用“#git.useForcePushWithLease#”设置以及 Git 版本“2.30.0”或更高版本。", "config.useForcePushWithLease": "控制是否使用更安全的 force-with-lease 进行强制推送。", "config.useIntegratedAskPass": "控制是否应覆盖 GIT_ASKPASS 以使用集成版本。", "config.verboseCommit": "启用`#git.useEditorAsCommitInput#`时启用详细输出。", "description": "Git 源代码管理集成", "displayName": "Git", "submenu.branch": "分支", "submenu.changes": "更改", "submenu.commit": "提交", "submenu.commit.amend": "修改", "submenu.commit.signoff": "注销", "submenu.explorer": "Git", "submenu.pullpush": "拉取，推送", "submenu.remotes": "远程", "submenu.stash": "存储", "submenu.tags": "标记", "view.workbench.cloneRepository": "您可以在本地克隆存储库。\r\n[克隆存储库](command:git.clone '启用 Git 扩展后立即克隆存储库')", "view.workbench.learnMore": "若要详细了解如何在 JoyCode 中使用 Git 和源代码管理参阅我们的文档。", "view.workbench.scm.closedRepositories": "找到以前关闭的 Git 存储库。\r\n[重新打开已关闭的存储库](command:git.reopenClosedRepositories)\r\n若要详细了解如何在 JoyCode 中使用 Git 和源代码管理参阅我们的文档。", "view.workbench.scm.closedRepository": "找到以前关闭的 Git 存储库。\r\n[重新打开已关闭的存储库](command:git.reopenClosedRepositories)\r\n若要详细了解如何在 JoyCode 中使用 Git 和源代码管理参阅我们的文档。", "view.workbench.scm.disabled": "如果要使用 Git 功能，请在[设置](command:workbench.action.openSettings?%5B%22git.enabled%22%5D)中启用 Git。\r\n若要详细了解如何在 JoyCode 中使用 Git 和源代码管理参阅我们的文档。", "view.workbench.scm.empty": "为了使用 Git 功能，可打开包含 Git 仓库的文件夹或从 URL 克隆。\r\n[打开文件夹](command:vscode.openFolder)\r\n[克隆仓库](command:git.cloneRecursive)\r\n若要详细了解如何在 JoyCode 中使用 Git 和源代码管理参阅我们的文档。", "view.workbench.scm.emptyWorkspace": "当前打开的工作区中没有任何包含 Git 仓库的文件夹。\r\n[将文件夹添加到工作区](command:workbench.action.addRootFolder)\r\n若要详细了解如何在 JoyCode 中使用 Git 和源代码管理参阅我们的文档。", "view.workbench.scm.folder": "当前打开的文件夹中没有 Git 存储库。可初始化一个仓库，它将实现 Git 提供支持的源代码管理功能。\r\n[初始化仓库](command:git.init?%5Btrue%5D)\r\n若要详细了解如何在 JoyCode 中使用 Git 和源代码管理 参阅我们的文档。", "view.workbench.scm.missing": "安装 Git (一种流行的源代码管理系统)，以跟踪代码更改并与他人协作。在我们的 Git 指南中了解详细信息。", "view.workbench.scm.missing.linux": "源代码管理取决于将安装的 Git。\r\n[下载适用于 Linux 的 Git](https://git-scm.com/download/linux)\r\n安装后，请[重新加载](command:workbench.action.reloadWindow) (或[执行故障排除](command:git.showOutput))。可以[从商城](command:workbench.extensions.search?%22%40category%3A%5C%22scm%20providers%5C%22%22)安装其他源代码管理提供程序。", "view.workbench.scm.missing.mac": "[下载适用于 macOS 的 Git](https://git-scm.com/download/mac)\r\n安装后，请[重新加载](command:workbench.action.reloadWindow) (或[执行故障排除](command:git.showOutput))。可以[从商城](command:workbench.extensions.search?%22%40category%3A%5C%22scm%20providers%5C%22%22)安装其他源代码管理提供程序。", "view.workbench.scm.missing.windows": "[下载适用于 Windows 的 Git](https://git-scm.com/download/win)\r\n安装后，请[重新加载](command:workbench.action.reloadWindow) (或[执行故障排除](command:git.showOutput))。可以[从商城](command:workbench.extensions.search?%22%40category%3A%5C%22scm%20providers%5C%22%22)安装其他源代码管理提供程序。", "view.workbench.scm.repositoriesInParentFolders": "在工作区的父文件夹或打开的文件中找到了 Git 存储库。\r\n[打开存储库](command:git.openRepositoriesInParentFolders)\r\n使用[git.openRepositoryInParentFolders](command:workbench.action.openSettings?%5B%22git.openRepositoryInParentFolders%22%5D)设置来控制是打开工作区或打开文件的父文件夹中的 Git 存储库。若要了解详细信息，请[阅读我们的文档](https://aka.ms/vscode-git-repository-in-parent-folders)。", "view.workbench.scm.repositoryInParentFolders": "在工作区的父文件夹或打开的文件中找到了 Git 存储库。\r\n[打开存储库](command:git.openRepositoriesInParentFolders)\r\n使用[git.openRepositoryInParentFolders](command:workbench.action.openSettings?%5B%22git.openRepositoryInParentFolders%22%5D)设置来控制是打开工作区或打开文件的父文件夹中的 Git 存储库。若要了解详细信息，请[阅读我们的文档](https://aka.ms/vscode-git-repository-in-parent-folders)。", "view.workbench.scm.scanFolderForRepositories": "正在扫描 Git 存储库的文件夹...", "view.workbench.scm.scanWorkspaceForRepositories": "正在扫描工作区中的 Git 存储库...", "view.workbench.scm.unsafeRepositories": "检测到的 Git 存储库可能不安全，因为该文件夹由当前用户以外的其他人所有。\r\n[管理不安全存储库](command:git.manageUnsafeRepositories)\r\n要详细了解不安全存储库，[请阅读我们的文档](https://aka.ms/vscode-git-unsafe-repository)。", "view.workbench.scm.unsafeRepository": "检测到的 Git 存储库可能不安全，因为该文件夹由当前用户以外的其他人所有。\r\n[管理不安全存储库](command:git.manageUnsafeRepositories)\r\n要详细了解不安全存储库，[请阅读我们的文档](https://aka.ms/vscode-git-unsafe-repository)。", "view.workbench.scm.workspace": "当前打开的工作区中没有任何包含 Git 仓库的文件夹。可初始化某文件夹上的一个仓库，该仓库将实现 Git 提供支持的源代码管理功能。\r\n[初始化仓库](command:git.init)\r\n若要详细了解如何在 JoyCode 中使用 Git 和源代码管理参阅我们的文档。"}}}