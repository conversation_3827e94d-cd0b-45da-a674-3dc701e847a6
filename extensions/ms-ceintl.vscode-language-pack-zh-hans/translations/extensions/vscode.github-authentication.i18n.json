{"": ["--------------------------------------------------------------------------------------------", "Copyright (c) Microsoft Corporation. All rights reserved.", "Licensed under the MIT License. See License.txt in the project root for license information.", "--------------------------------------------------------------------------------------------", "Do not edit this file. It is machine generated."], "version": "1.0.0", "contents": {"bundle": {"Continue to GitHub": "继续访问 GitHub", "Continue to GitHub to create a Personal Access Token (PAT)": "继续转到 GitHub 以创建个人访问令牌 (PAT)", "Copy & Continue to GitHub": "复制并继续访问 GitHub", "GitHub Enterprise Server URI is not a valid URI: {0}": "GitHub Enterprise 服务器 URI 不是有效的 URI: {0}", "Having trouble logging in? Would you like to try a different way? ({0})": "登录时遇到问题? 是否要尝试其他方式? ({0})", "No": "否", "Open [{0}]({0}) in a new tab and paste your one-time code: {1}/The [{0}]({0}) will be a url and the {1} will be a code, e.g. 123-456{Locked=\"[{0}]({0})\"}": "在新选项卡中打开 [{0}]({0})，并粘贴一次性代码: {1}", "Sign in failed: {0}": "登录失败: {0}", "Sign out failed: {0}": "注销失败: {0}", "Signing in to {0}.../The {0} will be a url, e.g. github.com": "正在登录到 {0}...", "To finish authenticating, navigate to GitHub and paste in the above one-time code.": "要完成身份验证，请导航到 GitHub 并粘贴以上一次性代码。", "To finish authenticating, navigate to GitHub to create a PAT then paste the PAT into the input box.": "若要完成身份验证，请导航到 GitHub 以创建 PAT，然后将 PAT 粘贴到输入框中。", "Yes": "是", "You have not yet finished authorizing this extension to use GitHub. Would you like to try a different way? ({0})": "尚未完成授权此扩展使用 GitHub 的操作。是否要尝试其他方式? ({0})", "Your Code: {0}/The {0} will be a code, e.g. 123-456": "你的代码: {0}", "device code": "设备代码", "local server": "本地服务器", "personal access token": "个人访问令牌", "url handler": "URL 处理程序"}, "package": {"config.github-enterprise.title": "GHE.com 和GitHub Enterprise服务器身份验证", "config.github-enterprise.uri.description": "GHE.com 或 GitHub Enterprise Server 实例的 URI。\r\n\r\n例子：\r\n* GHE.com： 'https://octocat.ghe.com`\r\n* GitHub Enterprise服务器：“https://github.octocat.com`\r\n\r\n> **注意：** 这应将 _not_ 设置为 GitHub.com URI。如果你的帐户在 GitHub.com 上存在或是GitHub Enterprise托管用户，则无需任何其他配置，只需登录 GitHub 即可。", "description": "GitHub 身份验证提供程序", "displayName": "GitHub 身份验证"}}}