{"": ["--------------------------------------------------------------------------------------------", "Copyright (c) Microsoft Corporation. All rights reserved.", "Licensed under the MIT License. See License.txt in the project root for license information.", "--------------------------------------------------------------------------------------------", "Do not edit this file. It is machine generated."], "version": "1.0.0", "contents": {"bundle": {"Emmet Abbreviation": "Emmet 缩写", "Enter Abbreviation": "输入缩写", "Invalid emmet.variables field. See https://code.visualstudio.com/docs/editor/emmet#_emmet-configuration for a valid example.": "emmet.variables 字段无效。有关有效示例，请参阅 https://code.visualstudio.com/docs/editor/emmet#_emmet-configuration。", "Invalid snippets file. See https://code.visualstudio.com/docs/editor/emmet#_using-custom-emmet-snippets for a valid example.": "代码段文件无效。有关有效示例，请参阅 https://code.visualstudio.com/docs/editor/emmet#_using-custom-emmet-snippets。", "Invalid syntax profile. See https://code.visualstudio.com/docs/editor/emmet#_emmet-configuration for a valid example.": "语法配置无效。有关有效示例，请参阅 https://code.visualstudio.com/docs/editor/emmet#_emmet-configuration。"}, "package": {"command.balanceIn": "平衡(向内)", "command.balanceOut": "平衡(向外)", "command.decrementNumberByOne": "减少 1", "command.decrementNumberByOneTenth": "减少 0.1", "command.decrementNumberByTen": "减少 10", "command.evaluateMathExpression": "求数学表达式的值", "command.incrementNumberByOne": "增加 1", "command.incrementNumberByOneTenth": "增加 0.1", "command.incrementNumberByTen": "增加 10", "command.matchTag": "转至匹配对", "command.mergeLines": "合并行", "command.nextEditPoint": "转到下一编辑点", "command.prevEditPoint": "转到上一编辑点", "command.reflectCSSValue": "映射 CSS 值", "command.removeTag": "删除标记", "command.selectNextItem": "选择下一项", "command.selectPrevItem": "选择上一项", "command.showEmmetCommands": "显示 Emmet 命令", "command.splitJoinTag": "分离/联接标记", "command.toggleComment": "切换注释", "command.updateImageSize": "更新图像大小", "command.updateTag": "更新标记", "command.wrapWithAbbreviation": "使用缩写包围", "description": "适用于 VS Code 的 Emmet 支持", "emmetExclude": "不应展开 Emmet 缩写的语言数组。", "emmetExtensionsPath": "一组路径，其中每个路径都可以包含 Emmet syntaxProfiles 和/或代码片段。\r\n发生冲突时，后面路径的配置文件/代码段将重写以前的路径。\r\n有关详细信息和示例片段文件，请参见 https://code.visualstudio.com/docs/editor/emmet。", "emmetExtensionsPathItem": "包含 Emmet syntaxProfiles 和/或片段的路径。", "emmetIncludeLanguages": "在默认不受支持的语言中启用 Emmet 缩写。在此语言和 Emmet 支持的语言之间添加映射。\r\n 例如: `{\"vue-html\": \"html\", \"javascript\": \"javascriptreact\"}`", "emmetOptimizeStylesheetParsing": "当设置为 `false` 时，将分析整个文件并确定当前位置能否展开 Emmet 缩写。当设置为 `true` 时，将仅在 CSS/SCSS/LESS 文件中分析当前位置周围的内容。", "emmetPreferences": "用于修改 Emmet 某些操作和解析程序的行为的首选项。", "emmetPreferencesAllowCompactBoolean": "若为“true”，将生成紧凑型布尔属性。", "emmetPreferencesBemElementSeparator": "在使用 BEM 过滤器时，类名使用的元素分隔符。", "emmetPreferencesBemModifierSeparator": "在使用 BEM 过滤器时，类名使用的修饰符分隔符。", "emmetPreferencesCssAfter": "展开 CSS 缩写时在 CSS 属性末尾放置的符号。", "emmetPreferencesCssBetween": "展开 CSS 缩写时在 CSS 属性之间放置的符号。", "emmetPreferencesCssColorShort": "如果为 \"true\"，则 `#f` 之类的颜色值将扩展为 `#fff` 而不是 `#ffffff`。", "emmetPreferencesCssFuzzySearchMinScore": "显示的缩写模糊匹配应达到的最低分数 (0 到 1 之间)。较低的值可能使匹配错误变多，较高的值可能将不会显示应有的匹配项。", "emmetPreferencesCssMozProperties": "Emmet 缩写中使用的由 \"-\" 打头有 \"moz\" 前缀的 CSS 属性，使用半角逗号 (\",\") 进行分隔。若要始终避免 \"moz\" 前缀，请设为空字符串。", "emmetPreferencesCssMsProperties": "Emmet 缩写中使用的由 \"-\" 打头有 \"ms\" 前缀的 CSS 属性，使用半角逗号 (\",\") 进行分隔。若要始终避免 \"ms\" 前缀，请设为空字符串。", "emmetPreferencesCssOProperties": "Emmet 缩写中使用的由 \"-\" 打头有 \"o\" 前缀的 CSS 属性，使用半角逗号 (\",\") 进行分隔。若要始终避免 \"o\" 前缀，请设为空字符串。", "emmetPreferencesCssWebkitProperties": "Emmet 缩写中使用的由 \"-\" 打头有 \"webkit\" 前缀的 CSS 属性，使用半角逗号 (\",\") 进行分隔。若要始终避免 \"webkit\" 前缀，请设为空字符串。", "emmetPreferencesFilterCommentAfter": "使用注释过滤器时，应置于匹配元素后注释的定义。", "emmetPreferencesFilterCommentBefore": "使用注释过滤器时，应置于匹配元素前注释的定义。", "emmetPreferencesFilterCommentTrigger": "用半角逗号(“,”)隔开的属性名缩写的数组，将由注释筛选器应用。", "emmetPreferencesFloatUnit": "浮点数值的默认单位。", "emmetPreferencesFormatForceIndentTags": "表示应始终向内缩进的标记名称数组。", "emmetPreferencesFormatNoIndentTags": "从不应向内缩进的标记名称数组。", "emmetPreferencesIntUnit": "整数值的默认单位。", "emmetPreferencesOutputInlineBreak": "要在这些元素之间放置换行符时所需的同级内联元素的数量。如果为 \"0\"，则内联元素始终扩展到一行。", "emmetPreferencesOutputReverseAttributes": "如果为 \"true\"，则在解析代码片段时反转属性合并方向。", "emmetPreferencesOutputSelfClosingStyle": "自结束标记的样式: html (`<br>`)、xml (`<br/>`) 或 xhtml (`<br />`)。", "emmetPreferencesSassAfter": "在 Sass 文件中展开 CSS 缩写时在 CSS 属性末尾放置的符号。", "emmetPreferencesSassBetween": "在 Sass 文件中展开 CSS 缩写时在 CSS 属性之间放置的符号。", "emmetPreferencesStylusAfter": "在 Stylus 文件中展开 CSS 缩写时在 CSS 属性末尾放置的符号。", "emmetPreferencesStylusBetween": "在 Stylus 文件中展开 CSS 缩写时在 CSS 属性之间放置的符号。", "emmetShowAbbreviationSuggestions": "将可能的 Emmet 缩写作为建议进行显示。当在样式表中或 emmet.showExpandedAbbreviation 设置为 `\"never\"` 时不适用。", "emmetShowExpandedAbbreviation": "以建议的形式显示展开的 Emmet 缩写。\r\n选项 `\"inMarkupAndStylesheetFilesOnly\"` 适用于 html、haml、jade、slim、xml、xsl、css、scss、sass、less 和 stylus。\r\n无论 markup/css 如何，选项 `\"always\"` 都适用于文件的各个部分。", "emmetShowSuggestionsAsSnippets": "若为 `true`，Emmet 建议将显示为代码片段。可以在 `#editor.snippetSuggestions#` 设置中排列其顺序。", "emmetSyntaxProfiles": "为指定的语法定义配置文件或使用带有特定规则的配置文件。", "emmetTriggerExpansionOnTab": "启用后，在按 Tab 时会展开 Emmet 缩写，即使未显示完成。禁用后，仍可通过按 TAB 接受显示的完成。", "emmetUseInlineCompletions": "如果为 `true`，Emmet 将使用内联完成来建议扩展。如果要防止非内联完成项提供程序在此设置为 `true` 时频繁显示，请将 `other` 项的 `#editor.quickSuggestions#` 转换为 `inline` 或 `off`。", "emmetVariables": "用于 Emmet 代码片段的变量。"}}}