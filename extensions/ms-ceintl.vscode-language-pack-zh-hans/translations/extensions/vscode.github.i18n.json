{"": ["--------------------------------------------------------------------------------------------", "Copyright (c) Microsoft Corporation. All rights reserved.", "Licensed under the MIT License. See License.txt in the project root for license information.", "--------------------------------------------------------------------------------------------", "Do not edit this file. It is machine generated."], "version": "1.0.0", "contents": {"bundle": {"Checkout on vscode.dev": "签出 vscode.dev", "Commit Changes": "提交更改", "Copy Anyway": "仍然复制", "Copy vscode.dev Link": "复制 vscode.dev 链接", "Create Fork": "创建分支", "Create GitHub fork": "创建 GitHub 分支", "Create PR": "创建 PR", "Creating GitHub Pull Request...": "正在创建 GitHub 拉取请求…", "Creating first commit": "正在创建第一个提交", "Forking \"{0}/{1}\"...": "正在创建“{0}/{1}”的分支…", "Learn More": "了解详细信息", "Log level: {0}": "日志级别: {0}", "No": "否", "No GitHub remotes found that contain this commit.": "找不到包含此提交的 GitHub 远程。", "No template": "无模板", "Open PR": "打开 PR", "Open on GitHub": "在 GitHub 上打开", "Pick a folder to publish to GitHub": "选择一个要发布到 GitHub 的文件夹", "Publish Branch & Copy Link": "发布分支并复制链接", "Publishing to a private GitHub repository": "正在发布到专用 GitHub 仓库", "Publishing to a public GitHub repository": "正在发布到公共 GitHub 仓库", "Pull Changes & Copy Link": "拉取更改并复制链接", "Push Commits & Copy Link": "推送提交并复制链接", "Pushing changes...": "正在推送更改…", "Select the Pull Request template": "选择拉取请求模板", "Select which files should be included in the repository.": "选择应包含在仓库中的文件。", "Successfully published the \"{0}\" repository to GitHub.": "已将“{0}”仓库成功发布到 GitHub。", "The PR \"{0}/{1}#{2}\" was successfully created on GitHub.": "已在 GitHub 上成功创建 PR“{0}/{1}#{2}”。", "The current branch has unpublished commits. Would you like to push your commits before copying a link?": "当前分支有未发布的提交。是否要在复制链接之前推送提交?", "The current branch is not published to the remote. Would you like to publish your branch before copying a link?": "当前分支未发布到远程。是否要在复制链接之前发布分支?", "The current branch is not up to date. Would you like to pull before copying a link?": "当前分支不是最新的。是否要在复制链接之前拉取?", "The current file has uncommitted changes. Please commit your changes before copying a link.": "当前文件具有未提交的更改。请在复制链接之前提交更改。", "The fork \"{0}\" was successfully created on GitHub.": "已在 GitHub 上成功创建分支“{0}”。", "Uploading files": "正在上传文件", "You don't have permissions to push to \"{0}/{1}\" on GitHub. Would you like to create a fork and push to it instead?": "你没有在 GitHub 上推送到“{0}/{1}”的权限。是否要创建一个分支并改为推送到该分支?", "Your push to \"{0}/{1}\" was rejected by GitHub because push protection is enabled and one or more secrets were detected.": "GitHub 拒绝了你对 \"{0}/{1}\" 的推送，因为已启用推送保护并检测到一个或多个机密。", "{0} Open on GitHub": "{0} 在 GitHub 上打开"}, "package": {"command.copyVscodeDevLink": "复制 vscode.dev 链接", "command.openOnGitHub": "在 GitHub 上打开", "command.openOnVscodeDev": "在 vscode.dev 中打开", "command.publish": "发布到 GitHub", "config.branchProtection": "控制是否查询 GitHub 存储库的存储库规则", "config.gitAuthentication": "控制是否在 VS Code 中为 git 命令启用自动 GitHub 身份验证。", "config.gitProtocol": "控制用于克隆 GitHub 仓库的协议", "config.showAvatar": "控制是否以各种悬停方式显示提交作者的 GitHub 虚拟形象 (，例如 Git 追溯、日程表、源代码管理图等 )", "description": "适用于 VS Code 的 GitHub 功能", "displayName": "GitHub", "welcome.publishFolder": "可以直接将此文件夹发布到 GitHub 仓库。发布后，你将有权访问由 Git 和 GitHub 提供支持的源代码管理功能。\r\n[$(github) 发布到 GitHub](command:github.publish)", "welcome.publishWorkspaceFolder": "可以直接将工作区文件夹发布到 GitHub 仓库。发布后，你将有权访问由 Git 和 GitHub 提供支持的源代码管理功能。\r\n[$(github) 发布到 GitHub](command:github.publish)"}}}