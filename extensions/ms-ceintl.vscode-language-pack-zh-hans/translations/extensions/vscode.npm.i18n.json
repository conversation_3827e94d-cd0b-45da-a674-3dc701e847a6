{"": ["--------------------------------------------------------------------------------------------", "Copyright (c) Microsoft Corporation. All rights reserved.", "Licensed under the MIT License. See License.txt in the project root for license information.", "--------------------------------------------------------------------------------------------", "Do not edit this file. It is machine generated."], "version": "1.0.0", "contents": {"bundle": {"Could not find a valid npm script at the selection.": "在所选内容中找不到有效的 npm 脚本。", "Debug": "调试", "Debug Script": "调试脚本", "Default bower.json": "默认 bower.json", "Default package.json": "默认 package.json", "Do not show again": "不再显示", "Latest version: {0}": "最新版本: {0}", "Latest version: {0} published {1}": "最新版本: {0} 已发布 {1}", "Learn more": "了解详细信息", "Matches the most recent major version (1.x.x)": "与最新主要版本(1.x.x)匹配", "Matches the most recent minor version (1.2.x)": "与最新次要版本(1.2.x)匹配", "No scripts found.": "未找到脚本。", "Npm task detection: failed to parse the file {0}": "npm 任务检测: 无法分析文件 {0}", "Request to the NPM repository failed: {0}": "对 NPM 仓库发出的请求失败: {0}", "Request to the bower repository failed: {0}": "对 Bower 仓库发出的请求失败: {0}", "Run Script": "运行脚本", "Run the script as a task": "将脚本作为任务运行", "Runs the script under the debugger": "在调试器下运行脚本", "The currently latest version of the package": "当前最新版本的包", "The setting \"npm.autoDetect\" is \"off\".": "\"npm.autoDetect\" 设置已设为“关”。", "Using {0} as the preferred package manager. Found multiple lockfiles for {1}.  To resolve this issue, delete the lockfiles that don't match your preferred package manager or change the setting \"npm.packageManager\" to a value other than \"auto\".": "将 {0} 用作首选包管理器。为 {1} 找到多个锁文件。  要解决此问题，请删除与首选包管理器不匹配的锁文件，或将设置 \"npm.packageManager\" 更改为 \"auto\" 以外的值。", "in {0}": "{0} 后", "latest": "最新", "now": "现在", "{0} day": "{0} 天", "{0} day ago": "{0} 天前", "{0} days": "{0} 天", "{0} days ago": "{0} 天前", "{0} hour": "{0} 小时", "{0} hour ago": "{0} 小时前", "{0} hours": "{0} 小时", "{0} hours ago": "{0} 小时前", "{0} hr": "{0} 小时", "{0} hr ago": "{0} 小时前", "{0} hrs": "{0} 小时", "{0} hrs ago": "{0} 小时前", "{0} min": "{0} 分钟", "{0} min ago": "{0} 分钟前", "{0} mins": "{0} 分钟", "{0} mins ago": "{0} 分钟前", "{0} minute": "{0} 分钟", "{0} minute ago": "{0} 分钟前", "{0} minutes": "{0} 分钟", "{0} minutes ago": "{0} 分钟前", "{0} mo": "{0} 个月", "{0} mo ago": "{0} 个月前", "{0} month": "{0} 月", "{0} month ago": "{0} 个月前", "{0} months": "{0} 个月", "{0} months ago": "{0} 个月前", "{0} mos": "{0} 个月", "{0} mos ago": "{0} 个月前", "{0} sec": "{0} 秒", "{0} sec ago": "{0} 秒前", "{0} second": "{0} 秒", "{0} second ago": "{0} 秒前", "{0} seconds": "{0} 秒", "{0} seconds ago": "{0} 秒前", "{0} secs": "{0} 秒", "{0} secs ago": "{0} 秒前", "{0} week": "{0} 周", "{0} week ago": "{0} 周前", "{0} weeks": "{0} 周", "{0} weeks ago": "{0} 周前", "{0} wk": "{0} 周", "{0} wk ago": "{0} 周前", "{0} wks": "{0} 周", "{0} wks ago": "{0} 周前", "{0} year": "{0} 年", "{0} year ago": "{0} 年前", "{0} years": "{0} 年", "{0} years ago": "{0} 年前", "{0} yr": "{0} 年", "{0} yr ago": "{0} 年前", "{0} yrs": "{0} 年", "{0} yrs ago": "{0} 年前"}, "package": {"command.debug": "调试", "command.openScript": "打开", "command.packageManager": "获取已配置的包管理器", "command.refresh": "刷新", "command.run": "运行", "command.runInstall": "运行 install", "command.runScriptFromFolder": "运行文件夹中的NPM脚本...", "command.runSelectedScript": "运行脚本", "config.npm.autoDetect": "控制是否自动检测 npm 脚本。", "config.npm.enableRunFromFolder": "从资源管理器上下文菜单中启用运行文件夹中包含的 NPM 脚本。", "config.npm.enableScriptExplorer": "在没有顶级 \"package.json\" 文件时，为 npm 脚本启用资源管理器视图。", "config.npm.exclude": "配置应从自动脚本检测中排除的文件夹的 glob 模式。", "config.npm.fetchOnlinePackageInfo": "从 https://registry.npmjs.org 和 https://registry.bower.io 获取数据，以提供自动补全和 npm 依赖项上的悬停功能信息。", "config.npm.packageManager": "用于安装依赖项的包管理器。", "config.npm.packageManager.auto": "根据锁定文件和已安装的包管理器自动检测要使用的包管理器。", "config.npm.packageManager.bun": "使用 bun 作为包管理器。", "config.npm.packageManager.npm": "使用 npm 作为包管理器。", "config.npm.packageManager.pnpm": "使用 pnpm 作为包管理器。", "config.npm.packageManager.yarn": "使用 yarn 作为包管理器。", "config.npm.runSilent": "使用 `--silent` 选项运行 npm 命令。", "config.npm.scriptExplorerAction": "NPM 脚本资源管理器中使用的默认单击操作: `open` 或 `run`，默认值为 `open`。", "config.npm.scriptExplorerExclude": "正则表达式的数组，指示应从 NPM 脚本视图中排除哪些脚本。", "config.npm.scriptHover": "使用脚本的“运行”和“调试”命令显示悬停。", "config.npm.scriptRunner": "用于运行脚本的脚本运行程序。", "config.npm.scriptRunner.auto": "根据锁定文件和已安装的包管理器自动检测要使用的脚本运行器。", "config.npm.scriptRunner.bun": "使用 bun 作为脚本运行程序。", "config.npm.scriptRunner.node": "使用 Node.js 作为脚本运行程序。", "config.npm.scriptRunner.npm": "使用 npm 作为脚本运行程序。", "config.npm.scriptRunner.pnpm": "使用 pnpm 作为脚本运行程序。", "config.npm.scriptRunner.yarn": "使用 Yarn 作为脚本运行程序。", "description": "为 npm 脚本提供任务支持的扩展。", "displayName": "适用于 VS Code 的 npm 支持", "npm.parseError": "npm 任务检测: 无法分析文件 {0}", "taskdef.path": "包含 package.json 文件的文件夹路径，其中 package.json 文件提供脚本。可以省略。", "taskdef.script": "要自定义的 npm 脚本。", "view.name": "npm 脚本", "virtualWorkspaces": "需要运行 “npm” 命令的功能在虚拟工作区中不可用。", "workspaceTrust": "此扩展可以执行任务，需要信任才能运行。"}}}