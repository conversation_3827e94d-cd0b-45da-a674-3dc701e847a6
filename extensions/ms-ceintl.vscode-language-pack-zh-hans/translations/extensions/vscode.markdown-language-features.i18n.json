{"": ["--------------------------------------------------------------------------------------------", "Copyright (c) Microsoft Corporation. All rights reserved.", "Licensed under the MIT License. See License.txt in the project root for license information.", "--------------------------------------------------------------------------------------------", "Do not edit this file. It is machine generated."], "version": "1.0.0", "contents": {"bundle": {"...1 additional file not shown": "...1 个其他文件未显示", "...{0} additional files not shown": "...{0} 个其他文件未显示", "Allow all content and script execution. Not recommended": "允许所有内容，执行所有脚本。不推荐", "Allow insecure content": "允许不安全内容", "Allow insecure local content": "允许不安全的本地内容", "Always": "始终", "An unexpected error occurred while restoring the Markdown preview.": "还原 Markdown 预览时出现意外错误。", "Checking for Markdown links to update": "正在检查要更新的 Markdown 链接", "Content Disabled Security Warning": "已禁用内容安全警告", "Could not load 'markdown.styles': {0}": "无法加载 'markdown.styles': {0}", "Could not open {0}": "无法打开 {0}", "Disable": "禁用", "Disable preview security warning in this workspace": "在此工作区中取消预览安全警告", "Disable validation of Markdown links": "禁用 Markdown 链接验证", "Does not affect the content security level": "不影响内容安全级别", "Enable": "启用", "Enable loading content over http": "允许通过 http 载入内容", "Enable loading content over http served from localhost": "允许通过 http 载入来自 localhost 的内容", "Enable preview security warnings in this workspace": "在此工作区中启用预览安全警告", "Enable validation of Markdown links": "启用 Markdown 链接验证", "Exclude '{0}' from link validation.": "从链接验证中排除 \"{0}\"。", "Extract to link definition": "提取到链接定义", "File does not exist at path: {0}": "路径中不存在文件: {0}", "Find file references failed. No resource provided.": "查找文件引用失败。未提供资源。", "Finding file references": "查找文件引用", "Follow link": "转到链接", "Go to link definition": "转到链接定义", "Header does not exist in file: {0}": "文件中不存在标头: {0}", "Insert Markdown Audio": "插入 Markdown 音频", "Insert Markdown Image": "插入 Markdown 图像", "Insert Markdown Images": "插入 Markdown 图像", "Insert Markdown Images and Links": "插入 Markdown 图像和链接", "Insert Markdown Link": "插入 Markdown 链接", "Insert Markdown Links": "插入 Markdown 链接", "Insert Markdown Media": "插入 Markdown 媒体", "Insert Markdown Media and Images": "插入 Markdown 媒体和图像", "Insert Markdown Media and Links": "插入 Markdown 媒体和链接", "Insert Markdown Video": "插入 Markdown 视频", "Insert image": "插入映像", "Insert link": "插入链接", "Link definition for '{0}' already exists": "“{0}”的链接定义已存在", "Link definition is unused": "链接定义未使用", "Link is already a reference": "链接已是引用", "Link is also defined here": "还在此处定义了链接", "Link to '# {0}' in '{1}'": "链接到“{1}”中的“# {0}”", "Link to '{0}'": "链接到“{0}”", "Markdown Language Server": "Markdown 语言服务器", "Markdown link validation disabled": "已禁用 Markdown 链接验证", "Markdown link validation enabled": "已启用 Markdown 链接验证", "Media": "媒体", "More Information": "更多信息", "Never": "从不", "No": "否", "No header found: '{0}'": "找不到标头:“{0}”", "No link definition found: '{0}'": "找不到链接定义:“{0}”", "Not on link": "不在链接上", "Only load secure content": "仅载入安全内容", "Paste and update pasted links": "粘贴和更新已粘贴的链接", "Potentially unsafe or insecure content has been disabled in the Markdown preview. Change the Markdown preview security setting to allow insecure content or enable scripts": "已禁用此 Markdown 预览中的可能不安全的内容。更改 Markdown 预览安全设置以允许不安全内容或启用脚本", "Preview {0}": "预览 {0}", "Reference link '{0}'": "参考链接“{0}”", "Remove duplicate link definition": "删除重复的链接定义", "Remove unused link definition": "移除未使用的链接定义", "Renaming is not supported here. Try renaming a header or link.": "此处不支持重命名。尝试重命名标头或链接。", "Select security settings for Markdown previews in this workspace": "选择此工作区中 Markdown 预览的安全设置", "Some content has been disabled in this document": "已禁用此文档中的部分内容", "Strict": "严格", "Update Markdown links for '{0}'?": "是否更新“{0}”的 Markdown 链接?", "Update Markdown links for the following {0} files?": "是否更新以下 {0} 个文件的 Markdown 链接?", "Yes": "是", "[Preview] {0}": "[预览] {0}", "{0} cannot be found": "找不到 {0}"}, "package": {"configuration.copyIntoWorkspace.mediaFiles": "尝试将外部图像和视频文件复制到工作区。", "configuration.copyIntoWorkspace.never": "请勿将外部文件复制到工作区。", "configuration.markdown.copyFiles.destination": "配置通过复制/粘贴或拖放创建的文件的路径及文件名。这是与 Markdown 文档路径匹配的 glob 映射到应在其中创建新文件的目标路径。\r\n\r\n目标路径可使用以下变量:\r\n\r\n- `${documentDirName}` - Markdown 文档的绝对父级目录路径，例如 `/Users/<USER>/myProject/docs`。\r\n- `${documentRelativeDirName}` - Markdown 文档的相对父级目录路径，例如 `docs`。如果该文件不是工作区的一部分，则此项与 `${documentDirName}` 相同。\r\n- `${documentFileName}` - Markdown 文档的完整文件名，例如 `README.md`。\r\n- `${documentBaseName}` - Markdown 文档的基名，例如 `README`。\r\n- `${documentExtName}` - Markdown 文档的扩展名，例如 `md`。\r\n- `${documentFilePath}` - Markdown 文档的绝对路径，例如 `/Users/<USER>/myProject/docs/README.md`。\r\n- `${documentRelativeFilePath}` - Markdown 文档的相对路径，例如 `docs/README.md`。如果文件不是工作区的一部分，则此项与 `${documentFilePath}` 相同。\r\n- `${documentWorkspaceFolder}` - Markdown 文档的工作区文件夹，例如 `/Users/<USER>/myProject`。如果该文件不是工作区的一部分，则此项与 `${documentDirName}` 相同。\r\n- `${fileName}` - 所删除文件的文件名，例如 `image.png`。\r\n- `${fileExtName}` - 所删除文件的扩展名，例如 `png`。\r\n- “${unixTime}” - 当前 Unix 时间戳(以毫秒为单位)。", "configuration.markdown.copyFiles.overwriteBehavior": "控制通过放置或粘贴操作创建的文件是否应覆盖现有文件。", "configuration.markdown.copyFiles.overwriteBehavior.nameIncrementally": "如果已存在具有相同名称的文件，请向文件名追加一个数字，例如: `image.png` 变为 `image-1.png`。", "configuration.markdown.copyFiles.overwriteBehavior.overwrite": "如果已存在具有相同名称的文件，则覆盖。", "configuration.markdown.editor.drop.copyIntoWorkspace": "控制是否应将放置到 Markdown 编辑器中的工作区之外的文件复制到工作区中。\r\n\r\n使用 `#markdown.copyFiles.destination#` 配置复制或放置的文件应位于哪个位置", "configuration.markdown.editor.drop.enabled": "通过按住 Shift 来启用将文件放入 Markdown 编辑器。需要启用 `#editor.dropIntoEditor.enabled#`。", "configuration.markdown.editor.drop.enabled.always": "始终插入 Markdown 链接。", "configuration.markdown.editor.drop.enabled.never": "从不创建 Markdown 链接。", "configuration.markdown.editor.drop.enabled.smart": "当未放置到代码块或其他特殊元素中时，默认情况下智能创建 Markdown 链接。使用放置小组件，以在粘贴为纯文本或 Markdown 链接之间切换。", "configuration.markdown.editor.filePaste.audioSnippet": "将音频添加到 Markdown 时使用的代码片段。此代码片段可以使用以下变量:\r\n-“${src}”- 音频文件的解析路径。\r\n-“${title}”- 用于音频的标题。将自动为此变量创建代码片段占位符。", "configuration.markdown.editor.filePaste.copyIntoWorkspace": "控制是否应将粘贴到 Markdown 编辑器中的工作区之外的文件复制到工作区中。\r\n\r\n使用 `#markdown.copyFiles.destination#` 配置应在哪个位置创建复制的文件。", "configuration.markdown.editor.filePaste.enabled": "启用将文件粘贴到 Markdown 编辑器以创建 Markdown 链接。需要启用 `#editor.pasteAs.enabled#`。", "configuration.markdown.editor.filePaste.enabled.always": "始终插入 Markdown 链接。", "configuration.markdown.editor.filePaste.enabled.never": "从不创建 Markdown 链接。", "configuration.markdown.editor.filePaste.enabled.smart": "当不粘贴到代码块或其他特殊元素时，默认情况下可以智能创建 Markdown 链接。使用粘贴小组件，在以纯文本形式粘贴或以 Markdown 链接形式粘贴之间切换。", "configuration.markdown.editor.filePaste.videoSnippet": "将视频添加到 Markdown 时使用的代码片段。此代码片段可以使用以下变量:\r\n-“${src}”- 视频文件的解析路径。\r\n-“${title}”- 用于视频的标题。将自动为此变量创建代码片段占位符。", "configuration.markdown.editor.pasteUrlAsFormattedLink.enabled": "控制是否在将 URL 粘贴到 Markdown 编辑器时创建 Markdown 链接。需要启用“#editor.pasteAs.enabled#”。", "configuration.markdown.editor.updateLinksOnPaste.enabled": "启用/禁用粘贴选项，该选项可更新在 Markdown 编辑器之间复制和粘贴的文本中的链接和引用。\r\n\r\n若要使用此功能，粘贴包含可更新链接的文本后，只需单击“粘贴”小组件，然后选择“粘贴并更新粘贴的链接”即可。", "configuration.markdown.links.openLocation.beside": "打开活动编辑器旁边的链接。", "configuration.markdown.links.openLocation.currentGroup": "打开活动编辑器组中的链接。", "configuration.markdown.links.openLocation.description": "控制应在哪里打开 Markdown 文件中的链接。", "configuration.markdown.occurrencesHighlight.enabled": "启用突出显示当前文档中的链接匹配项。", "configuration.markdown.preferredMdPathExtensionStyle": "控制是否为指向 Markdown 文件的链接添加文件扩展名(例如“.md”)。通过工具(如路径完成或文件重命名)添加文件路径时，将使用此设置。", "configuration.markdown.preferredMdPathExtensionStyle.auto": "对于现有路径，请尝试维护文件扩展名样式。对于新路径，请添加文件扩展名。", "configuration.markdown.preferredMdPathExtensionStyle.includeExtension": "首选包括文件扩展名。例如，名为 \"file.md\" 的文件的路径完成将插入 \"file.md\"。", "configuration.markdown.preferredMdPathExtensionStyle.removeExtension": "首选删除文件扩展名。例如，名为 \"file.md\" 的文件的路径完成将插入 \"file\" 而不插入 \".md\"。", "configuration.markdown.preview.openMarkdownLinks.description": "控制如何打开 Markdown 预览中其他 Markdown 文件的链接。", "configuration.markdown.preview.openMarkdownLinks.inEditor": "尝试在编辑器中打开链接。", "configuration.markdown.preview.openMarkdownLinks.inPreview": "尝试在 Markdown 预览中打开链接。", "configuration.markdown.suggest.paths.enabled.description": "在 Markdown 文件中写入链接时启用路径建议。", "configuration.markdown.suggest.paths.includeWorkspaceHeaderCompletions": "为当前工作区中其他 Markdown 文件中的标头启用建议。接受这些建议之一将插入该文件中标头的完整路径，例如: `[link text](/path/to/file.md#header)`。", "configuration.markdown.suggest.paths.includeWorkspaceHeaderCompletions.never": "禁用工作区标头建议。", "configuration.markdown.suggest.paths.includeWorkspaceHeaderCompletions.onDoubleHash": "在路径中键入 `##` 后启用工作区标头建议，例如: `[link text](##`。", "configuration.markdown.suggest.paths.includeWorkspaceHeaderCompletions.onSingleOrDoubleHash": "在路径中键入 `##` 或 `#` 后启用工作区标头建议，例如: `[link text](#` 或 `[link text](##`。", "configuration.markdown.updateLinksOnFileMove.enableForDirectories": "启用在工作区中移动或重命名目录时更新链接。", "configuration.markdown.updateLinksOnFileMove.enabled": "重命名/移动工作区中的文件时，请尝试更新 Markdown 文件中的链接。使用 `#markdown.updateLinksOnFileMove.include#` 配置触发链接更新的文件。", "configuration.markdown.updateLinksOnFileMove.enabled.always": "始终自动更新链接。", "configuration.markdown.updateLinksOnFileMove.enabled.never": "从不尝试更新链接且不提示。", "configuration.markdown.updateLinksOnFileMove.enabled.prompt": "每次移动文件时进行提示。", "configuration.markdown.updateLinksOnFileMove.include": "指定触发自动链接更新的文件的 Glob 模式。有关此功能的详细信息，请参阅 `#markdown.updateLinksOnFileMove.enabled#`。", "configuration.markdown.updateLinksOnFileMove.include.property": "要与文件路径匹配的 glob 模式。设置为 true 以启用模式。", "configuration.markdown.validate.duplicateLinkDefinitions.description": "验证当前文件中的重复定义。", "configuration.markdown.validate.enabled.description": "启用 Markdown 文件中的所有错误报告。", "configuration.markdown.validate.fileLinks.enabled.description": "验证指向 Markdown 文件中其他文件的链接，例如 `[link](/path/to/file.md)`。此操作将检查目标文件是否存在。需要启用 ·#markdown.validate.enabled#·。", "configuration.markdown.validate.fileLinks.markdownFragmentLinks.description": "验证 Markdown 文件中其他文件中标头的链接片段部分，例如: `[link](/path/to/file.md#header)`。默认情况下从 `#markdown.validate.fragmentLinks.enabled#` 继承设置值。", "configuration.markdown.validate.fragmentLinks.enabled.description": "验证当前 Markdown 文件中标头的片段链接，例如: `[link](#header)`。需要启用 `#markdown.validate.enabled#`。", "configuration.markdown.validate.ignoredLinks.description": "配置不应被验证的链接。例如，添加 `/about` 不会验证链接 `[about](/about)`，而 `/assets/**/*.svg` 会允许你跳过对 `assets` 目录下 `.svg` 文件的任何链接的验证。", "configuration.markdown.validate.referenceLinks.enabled.description": "验证 Markdown 文件中的引用链接，例如: `[link][ref]`。需要启用 `#markdown.validate.enabled#`。", "configuration.markdown.validate.unusedLinkDefinitions.description": "验证当前文件中未使用的链接定义。", "configuration.pasteUrlAsFormattedLink.always": "始终插入 Markdown 链接。", "configuration.pasteUrlAsFormattedLink.never": "从不创建 Markdown 链接。", "configuration.pasteUrlAsFormattedLink.smart": "当不粘贴到代码块或其他特殊元素时，默认情况下可以智能创建 Markdown 链接。使用粘贴小组件，在以纯文本形式粘贴或以 Markdown 链接形式粘贴之间切换。", "configuration.pasteUrlAsFormattedLink.smartWithSelection": "当你选择了文本，并且不粘贴到代码块或其他特殊元素时，默认情况下可以智能创建 Markdown 链接。使用粘贴小组件，在以纯文本形式粘贴或以 Markdown 链接形式粘贴之间切换。", "description": "为 Markdown 提供丰富的语言支持。", "displayName": "Markdown 语言功能", "markdown.copyImage.title": "复制图像", "markdown.editor.insertImageFromWorkspace": "从工作区插入映像", "markdown.editor.insertLinkFromWorkspace": "在工作区中插入文件链接", "markdown.findAllFileReferences": "查找文件引用", "markdown.openImage.title": "打开图像", "markdown.preview.breaks.desc": "设置换行符在 Markdown 预览中的呈现方式。如果将其设置为“true”，则将为段落内的新行创建一个“<br>”。", "markdown.preview.doubleClickToSwitchToEditor.desc": "在 Markdown 预览中双击以切换到编辑器。", "markdown.preview.fontFamily.desc": "控制 Markdown 预览中使用的字体系列。", "markdown.preview.fontSize.desc": "控制 Markdown 预览中使用的字号(以像素为单位)。", "markdown.preview.lineHeight.desc": "控制 Markdown 预览中使用的行高。此数值与字号相关。", "markdown.preview.linkify": "将类似于 URL 的文本转换为 Markdown 预览中的链接。", "markdown.preview.markEditorSelection.desc": "在 Markdown 预览中标记当前的编辑器选定内容。", "markdown.preview.refresh.title": "刷新预览", "markdown.preview.scrollEditorWithPreview.desc": "滚动 Markdown 预览时，更新其编辑器视图。", "markdown.preview.scrollPreviewWithEditor.desc": "滚动 Markdown 编辑器时，更新其预览视图。", "markdown.preview.title": "打开预览", "markdown.preview.toggleLock.title": "切换开关锁定预览", "markdown.preview.typographer": "在 Markdown 预览中启用或一些与语言无关的替换和引文美化。", "markdown.previewSide.title": "打开侧边预览", "markdown.server.log.desc": "控制 Markdown 语言服务器的日志记录级别。", "markdown.showLockedPreviewToSide.title": "在侧边打开锁定的预览", "markdown.showPreviewSecuritySelector.title": "更改预览安全设置", "markdown.showSource.title": "显示源", "markdown.styles.dec": "要从 Markdown 预览使用的 CSS 样式表的 URL 或本地路径的列表。相对路径解释为相对于资源管理器中打开的文件夹。如果没有打开的文件夹，则解释为相对于 Markdown 文件的位置。所有 '\\' 都需写为 '\\\\'。", "markdown.trace.extension.desc": "对 Markdown 扩展启用调试日志记录。", "markdown.trace.server.desc": "跟踪 VS Code 和 Markdown 语言服务器之间的通信。", "workspaceTrust": "加载在工作区中配置的样式时需要。"}}}