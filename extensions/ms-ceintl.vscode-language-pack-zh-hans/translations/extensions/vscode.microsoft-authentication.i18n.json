{"": ["--------------------------------------------------------------------------------------------", "Copyright (c) Microsoft Corporation. All rights reserved.", "Licensed under the MIT License. See License.txt in the project root for license information.", "--------------------------------------------------------------------------------------------", "Do not edit this file. It is machine generated."], "version": "1.0.0", "contents": {"bundle": {"Authorization code is required.": "授权代码是必需的。", "Error validating custom environment setting: {0}": "验证自定义环境设置时出错: {0}", "Having trouble logging in? Would you like to try a different way? ({0})": "登录时遇到问题? 是否要尝试其他方式? ({0})", "Microsoft Account configuration has been changed.": "Microsoft 帐户配置已更改。", "Microsoft Authentication": "Microsoft 身份验证", "Microsoft Sovereign Cloud Authentication": "Microsoft 主权云身份验证", "No": "否", "Open settings": "打开设置", "Paste authorization code here...": "在此处粘贴授权代码...", "Provide the authorization code to complete the sign in flow.": "提供授权代码以完成登录流。", "Reload": "重新加载", "Signing in to Microsoft...": "正在登录到 Microsoft...", "Signing in to your account...": "正在登录到你的帐户...", "The environment `{0}` is not a valid environment.": "环境`{0}`不是有效的环境。", "Yes": "是", "You have been signed out because reading stored authentication information failed.": "你已被注销，因为未能读取存储的身份验证信息。", "You have not yet finished authorizing this extension to use your Microsoft Account. Would you like to try a different way? ({0})": "你尚未完成授权此扩展来使用 Microsoft 帐户。是否要尝试其他方式? ({0})", "You must also specify a custom environment in order to use the custom environment auth provider.": "还必须指定自定义环境以使用自定义环境身份验证提供程序。"}, "package": {"description": "Microsoft 身份验证提供程序", "displayName": "Microsoft 帐户", "microsoft-authentication.implementation.description": "用于使用Microsoft 帐户登录的身份验证实现。\r\n\r\n*注意： 'classic' 实现已弃用，并将在将来的版本中与此设置一起删除。如果只有 “classic” 实现适用于你，请 [open an issue](command：workbench.action.openIssueReporter) 并解释你尝试登录的内容。*", "microsoft-authentication.implementation.enumDescriptions.classic": "(弃用) 使用经典身份验证流使用Microsoft 帐户登录。", "microsoft-authentication.implementation.enumDescriptions.msal": "使用 Microsoft 身份验证库(MSAL) 通过 Microsoft 帐户登录。", "microsoft-sovereign-cloud.customEnvironment.activeDirectoryEndpointUrl.description": "自定义主权云的 Active Directory 终结点。", "microsoft-sovereign-cloud.customEnvironment.activeDirectoryResourceId.description": "自定义主权云的 Active Directory 资源 ID。", "microsoft-sovereign-cloud.customEnvironment.description": "用于 Microsoft 主权云身份验证提供程序的主权云的自定义配置。使用此功能需要将`#microsoft-sovereign-cloud.environment#`设置为“custom”。", "microsoft-sovereign-cloud.customEnvironment.managementEndpointUrl.description": "自定义主权云的管理终结点。", "microsoft-sovereign-cloud.customEnvironment.name.description": "自定义主权云的名称。", "microsoft-sovereign-cloud.customEnvironment.portalUrl.description": "自定义主权云的门户 URL。", "microsoft-sovereign-cloud.customEnvironment.resourceManagerEndpointUrl.description": "自定义主权云的资源管理器终结点。", "microsoft-sovereign-cloud.environment.description": "用于身份验证的主权云。如果选择“custom”，则还必须设置`#microsoft-sovereign-cloud.customEnvironment#`。", "microsoft-sovereign-cloud.environment.enumDescriptions.AzureChinaCloud": "Azure 中国", "microsoft-sovereign-cloud.environment.enumDescriptions.AzureUSGovernment": "Azure 美国政府", "microsoft-sovereign-cloud.environment.enumDescriptions.custom": "自定义 Microsoft 主权云", "signIn": "登录", "signOut": "注销"}}}