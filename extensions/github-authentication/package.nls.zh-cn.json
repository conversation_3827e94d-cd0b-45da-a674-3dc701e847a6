{"displayName": "GitHub 身份验证", "description": "GitHub 身份验证提供程序", "config.github-enterprise.title": "GHE.com 和 GitHub Enterprise Server 身份验证", "config.github-enterprise.uri.description": "您的 GHE.com 或 GitHub Enterprise Server 实例的 URI。\n\n示例：\n* GHE.com: `https://octocat.ghe.com`\n* GitHub Enterprise Server: `https://github.octocat.com`\n\n> **注意：** 这不应设置为 GitHub.com URI。如果您的帐户存在于 GitHub.com 上或是 GitHub Enterprise 托管用户，则不需要任何其他配置，只需登录 GitHub 即可。"}