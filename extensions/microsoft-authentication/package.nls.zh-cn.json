{"displayName": "Microsoft 帐户", "description": "Microsoft 身份验证提供程序", "signIn": "登录", "signOut": "注销", "microsoft-authentication.implementation.description": {"message": "用于使用 Microsoft 帐户登录的身份验证实现。\n\n*注意：`classic` 实现已弃用，将在未来版本中与此设置一起删除。如果只有 `classic` 实现对您有效，请[打开问题](command:workbench.action.openIssueReporter)并解释您尝试登录的内容。*", "comment": ["{Locked='[(command:workbench.action.openIssueReporter)]'}", "The `command:` syntax will turn into a link. Do not translate it."]}, "microsoft-authentication.implementation.enumDescriptions.msal": "使用 Microsoft 身份验证库 (MSAL) 登录 Microsoft 帐户。", "microsoft-authentication.implementation.enumDescriptions.classic": "（已弃用）使用经典身份验证流程登录 Microsoft 帐户。", "microsoft-authentication.clientIdVersion.description": "用于使用 Microsoft 帐户登录的 Microsoft 帐户客户端 ID 版本。仅在被要求时更改此设置。默认值为 `v1`。", "microsoft-authentication.clientIdVersion.enumDescriptions.v1": "使用 v1 Microsoft 帐户客户端 ID 登录 Microsoft 帐户。", "microsoft-authentication.clientIdVersion.enumDescriptions.v2": "使用 v2 Microsoft 帐户客户端 ID 登录 Microsoft 帐户。", "microsoft-sovereign-cloud.environment.description": {"message": "用于身份验证的主权云。如果选择 `custom`，还必须设置 `#microsoft-sovereign-cloud.customEnvironment#` 设置。", "comment": ["{Locked='`#microsoft-sovereign-cloud.customEnvironment#`'}", "The `#microsoft-sovereign-cloud.customEnvironment#` syntax will turn into a link. Do not translate it."]}, "microsoft-sovereign-cloud.environment.enumDescriptions.AzureChinaCloud": "Azure 中国", "microsoft-sovereign-cloud.environment.enumDescriptions.AzureUSGovernment": "Azure 美国政府", "microsoft-sovereign-cloud.environment.enumDescriptions.custom": "自定义 Microsoft 主权云", "microsoft-sovereign-cloud.customEnvironment.description": {"message": "与 Microsoft 主权云身份验证提供程序一起使用的主权云的自定义配置。这与将 `#microsoft-sovereign-cloud.environment#` 设置为 `custom` 一起是使用此功能所必需的。", "comment": ["{Locked='`#microsoft-sovereign-cloud.environment#`'}", "The `#microsoft-sovereign-cloud.environment#` syntax will turn into a link. Do not translate it."]}, "microsoft-sovereign-cloud.customEnvironment.name.description": "自定义主权云的名称。", "microsoft-sovereign-cloud.customEnvironment.portalUrl.description": "自定义主权云的门户 URL。", "microsoft-sovereign-cloud.customEnvironment.managementEndpointUrl.description": "自定义主权云的管理端点。", "microsoft-sovereign-cloud.customEnvironment.resourceManagerEndpointUrl.description": "自定义主权云的资源管理器端点。", "microsoft-sovereign-cloud.customEnvironment.activeDirectoryEndpointUrl.description": "自定义主权云的 Active Directory 端点。", "microsoft-sovereign-cloud.customEnvironment.activeDirectoryResourceId.description": "自定义主权云的 Active Directory 资源 ID。"}