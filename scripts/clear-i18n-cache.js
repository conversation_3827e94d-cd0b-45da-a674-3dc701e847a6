#!/usr/bin/env node

/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

'use strict';

const fs = require('fs');
const path = require('path');
const os = require('os');

/**
 * 清理国际化缓存脚本
 * 用于解决中文国际化文档修改后出现乱码的问题
 */

function getJoyCodeUserDataPath() {
    const platform = process.platform;
    const homeDir = os.homedir();
    
    switch (platform) {
        case 'win32':
            return path.join(homeDir, 'AppData', 'Roaming', 'JoyCode');
        case 'darwin':
            return path.join(homeDir, 'Library', 'Application Support', 'JoyCode');
        case 'linux':
            return path.join(homeDir, '.config', 'JoyCode');
        default:
            return path.join(homeDir, '.config', 'JoyCode');
    }
}

function clearDirectory(dirPath) {
    if (fs.existsSync(dirPath)) {
        console.log(`清理目录: ${dirPath}`);
        try {
            fs.rmSync(dirPath, { recursive: true, force: true });
            console.log(`✓ 成功清理: ${dirPath}`);
            return true;
        } catch (error) {
            console.error(`✗ 清理失败: ${dirPath} - ${error.message}`);
            return false;
        }
    } else {
        console.log(`目录不存在: ${dirPath}`);
        return true;
    }
}

function clearFile(filePath) {
    if (fs.existsSync(filePath)) {
        console.log(`清理文件: ${filePath}`);
        try {
            fs.unlinkSync(filePath);
            console.log(`✓ 成功清理: ${filePath}`);
            return true;
        } catch (error) {
            console.error(`✗ 清理失败: ${filePath} - ${error.message}`);
            return false;
        }
    } else {
        console.log(`文件不存在: ${filePath}`);
        return true;
    }
}

function resetLanguagePacksConfig(userDataPath) {
    const languagePacksJsonPath = path.join(userDataPath, 'languagepacks.json');
    
    if (fs.existsSync(languagePacksJsonPath)) {
        console.log(`重置语言包配置: ${languagePacksJsonPath}`);
        try {
            // 尝试读取现有配置
            const content = fs.readFileSync(languagePacksJsonPath, { encoding: 'utf8' });
            const config = JSON.parse(content);
            
            // 验证并清理损坏的配置
            const cleanConfig = {};
            for (const [locale, pack] of Object.entries(config)) {
                if (pack && typeof pack === 'object' && pack.hash && pack.translations) {
                    cleanConfig[locale] = pack;
                } else {
                    console.log(`移除损坏的语言包配置: ${locale}`);
                }
            }
            
            // 写入清理后的配置
            fs.writeFileSync(languagePacksJsonPath, JSON.stringify(cleanConfig, null, 2), { encoding: 'utf8' });
            console.log(`✓ 成功重置语言包配置`);
            return true;
        } catch (error) {
            console.log(`配置文件损坏，重置为空配置: ${error.message}`);
            try {
                fs.writeFileSync(languagePacksJsonPath, '{}', { encoding: 'utf8' });
                console.log(`✓ 成功重置为空配置`);
                return true;
            } catch (writeError) {
                console.error(`✗ 重置配置失败: ${writeError.message}`);
                return false;
            }
        }
    } else {
        console.log(`语言包配置文件不存在: ${languagePacksJsonPath}`);
        return true;
    }
}

function clearI18nCache() {
    console.log('开始清理国际化缓存...\n');
    
    const userDataPath = getJoyCodeUserDataPath();
    console.log(`用户数据目录: ${userDataPath}\n`);
    
    let success = true;
    
    // 1. 清理语言包缓存目录
    const clpDir = path.join(userDataPath, 'clp');
    success = clearDirectory(clpDir) && success;
    
    // 2. 清理扩展缓存
    const extensionsCacheDir = path.join(userDataPath, 'CachedExtensions');
    success = clearDirectory(extensionsCacheDir) && success;
    
    // 3. 清理工作区缓存
    const workspaceStorageDir = path.join(userDataPath, 'workspaceStorage');
    success = clearDirectory(workspaceStorageDir) && success;
    
    // 4. 重置语言包配置
    success = resetLanguagePacksConfig(userDataPath) && success;
    
    // 5. 清理可能的临时文件
    const tempFiles = [
        path.join(userDataPath, 'logs'),
        path.join(userDataPath, 'CachedData'),
        path.join(userDataPath, 'CachedExtensionVSIXs')
    ];
    
    tempFiles.forEach(tempPath => {
        clearDirectory(tempPath);
    });
    
    console.log('\n' + '='.repeat(50));
    if (success) {
        console.log('✓ 国际化缓存清理完成！');
        console.log('请重新启动 JoyCode 以使更改生效。');
    } else {
        console.log('✗ 部分缓存清理失败，请检查权限或手动清理。');
    }
    console.log('='.repeat(50));
    
    return success;
}

// 主函数
function main() {
    const args = process.argv.slice(2);
    
    if (args.includes('--help') || args.includes('-h')) {
        console.log('用法: node clear-i18n-cache.js');
        console.log('');
        console.log('清理 JoyCode 国际化缓存，解决中文乱码问题。');
        console.log('');
        console.log('选项:');
        console.log('  --help, -h    显示帮助信息');
        console.log('  --force, -f   强制清理（跳过确认）');
        return;
    }
    
    const force = args.includes('--force') || args.includes('-f');
    
    if (!force) {
        console.log('此脚本将清理 JoyCode 的国际化缓存。');
        console.log('这将删除语言包缓存、扩展缓存和工作区缓存。');
        console.log('请确保 JoyCode 已完全关闭。');
        console.log('');
        console.log('按 Ctrl+C 取消，或按 Enter 继续...');
        
        // 等待用户输入
        process.stdin.setRawMode(true);
        process.stdin.resume();
        process.stdin.on('data', (key) => {
            if (key[0] === 3) { // Ctrl+C
                console.log('\n操作已取消。');
                process.exit(0);
            } else if (key[0] === 13) { // Enter
                process.stdin.setRawMode(false);
                process.stdin.pause();
                clearI18nCache();
            }
        });
    } else {
        clearI18nCache();
    }
}

if (require.main === module) {
    main();
}

module.exports = { clearI18nCache, getJoyCodeUserDataPath };
