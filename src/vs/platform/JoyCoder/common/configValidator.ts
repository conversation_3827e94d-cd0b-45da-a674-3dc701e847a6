/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

import * as fs from 'original-fs';
import * as path from 'path';
import { ILogService } from '../../log/common/log.js';
import { IEnvironmentService } from '../../environment/common/environment.js';
import { IFileService } from '../../files/common/files.js';
import { IDialogMainService } from '../../dialogs/electron-main/dialogMainService.js';
import { IProductService } from '../../product/common/productService.js';
import { parse as parseJsonc } from '../../../base/common/json.js';


/**
 * 验证用户配置文件是否有效
 * 如果配置文件损坏，则重命名配置目录并创建新的默认配置
 * 如果是第一次启动，则初始化配置目录，确保必要的目录和文件存在
 *
 * 版本号检查：只有在版本号不一致时才进行完整的配置检查
 */

/**
 * 获取存储的版本号
 */
async function getStoredVersion(logService: ILogService): Promise<string | null> {
    try {
        const homePath = process.env.HOME || process.env.USERPROFILE || '';
        if (!homePath) {
            return null;
        }

        // 使用默认的数据目录名称，因为这个函数没有 productService 参数
        const versionFilePath = path.join(homePath, '.joycode-editor', 'version.json');
        if (!fs.existsSync(versionFilePath)) {
            return null;
        }

        const content = fs.readFileSync(versionFilePath, 'utf8');
        const versionData = JSON.parse(content);
        return versionData.version || null;
    } catch (error) {
        logService.warn('[ConfigValidator] 读取版本号文件失败:', error);
        return null;
    }
}

/**
 * 保存当前版本号
 */
async function saveCurrentVersion(currentVersion: string, logService: ILogService): Promise<void> {
    try {
        const homePath = process.env.HOME || process.env.USERPROFILE || '';
        if (!homePath) {
            return;
        }

        const joycodeEditorPath = path.join(homePath, '.joycode-editor');
        await ensureDirectoryExists(joycodeEditorPath, logService);

        const versionFilePath = path.join(joycodeEditorPath, 'version.json');
        const versionData = {
            version: currentVersion,
            lastCheck: new Date().toISOString()
        };

        await fs.promises.writeFile(versionFilePath, JSON.stringify(versionData, null, 2), 'utf8');
    } catch (error) {
        logService.error(error);
    }
}

/**
 * 检查版本号是否需要进行配置检查
 */
async function shouldPerformConfigCheck(currentVersion: string, logService: ILogService): Promise<boolean> {
    const storedVersion = await getStoredVersion(logService);

    if (!storedVersion) {
        return true;
    }

    if (storedVersion !== currentVersion) {
        return true;
    }
    return false;
}
/**
 * 初始化配置目录
 * 在第一次启动时调用
 * 检查目录是否存在，如果不存在则创建
 */
async function initializeConfigDirs(logService: ILogService, productService: IProductService): Promise<void> {
    try {
        // 获取用户主目录路径
        const homePath = process.env.HOME || process.env.USERPROFILE || '';
        if (!homePath) {
            return;
        }

        // 从产品配置获取数据目录名称
        const dataFolderName = productService.dataFolderName || '.joycode-editor';
        const joycodePathName = '.joycode';

        // 配置目录路径
        const joycodePath = path.join(homePath, joycodePathName);
        const joycodeEditorPath = path.join(homePath, dataFolderName);

        // 检查目录是否存在，如果不存在则创建
        if (!fs.existsSync(joycodePath)) {
            await fs.promises.mkdir(joycodePath, { recursive: true });
        }

        if (!fs.existsSync(joycodeEditorPath)) {
            await fs.promises.mkdir(joycodeEditorPath, { recursive: true });

            // 创建必要的子目录
            const userDataProfilePath = path.join(joycodeEditorPath, 'User');
            await ensureDirectoryExists(userDataProfilePath, logService);

            const globalStoragePath = path.join(userDataProfilePath, 'globalStorage');
            await ensureDirectoryExists(globalStoragePath, logService);

            // 创建默认的 argv.json 文件
            const argvJsonPath = path.join(joycodeEditorPath, 'argv.json');
            await ensureValidArgvJson(argvJsonPath, logService);
        } else {

            // 确保必要的子目录存在
            const userDataProfilePath = path.join(joycodeEditorPath, 'User');
            await ensureDirectoryExists(userDataProfilePath, logService);

            const globalStoragePath = path.join(userDataProfilePath, 'globalStorage');
            await ensureDirectoryExists(globalStoragePath, logService);

            // 检查并修复 argv.json 文件
            const argvJsonPath = path.join(joycodeEditorPath, 'argv.json');
            await ensureValidArgvJson(argvJsonPath, logService);
        }
    } catch (error) {
        logService.error(error);
    }
}

/**
 * 确保目录存在，如果不存在则创建
 */
async function ensureDirectoryExists(dirPath: string, logService: ILogService): Promise<void> {
    try {
        if (!fs.existsSync(dirPath)) {
            await fs.promises.mkdir(dirPath, { recursive: true });
        }
    } catch (error) {
        logService.error(error);
        throw error;
    }
}

/**
 * 检查字符串是否有编码问题
 */
function hasEncodingIssues(content: string): boolean {
    try {
        // 检查是否包含常见的编码问题字符
        const encodingIssuePatterns = [
            /\uFFFD/g, // 替换字符，表示编码错误
            /[\u0080-\u009F]/g, // 控制字符范围，可能是编码问题
        ];

        for (const pattern of encodingIssuePatterns) {
            if (pattern.test(content)) {
                return true;
            }
        }

        return false;
    } catch (error) {
        return true; // 如果检查过程出错，认为有编码问题
    }
}

/**
 * 安全解析 JSON 或 JSONC 内容
 * 支持带注释的 JSON 格式
 */
function safeParseJson(content: string, logService: ILogService): any {
    try {
        // 首先尝试标准 JSON 解析
        return JSON.parse(content);
    } catch (standardJsonError) {
        try {
            // 如果标准 JSON 解析失败，尝试 JSONC 解析
            const result = parseJsonc(content);
            if (result.errors && result.errors.length > 0) {
                logService.warn(result.errors);
            }
            return result.value;
        } catch (jsoncError) {
            // 如果两种解析都失败，抛出原始错误
            throw standardJsonError;
        }
    }
}



/**
 * 检查是否应该添加中文语言设置
 */
async function shouldAddChineseLocale(existingConfig: any, logService: ILogService): Promise<boolean> {
    try {
        // 如果已经有语言设置，检查是否是中文
        if (existingConfig && existingConfig.locale) {
            const currentLocale = existingConfig.locale;
            if (currentLocale === 'zh-cn' || currentLocale === 'zh-hans') {
                return false; // 已经是中文，不需要修改
            }
        }

        const homePath = process.env.HOME || process.env.USERPROFILE || '';
        if (!homePath) {
            return false;
        }

        // 检查主应用的语言包配置（使用正确的目录名）
        const mainAppDataPath = path.join(homePath, 'Library', 'Application Support', 'JoyCode');
        const languagePacksPath = path.join(mainAppDataPath, 'languagepacks.json');

        // 检查是否存在版本切换标记文件，如果存在则跳过中文设置
        const versionSwitchMarkerPath = path.join(mainAppDataPath, '.version-switch-to-english');
        if (fs.existsSync(versionSwitchMarkerPath)) {
            return false;
        }

        if (fs.existsSync(languagePacksPath)) {
            const content = fs.readFileSync(languagePacksPath, 'utf8');

            // 检查编码问题
            if (hasEncodingIssues(content)) {
                return false;
            }

            const languagePacks = safeParseJson(content, logService);

            // 检查是否有中文语言包
            if (languagePacks['zh-cn'] || languagePacks['zh-hans']) {
                return true;
            }
        }

        return false;
    } catch (error) {
        logService.warn(error);
        return false;
    }
}

/**
 * 确保 argv.json 文件存在且格式正确
 * 如果文件格式不正确，则直接删除并创建新文件
 */
async function ensureValidArgvJson(filePath: string, logService: ILogService): Promise<void> {
    try {
        let needsCreate = false;
        let existingConfig: any = null;

        // 检查文件是否存在
        if (fs.existsSync(filePath)) {
            try {
                // 尝试读取并解析现有文件
                const content = fs.readFileSync(filePath, 'utf8');

                // 检查编码问题
                if (hasEncodingIssues(content)) {
                    needsCreate = true;
                } else {
                    existingConfig = safeParseJson(content, logService); // 使用安全解析，支持 JSON

                    // 检查是否需要添加语言设置
                    const shouldAddLocale = await shouldAddChineseLocale(existingConfig, logService);
                    if (shouldAddLocale) {
                        existingConfig.locale = 'zh-cn';
                        await fs.promises.writeFile(filePath, JSON.stringify(existingConfig, null, 2), 'utf8');
                    }
                }
            } catch (e) {
                try {
                    // 删除有问题的文件
                    fs.unlinkSync(filePath);
                } catch (unlinkError) {
                    logService.error(unlinkError);
                }
                needsCreate = true;
            }
        } else {
            // 文件不存在，需要创建
            needsCreate = true;
        }

        if (needsCreate) {
            // 检查是否有中文语言包配置
            let shouldSetChineseLocale = false;
            try {
                const homePath = process.env.HOME || process.env.USERPROFILE || '';
                if (homePath) {
                    // 检查主应用的语言包配置
                    const mainAppDataPath = path.join(homePath, 'Library', 'Application Support', 'JoyCode');
                    const languagePacksPath = path.join(mainAppDataPath, 'languagepacks.json');

                    // 检查是否存在版本切换标记文件，如果存在则跳过中文设置
                    const versionSwitchMarkerPath = path.join(mainAppDataPath, '.version-switch-to-english');
                    if (fs.existsSync(versionSwitchMarkerPath)) {
                        shouldSetChineseLocale = false;
                    } else if (fs.existsSync(languagePacksPath)) {
                        const content = fs.readFileSync(languagePacksPath, 'utf8');
                        const languagePacks = safeParseJson(content, logService);

                        // 检查是否有中文语言包
                        if (languagePacks['zh-cn'] || languagePacks['zh-hans']) {
                            shouldSetChineseLocale = true;
                        }
                    }
                }
            } catch (error) {
                logService.warn(error);
            }

            // 创建默认的 argv.json 文件
            const defaultArgvJson: any = {
                'enable-crash-reporter': true,
                'crash-reporter-id': 'JoyCode',
                'use-inmemory-secretstorage': true
            };

            // 如果检测到中文语言包，设置中文语言
            if (shouldSetChineseLocale) {
                defaultArgvJson.locale = 'zh-cn';
            }

            // 写入文件，确保使用 UTF-8 编码
            await fs.promises.writeFile(filePath, JSON.stringify(defaultArgvJson, null, 2), 'utf8');
        }
    } catch (error) {
        logService.error(error);
    }
}

export async function validateUserConfiguration(
    _environmentService: IEnvironmentService,
    logService: ILogService,
    _fileService: IFileService,
    productService: IProductService,
    _dialogMainService?: IDialogMainService,
    isFirstLaunch?: boolean
): Promise<boolean> {
    try {
        // 获取当前版本号
        const currentVersion = productService.version || '1.0.0';

        // 如果是第一次启动，初始化配置目录
        if (isFirstLaunch) {
            await initializeConfigDirs(logService, productService);
            // 保存当前版本号
            await saveCurrentVersion(currentVersion, logService);
            return true; // 返回 true 表示配置已初始化
        }

        // 检查是否需要进行配置检查（基于版本号）
        const needsCheck = await shouldPerformConfigCheck(currentVersion, logService);
        if (!needsCheck) {
            // 版本号一致，跳过检查
            return false;
        }

        // 获取用户主目录路径
        const homePath = process.env.HOME || process.env.USERPROFILE || '';

        // 从产品配置获取数据目录名称
        const dataFolderName = productService.dataFolderName || '.joycode-editor';
        const joycodePathName = '.joycode';

        // 检查配置目录
        const joycodePath = path.join(homePath, joycodePathName);
        const joycodeEditorPath = path.join(homePath, dataFolderName);

        // 确保必要的目录存在
        await ensureDirectoryExists(joycodePath, logService);
        await ensureDirectoryExists(joycodeEditorPath, logService);

        // 确保用户数据目录存在
        const userDataProfilePath = path.join(joycodeEditorPath, 'User');
        await ensureDirectoryExists(userDataProfilePath, logService);

        // 确保全局存储目录存在
        const globalStoragePath = path.join(userDataProfilePath, 'globalStorage');
        await ensureDirectoryExists(globalStoragePath, logService);

        // 检查并修复 argv.json 文件
        const argvJsonPath = path.join(joycodeEditorPath, 'argv.json');
        await ensureValidArgvJson(argvJsonPath, logService);

        let configCorrupted = false;
        let errorMessage = '';

        // 验证配置文件
        try {
            // 尝试读取一些关键配置文件来验证它们是否有效
            if (fs.existsSync(joycodePath)) {
                // 检查 .joycode 目录中的关键文件
                const storageFilePath = path.join(joycodePath, 'storage.json');
                if (fs.existsSync(storageFilePath)) {
                    try {
                        const content = fs.readFileSync(storageFilePath, 'utf8');
                        JSON.parse(content); // 尝试解析 JSON
                    } catch (e) {
                        configCorrupted = true;
                        errorMessage = `Configuration file is corrupted: ${storageFilePath}`;
                        logService.error(`[ConfigValidator] ${errorMessage}`, e);
                    }
                }
            }

            if (fs.existsSync(joycodeEditorPath)) {
                // 检查 .joycode-editor 目录中的关键文件
                const userDataProfilePath = path.join(joycodeEditorPath, 'User');
                if (fs.existsSync(userDataProfilePath)) {
                    const settingsPath = path.join(userDataProfilePath, 'settings.json');
                    if (fs.existsSync(settingsPath)) {
                        try {
                            const content = fs.readFileSync(settingsPath, 'utf8');
                            JSON.parse(content); // 尝试解析 JSON
                        } catch (e) {
                            configCorrupted = true;
                            errorMessage = `Configuration file is corrupted: ${settingsPath}`;
                            logService.error(`[ConfigValidator] ${errorMessage}`, e);
                        }
                    }

                    const storageFilePath = path.join(userDataProfilePath, 'globalStorage', 'storage.json');
                    if (fs.existsSync(storageFilePath)) {
                        try {
                            const content = fs.readFileSync(storageFilePath, 'utf8');
                            JSON.parse(content); // 尝试解析 JSON
                        } catch (e) {
                            configCorrupted = true;
                            errorMessage = `Configuration file is corrupted: ${storageFilePath}`;
                            logService.error(`[ConfigValidator] ${errorMessage}`, e);
                        }
                    }
                }
            }
        } catch (e) {
            configCorrupted = true;
            errorMessage = `Configuration file is corrupted`;
            logService.error(`[ConfigValidator] ${errorMessage}`, e);
        }

        // 如果配置文件损坏，重命名配置目录并创建新的默认配置
        if (configCorrupted) {
            logService.warn(`[ConfigValidator] Detected configuration file corruption, resetting configuration`);

            // 重命名配置目录
            const timestamp = new Date().toISOString().replace(/[:.]/g, '-');

            if (fs.existsSync(joycodePath)) {
                const backupPath = `${joycodePath}.backup-${timestamp}`;
                await fs.promises.rename(joycodePath, backupPath);
            }

            if (fs.existsSync(joycodeEditorPath)) {
                const backupPath = `${joycodeEditorPath}.backup-${timestamp}`;
                await fs.promises.rename(joycodeEditorPath, backupPath);
            }

            // 保存当前版本号（配置已重置）
            await saveCurrentVersion(currentVersion, logService);
            return true; // 配置已重置
        }

        // 配置正常，保存当前版本号
        await saveCurrentVersion(currentVersion, logService);
        return false; // 配置正常
    } catch (error) {
        logService.error('[ConfigValidator] An error occurred while validating the user configuration:', error);
        return false;
    }
}
