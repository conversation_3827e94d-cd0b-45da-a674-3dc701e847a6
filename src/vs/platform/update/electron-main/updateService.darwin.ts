/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

import * as electron from 'electron';
// import { MacUpdater } from 'electron-updater';
import { memoize } from '../../../base/common/decorators.js';
import { Event } from '../../../base/common/event.js';
import { hash } from '../../../base/common/hash.js';
import { DisposableStore } from '../../../base/common/lifecycle.js';
import { localize } from '../../../nls.js';
import { IConfigurationService } from '../../configuration/common/configuration.js';
import { IEnvironmentMainService } from '../../environment/electron-main/environmentMainService.js';
import { ILifecycleMainService, IRelaunchHandler, IRelaunchOptions } from '../../lifecycle/electron-main/lifecycleMainService.js';
import { ILogService } from '../../log/common/log.js';
import { IProductService } from '../../product/common/productService.js';
import { IRequestService } from '../../request/common/request.js';
import { ITelemetryService } from '../../telemetry/common/telemetry.js';
import { IUpdate, State, StateType, UpdateType } from '../common/update.js';
import { AbstractUpdateService, createUpdateURL, UpdateErrorClassification, UpdateNotAvailableClassification } from './abstractUpdateService.js';
import { createUpdate } from "../custom/custom.js";
import { CustomAdapter } from "../custom/types.js";
import { IDialogMainService } from '../../dialogs/electron-main/dialogMainService.js';


export class DarwinUpdateService extends AbstractUpdateService implements IRelaunchHandler {

	private readonly disposables = new DisposableStore();
	protected override url: string | undefined;
	private customUpdate: CustomAdapter;

	@memoize private get onRawError(): Event<string> { return Event.fromNodeEventEmitter(electron.autoUpdater, 'error', (_, message) => message); }
	@memoize private get onRawUpdateNotAvailable(): Event<void> { return Event.fromNodeEventEmitter<void>(electron.autoUpdater, 'update-not-available'); }
	@memoize private get onRawUpdateAvailable(): Event<void> { return Event.fromNodeEventEmitter(electron.autoUpdater, 'update-available'); }
	@memoize private get onRawUpdateDownloaded(): Event<IUpdate> { return Event.fromNodeEventEmitter(electron.autoUpdater, 'update-downloaded', (_, _releaseNotes, version, timestamp) => ({ version, productVersion: version, timestamp })); }

	constructor(
		@ILifecycleMainService lifecycleMainService: ILifecycleMainService,
		@IConfigurationService configurationService: IConfigurationService,
		@ITelemetryService private readonly telemetryService: ITelemetryService,
		@IEnvironmentMainService environmentMainService: IEnvironmentMainService,
		@IRequestService requestService: IRequestService,
		@ILogService logService: ILogService,
		@IProductService productService: IProductService,
		@IDialogMainService private readonly dialogMainService: IDialogMainService
	) {
		super(lifecycleMainService, configurationService, environmentMainService, requestService, logService, productService);

		this.customUpdate = createUpdate({
			upDataUrl: this.buildUpdateFeedUrl(''),
			joyCoderVersion: this.productService.version as string,
		});
		lifecycleMainService.setRelaunchHandler(this);
	}

	handleRelaunch(options?: IRelaunchOptions): boolean {
		if (options?.addArgs || options?.removeArgs) {
			return false; // we cannot apply an update and restart with different args
		}

		if (this.state.type !== StateType.Ready) {
			return false; // we only handle the relaunch when we have a pending update
		}

		this.logService.trace('update#handleRelaunch(): running raw#quitAndInstall()');
		this.doQuitAndInstall();

		return true;
	}

	protected override async initialize(): Promise<void> {
		await super.initialize();
		this.onRawError(this.onError, this, this.disposables);
		this.onRawUpdateAvailable(this.onUpdateAvailable, this, this.disposables);
		this.onRawUpdateDownloaded(this.onUpdateDownloaded, this, this.disposables);
		this.onRawUpdateNotAvailable(this.onUpdateNotAvailable, this, this.disposables);

		this.customUpdate.on('checking-for-update', (info: any) => {
			this.logService.info('Checking for updates:', info);
			this.setState(State.CheckingForUpdates(false));
		});
		this.customUpdate.on('update-available', (info: any) => {
			const update: IUpdate = {
				version: info.version,
				productVersion: info.version,
				url: info.url
			};
			this.logService.info('Starting update:', update);
			this.setState(State.AvailableForDownload(update));
		});
		this.customUpdate.on('update-not-available', (info: any) => {
			// this.logService.info('Version is consistent, no update needed.', info);
			this.setState(State.Idle(UpdateType.Setup));
			if (this.customUpdate.isClickMenu) {
				this.dialogMainService.showMessageBox({
					type: 'info',
					title: localize('checkForUpdates', 'Check for Updates'),
					message: localize('noUpdatesAvailable', 'No updates available'),
					buttons: [localize('ok', 'OK')],
				}, electron.BrowserWindow.getFocusedWindow() ?? undefined);
			}
		});

		this.customUpdate.on('error', (err: any) => {
			// this.logService.info('Update error:', err);
			if (typeof err === 'string' && err.includes('disk space')) {
				// Notify all windows to show disk space insufficient message
				const allWindows = electron.BrowserWindow.getAllWindows();
				allWindows?.forEach(win => {
					// console.log('Sending to window ID:', win.id);
					win.webContents.send('vscode:show-disk-space-warning', err);
				});
				// console.log('Disk space warning messages sent to all windows');
			}
			this.setState(State.Idle(UpdateType.Setup));
		});

		this.customUpdate.on('download-progress', (percent: any) => {
			this.logService.info('Download progress', percent);
			if (this.customUpdate.isClickMenu && typeof percent === 'number') {
				this.setState(State.DownloadingPro(percent));
			} else {
				this.setState(State.AutoDownloading);
			}
		});

		this.customUpdate.on('update-downloaded', (info: any) => {
			this.logService.info('Download completed', info);

			const update: IUpdate = {
				version: info.version,
				productVersion: info.version
			};
			this.setState(State.Downloaded(update));
			this.onUpdateDownloaded(update);
		});

		this.customUpdate.on('log', (info: any) => {
			// this.logService.info('Auto update log', info);
		});

	}

	private onError(err: string): void {
		this.telemetryService.publicLog2<{ messageHash: string }, UpdateErrorClassification>('update:error', { messageHash: String(hash(String(err))) });
		this.logService.error('UpdateService error:', err);

		// only show message when explicitly checking for updates
		const message = (this.state.type === StateType.CheckingForUpdates && this.state.explicit) ? err : undefined;
		this.setState(State.Idle(UpdateType.Archive, message));
	}

	protected buildUpdateFeedUrl(quality: string): string | undefined {
		let assetID: string;
		if (!this.productService.darwinUniversalAssetId) {
			assetID = process.arch === 'x64' ? 'darwin-x64' : 'darwin-arm64';
		} else {
			assetID = this.productService.darwinUniversalAssetId;
		}
		const url = createUpdateURL(assetID, quality, this.productService);
		try {
			// electron.autoUpdater.setFeedURL({ url });
			this.url = url;
		} catch (e) {
			// application is very likely not signed
			this.logService.error('Failed to set update feed URL', e);
			return undefined;
		}
		return url;
	}

	protected doCheckForUpdates(_context: any): void {
		this.logService.info('Check if update is triggered by user:', _context);
		// @ts-ignore
		const customUpdate = this.customUpdate;
		// Check for updates
		customUpdate.checkForUpdatesAndNotify(!!_context);
	}
	// protected doCheckForUpdates2(context: any): void {
	// 	// customUpdate;
	//
	// 	this.setState(State.CheckingForUpdates(context));
	// 	// electron.autoUpdater.checkForUpdates();
	// 	const options: any = {
	// 		requestHeaders: {
	// 			// Any request headers to include here
	// 		},
	// 		provider: 'generic',
	// 		url: this.url,
	// 		// Remove custom cache directory name, use default cache directory based on application ID
	// 	}
	// 	this.logService.info('MacUpdater options:', JSON.stringify(options, null, 2));
	// 	const autoUpdater = new MacUpdater(options);
	// 	this.logService.info('MacUpdater instance created');
	// 	autoUpdater.setFeedURL({
	// 		provider: 'generic',
	// 		url: this.url ?? '',
	// 	});
	// 	// autoUpdater.downloadPath = path.join(os.homedir(), '.config', 'Code', 'config.json');
	// 	autoUpdater.forceDevUpdateConfig = true; // Force detection in development environment
	// 	autoUpdater.disableWebInstaller = true;
	// 	autoUpdater.allowDowngrade = false;
	// 	autoUpdater.allowPrerelease = false;
	// 	autoUpdater.disableDifferentialDownload = false;
	// 	autoUpdater.fullChangelog = true;
	// 	// Auto download
	// 	autoUpdater.autoDownload = true; // Enable auto download
	//
	// 	// Trigger detection
	// 	this.logService.info('Starting update check, URL:', this.url);
	// 	autoUpdater.checkForUpdatesAndNotify().catch((err) => {
	// 		this.logService.error('checkForUpdatesAndNotify error:', err);
	// 		// Try calling checkForUpdates directly as fallback
	// 		try {
	// 			autoUpdater.checkForUpdates().catch(e => this.logService.error('checkForUpdates fallback error:', e));
	// 		} catch (e) {
	// 			this.logService.error('Failed to call checkForUpdates:', e);
	// 		}
	// 	});
	//
	// 	const isUpdaterActive = autoUpdater.isUpdaterActive();
	// 	this.logService.info('Update service status isUpdaterActive:', isUpdaterActive);
	//
	// 	autoUpdater.on('update-available', (info) => {
	// 		// const cachePath =path.join(os.homedir(), 'Library/Caches/JoyCode'); // Specify update cache path
	// 		// Set download path
	// 		// autoUpdater.downloadPath = cachePath;
	// 		this.logService.info('New update available:', info);
	// 		this.logService.info('Starting update download...');
	//
	// 		autoUpdater.downloadUpdate().catch(e => this.logService.error('Failed to download update:', e));
	// 	});
	//
	// 	autoUpdater.on('update-not-available', (info) => {
	// 		this.logService.info("No update needed:", info);
	// 	});
	//
	// 	autoUpdater.on('download-progress', (progress) => {
	// 		this.logService.info(`Download progress: ${progress.percent}%`);
	// 	});
	//
	// 	autoUpdater.on('update-downloaded', (info) => {
	// 		this.logService.info('Update downloaded, ready to install');
	// 		// Download file path can be obtained here
	// 		this.logService.info('Update downloaded, ready to install, get installation path' + info.downloadedFile);
	// 		autoUpdater.quitAndInstall();
	// 	});
	//
	// 	autoUpdater.on('error', (error) => {
	// 		this.logService.error('Update failed:', error);
	// 		// Log more detailed error information
	// 		if (error && error.stack) {
	// 			this.logService.error('Error stack:', error.stack);
	// 		}
	// 		// Add more diagnostic information
	// 		this.logService.error('Current app version:', this.productService.version);
	// 		this.logService.error('Update URL:', this.url);
	// 	});
	//
	// 	// Add new log records
	// 	this.logService.info('Current app version:', this.productService.version);
	// 	this.logService.info('Update URL:', this.url);
	// }

	private onUpdateAvailable(): void {
		if (this.state.type !== StateType.CheckingForUpdates) {
			return;
		}

		this.setState(State.Downloading);
	}

	private onUpdateDownloaded(update: IUpdate): void {
		// if (this.state.type !== StateType.Downloading) {
		// 	return;
		// }

		this.setState(State.Downloaded(update));

		type UpdateDownloadedClassification = {
			owner: 'joaomoreno';
			version: { classification: 'SystemMetaData'; purpose: 'FeatureInsight'; comment: 'The version number of the new VS Code that has been downloaded.' };
			comment: 'This is used to know how often VS Code has successfully downloaded the update.';
		};
		this.telemetryService.publicLog2<{ version: String }, UpdateDownloadedClassification>('update:downloaded', { version: update.version });

		this.setState(State.Ready(update));
	}

	private onUpdateNotAvailable(): void {
		if (this.state.type !== StateType.CheckingForUpdates) {
			return;
		}
		this.telemetryService.publicLog2<{ explicit: boolean }, UpdateNotAvailableClassification>('update:notAvailable', { explicit: this.state.explicit });

		this.setState(State.Idle(UpdateType.Archive));
	}

	protected override doQuitAndInstall(): void {
		this.logService.trace('update#quitAndInstall(): running raw#quitAndInstall()');
		// @ts-ignore
		const customUpdate = this.customUpdate;
		this.logService.info('customUpdate instance:', customUpdate);
		this.logService.info('updatePath:', customUpdate.updatePath);

		if (!customUpdate.updatePath) {
			this.logService.error('Cannot quit and install: updatePath is not set. Make sure download is completed.');
			return;
		}

		// Restart and install immediately
		customUpdate.quitAndInstall();
	}

	protected override doWaitAndInstall(): void {
		this.logService.trace('update#waitAndInstall(): running raw#waitAndInstall()');
		// @ts-ignore
		const customUpdate = this.customUpdate;
		this.logService.info('customUpdate instance:', customUpdate);
		this.logService.info('updatePath:', customUpdate.updatePath);

		if (!customUpdate.updatePath) {
			this.logService.error('Cannot wait and install: updatePath is not set. Make sure download is completed.');
			return;
		}

		// Restart and install immediately
		customUpdate.quitAndInstall(false);
	}

	dispose(): void {
		this.disposables.dispose();
	}
}
