import { IStorageMainService } from '../../platform/storage/electron-main/storageMainService.js';
import { IUserDataProfilesMainService } from '../../platform/userDataProfile/electron-main/userDataProfile.js';
import { createDecorator } from '../instantiation/common/instantiation.js';
import { ILifecycleMainService, LifecycleMainPhase } from '../lifecycle/electron-main/lifecycleMainService.js';
import { ILogService } from '../log/common/log.js';
import { asJson, IRequestService } from '../request/common/request.js';
import { IProductService } from '../product/common/productService.js';
import { IConfigurationService } from '../configuration/common/configuration.js';
import { CancellationToken } from '../../base/common/observableInternal/commonFacade/cancellation.js';
import { Disposable } from '../../base/common/lifecycle.js';
import { fetchUserInfo } from '../login/electron-main/loginMainService.js';
import { HttpAuthService } from '../login/common/httpAuthService.js';

export interface UserInfo {
	userName: string;
	userId: string;
	loginType: string;
	loginUrl: string;
	pk: string;
	ptKey: string;
	orgName: string; //机构名称
	resources: any; //资源列表
	env?: string; //环境标识
	base_url?: string; //基础URL
}
export interface CommonResult<T = ConfigData> {
	code: number;
	msg: string;
	data: T;
}

export interface ConfigData {
	mainKey: string;
	syncKeys: string[];
}



export interface JoyCoderEditorValue {
	jdhLoginInfo: UserInfo;
	joyCoderUser: UserInfo;
}

export interface IJoyCoderUserService {
	getJoyCoderEditorValue(mainKey: string): JoyCoderEditorValue | null;
	getUserInfo(): UserInfo | null;
	writeMainUserInfo(joyCoderUser: UserInfo | null): void;
	syncUserInfo(): Promise<void>;
}
export const IJoyCoderUserService = createDecorator<IJoyCoderUserService>('joyCoderUserService');


export class JoyCoderUserService extends Disposable implements IJoyCoderUserService {
	private intervalId: NodeJS.Timeout | null = null;
	// private readonly syncInterval = 60 * 1000; // 1分钟

	constructor(
		@IStorageMainService private readonly storageMainService: IStorageMainService,
		@IUserDataProfilesMainService private readonly userDataProfilesService: IUserDataProfilesMainService,
		@ILogService private readonly logService: ILogService,
		@ILifecycleMainService private readonly lifecycleMainService: ILifecycleMainService,
		@IRequestService private readonly requestService: IRequestService,
		@IProductService private readonly productService: IProductService,
		@IConfigurationService private readonly configurationService: IConfigurationService
	) {
		super();
		this._register(this.lifecycleMainService.onWillShutdown(() => this.dispose()));
		this.lifecycleMainService.when(LifecycleMainPhase.AfterWindowOpen)
			.finally(() => this.initialize());
	}
	async syncUserInfo(): Promise<void> {
		const userInfo = this.getUserInfo();
		if (!this.isValidUserInfo(userInfo)) {
			this.clearMainUser();
			this.onlySyncUser(null);
			return;
		}

		try {
			const queryUser = await this.fetchAndValidateUserInfo(userInfo);
			if (!this.isValidUserInfo(queryUser)) {
				this.logService.debug('Invalid user info received from server');
				this.clearMainUser();
				this.onlySyncUser(null);
				return;
			}

			// 使用从服务器获取的完整用户信息，而不是本地的不完整信息
			const completeUserInfo = {
				...userInfo,
				...queryUser,
				ptKey: userInfo.ptKey, // 保留本地的 ptKey
				loginType: userInfo.loginType // 保留本地的 loginType
			};

			// 更新本地存储的用户信息，跳过同步以避免死循环
			await this.writeMainUserInfo(completeUserInfo, true);
			await this.onlySyncUser(completeUserInfo);

		} catch (error) {
			this.handleSyncError(error);
		}
	}

	async onlySyncUser(userInfo: any): Promise<void> {
		await this.syncUserInfoToKeys(userInfo);
	}

	async clearMainUser(): Promise<void> {
		const baseGlobalName = this.productService.baseGlobalName;
		const baseGlobalUserKey = this.productService.baseGlobalUserKey;
		const globalStorage = this.storageMainService.profileStorage(this.userDataProfilesService.defaultProfile);
		if (baseGlobalName && baseGlobalUserKey) {
			const rowData = globalStorage.storage.get(baseGlobalName, '{}');
			const rowJson = JSON.parse(rowData);
			rowJson[baseGlobalUserKey] = {};
			globalStorage.storage.set(baseGlobalName, JSON.stringify(rowJson));
		}

	}

	private isValidUserInfo(userInfo: any): userInfo is UserInfo {
		return userInfo && typeof userInfo.ptKey === 'string' && userInfo.ptKey.trim() !== '';
	}


	private getBaseUrl(): string {
		// 优先级1: 配置服务中的 server.baseUrl
		const configBaseUrl = this.configurationService.getValue<string>('server.baseUrl');
		if (configBaseUrl) {
			return configBaseUrl;
		}

		// 优先级2: 用户存储配置的 base_url
		try {
			const httpAuthService = HttpAuthService.getInstance();
			const userConfig = httpAuthService.getStoredUserConfig();
			if (userConfig?.base_url) {
				return userConfig.base_url;
			}
		} catch (error) {
			this.logService.warn('Failed to get stored user config in getBaseUrl:', error);
		}

		// 优先级3: 产品服务中的默认 URL
		return this.productService.joyCoderBaseUrl || '';
	}

	private async fetchAndValidateUserInfo(userInfo: { ptKey: string; loginType: string }): Promise<any> {
		return await fetchUserInfo(userInfo.ptKey, userInfo.loginType, this.configurationService, this.productService, this.logService);
	}

	// private isValidSyncKeyInfo(syncKeyInfo: any): syncKeyInfo is { mainKey: string; syncKeys: string[] } {
	// 	return syncKeyInfo && syncKeyInfo.mainKey && Array.isArray(syncKeyInfo.syncKeys) && syncKeyInfo.syncKeys.length > 0;
	// }


	private handleSyncError(error: unknown): void {
		if (error instanceof Error) {
			this.logService.error(`Failed to sync user info: ${error.message}`);
		} else {
			this.logService.error('Failed to sync user info: Unknown error');
		}
	}

	initialize(): void {
		this.logService.info('initializing');
	}

	getJoyCoderEditorValue(mainKey: string): JoyCoderEditorValue | null {
		if (!mainKey) {
			this.logService.debug('getJoyCoderEditorValue called with empty mainKey');
			return null;
		}
		const globalStorage = this.storageMainService.profileStorage(this.userDataProfilesService.defaultProfile);
		const rawValue = globalStorage.storage.get(mainKey, '');

		if (!rawValue) {
			return null;
		}

		try {
			return JSON.parse(rawValue) as JoyCoderEditorValue;
		} catch (error) {
			this.logService.error('Failed to parse JoyCode editor value:', error);
			return null;
		}
	}

	getUserInfo(): UserInfo | null {
		// 首先尝试从IDE获取用户信息
		const baseGlobalName = this.productService.baseGlobalName;
		if (baseGlobalName) {
			const editorValueIde = this.getJoyCoderEditorValue(baseGlobalName);
			if (this.isValidUserInfo(editorValueIde?.joyCoderUser)) {
				const userInfo = editorValueIde?.joyCoderUser;
				return userInfo;
			} else {
				this.logService.info('getUserInfo: joyCodeUser is not valid or missing');
			}
		}

		return null;
	}

	async writeMainUserInfo(joyCodeUser: UserInfo | null, skipSync: boolean = false): Promise<void> {
		const baseGlobalName = this.productService.baseGlobalName;
		const baseGlobalUserKey = this.productService.baseGlobalUserKey;

		if (baseGlobalName && baseGlobalUserKey) {
			const globalStorage = this.storageMainService.profileStorage(this.userDataProfilesService.defaultProfile);
			const rowData = globalStorage.storage.get(baseGlobalName, '{}');
			const rowJson = JSON.parse(rowData);
			rowJson[baseGlobalUserKey] = joyCodeUser || {};
			globalStorage.storage.set(baseGlobalName, JSON.stringify(rowJson));

			// 只有在不跳过同步且不是在同步过程中时才调用 syncUserInfo
			if (!skipSync) {
				await this.syncUserInfo();
			}
		}
	}

	private async syncUserInfoToKeys(userInfo: UserInfo): Promise<void> {
		const globalStorage = this.storageMainService.profileStorage(this.userDataProfilesService.defaultProfile);
		if (!globalStorage || !globalStorage.storage) {
			this.logService.error('Global storage not available');
			return;
		}
		const key = this.productService.joyCoderExtGlobalName;
		const mainKey = this.productService.joyCoderExtGlobalUserKey;

		try {
			if (key && mainKey) {
				const rowData = globalStorage.storage.get(key, '{}');
				const rowJson = JSON.parse(rowData);
				rowJson[mainKey] = userInfo;
				globalStorage.storage.set(key, JSON.stringify(rowJson));
			}
		} catch (error) {
			this.logService.error(`syncUserInfoToKeys ${key}`, error);
		}
	}

	async getKeyList(ptKey: string | ''): Promise<ConfigData> {
		try {
			// 优先使用配置服务中的server.baseUrl
			const baseUrl = this.getBaseUrl();
			const apiUrl = `${baseUrl}/api/saas/config/v1/config/syncUserKey`;
			const context = await this.requestService.request({
				type: 'GET',
				url: apiUrl,
				headers: { 'ptKey': ptKey ? ptKey : '' }
			}, CancellationToken.None);

			if (!context.res.statusCode || context.res.statusCode !== 200) {
				this.logService.warn(`Failed to get key list: HTTP ${context.res.statusCode}`);
				return { mainKey: '', syncKeys: [] };
			}

			const result = await asJson(context) as CommonResult<ConfigData>;
			if (result.code !== 0 || !result.data) {
				this.logService.warn(`Invalid response from key list API: code=${result.code}, message=${result.msg}`);
				return { mainKey: '', syncKeys: [] };
			}

			return result.data;
		} catch (error) {
			this.logService.error('Error fetching key list:', error);
			return { mainKey: '', syncKeys: [] };
		}
	}


	override dispose(): void {
		if (this.intervalId) {
			clearInterval(this.intervalId);
			this.intervalId = null;
			this.logService.debug('User info sync interval stopped');
		}
		super.dispose();
	}
}
