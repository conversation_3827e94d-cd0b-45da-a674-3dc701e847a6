# StorageUser 持久化存储功能文档

## 概述

本文档描述了如何在 JoyCode IDE 中实现 `storageUser` 对象的持久化存储功能。该功能允许在用户登录过程中保存环境配置信息（如租户、基础URL、环境等），并在后续会话中恢复这些信息。

## 功能特性

- **持久化存储**: 使用 VS Code 的存储服务将用户配置保存到本地
- **自动恢复**: 在应用重启后自动恢复之前保存的配置
- **类型安全**: 使用 TypeScript 接口确保数据类型安全
- **错误处理**: 包含完整的错误处理和日志记录

## 数据结构

### StorageUser 接口

```typescript
export interface StorageUser {
    tenant?: string;      // 租户信息
    base_url?: string;    // 基础URL地址
    env?: string;         // 环境标识 (prod, dev, test等)
}
```

## 核心方法

### 1. 存储用户配置

```typescript
public storeUserConfig(storageUser: StorageUser): void
```

**功能**: 将用户环境配置信息保存到持久化存储中

**参数**:
- `storageUser`: 包含租户、基础URL和环境信息的对象

**示例**:
```typescript
const httpAuthService = HttpAuthService.getInstance();
const userConfig: StorageUser = {
    tenant: 'my-tenant',
    base_url: 'https://api.example.com',
    env: 'prod'
};
httpAuthService.storeUserConfig(userConfig);
```

### 2. 获取存储的用户配置

```typescript
public getStoredUserConfig(): StorageUser | null
```

**功能**: 从持久化存储中获取之前保存的用户配置

**返回值**:
- `StorageUser` 对象：如果找到有效配置
- `null`：如果没有找到配置或配置无效

**示例**:
```typescript
const httpAuthService = HttpAuthService.getInstance();
const storedConfig = httpAuthService.getStoredUserConfig();
if (storedConfig) {
    console.log('找到存储的配置:', storedConfig);
} else {
    console.log('未找到存储的配置');
}
```

### 3. 清除存储的用户配置

```typescript
public clearStoredUserConfig(): void
```

**功能**: 清除持久化存储中的用户配置信息

**示例**:
```typescript
const httpAuthService = HttpAuthService.getInstance();
httpAuthService.clearStoredUserConfig();
```

## 使用场景

### 1. 登录过程中自动存储

在 `httpAuthService.ts` 的登录回调处理中，系统会自动提取并存储用户配置：

```typescript
const storageUser: StorageUser = {
    tenant,
    base_url,
    env
};

// 自动存储用户环境配置信息
this.storeUserConfig(storageUser);
```

### 2. getBaseUrl 函数中的优先级使用

各个服务中的 `getBaseUrl` 函数现在按以下优先级获取 URL：

1. **配置服务优先级最高**: `configurationService.getValue<string>('server.baseUrl')`
2. **用户存储配置次之**: `userConfig.base_url` (从持久化存储中获取)
3. **产品服务默认值**: `productService.joyCodeApiBaseUrl` 或 `productService.joyCoderBaseUrl`

```typescript
private getBaseUrl(): string {
    // 优先级1: 配置服务中的 server.baseUrl
    const configBaseUrl = this.configurationService.getValue<string>('server.baseUrl');
    if (configBaseUrl) {
        return configBaseUrl;
    }

    // 优先级2: 用户存储配置的 base_url
    try {
        const httpAuthService = HttpAuthService.getInstance();
        const userConfig = httpAuthService.getStoredUserConfig();
        if (userConfig?.base_url) {
            return userConfig.base_url;
        }
    } catch (error) {
        this.logService.warn('Failed to get stored user config:', error);
    }

    // 优先级3: 产品服务中的默认 URL
    return this.productService?.joyCodeApiBaseUrl || '';
}
```

### 3. 应用启动时恢复配置

应用启动时可以恢复之前保存的配置：

```typescript
const httpAuthService = HttpAuthService.getInstance();
const savedConfig = httpAuthService.getStoredUserConfig();
if (savedConfig) {
    // 使用保存的配置初始化应用
    initializeWithConfig(savedConfig);
}
```

### 4. 用户登出时清理配置

用户登出时可以选择清理保存的配置：

```typescript
// 在登出逻辑中
httpAuthService.clearStoredUserConfig();
```

## 技术实现

### 存储机制

- 使用 VS Code 的 `IStorageMainService` 进行持久化存储
- 数据存储在用户配置文件的 profile storage 中
- 存储键值: `'joycoder.storageUser'`

### 数据格式

配置以 JSON 字符串格式存储：

```json
{
    "tenant": "example-tenant",
    "base_url": "https://api.example.com",
    "env": "prod"
}
```

### 错误处理

所有存储操作都包含错误处理：

- 存储服务未初始化时的警告
- JSON 解析错误的处理
- 存储操作失败的错误日志

## 配置和初始化

### 服务依赖

HttpAuthService 需要以下服务才能正常工作：

- `IStorageMainService`: 提供持久化存储功能
- `IUserDataProfilesMainService`: 提供用户配置文件管理

### 初始化代码

在 `loginMainServiceRegistration.ts` 中：

```typescript
// 获取HttpAuthService实例并设置存储服务
const httpAuthService = HttpAuthService.getInstance();
httpAuthService.setStorageServices(storageMainService, userDataProfilesMainService);
```

## 测试

参考 `src/vs/platform/login/test/storageUserTest.ts` 文件中的测试示例，了解如何测试存储功能。

## 注意事项

1. **服务初始化**: 确保在使用存储功能前已正确初始化存储服务
2. **数据验证**: 获取存储数据时会验证数据的有效性
3. **错误处理**: 所有操作都包含适当的错误处理和日志记录
4. **性能考虑**: 存储操作是同步的，适合小量配置数据

## 受影响的文件和服务

以下文件中的 `getBaseUrl` 方法已更新为支持新的优先级逻辑：

### 1. NewServerMainService
- **文件**: `src/vs/workbench/contrib/JoyCoder/common/newServerMainService.ts`
- **方法**: `private getBaseUrl(): string`
- **用途**: 项目管理相关的 API 请求

### 2. LoginMainService
- **文件**: `src/vs/platform/login/electron-main/loginMainService.ts`
- **方法**: `function getBaseUrl(configurationService, productService): string`
- **用途**: 用户登录和身份验证相关的 API 请求

### 3. JoyCoderUserService
- **文件**: `src/vs/platform/user/joyCoderUserService.ts`
- **方法**: `private getBaseUrl(): string`
- **用途**: 用户信息和配置同步相关的 API 请求

### 4. HttpAuthService
- **文件**: `src/vs/platform/login/common/httpAuthService.ts`
- **新增方法**:
  - `storeUserConfig(storageUser: StorageUser): void`
  - `getStoredUserConfig(): StorageUser | null`
  - `clearStoredUserConfig(): void`

## 扩展性

该存储机制可以轻松扩展以支持更多配置项：

1. 在 `StorageUser` 接口中添加新字段
2. 更新验证逻辑以包含新字段
3. 在登录流程中提取和存储新字段

这样的设计确保了功能的可扩展性和维护性。
