/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

import { app } from 'electron';
import { ILoginMainService } from '../common/login.js';
import { ILogService } from '../../../platform/log/common/log.js';
import { Disposable } from '../../../base/common/lifecycle.js';
import { ILifecycleMainService, LifecycleMainPhase } from '../../../platform/lifecycle/electron-main/lifecycleMainService.js';
import { IProductService } from '../../../platform/product/common/productService.js';

export class LoginProtocolHandler extends Disposable {
	private static readonly PROTOCOL_SCHEME = 'joycode';

	constructor(
		@ILoginMainService private readonly loginService: ILoginMainService,
		@ILogService private readonly logService: ILogService,
		@ILifecycleMainService private readonly lifecycleMainService: ILifecycleMainService,
		@IProductService private readonly productService: IProductService
	) {
		super();

		// 延迟到Ready阶段再注册协议处理程序
		this.lifecycleMainService.when(LifecycleMainPhase.Ready).then(() => {
			this.registerProtocolHandler();
		});
	}

	private registerProtocolHandler(): void {
		// Ensure application name is set before registering protocol handler
		// This helps Windows display the correct application name in protocol dialogs
		app.setName(this.productService.nameLong || 'JoyCode');

		// 设置为应用的默认协议处理程序
		if (!app.isDefaultProtocolClient(LoginProtocolHandler.PROTOCOL_SCHEME)) {
			const success = app.setAsDefaultProtocolClient(LoginProtocolHandler.PROTOCOL_SCHEME);
			if (!success) {
				this.logService.warn('Failed to register protocol handler for:', LoginProtocolHandler.PROTOCOL_SCHEME);
			}
		}

		// 处理Windows/Linux系统启动参数 TODO: windows会打开另外的窗口，需要测试
		// 确保应用是单实例的
		const gotTheLock = app.requestSingleInstanceLock();

		if (!gotTheLock) {
			// 如果无法获取锁，说明已有一个实例在运行，此时应该退出
			this.logService.info('Another instance is already running, quitting...');
			app.quit();
			return;
		}

		// 监听second-instance事件
		const secondInstanceHandler = (_event: any, commandLine: string[]) => {
			const protocolUrl = commandLine.find(arg => arg.startsWith(`${LoginProtocolHandler.PROTOCOL_SCHEME}://`));
			if (protocolUrl) {
				this.loginService.handleLoginCallback(protocolUrl);
			}
		};

		app.on('second-instance', secondInstanceHandler);

		// 处理macOS协议打开
		const openUrlHandler = (event: any, url: string) => {
			event.preventDefault();
			if (url.startsWith(`${LoginProtocolHandler.PROTOCOL_SCHEME}://`)) {
				this.loginService.handleLoginCallback(url);
			}
		};

		app.on('open-url', openUrlHandler);

		// 处理可能已经收到的参数
		const args = process.argv;
		const protocolUrl = args.find(arg => arg.startsWith(`${LoginProtocolHandler.PROTOCOL_SCHEME}://`));
		if (protocolUrl) {
			this.loginService.handleLoginCallback(protocolUrl);
		}
	}
}
