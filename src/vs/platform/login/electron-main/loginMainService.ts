/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

import { ILoginMainService, ILoginInfo } from "../common/login.js";
import { ILogService } from "../../../platform/log/common/log.js";
import { IJoyCoderUserService } from "../../../platform/user/joyCoderUserService.js";
import { IProductService } from "../../../platform/product/common/productService.js";
import { IConfigurationService } from "../../configuration/common/configuration.js";
import { shell } from "electron";
import { HttpAuthService } from "../common/httpAuthService.js";
import { Emitter } from "../../../base/common/event.js";
import * as https from 'https';
import * as http from 'http';
import { URL } from 'url';
import { UserInfo } from '../../../platform/user/joyCoderUserService.js';

/**
 * Get base URL, prioritize server.baseUrl from configuration service, fallback to productService.joyCoderBaseUrl
 * @param configurationService Configuration service
 * @param productService Product service
 * @returns Base URL
 */
function getBaseUrl(configurationService: IConfigurationService, productService: IProductService): string {
	const configBaseUrl = configurationService.getValue<string>('server.baseUrl');
	if (configBaseUrl) {
		return configBaseUrl;
	}

	// Priority 2: User stored configuration base_url
	try {
		const httpAuthService = HttpAuthService.getInstance();
		const userConfig = httpAuthService.getStoredUserConfig();
		if (userConfig?.base_url) {
			return userConfig.base_url;
		}
	} catch (error) {
		console.warn('Failed to get stored user config in getBaseUrl:', error);
	}

	// Priority 3: Default URL from product service
	return productService.joyCoderBaseUrl || '';
}

/**
 * Get user info from pt_key
 * @param ptKey User login credential
 * @param loginType Login type, default is PIN
 * @param configurationService Configuration service
 * @param productService Product service
 * @param logService Log service
 * @returns User info or null
 */
export async function fetchUserInfo(ptKey: string, loginType: string = 'PIN', configurationService: IConfigurationService, productService: IProductService, logService: ILogService): Promise<any | null> {
	return new Promise<any | null>((resolve) => {
		try {
			// Set request URL, prioritize server.baseUrl from configuration service
			const baseUrl = getBaseUrl(configurationService, productService);
			const apiUrl = `${baseUrl}/api/saas/user/v1/userInfo`;
			const parsedUrl = new URL(apiUrl);
			// Request options
			const options = {
				hostname: parsedUrl.hostname,
				port: parsedUrl.port || (parsedUrl.protocol === 'https:' ? 443 : 80),
				path: parsedUrl.pathname + parsedUrl.search,
				method: 'GET',
				headers: {
					'ptKey': ptKey,
					'loginType': loginType
				}
			};

			// Choose http or https module
			const requestModule = parsedUrl.protocol === 'https:' ? https : http;

			// Send request
			const req = requestModule.request(options, (res) => {
				let data = '';

				// Receive data
				res.on('data', (chunk) => {
					data += chunk;
				});

				// Request completed
				res.on('end', () => {
					// Check status code
					if (res.statusCode !== 200) {
						resolve('Failed to get user info, HTTP status code: ' + res.statusCode);
						return;
					}

					try {
						// Parse response data
						const result = JSON.parse(data);

						// Check response status
						if (result.code === 0) {
							// Login successful, check if data exists
							if (!result.data) {
								resolve('Failed to get user info, returned data is empty');
								return;
							}
							resolve(result.data);
						} else if (result.code === 401) {
							// User not logged in, but may contain loginUrl and other info, return data directly
							resolve(result.data || result);
						} else {
							// Other errors
							resolve('Failed to get user info, error code: ' + result.code + ' error message: ' + result.msg);
							return;
						}
					} catch (error) {
						logService.error('Login error occurred! Request parameters:', options, 'Return result:', error);
						resolve('Failed to parse user info response');
					}
				});
			});

			// Handle request errors
			req.on('error', (error) => {
				resolve('User info request error');
			});

			// End request
			req.end();
		} catch (error) {
			logService.error('Error occurred while getting user info:', error);
			resolve('Error occurred while getting user info');
		}
	});
}

export class LoginMainService implements ILoginMainService {
	declare readonly _serviceBrand: undefined;

	// private static readonly LOGIN_URL = 'https://joycode.jd.com/login?ideAppName=JoyCode&fromIde=ide';
	// private static readonly LOGIN_URL = 'https://pre-joycoder.jd.com/login?ideAppName=JoyCode&fromIde=ide';


	private readonly _onDidChangeLoginStatus = new Emitter<boolean>();
	public readonly onDidChangeLoginStatus = this._onDidChangeLoginStatus.event;

	private httpAuthService: HttpAuthService;
	// Add login in progress flag to prevent duplicate login
	private _isLoggingIn: boolean = false;

	constructor(
		// @IStorageMainService private readonly storageMainService: IStorageMainService,
		@ILogService private readonly logService: ILogService,
		@IJoyCoderUserService private readonly joyCoderUserService: IJoyCoderUserService,
		@IProductService private readonly productService: IProductService,
		@IConfigurationService private readonly configurationService: IConfigurationService
	) {
		this.httpAuthService = HttpAuthService.getInstance();
		this.httpAuthService.setLoginMainService(this);
	}

	public async login(): Promise<boolean> {
		// Prevent duplicate login
		if (this._isLoggingIn) {
			this.logService.warn("Login already in progress, ignoring duplicate request");
			return false;
		}

		try {
			this._isLoggingIn = true;

			// Check HTTP auth server status
			const serverStatus = this.httpAuthService.getServerStatus();
			this.logService.info("HTTP auth server status:", serverStatus);

			// First call fetchUserInfo to get login URL
			const fetchDataInfo = await fetchUserInfo('', 'PIN', this.configurationService, this.productService, this.logService);
			let LOGIN_URL = '';

			// If fetchUserInfo returned loginUrl, use it; otherwise use default URL from config
			if (fetchDataInfo && typeof fetchDataInfo === 'object' && fetchDataInfo.loginUrl) {
				LOGIN_URL = fetchDataInfo.loginUrl;
			}

			if (!LOGIN_URL) {
				this.logService.error("No login URL available");
				return false;
			}

			// Start or reuse HTTP auth service
			this.logService.info("Preparing to start HTTP auth server...");
			const authInfo = await this.httpAuthService.startServer();

			// Build login URL with auth info
			const loginUrl = new URL(LOGIN_URL);

			// Add necessary IDE parameters
			loginUrl.searchParams.append('ideAppName', 'JoyCode');
			loginUrl.searchParams.append('fromIde', 'ide');
			loginUrl.searchParams.append('redirect', '0');

			// Add auth info
			loginUrl.searchParams.append('authPort', authInfo.port.toString());
			loginUrl.searchParams.append('authKey', authInfo.authKey);

			this.logService.info("Opening login page:", loginUrl.toString());
			await shell.openExternal(loginUrl.toString());
			return true;
		} catch (error) {
			this.logService.error("Failed to open login page:", error);
			return false;
		} finally {
			// Delay reset login status to avoid rapid repeated calls
			setTimeout(() => {
				this._isLoggingIn = false;
			}, 2000);
		}
	}

	public async handleLoginCallback(pt_key: string, login_type: string = 'PIN'): Promise<boolean | string> {
		try {
			const ptKey = pt_key;
			if (!ptKey) {
				return "pt_key error, please login again";
			}

			// Get user info
			const fetchDataInfo = await fetchUserInfo(ptKey, login_type, this.configurationService, this.productService, this.logService);
			if (!fetchDataInfo?.pk) {
				return fetchDataInfo;
			}

			// 获取存储的用户配置信息中的 env 值
			let envFromStorage = 'prod'; // 默认值
			let base_urlFromStorage = ''; // 默认值
			try {
				const httpAuthService = HttpAuthService.getInstance();
				const storedUserConfig = httpAuthService.getStoredUserConfig();
				if (storedUserConfig?.env) {
					envFromStorage = storedUserConfig.env;
					base_urlFromStorage = storedUserConfig.base_url || '';
				}
			} catch (error) {
				this.logService.warn('Failed to get env from stored user config:', error);
			}

			const userInfo: UserInfo = {
				ptKey: ptKey,
				userName: fetchDataInfo.userId,
				userId: fetchDataInfo.userId,
				loginType: fetchDataInfo.loginType,
				loginUrl: fetchDataInfo.loginUrl,
				pk: fetchDataInfo.pk,
				orgName: fetchDataInfo.orgName,
				resources: fetchDataInfo.resources,
				env: envFromStorage,
				base_url: base_urlFromStorage
			};
			try {
				// Store login info to global storage
				await this.joyCoderUserService.writeMainUserInfo(userInfo);
			} catch (error) {
				this.logService.error('Failed to write main user info:', error);
			}
			// Login successful, reset login status
			this._isLoggingIn = false;

			this._onDidChangeLoginStatus.fire(true);
			return true;
		} catch (error) {
			this.logService.error('Failed to handle login callback:', error);
			return "Login error occurred";
		}
	}



	public async getLoginInfo(): Promise<ILoginInfo | null> {
		// Get user info from joyCoderUserService instead of applicationStorage
		const userInfo = this.joyCoderUserService.getUserInfo();

		if (!userInfo || !userInfo.ptKey) {
			return null;
		}
		try {
			// Convert UserInfo to ILoginInfo
			return {
				pt_key: userInfo.ptKey,
				userName: userInfo.userName,
				userId: userInfo.userId,
				loginType: userInfo.loginType,
				loginUrl: userInfo.loginUrl,
				pk: userInfo.pk,
				orgName: userInfo.orgName,
				resources: userInfo.resources,
				env: userInfo.env,
				base_url: userInfo.base_url
			} as ILoginInfo;
		} catch (error) {
			this.logService.error('Failed to process login info:', error);
			return null;
		}
	}

	public async isLoggedIn(): Promise<boolean> {
		const loginInfo = await this.getLoginInfo();
		return !!loginInfo && !!loginInfo.pt_key;
	}

	public async logout(): Promise<boolean> {
		try {
			// Use joyCoderUserService to write empty user info
			this.joyCoderUserService.writeMainUserInfo(null);
			this.logService.info('logout env', this.productService.joyCoderEnv);
			// Clear stored user configuration
			try {
				const httpAuthService = HttpAuthService.getInstance();
				httpAuthService.clearStoredUserConfig();
				this.logService.info('User config cleared successfully during logout');
			} catch (configError) {
				this.logService.warn('Failed to clear user config during logout:', configError);
			}


			// Trigger login status change event
			this._onDidChangeLoginStatus.fire(false);
			return true;
		} catch (error) {
			this.logService.error('Logout failed:', error);
			return false;
		}
	}
}

// No longer use registerSingleton, use manual registration instead
