/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

import { LoginChannel } from '../common/loginIpc.js';
import { LoginMainService } from './loginMainService.js';
import { LoginProtocolHandler } from './loginProtocolHandler.js';
import { Server as ElectronIPCServer } from '../../../base/parts/ipc/electron-main/ipc.electron.js';
import { ILogService } from '../../../platform/log/common/log.js';
import { ILifecycleMainService } from '../../../platform/lifecycle/electron-main/lifecycleMainService.js';
import { IJoyCoderUserService } from '../../../platform/user/joyCoderUserService.js';
import { IProductService } from '../../product/common/productService.js';
import { IConfigurationService } from '../../configuration/common/configuration.js';
// import { INativeHostMainService } from '../../native/electron-main/nativeHostMainService.js';
// import { ILoginMainService } from '../common/login.js';
import { IStorageMainService } from '../../../platform/storage/electron-main/storageMainService.js';
import { IUserDataProfilesMainService } from '../../userDataProfile/electron-main/userDataProfile.js';
import { HttpAuthService } from '../common/httpAuthService.js';

/**
 * 注册登录主进程服务
 */
export function registerLoginMainService(
	mainProcessElectronServer: ElectronIPCServer,
	logService: ILogService,
	lifecycleMainService: ILifecycleMainService,
	joyCoderUserService: IJoyCoderUserService,
	productService: IProductService,
	configurationService: IConfigurationService,
	storageMainService: IStorageMainService,
	userDataProfilesMainService: IUserDataProfilesMainService
): void {
	// 创建登录服务实例
	const loginService = new LoginMainService(
		logService,
		joyCoderUserService,
		productService,
		configurationService
	);

	// 获取HttpAuthService实例并设置存储服务
	const httpAuthService = HttpAuthService.getInstance();
	httpAuthService.setStorageServices(storageMainService, userDataProfilesMainService);

	// 创建协议处理器（不需要保存引用）
	new LoginProtocolHandler(loginService, logService, lifecycleMainService, productService);

	// 创建登录通道
	const loginChannel = new LoginChannel(loginService);

	// 注册通道
	mainProcessElectronServer.registerChannel('login', loginChannel);
}
