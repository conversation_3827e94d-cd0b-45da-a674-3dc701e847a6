/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/
import { createServer, Server, IncomingMessage, ServerResponse } from 'http';
import { randomBytes } from 'crypto';
import { BrowserWindow, app } from "electron";
import { ILoginMainService } from './login.js';
import { URL } from 'url';
import { IStorageMainService } from '../../storage/electron-main/storageMainService.js';
import { IUserDataProfilesMainService } from '../../userDataProfile/electron-main/userDataProfile.js';

export interface HttpAuthInfo {
	port: number;
	authKey: string;
}

export interface StorageUser {
	tenant?: string;
	base_url?: string;
	env?: string;
}

export class HttpAuthService {
	private static instance: HttpAuthService;
	private server: Server | null = null;
	private currentAuthInfo: HttpAuthInfo | null = null;
	private timeout: NodeJS.Timeout | null = null;
	private loginMainService: ILoginMainService | null = null;
	private storageMainService: IStorageMainService | null = null;
	private userDataProfilesMainService: IUserDataProfilesMainService | null = null;

	private static readonly STORAGE_USER_KEY = 'joycode.storageUser';

	private constructor() { }

	public static getInstance(): HttpAuthService {
		if (!HttpAuthService.instance) {
			HttpAuthService.instance = new HttpAuthService();
		}
		return HttpAuthService.instance;
	}

	public setLoginMainService(service: ILoginMainService): void {
		this.loginMainService = service;
	}

	public setStorageServices(
		storageMainService: IStorageMainService,
		userDataProfilesMainService: IUserDataProfilesMainService
	): void {
		this.storageMainService = storageMainService;
		this.userDataProfilesMainService = userDataProfilesMainService;
	}

	/**
	 * Store user environment configuration information
	 * @param storageUser User environment configuration object
	 */
	public storeUserConfig(storageUser: StorageUser): void {
		if (!this.storageMainService || !this.userDataProfilesMainService) {
			console.warn('Storage services not initialized, cannot store user config');
			return;
		}

		try {
			const profileStorage = this.storageMainService.profileStorage(
				this.userDataProfilesMainService.defaultProfile
			);

			const storageValue = JSON.stringify(storageUser);
			profileStorage.storage.set(HttpAuthService.STORAGE_USER_KEY, storageValue);
		} catch (error) {
			console.error('Failed to store user config:', error);
		}
	}

	/**
	 * Get stored user environment configuration information
	 * @returns StorageUser object or null
	 */
	public getStoredUserConfig(): StorageUser | null {
		if (!this.storageMainService || !this.userDataProfilesMainService) {
			console.warn('Storage services not initialized, cannot retrieve user config');
			return null;
		}

		try {
			const profileStorage = this.storageMainService.profileStorage(
				this.userDataProfilesMainService.defaultProfile
			);

			const storageValue = profileStorage.storage.get(HttpAuthService.STORAGE_USER_KEY, '{}');
			const parsedValue = JSON.parse(storageValue);

			// Validate if it's a valid StorageUser object
			if (parsedValue && (parsedValue.tenant || parsedValue.base_url || parsedValue.env)) {
				return parsedValue as StorageUser;
			}

			return null;
		} catch (error) {
			console.error('Failed to retrieve user config:', error);
			return null;
		}
	}

	/**
	 * Clear stored user environment configuration information
	 */
	public clearStoredUserConfig(): void {
		if (!this.storageMainService || !this.userDataProfilesMainService) {
			console.warn('Storage services not initialized, cannot clear user config');
			return;
		}

		try {
			const profileStorage = this.storageMainService.profileStorage(
				this.userDataProfilesMainService.defaultProfile
			);

			profileStorage.storage.delete(HttpAuthService.STORAGE_USER_KEY);
			console.log('User config cleared successfully');
		} catch (error) {
			console.error('Failed to clear user config:', error);
		}
	}

	private async findAvailablePort(startPort: number): Promise<number> {
		const maxPort = 65535;
		for (let port = startPort; port <= maxPort; port++) {
			try {
				const server = createServer();
				await new Promise((resolve, reject) => {
					server.once('error', reject);
					server.once('listening', () => {
						server.close();
						resolve(port);
					});
					server.listen(port);
				});
				return port;
			} catch (err) {
				continue;
			}
		}
		throw new Error('No available ports found');
	}

	private generateAuthKey(): string {
		return randomBytes(16).toString('hex');
	}

	private parseQueryParams(url: string): { [key: string]: string } {
		const queryParams: { [key: string]: string } = {};
		const urlObj = new URL(url, 'http://localhost');
		urlObj.searchParams.forEach((value, key) => {
			queryParams[key] = value;
		});
		return queryParams;
	}

	private sendResponse(res: ServerResponse, statusCode: number, data: any): void {
		res.writeHead(statusCode, {
			'Content-Type': 'application/json',
			'Access-Control-Allow-Origin': '*',
			'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
			'Access-Control-Allow-Headers': 'Content-Type'
		});
		if (statusCode === 307) {
			res.end(JSON.stringify(data));
		} else {
			res.end();
		}

	}

	private redirectResponse(res: ServerResponse, location: string, redirect: string, message?: string): void {
		const urlObj = new URL(location);
		urlObj.searchParams.set('redirect', redirect);
		message && urlObj.searchParams.set('message', message);
		const newCallbackUrl = urlObj.toString();
		// console.log('redirectResponse----url', newCallbackUrl);

		res.writeHead(307, {
			'Location': newCallbackUrl,
			'Access-Control-Allow-Origin': '*',
			'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
			'Access-Control-Allow-Headers': 'Content-Type'
		});
		res.end();
	}

	public async startServer(): Promise<HttpAuthInfo> {
		// If server is already running, check if server is actually listening
		if (this.server && this.currentAuthInfo) {
			// Check if server is actually listening
			if (this.server.listening) {
				console.log('HTTP auth server is already running, reusing existing server', this.currentAuthInfo);
				return this.currentAuthInfo;
			} else {
				// Server object exists but not listening, clean up state
				console.log('HTTP auth server object exists but not listening, cleaning up state');
				this.server = null;
				this.currentAuthInfo = null;
				if (this.timeout) {
					clearTimeout(this.timeout);
					this.timeout = null;
				}
			}
		}

		console.log('Starting new HTTP auth server');

		// Only clear login status when server is not running, avoid state confusion during duplicate login
		if (this.loginMainService?.logout) {
			this.loginMainService?.logout();
		}

		const port = await this.findAvailablePort(10000 + Math.floor(Math.random() * 50000));
		const authKey = this.generateAuthKey();

		this.server = createServer(async (req: IncomingMessage, res: ServerResponse) => {
			// Handle CORS preflight requests
			if (req.method === 'OPTIONS') {
				res.writeHead(204, {
					'Access-Control-Allow-Origin': '*',
					'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
					'Access-Control-Allow-Headers': 'Content-Type'
				});
				res.end();
				return;
			}

			if (req.method === 'GET' && req.url) {
				const queryParams = this.parseQueryParams(req.url);
				if (!queryParams) return;
				const { authPort, pt_key, login_type, ideAppName, fromIde, callbackUrl } = queryParams;
				// tenant: tenant;
				// in_base_url: internal network address;
				// out_base_url: external network address;
				// env: pr pre-release, inner internal production, prod public environment
				const { tenant, base_url, env = "prod" } = queryParams;
				const receivedKey = queryParams.authKey;

				const storageUser: StorageUser = {
					tenant,
					base_url,
					env
				};

				// console.log('Storing user environment configuration info:', storageUser, req.url);
				// Store user environment configuration information
				this.storeUserConfig(storageUser);

				try {
					// Notify renderer processes about the auth config update
					const { webContents } = require('electron');
					const allWebContents = webContents.getAllWebContents();
					allWebContents.forEach((wc: any) => {
						if (!wc.isDestroyed()) {
							wc.send('auth-config-updated', { env, base_url });
						}
					});
					console.log('Notified renderer processes about auth config update:', { env, base_url });
				} catch (error) {
					console.warn('Failed to notify renderer process about auth config update:', error);
				}

				if (receivedKey === authKey && Number(authPort) === Number(port)) {
					console.log('Received parameters:', queryParams);

					// Handle login callback
					if (this.loginMainService && pt_key && ideAppName === "JoyCode" && fromIde === "ide") {
						try {
							const login_status = await this.loginMainService.handleLoginCallback(pt_key || '', login_type || '');
							if (typeof login_status === 'boolean' && login_status) {
								// Restore window focus after successful login to prevent window from going to bottom
								console.log('Login successful, starting to restore window focus');

								// Delay window focus execution to ensure login process is fully completed
								setTimeout(() => {
									let targetWindow: BrowserWindow | null = BrowserWindow.getFocusedWindow();

									// If no focused window, try to get the last active window
									if (!targetWindow) {
										const allWindows = BrowserWindow.getAllWindows();
										console.log(`Found ${allWindows.length} windows`);

										// Prioritize visible and non-minimized windows
										const visibleWindow = allWindows.find(w => {
											const isVisible = w.isVisible();
											const isMinimized = w.isMinimized();
											console.log(`Window ${w.id}: visible=${isVisible}, minimized=${isMinimized}`);
											return isVisible && !isMinimized;
										});

										if (visibleWindow) {
											targetWindow = visibleWindow;
										} else if (allWindows.length > 0) {
											// If no suitable window found, use the first window
											targetWindow = allWindows[0];
											console.log(`Using first window: ${targetWindow.id}`);
										}
									}

									if (targetWindow) {
										console.log(`Preparing to focus window: ${targetWindow.id}`);

										// Ensure window is not minimized
										if (targetWindow.isMinimized()) {
											console.log('Window is minimized, restoring');
											targetWindow.restore();
										}

										// If window is not visible, show window
										if (!targetWindow.isVisible()) {
											console.log('Window is not visible, showing');
											targetWindow.show();
										}

										// Focus window to prevent it from going to bottom
										targetWindow.focus();

										// Use different focus strategies on different platforms
										if (process.platform === 'darwin') {
											// Mac system uses special way to get focus
											app.focus({ steal: true });
											console.log('Mac system: called app.focus({ steal: true })');
										} else if (process.platform === 'win32') {
											// Windows system additional handling
											targetWindow.setAlwaysOnTop(true);
											setTimeout(() => {
												targetWindow.setAlwaysOnTop(false);
											}, 500);
											console.log('Windows system: set temporary always on top');
										}

										console.log('Login successful, window focused');
									} else {
										console.warn('Login successful, but no operable window found');
									}
								}, 200); // 200ms delay to ensure login process completion


								// this.sendResponse(res, 200, { success: true, message: 'Authentication successful' });
								this.redirectResponse(res, callbackUrl, '1', 'Authentication successful');
								console.log('Authentication successful, about to close auth server');
								this.stopServer();
							} else {
								console.log('IDE login error');
								this.redirectResponse(res, callbackUrl, '2', 'IDE failed to process login info, please contact administrator');
								// this.sendResponse(res, 500, { success: false, message: login_status });
							}

						} catch (error) {
							console.error('Failed to handle login callback:', error);
							this.redirectResponse(res, callbackUrl, '2', 'Failed to process login info, please contact administrator');
						}
					} else {
						this.redirectResponse(res, callbackUrl, '2', 'Missing login info, please login from IDE again');
					}
				} else {
					this.redirectResponse(res, callbackUrl, '2', 'IDE authentication failed, please login from IDE again');
				}
			} else {
				this.sendResponse(res, 405, { success: false, message: 'Method not allowed, please login from IDE again' });
			}
		});

		// Listen for server error and close events to ensure state synchronization
		this.server.on('error', (error) => {
			console.error('HTTP auth server error occurred:', error);
			this.stopServer();
		});

		this.server.on('close', () => {
			console.log('HTTP auth server closed');
			// Clean up state, but don't call stopServer repeatedly to avoid loops
			this.server = null;
			this.currentAuthInfo = null;
			if (this.timeout) {
				clearTimeout(this.timeout);
				this.timeout = null;
			}
		});

		this.server.listen(port);
		this.currentAuthInfo = { port, authKey };

		console.log('HTTP auth server started, port:', port);

		// Set 10 minute timeout
		this.timeout = setTimeout(() => {
			console.log('HTTP auth server timeout, auto closing');
			this.stopServer();
		}, 10 * 60 * 1000);

		return this.currentAuthInfo;
	}

	public stopServer(): void {
		if (this.server) {
			console.log('Closing HTTP auth server...');

			// Remove event listeners to avoid triggering close event handling during manual close
			this.server.removeAllListeners('error');
			this.server.removeAllListeners('close');

			this.server.close(() => {
				console.log('HTTP auth server successfully closed');
			});

			this.server = null;
			this.currentAuthInfo = null;

			if (this.timeout) {
				clearTimeout(this.timeout);
				this.timeout = null;
			}
		}
	}

	public getCurrentAuthInfo(): HttpAuthInfo | null {
		return this.currentAuthInfo;
	}

	/**
	 * Check if HTTP auth server is running
	 * @returns true if server is running, false otherwise
	 */
	public isServerRunning(): boolean {
		return !!(this.server && this.server.listening && this.currentAuthInfo);
	}

	/**
	 * Get server running status information
	 * @returns Object containing server status
	 */
	public getServerStatus(): { isRunning: boolean; authInfo: HttpAuthInfo | null; hasTimeout: boolean } {
		return {
			isRunning: this.isServerRunning(),
			authInfo: this.currentAuthInfo,
			hasTimeout: !!this.timeout
		};
	}
}
