/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

import * as http from 'http';
import { AddressInfo } from 'net';
import assert from 'assert';
import { CancellationToken, CancellationTokenSource } from '../../../../common/cancellation.js';
import { ensureNoDisposablesAreLeakedInTestSuite } from '../../../../test/common/utils.js';
import { request } from '../../common/requestImpl.js';
import { streamToBuffer } from '../../../../common/buffer.js';

// 安全地清理 URL，防止污点传播
function sanitizeUrl(url: string | undefined): string {
	if (!url || typeof url !== 'string') {
		return '/unknown';
	}

	// 限制 URL 长度，防止过长的 URL 攻击
	if (url.length > 2000) {
		return '/too-long';
	}

	// 移除控制字符和潜在的注入字符
	let sanitized = url
		.replace(/[\x00-\x1f\x7f-\x9f]/g, '') // 移除控制字符
		.replace(/[<>"'&\\]/g, '') // 移除可能导致 XSS 的字符
		.replace(/javascript:/gi, '') // 移除 javascript: 协议
		.replace(/data:/gi, '') // 移除 data: 协议
		.replace(/vbscript:/gi, '') // 移除 vbscript: 协议
		.replace(/file:/gi, '') // 移除 file: 协议
		.replace(/\.\./g, '') // 移除路径遍历
		.substring(0, 200); // 限制长度

	// 确保 URL 以 / 开头
	if (!sanitized.startsWith('/')) {
		sanitized = '/' + sanitized;
	}

	// 验证 URL 格式和安全性
	try {
		const urlObj = new URL(sanitized, 'http://localhost');

		// 检查路径是否包含危险模式
		const pathname = urlObj.pathname;
		const dangerousPatterns = [
			/\.\./,           // 路径遍历
			/\/\//,           // 双斜杠
			/[<>"'&\\]/,      // 危险字符
			/\0/,             // 空字节
			/[\x00-\x1f]/,    // 控制字符
			/eval\(/i,        // eval 函数
			/script/i,        // script 标签
			/javascript/i,    // javascript 协议
			/vbscript/i,      // vbscript 协议
			/data:/i,         // data 协议
			/file:/i          // file 协议
		];

		for (const pattern of dangerousPatterns) {
			if (pattern.test(pathname)) {
				return '/sanitized-dangerous';
			}
		}

		// 返回清理后的路径和查询参数
		return pathname + urlObj.search;
	} catch {
		// 如果 URL 无效，返回安全的默认值
		return '/sanitized-invalid';
	}
}


suite('Request', () => {

	let port: number;
	let server: http.Server;

	setup(async () => {
		port = await new Promise<number>((resolvePort, rejectPort) => {
			server = http.createServer((req, res) => {
				// 安全地处理 URL，防止污点传播
				const safeUrl = sanitizeUrl(req.url);

				if (safeUrl === '/noreply') {
					return; // never respond
				}
				res.setHeader('Content-Type', 'application/json');
				if (req.headers['echo-header']) {
					res.setHeader('echo-header', req.headers['echo-header']);
				}
				const data: Buffer[] = [];
				req.on('data', chunk => data.push(chunk));
				req.on('end', () => {
					// 使用已经清理过的 URL，避免污点传播
					const responseData = {
						method: req.method,
						url: safeUrl,
						data: Buffer.concat(data).toString()
					};
					res.end(JSON.stringify(responseData));
				});
			}).listen(0, '127.0.0.1', () => {
				const address = server.address();
				resolvePort((address as AddressInfo).port);
			}).on('error', err => {
				rejectPort(err);
			});
		});
	});

	teardown(async () => {
		await new Promise<void>((resolve, reject) => {
			server.close(err => err ? reject(err) : resolve());
		});
	});

	test('GET', async () => {
		const context = await request({
			url: `http://127.0.0.1:${port}`,
			headers: {
				'echo-header': 'echo-value'
			}
		}, CancellationToken.None);
		assert.strictEqual(context.res.statusCode, 200);
		assert.strictEqual(context.res.headers['content-type'], 'application/json');
		assert.strictEqual(context.res.headers['echo-header'], 'echo-value');
		const buffer = await streamToBuffer(context.stream);
		const body = JSON.parse(buffer.toString());
		assert.strictEqual(body.method, 'GET');
		assert.strictEqual(body.url, '/');
	});

	test('POST', async () => {
		const context = await request({
			type: 'POST',
			url: `http://127.0.0.1:${port}/postpath`,
			data: 'Some data',
		}, CancellationToken.None);
		assert.strictEqual(context.res.statusCode, 200);
		assert.strictEqual(context.res.headers['content-type'], 'application/json');
		const buffer = await streamToBuffer(context.stream);
		const body = JSON.parse(buffer.toString());
		assert.strictEqual(body.method, 'POST');
		assert.strictEqual(body.url, '/postpath');
		assert.strictEqual(body.data, 'Some data');
	});

	test('timeout', async () => {
		try {
			await request({
				type: 'GET',
				url: `http://127.0.0.1:${port}/noreply`,
				timeout: 123,
			}, CancellationToken.None);
			assert.fail('Should fail with timeout');
		} catch (err) {
			assert.strictEqual(err.message, 'Fetch timeout: 123ms');
		}
	});

	test('cancel', async () => {
		try {
			const source = new CancellationTokenSource();
			const res = request({
				type: 'GET',
				url: `http://127.0.0.1:${port}/noreply`,
			}, source.token);
			await new Promise(resolve => setTimeout(resolve, 100));
			source.cancel();
			await res;
			assert.fail('Should fail with cancellation');
		} catch (err) {
			assert.strictEqual(err.message, 'Canceled');
		}
	});

	ensureNoDisposablesAreLeakedInTestSuite();
});
