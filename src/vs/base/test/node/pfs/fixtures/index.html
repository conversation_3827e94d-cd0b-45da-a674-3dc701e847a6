<!DOCTYPE html>
<html>
<head id='headID'>
    <title>Strada </title>
    <link href="site.css" rel="stylesheet" type="text/css" />
    <script src="jquery-1.4.1.js"></script>
    <script src="../compiler/dtree.js" type="text/javascript"></script>
    <script src="../compiler/typescript.js" type="text/javascript"></script>
    <script type="text/javascript">

    // Compile strada source into resulting javascript
    function compile(prog, libText) {
        var outfile = {
          source: "",
          Write: function (s) { this.source += s; },
          WriteLine: function (s) { this.source += s + "\r"; },
        }

        var parseErrors = []

        var compiler=new Tools.TypeScriptCompiler(outfile,true);
        compiler.setErrorCallback(function(start,len, message) { parseErrors.push({start:start, len:len, message:message}); });
        compiler.addUnit(libText,"lib.ts");
        compiler.addUnit(prog,"input.ts");
        compiler.typeCheck();
        compiler.emit();

        if(parseErrors.length > 0 ) {
          //throw new Error(parseErrors);
        }

	while(outfile.source[0] == '/' && outfile.source[1] == '/' && outfile.source[2] == ' ') {
            outfile.source = outfile.source.slice(outfile.source.indexOf('\r')+1);
        }
        var errorPrefix = "";
	for(var i = 0;i<parseErrors.length;i++) {
          errorPrefix += "// Error: (" + parseErrors[i].start + "," + parseErrors[i].len + ") " + parseErrors[i].message + "\r";
        }

        return errorPrefix + outfile.source;
    }
    </script>
    <script type="text/javascript">

        var libText = "";
        $.get("../compiler/lib.ts", function(newLibText) {
            libText = newLibText;
        });


        // execute the javascript in the compiledOutput pane
        function execute() {
          $('#compilation').text("Running...");
          var txt = $('#compiledOutput').val();
          var res;
          try {
             // 验证输入不为空且为字符串
             if (!txt || typeof txt !== 'string') {
               throw new Error("Invalid input: code must be a non-empty string");
             }

             // 限制代码长度
             if (txt.length > 5000) {
               throw new Error("Code too long: maximum 5000 characters allowed");
             }

             var ret = safeExecute(txt);
             res = "Ran successfully! Result: " + String(ret).substring(0, 100);
          } catch(e) {
             res = "Exception thrown: " + String(e).substring(0, 200);
          }
          $('#compilation').text(String(res));
        }

        // 安全地执行代码，防止代码注入攻击
        function safeExecute(code) {
          // 验证和清理输入代码
          var sanitizedCode = sanitizeCode(code);
          if (!sanitizedCode) {
            throw new Error("Invalid or unsafe code detected");
          }

          // 创建安全的执行环境，隔离全局作用域
          var safeGlobals = {
            console: {
              log: function() {
                var args = Array.prototype.slice.call(arguments);
                return "Console: " + args.join(' ');
              }
            },
            Math: Math,
            JSON: JSON,
            Array: Array,
            Object: Object,
            String: String,
            Number: Number,
            Boolean: Boolean,
            Date: Date,
            RegExp: RegExp
          };

          // 使用 Function 构造函数替代 eval，提供更好的安全性
          // 在严格模式下执行，限制访问全局作用域
          try {
            // 创建一个安全的执行函数，只传递安全的全局对象
            var funcBody = '"use strict"; var window=undefined, document=undefined, location=undefined, navigator=undefined, history=undefined, parent=undefined, top=undefined, frames=undefined, self=undefined; return (' + sanitizedCode + ')';
            var func = new Function('safeGlobals', 'with(safeGlobals) { ' + funcBody + ' }');
            return func(safeGlobals);
          } catch (e) {
            // 如果作为表达式执行失败，尝试作为语句执行
            try {
              var stmtBody = '"use strict"; var window=undefined, document=undefined, location=undefined, navigator=undefined, history=undefined, parent=undefined, top=undefined, frames=undefined, self=undefined; ' + sanitizedCode;
              var stmtFunc = new Function('safeGlobals', 'with(safeGlobals) { ' + stmtBody + ' }');
              return stmtFunc(safeGlobals);
            } catch (e2) {
              throw new Error("Code execution failed: " + e2.message);
            }
          }
        }

        // 清理和验证代码，防止恶意注入
        function sanitizeCode(code) {
          if (!code || typeof code !== 'string') {
            return null;
          }

          // 移除注释以防止注释注入
          var cleaned = code.replace(/\/\*[\s\S]*?\*\//g, '').replace(/\/\/.*$/gm, '');

          // 检查代码长度限制
          if (cleaned.length > 10000) {
            throw new Error("Code too long");
          }

          // 检查危险的全局对象和函数
          var dangerousPatterns = [
            /\beval\s*\(/i,
            /\bFunction\s*\(/i,
            /\bsetTimeout\s*\(/i,
            /\bsetInterval\s*\(/i,
            /\bdocument\s*[\.\[]/i,
            /\bwindow\s*[\.\[]/i,
            /\blocation\s*[\.\[]/i,
            /\bnavigator\s*[\.\[]/i,
            /\bhistory\s*[\.\[]/i,
            /\bparent\s*[\.\[]/i,
            /\btop\s*[\.\[]/i,
            /\bframes\s*[\.\[]/i,
            /\bself\s*[\.\[]/i,
            /\bthis\s*[\.\[]/i,
            /\b__proto__\b/i,
            /\bconstructor\b/i,
            /\bprototype\b/i,
            /\bimport\s+/i,
            /\brequire\s*\(/i,
            /\bprocess\s*[\.\[]/i,
            /\bglobal\s*[\.\[]/i,
            /\bmodule\s*[\.\[]/i,
            /\bexports\s*[\.\[]/i,
            /\balert\s*\(/i,
            /\bconfirm\s*\(/i,
            /\bprompt\s*\(/i,
            /\bopen\s*\(/i,
            /\bclose\s*\(/i,
            /\bfocus\s*\(/i,
            /\bblur\s*\(/i,
            /\bXMLHttpRequest\b/i,
            /\bfetch\s*\(/i,
            /\bWebSocket\b/i,
            /\bWorker\b/i,
            /\bSharedWorker\b/i,
            /\bServiceWorker\b/i,
            /\bEventSource\b/i,
            /\bIndexedDB\b/i,
            /\blocalStorage\b/i,
            /\bsessionStorage\b/i,
            /\bgetElementById\b/i,
            /\bquerySelector\b/i,
            /\binnerHTML\b/i,
            /\bouterHTML\b/i,
            /\binsertAdjacentHTML\b/i,
            /\bexecScript\b/i,
            /\bsetImmediate\b/i,
            /\brequestAnimationFrame\b/i,
            /\bpostMessage\b/i,
            /\baddEventListener\b/i,
            /\bremoveEventListener\b/i,
            /\bwith\s*\(/i,
            /\btry\s*\{[\s\S]*catch\s*\(/i,
            /\bthrow\s+/i,
            /\bnew\s+Function\s*\(/i,
            /\bnew\s+Error\s*\(/i,
            /\[\s*["']constructor["']\s*\]/i,
            /\[\s*["']__proto__["']\s*\]/i,
            /\[\s*["']prototype["']\s*\]/i
          ];

          for (var i = 0; i < dangerousPatterns.length; i++) {
            if (dangerousPatterns[i].test(cleaned)) {
              throw new Error("Potentially dangerous code detected: " + dangerousPatterns[i].source);
            }
          }

          // 只允许基本的 JavaScript 语法
          var allowedPatterns = [
            /^[\s\w\+\-\*\/\%\=\!\<\>\&\|\^\~\?\:\;\,\.\(\)\[\]\{\}\"\'`\n\r\t]*$/
          ];

          var isValid = allowedPatterns.some(function(pattern) {
            return pattern.test(cleaned);
          });

          if (!isValid) {
            throw new Error("Code contains invalid characters");
          }

          return cleaned.trim();
        }

        // recompile the stradaSrc and populate the compiledOutput pane
        function srcUpdated() {
            var newText = $('#stradaSrc').val();
            var compiledSource;
            try {
                compiledSource = compile(newText, libText);
            } catch (e) {
                compiledSource = "//Parse error"
                for(var i in e)
                  compiledSource += "\r// " + e[i];
            }
            $('#compiledOutput').val(compiledSource);
        }

        // Populate the stradaSrc pane with one of the built in samples
        function exampleSelectionChanged() {
            var examples = document.getElementById('examples');
            var selectedExample = examples.options[examples.selectedIndex].value;
            if (selectedExample != "") {
                $.get('examples/' + selectedExample, function (srcText) {
                    $('#stradaSrc').val(srcText);
                    setTimeout(srcUpdated,100);
                }, function (err) {
                    console.log(err);
                });
            }
        }

    </script>
</head>
<body>
    <h1>TypeScript</h1>
    <br />
    <select id="examples" onchange='exampleSelectionChanged()'>
        <option value="">Select...</option>
        <option value="small.ts">Small</option>
        <option value="employee.ts">Employees</option>
        <option value="conway.ts">Conway Game of Life</option>
        <option value="typescript.ts">TypeScript Compiler</option>
    </select>

    <div>
        <textarea id='stradaSrc' rows='40' cols='80' onchange='srcUpdated()' onkeyup='srcUpdated()' spellcheck="false">
//Type your TypeScript here...
      </textarea>
      <textarea id='compiledOutput' rows='40' cols='80' spellcheck="false">
//Compiled code will show up here...
      </textarea>
      <br />
      <button onclick='execute()'/>Run</button>
      <div id='compilation'>Press 'run' to execute code...</div>
      <div id='results'>...write your results into #results...</div>
    </div>
    <div id='bod' style='display:none'></div>
</body>
</html>
