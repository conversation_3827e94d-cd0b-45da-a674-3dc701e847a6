/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

import { isStandalone } from '../../../base/browser/browser.js';
import { mainWindow } from '../../../base/browser/window.js';
import { VSBuffer, decodeBase64, encodeBase64 } from '../../../base/common/buffer.js';
import { Emitter } from '../../../base/common/event.js';
import { Disposable, IDisposable } from '../../../base/common/lifecycle.js';
import { parse } from '../../../base/common/marshalling.js';
import { Schemas } from '../../../base/common/network.js';
import { posix } from '../../../base/common/path.js';
import { isEqual } from '../../../base/common/resources.js';
import { ltrim } from '../../../base/common/strings.js';
import { URI, UriComponents } from '../../../base/common/uri.js';
import product from '../../../platform/product/common/product.js';
import { ISecretStorageProvider } from '../../../platform/secrets/common/secrets.js';
import { isFolderToOpen, isWorkspaceToOpen } from '../../../platform/window/common/window.js';
import type { IWorkbenchConstructionOptions, IWorkspace, IWorkspaceProvider } from '../../../workbench/browser/web.api.js';
import { AuthenticationSessionInfo } from '../../../workbench/services/authentication/browser/authenticationService.js';
import type { IURLCallbackProvider } from '../../../workbench/services/url/browser/urlService.js';
import { create } from '../../../workbench/workbench.web.main.internal.js';

// 安全的 URL 构建和验证
class SecureUrlBuilder {
	private static readonly ALLOWED_PROTOCOLS = ['http:', 'https:'];
	private static readonly ALLOWED_HOSTS = new Set([
		'localhost',
		'127.0.0.1',
		'[::1]'
	]);

	/**
	 * 安全地获取当前页面的 origin 和 pathname
	 */
	static getSafeBaseUrl(): { origin: string; pathname: string } | null {
		try {
			const location = document.location;

			// 验证协议
			if (!this.ALLOWED_PROTOCOLS.includes(location.protocol)) {
				console.warn('[SECURITY] Invalid protocol detected:', location.protocol);
				return null;
			}

			// 验证主机名（对于本地开发和已知的安全主机）
			const hostname = location.hostname.toLowerCase();
			const isLocalhost = this.ALLOWED_HOSTS.has(hostname);
			const isProductHost = this.isProductHost(hostname);

			if (!isLocalhost && !isProductHost) {
				console.warn('[SECURITY] Untrusted hostname detected:', hostname);
				return null;
			}

			// 清理和验证 pathname
			const pathname = this.sanitizePathname(location.pathname);

			return {
				origin: location.origin,
				pathname: pathname
			};
		} catch (error) {
			console.error('[SECURITY] Error getting base URL:', error);
			return null;
		}
	}

	/**
	 * 检查是否为产品相关的可信主机
	 */
	private static isProductHost(hostname: string): boolean {
		// 检查是否为产品配置中的可信域名
		const productHosts = [
			'vscode.dev',
			'github.dev',
			'codespaces.new'
		];

		// 检查完全匹配或子域名匹配
		return productHosts.some(host =>
			hostname === host || hostname.endsWith('.' + host)
		);
	}

	/**
	 * 清理和验证 pathname
	 */
	private static sanitizePathname(pathname: string): string {
		if (!pathname || typeof pathname !== 'string') {
			return '/';
		}

		// 移除危险字符
		const sanitized = pathname
			.replace(/[<>"'&]/g, '') // 移除 XSS 风险字符
			.replace(/[\x00-\x1f\x7f-\x9f]/g, '') // 移除控制字符
			.replace(/\.\./g, '') // 防止路径遍历
			.substring(0, 500); // 限制长度

		// 确保以 / 开头
		return sanitized.startsWith('/') ? sanitized : '/' + sanitized;
	}

	/**
	 * 安全地获取当前页面的 URL 对象
	 */
	static getSafeCurrentUrl(): URL | null {
		try {
			const location = document.location;

			// 验证协议
			if (!this.ALLOWED_PROTOCOLS.includes(location.protocol)) {
				console.warn('[SECURITY] Invalid protocol in current URL:', location.protocol);
				return null;
			}

			// 验证主机名
			const hostname = location.hostname.toLowerCase();
			const isLocalhost = this.ALLOWED_HOSTS.has(hostname);
			const isProductHost = this.isProductHost(hostname);

			if (!isLocalhost && !isProductHost) {
				console.warn('[SECURITY] Untrusted hostname in current URL:', hostname);
				return null;
			}

			// 创建安全的 URL 对象
			return new URL(location.href);
		} catch (error) {
			console.error('[SECURITY] Error parsing current URL:', error);
			return null;
		}
	}

	/**
	 * 验证 URL 的安全性
	 */
	static validateUrl(url: string): boolean {
		try {
			const urlObj = new URL(url);

			// 验证协议
			if (!this.ALLOWED_PROTOCOLS.includes(urlObj.protocol)) {
				console.warn('[SECURITY] Invalid protocol in target URL:', urlObj.protocol);
				return false;
			}

			// 验证主机名
			const hostname = urlObj.hostname.toLowerCase();
			const isLocalhost = this.ALLOWED_HOSTS.has(hostname);
			const isProductHost = this.isProductHost(hostname);

			if (!isLocalhost && !isProductHost) {
				console.warn('[SECURITY] Untrusted hostname in target URL:', hostname);
				return false;
			}

			// 验证路径不包含危险模式
			const pathname = urlObj.pathname;
			const dangerousPatterns = [
				/\.\./,           // 路径遍历
				/\/\//,           // 双斜杠
				/[<>"'&\\]/,      // 危险字符
				/\0/,             // 空字节
				/javascript:/i,   // javascript 协议
				/data:/i,         // data 协议
				/vbscript:/i      // vbscript 协议
			];

			for (const pattern of dangerousPatterns) {
				if (pattern.test(pathname)) {
					console.warn('[SECURITY] Dangerous pattern detected in target URL path:', pattern.source);
					return false;
				}
			}

			// 验证查询参数
			for (const [key, value] of urlObj.searchParams) {
				if (!this.isValidQueryParam(key, value)) {
					console.warn('[SECURITY] Invalid query parameter in target URL:', key, value);
					return false;
				}
			}

			return true;
		} catch (error) {
			console.error('[SECURITY] Error validating target URL:', error);
			return false;
		}
	}

	/**
	 * 安全地构建目标 URL
	 */
	static buildTargetUrl(queryParams: Record<string, string>): string | null {
		const baseUrl = this.getSafeBaseUrl();
		if (!baseUrl) {
			return null;
		}

		try {
			const url = new URL(baseUrl.pathname, baseUrl.origin);

			// 添加查询参数
			Object.entries(queryParams).forEach(([key, value]) => {
				if (this.isValidQueryParam(key, value)) {
					url.searchParams.set(key, value);
				}
			});

			return url.toString();
		} catch (error) {
			console.error('[SECURITY] Error building target URL:', error);
			return null;
		}
	}

	/**
	 * 验证查询参数的安全性
	 */
	private static isValidQueryParam(key: string, value: string): boolean {
		// 验证键名
		if (!key || typeof key !== 'string' || key.length > 100) {
			return false;
		}

		// 验证值
		if (typeof value !== 'string' || value.length > 2000) {
			return false;
		}

		// 移除危险字符
		const sanitizedValue = value.replace(/[<>"'&\x00-\x1f\x7f-\x9f]/g, '');
		return sanitizedValue.length > 0;
	}
}

interface ISecretStorageCrypto {
	seal(data: string): Promise<string>;
	unseal(data: string): Promise<string>;
}

class TransparentCrypto implements ISecretStorageCrypto {
	async seal(data: string): Promise<string> {
		return data;
	}

	async unseal(data: string): Promise<string> {
		return data;
	}
}

const enum AESConstants {
	ALGORITHM = 'AES-GCM',
	KEY_LENGTH = 256,
	IV_LENGTH = 12,
}

class NetworkError extends Error {
	constructor(inner: Error) {
		super(inner.message);
		this.name = inner.name;
		this.stack = inner.stack;
	}
}

class ServerKeyedAESCrypto implements ISecretStorageCrypto {
	private _serverKey: Uint8Array | undefined;

	/** Gets whether the algorithm is supported; requires a secure context */
	public static supported() {
		return !!crypto.subtle;
	}

	constructor(private readonly authEndpoint: string) { }

	async seal(data: string): Promise<string> {
		// Get a new key and IV on every change, to avoid the risk of reusing the same key and IV pair with AES-GCM
		// (see also: https://developer.mozilla.org/en-US/docs/Web/API/AesGcmParams#properties)
		const iv = mainWindow.crypto.getRandomValues(new Uint8Array(AESConstants.IV_LENGTH));
		// crypto.getRandomValues isn't a good-enough PRNG to generate crypto keys, so we need to use crypto.subtle.generateKey and export the key instead
		const clientKeyObj = await mainWindow.crypto.subtle.generateKey(
			{ name: AESConstants.ALGORITHM as const, length: AESConstants.KEY_LENGTH as const },
			true,
			['encrypt', 'decrypt']
		);

		const clientKey = new Uint8Array(await mainWindow.crypto.subtle.exportKey('raw', clientKeyObj));
		const key = await this.getKey(clientKey);
		const dataUint8Array = new TextEncoder().encode(data);
		const cipherText: ArrayBuffer = await mainWindow.crypto.subtle.encrypt(
			{ name: AESConstants.ALGORITHM as const, iv },
			key,
			dataUint8Array
		);

		// Base64 encode the result and store the ciphertext, the key, and the IV in localStorage
		// Note that the clientKey and IV don't need to be secret
		const result = new Uint8Array([...clientKey, ...iv, ...new Uint8Array(cipherText)]);
		return encodeBase64(VSBuffer.wrap(result));
	}

	async unseal(data: string): Promise<string> {
		// encrypted should contain, in order: the key (32-byte), the IV for AES-GCM (12-byte) and the ciphertext (which has the GCM auth tag at the end)
		// Minimum length must be 44 (key+IV length) + 16 bytes (1 block encrypted with AES - regardless of key size)
		const dataUint8Array = decodeBase64(data);

		if (dataUint8Array.byteLength < 60) {
			throw Error('Invalid length for the value for credentials.crypto');
		}

		const keyLength = AESConstants.KEY_LENGTH / 8;
		const clientKey = dataUint8Array.slice(0, keyLength);
		const iv = dataUint8Array.slice(keyLength, keyLength + AESConstants.IV_LENGTH);
		const cipherText = dataUint8Array.slice(keyLength + AESConstants.IV_LENGTH);

		// Do the decryption and parse the result as JSON
		const key = await this.getKey(clientKey.buffer);
		const decrypted = await mainWindow.crypto.subtle.decrypt(
			{ name: AESConstants.ALGORITHM as const, iv: iv.buffer },
			key,
			cipherText.buffer
		);

		return new TextDecoder().decode(new Uint8Array(decrypted));
	}

	/**
	 * Given a clientKey, returns the CryptoKey object that is used to encrypt/decrypt the data.
	 * The actual key is (clientKey XOR serverKey)
	 */
	private async getKey(clientKey: Uint8Array): Promise<CryptoKey> {
		if (!clientKey || clientKey.byteLength !== AESConstants.KEY_LENGTH / 8) {
			throw Error('Invalid length for clientKey');
		}

		const serverKey = await this.getServerKeyPart();
		const keyData = new Uint8Array(AESConstants.KEY_LENGTH / 8);

		for (let i = 0; i < keyData.byteLength; i++) {
			keyData[i] = clientKey[i]! ^ serverKey[i]!;
		}

		return mainWindow.crypto.subtle.importKey(
			'raw',
			keyData,
			{
				name: AESConstants.ALGORITHM as const,
				length: AESConstants.KEY_LENGTH as const,
			},
			true,
			['encrypt', 'decrypt']
		);
	}

	private async getServerKeyPart(): Promise<Uint8Array> {
		if (this._serverKey) {
			return this._serverKey;
		}

		let attempt = 0;
		let lastError: Error | undefined;

		while (attempt <= 3) {
			try {
				const res = await fetch(this.authEndpoint, { credentials: 'include', method: 'POST' });
				if (!res.ok) {
					throw new Error(res.statusText);
				}
				const serverKey = new Uint8Array(await res.arrayBuffer());
				if (serverKey.byteLength !== AESConstants.KEY_LENGTH / 8) {
					throw Error(`The key retrieved by the server is not ${AESConstants.KEY_LENGTH} bit long.`);
				}
				this._serverKey = serverKey;
				return this._serverKey;
			} catch (e) {
				lastError = e instanceof Error ? e : new Error(String(e));
				attempt++;

				// exponential backoff
				await new Promise(resolve => setTimeout(resolve, attempt * attempt * 100));
			}
		}

		if (lastError) {
			throw new NetworkError(lastError);
		}
		throw new Error('Unknown error');
	}
}

export class LocalStorageSecretStorageProvider implements ISecretStorageProvider {
	private readonly _storageKey = 'secrets.provider';

	private _secretsPromise: Promise<Record<string, string>> = this.load();

	type: 'in-memory' | 'persisted' | 'unknown' = 'persisted';

	constructor(
		private readonly crypto: ISecretStorageCrypto,
	) { }

	private async load(): Promise<Record<string, string>> {
		const record = this.loadAuthSessionFromElement();
		// Get the secrets from localStorage
		const encrypted = localStorage.getItem(this._storageKey);
		if (encrypted) {
			try {
				const decrypted = JSON.parse(await this.crypto.unseal(encrypted));
				return { ...record, ...decrypted };
			} catch (err) {
				// TODO: send telemetry
				console.error('Failed to decrypt secrets from localStorage', err);
				if (!(err instanceof NetworkError)) {
					localStorage.removeItem(this._storageKey);
				}
			}
		}

		return record;
	}

	private loadAuthSessionFromElement(): Record<string, string> {
		let authSessionInfo: (AuthenticationSessionInfo & { scopes: string[][] }) | undefined;
		const authSessionElement = mainWindow.document.getElementById('vscode-workbench-auth-session');
		const authSessionElementAttribute = authSessionElement ? authSessionElement.getAttribute('data-settings') : undefined;
		if (authSessionElementAttribute) {
			try {
				authSessionInfo = JSON.parse(authSessionElementAttribute);
			} catch (error) { /* Invalid session is passed. Ignore. */ }
		}

		if (!authSessionInfo) {
			return {};
		}

		const record: Record<string, string> = {};

		// Settings Sync Entry
		record[`${product.urlProtocol}.loginAccount`] = JSON.stringify(authSessionInfo);

		// Auth extension Entry
		if (authSessionInfo.providerId !== 'github') {
			console.error(`Unexpected auth provider: ${authSessionInfo.providerId}. Expected 'github'.`);
			return record;
		}

		const authAccount = JSON.stringify({ extensionId: 'vscode.github-authentication', key: 'github.auth' });
		record[authAccount] = JSON.stringify(authSessionInfo.scopes.map(scopes => ({
			id: authSessionInfo.id,
			scopes,
			accessToken: authSessionInfo.accessToken
		})));

		return record;
	}

	async get(key: string): Promise<string | undefined> {
		const secrets = await this._secretsPromise;
		return secrets[key];
	}
	async set(key: string, value: string): Promise<void> {
		const secrets = await this._secretsPromise;
		secrets[key] = value;
		this._secretsPromise = Promise.resolve(secrets);
		this.save();
	}
	async delete(key: string): Promise<void> {
		const secrets = await this._secretsPromise;
		delete secrets[key];
		this._secretsPromise = Promise.resolve(secrets);
		this.save();
	}

	private async save(): Promise<void> {
		try {
			const encrypted = await this.crypto.seal(JSON.stringify(await this._secretsPromise));
			localStorage.setItem(this._storageKey, encrypted);
		} catch (err) {
			console.error(err);
		}
	}
}


class LocalStorageURLCallbackProvider extends Disposable implements IURLCallbackProvider {

	private static REQUEST_ID = 0;

	private static QUERY_KEYS: ('scheme' | 'authority' | 'path' | 'query' | 'fragment')[] = [
		'scheme',
		'authority',
		'path',
		'query',
		'fragment'
	];

	private readonly _onCallback = this._register(new Emitter<URI>());
	readonly onCallback = this._onCallback.event;

	private pendingCallbacks = new Set<number>();
	private lastTimeChecked = Date.now();
	private checkCallbacksTimeout: unknown | undefined = undefined;
	private onDidChangeLocalStorageDisposable: IDisposable | undefined;

	constructor(private readonly _callbackRoute: string) {
		super();
	}

	create(options: Partial<UriComponents> = {}): URI {
		const id = ++LocalStorageURLCallbackProvider.REQUEST_ID;
		const queryParams: string[] = [`vscode-reqid=${id}`];

		for (const key of LocalStorageURLCallbackProvider.QUERY_KEYS) {
			const value = options[key];

			if (value) {
				queryParams.push(`vscode-${key}=${encodeURIComponent(value)}`);
			}
		}

		// TODO@joao remove eventually
		// https://github.com/microsoft/vscode-dev/issues/62
		// https://github.com/microsoft/vscode/blob/159479eb5ae451a66b5dac3c12d564f32f454796/extensions/github-authentication/src/githubServer.ts#L50-L50
		if (!(options.authority === 'vscode.github-authentication' && options.path === '/dummy')) {
			const key = `vscode-web.url-callbacks[${id}]`;
			localStorage.removeItem(key);

			this.pendingCallbacks.add(id);
			this.startListening();
		}

		return URI.parse(mainWindow.location.href).with({ path: this._callbackRoute, query: queryParams.join('&') });
	}

	private startListening(): void {
		if (this.onDidChangeLocalStorageDisposable) {
			return;
		}

		const fn = () => this.onDidChangeLocalStorage();
		mainWindow.addEventListener('storage', fn);
		this.onDidChangeLocalStorageDisposable = { dispose: () => mainWindow.removeEventListener('storage', fn) };
	}

	private stopListening(): void {
		this.onDidChangeLocalStorageDisposable?.dispose();
		this.onDidChangeLocalStorageDisposable = undefined;
	}

	// this fires every time local storage changes, but we
	// don't want to check more often than once a second
	private async onDidChangeLocalStorage(): Promise<void> {
		const ellapsed = Date.now() - this.lastTimeChecked;

		if (ellapsed > 1000) {
			this.checkCallbacks();
		} else if (this.checkCallbacksTimeout === undefined) {
			this.checkCallbacksTimeout = setTimeout(() => {
				this.checkCallbacksTimeout = undefined;
				this.checkCallbacks();
			}, 1000 - ellapsed);
		}
	}

	private checkCallbacks(): void {
		let pendingCallbacks: Set<number> | undefined;

		for (const id of this.pendingCallbacks) {
			const key = `vscode-web.url-callbacks[${id}]`;
			const result = localStorage.getItem(key);

			if (result !== null) {
				try {
					this._onCallback.fire(URI.revive(JSON.parse(result)));
				} catch (error) {
					console.error(error);
				}

				pendingCallbacks = pendingCallbacks ?? new Set(this.pendingCallbacks);
				pendingCallbacks.delete(id);
				localStorage.removeItem(key);
			}
		}

		if (pendingCallbacks) {
			this.pendingCallbacks = pendingCallbacks;

			if (this.pendingCallbacks.size === 0) {
				this.stopListening();
			}
		}

		this.lastTimeChecked = Date.now();
	}
}

class WorkspaceProvider implements IWorkspaceProvider {

	private static QUERY_PARAM_EMPTY_WINDOW = 'ew';
	private static QUERY_PARAM_FOLDER = 'folder';
	private static QUERY_PARAM_WORKSPACE = 'workspace';

	private static QUERY_PARAM_PAYLOAD = 'payload';

	static create(config: IWorkbenchConstructionOptions & { folderUri?: UriComponents; workspaceUri?: UriComponents }) {
		let foundWorkspace = false;
		let workspace: IWorkspace;
		let payload = Object.create(null);

		// 安全地获取当前页面的查询参数
		const safeUrl = SecureUrlBuilder.getSafeCurrentUrl();
		if (!safeUrl) {
			console.error('[SECURITY] Unable to safely parse current URL');
			// 使用默认配置继续
			return new WorkspaceProvider(workspace, payload, config);
		}

		const query = safeUrl.searchParams;
		query.forEach((value, key) => {
			switch (key) {

				// Folder
				case WorkspaceProvider.QUERY_PARAM_FOLDER:
					if (config.remoteAuthority && value.startsWith(posix.sep)) {
						// when connected to a remote and having a value
						// that is a path (begins with a `/`), assume this
						// is a vscode-remote resource as simplified URL.
						workspace = { folderUri: URI.from({ scheme: Schemas.vscodeRemote, path: value, authority: config.remoteAuthority }) };
					} else {
						workspace = { folderUri: URI.parse(value) };
					}
					foundWorkspace = true;
					break;

				// Workspace
				case WorkspaceProvider.QUERY_PARAM_WORKSPACE:
					if (config.remoteAuthority && value.startsWith(posix.sep)) {
						// when connected to a remote and having a value
						// that is a path (begins with a `/`), assume this
						// is a vscode-remote resource as simplified URL.
						workspace = { workspaceUri: URI.from({ scheme: Schemas.vscodeRemote, path: value, authority: config.remoteAuthority }) };
					} else {
						workspace = { workspaceUri: URI.parse(value) };
					}
					foundWorkspace = true;
					break;

				// Empty
				case WorkspaceProvider.QUERY_PARAM_EMPTY_WINDOW:
					workspace = undefined;
					foundWorkspace = true;
					break;

				// Payload
				case WorkspaceProvider.QUERY_PARAM_PAYLOAD:
					try {
						payload = parse(value); // use marshalling#parse() to revive potential URIs
					} catch (error) {
						console.error(error); // possible invalid JSON
					}
					break;
			}
		});

		// If no workspace is provided through the URL, check for config
		// attribute from server
		if (!foundWorkspace) {
			if (config.folderUri) {
				workspace = { folderUri: URI.revive(config.folderUri) };
			} else if (config.workspaceUri) {
				workspace = { workspaceUri: URI.revive(config.workspaceUri) };
			}
		}

		return new WorkspaceProvider(workspace, payload, config);
	}

	readonly trusted = true;

	private constructor(
		readonly workspace: IWorkspace,
		readonly payload: object,
		private readonly config: IWorkbenchConstructionOptions
	) {
	}

	async open(workspace: IWorkspace, options?: { reuse?: boolean; payload?: object }): Promise<boolean> {
		if (options?.reuse && !options.payload && this.isSame(this.workspace, workspace)) {
			return true; // return early if workspace and environment is not changing and we are reusing window
		}

		const targetHref = this.createTargetUrl(workspace, options);
		if (targetHref) {
			// 额外的安全验证：确保生成的 URL 是安全的
			if (!this.isSecureTargetUrl(targetHref)) {
				console.error('[SECURITY] Generated target URL failed security validation:', targetHref);
				return false;
			}

			if (options?.reuse) {
				mainWindow.location.href = targetHref;
				return true;
			} else {
				let result;
				if (isStandalone()) {
					result = mainWindow.open(targetHref, '_blank', 'toolbar=no'); // ensures to open another 'standalone' window!
				} else {
					result = mainWindow.open(targetHref);
				}

				return !!result;
			}
		}
		return false;
	}

	/**
	 * 验证目标 URL 的安全性
	 */
	private isSecureTargetUrl(url: string): boolean {
		// 使用 SecureUrlBuilder 的验证方法
		return SecureUrlBuilder.validateUrl(url);
	}

	private createTargetUrl(workspace: IWorkspace, options?: { reuse?: boolean; payload?: object }): string | undefined {
		const queryParams: Record<string, string> = {};

		// Empty
		if (!workspace) {
			queryParams[WorkspaceProvider.QUERY_PARAM_EMPTY_WINDOW] = 'true';
		}
		// Folder
		else if (isFolderToOpen(workspace)) {
			const queryParamFolder = this.encodeWorkspacePath(workspace.folderUri);
			if (queryParamFolder) {
				queryParams[WorkspaceProvider.QUERY_PARAM_FOLDER] = queryParamFolder;
			} else {
				console.warn('[SECURITY] Failed to encode folder workspace path');
				return undefined;
			}
		}
		// Workspace
		else if (isWorkspaceToOpen(workspace)) {
			const queryParamWorkspace = this.encodeWorkspacePath(workspace.workspaceUri);
			if (queryParamWorkspace) {
				queryParams[WorkspaceProvider.QUERY_PARAM_WORKSPACE] = queryParamWorkspace;
			} else {
				console.warn('[SECURITY] Failed to encode workspace path');
				return undefined;
			}
		}

		// Append payload if any
		if (options?.payload) {
			try {
				const payloadStr = JSON.stringify(options.payload);
				// 验证 payload 大小和内容
				if (payloadStr.length <= 1000 && this.isValidPayload(options.payload)) {
					queryParams[WorkspaceProvider.QUERY_PARAM_PAYLOAD] = encodeURIComponent(payloadStr);
				} else {
					console.warn('[SECURITY] Invalid or oversized payload detected');
				}
			} catch (error) {
				console.warn('[SECURITY] Failed to serialize payload:', error);
			}
		}

		// 使用安全的 URL 构建器
		return SecureUrlBuilder.buildTargetUrl(queryParams) || undefined;
	}

	/**
	 * 验证 payload 对象的安全性
	 */
	private isValidPayload(payload: object): boolean {
		try {
			// 检查 payload 是否为简单对象
			if (!payload || typeof payload !== 'object' || Array.isArray(payload)) {
				return false;
			}

			// 检查属性数量
			const keys = Object.keys(payload);
			if (keys.length > 10) {
				return false;
			}

			// 检查每个属性
			for (const key of keys) {
				if (typeof key !== 'string' || key.length > 50) {
					return false;
				}

				const value = (payload as any)[key];
				if (typeof value !== 'string' && typeof value !== 'number' && typeof value !== 'boolean') {
					return false;
				}

				if (typeof value === 'string' && value.length > 200) {
					return false;
				}
			}

			return true;
		} catch {
			return false;
		}
	}

	private encodeWorkspacePath(uri: URI): string | null {
		try {
			// 验证 URI 的安全性
			if (!uri || !this.isValidWorkspaceUri(uri)) {
				console.warn('[SECURITY] Invalid workspace URI detected');
				return null;
			}

			if (this.config.remoteAuthority && uri.scheme === Schemas.vscodeRemote) {
				// when connected to a remote and having a folder
				// or workspace for that remote, only use the path
				// as query value to form shorter, nicer URLs.
				// however, we still need to `encodeURIComponent`
				// to ensure to preserve special characters, such
				// as `+` in the path.

				const path = this.sanitizePath(uri.path);
				if (!path) {
					return null;
				}

				return encodeURIComponent(`${posix.sep}${ltrim(path, posix.sep)}`).replaceAll('%2F', '/');
			}

			// 对于非远程 URI，使用完整的 URI 字符串
			const uriString = uri.toString(true);
			if (uriString.length > 2000) {
				console.warn('[SECURITY] URI too long');
				return null;
			}

			return encodeURIComponent(uriString);
		} catch (error) {
			console.error('[SECURITY] Error encoding workspace path:', error);
			return null;
		}
	}

	/**
	 * 验证工作区 URI 的安全性
	 */
	private isValidWorkspaceUri(uri: URI): boolean {
		// 检查 scheme 是否在允许列表中
		const allowedSchemes = [
			Schemas.file,
			Schemas.vscodeRemote,
			Schemas.vscodeUserData,
			Schemas.untitled,
			'http',
			'https'
		];

		if (!allowedSchemes.includes(uri.scheme)) {
			return false;
		}

		// 检查路径长度
		if (uri.path && uri.path.length > 1000) {
			return false;
		}

		// 检查是否包含危险字符
		const uriString = uri.toString();
		if (uriString.includes('<') || uriString.includes('>') || uriString.includes('"')) {
			return false;
		}

		return true;
	}

	/**
	 * 清理和验证路径
	 */
	private sanitizePath(path: string): string | null {
		if (!path || typeof path !== 'string') {
			return null;
		}

		// 移除危险字符
		const sanitized = path
			.replace(/[<>"'&]/g, '') // 移除 XSS 风险字符
			.replace(/[\x00-\x1f\x7f-\x9f]/g, '') // 移除控制字符
			.substring(0, 1000); // 限制长度

		// 检查路径遍历攻击
		if (sanitized.includes('..')) {
			console.warn('[SECURITY] Path traversal attempt detected');
			return null;
		}

		return sanitized;
	}

	private isSame(workspaceA: IWorkspace, workspaceB: IWorkspace): boolean {
		if (!workspaceA || !workspaceB) {
			return workspaceA === workspaceB; // both empty
		}

		if (isFolderToOpen(workspaceA) && isFolderToOpen(workspaceB)) {
			return isEqual(workspaceA.folderUri, workspaceB.folderUri); // same workspace
		}

		if (isWorkspaceToOpen(workspaceA) && isWorkspaceToOpen(workspaceB)) {
			return isEqual(workspaceA.workspaceUri, workspaceB.workspaceUri); // same workspace
		}

		return false;
	}

	hasRemote(): boolean {
		if (this.workspace) {
			if (isFolderToOpen(this.workspace)) {
				return this.workspace.folderUri.scheme === Schemas.vscodeRemote;
			}

			if (isWorkspaceToOpen(this.workspace)) {
				return this.workspace.workspaceUri.scheme === Schemas.vscodeRemote;
			}
		}

		return true;
	}
}

function readCookie(name: string): string | undefined {
	const cookies = document.cookie.split('; ');
	for (const cookie of cookies) {
		if (cookie.startsWith(name + '=')) {
			return cookie.substring(name.length + 1);
		}
	}

	return undefined;
}

(function () {

	// Find config by checking for DOM
	const configElement = mainWindow.document.getElementById('vscode-workbench-web-configuration');
	const configElementAttribute = configElement ? configElement.getAttribute('data-settings') : undefined;
	if (!configElement || !configElementAttribute) {
		throw new Error('Missing web configuration element');
	}
	const config: IWorkbenchConstructionOptions & { folderUri?: UriComponents; workspaceUri?: UriComponents; callbackRoute: string } = JSON.parse(configElementAttribute);
	const secretStorageKeyPath = readCookie('vscode-secret-key-path');
	const secretStorageCrypto = secretStorageKeyPath && ServerKeyedAESCrypto.supported()
		? new ServerKeyedAESCrypto(secretStorageKeyPath) : new TransparentCrypto();

	// Create workbench
	create(mainWindow.document.body, {
		...config,
		windowIndicator: config.windowIndicator ?? { label: '$(remote)', tooltip: `${product.nameShort} Web` },
		settingsSyncOptions: config.settingsSyncOptions ? { enabled: config.settingsSyncOptions.enabled, } : undefined,
		workspaceProvider: WorkspaceProvider.create(config),
		urlCallbackProvider: new LocalStorageURLCallbackProvider(config.callbackRoute),
		secretStorageProvider: config.remoteAuthority && !secretStorageKeyPath
			? undefined /* with a remote without embedder-preferred storage, store on the remote */
			: new LocalStorageSecretStorageProvider(secretStorageCrypto),
	});
})();
