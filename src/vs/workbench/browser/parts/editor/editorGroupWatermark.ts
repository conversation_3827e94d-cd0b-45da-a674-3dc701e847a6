/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/
// 全局事件：用于外部触发 EditorGroupWatermark 刷新
import { Emitter } from '../../../../base/common/event.js';
import { localize } from '../../../../nls.js';
import { Disposable, DisposableStore, IDisposable } from '../../../../base/common/lifecycle.js';
import { IWorkspaceContextService, WorkbenchState } from '../../../../platform/workspace/common/workspace.js';
import { append, clearNode, $, h } from '../../../../base/browser/dom.js';
import { editorForeground, registerColor, transparent } from '../../../../platform/theme/common/colorRegistry.js';
import { isRecentFolder, IWorkspacesService } from '../../../../platform/workspaces/common/workspaces.js';

import { ICommandService } from '../../../../platform/commands/common/commands.js';
import { OpenFileFolderAction, OpenFolderAction } from '../../actions/workspaceActions.js';
import { isMacintosh, isNative } from '../../../../base/common/platform.js';
import { IWindowOpenable } from '../../../../platform/window/common/window.js';
import { ILabelService, Verbosity } from '../../../../platform/label/common/label.js';
import { splitRecentLabel } from '../../../../base/common/labels.js';
import { IHostService } from '../../../services/host/browser/host.js';

import { Codicon } from '../../../../base/common/codicons.js';
import { ILoginService } from '../../../../platform/login/common/login.js';
import { NEW_PROJECT_COMMAND_ID } from '../../../contrib/JoyCoder/browser/commands/newProjectCommands.js';
import { CommandsRegistry } from '../../../../platform/commands/common/commands.js';

import './media/iconfont.css';


registerColor('editorWatermark.foreground', { dark: transparent(editorForeground, 0.6), light: transparent(editorForeground, 0.68), hcDark: editorForeground, hcLight: editorForeground }, localize('editorLineHighlight', 'Foreground color for the labels in the editor watermark.'));

export const onForceRenderWatermark = new Emitter<void>();

export const EDITOR_WATER_MARK_RENDER_ID = 'editorGroupWatermark.forceRender'

// 全局标志，确保只有一个实例在渲染按钮
let isGloballyRendering = false;


export class EditorGroupWatermark extends Disposable {
	private readonly shortcuts: HTMLElement;
	private isDark: boolean = true;
	private readonly transientDisposables = this._register(new DisposableStore());
	// private enabled: boolean = false;

	private currentDisposables = new Set<IDisposable>();
	private isRendering: boolean = false;
	private renderTimeoutId: NodeJS.Timeout | null = null;


	constructor(
		container: HTMLElement,
		@IWorkspaceContextService private readonly contextService: IWorkspaceContextService,
		@IWorkspacesService private readonly workspacesService: IWorkspacesService,
		@ICommandService private readonly commandService: ICommandService,
		@IHostService private readonly hostService: IHostService,
		@ILabelService private readonly labelService: ILabelService,
		@ILoginService private readonly loginService: ILoginService,
	) {
		super();

		const elements = h('.editor-group-watermark.width-auto', [
			h('.editor-universal-box@box'),
			h('.shortcuts@shortcuts'),
		]);

		append(container, elements.root);
		this.shortcuts = elements.shortcuts; // shortcuts div is modified on render()

		this.registerListeners();
		this.render();
	}

	private registerListeners(): void {
		this._register(this.loginService.onDidChangeLoginStatus(() => {
			this.render();
		}));
	}

	public refreshUI(): void {
		this.render();
	}

	// 设置按钮文本的公共样式
	private setButtonTextStyle(textSpan: HTMLElement, text: string): void {
		textSpan.textContent = text;
		textSpan.style.whiteSpace = 'normal';
		textSpan.style.overflowWrap = 'break-word';
		textSpan.style.lineHeight = '1.2';
		textSpan.style.color = 'var(--vscode-editor-foreground)';
		textSpan.style.cursor = 'pointer';
	}

	// 创建按钮的公共样式和结构
	private createWatermarkButton(iconId: string, text: string, onClick: () => void, marginLeft: string = '0px', title?: string, iconClassName?: string): HTMLElement {
		const iconSpan = $('span');
		iconSpan.className = iconClassName || `codicon codicon-${iconId} icon-widget`;
		iconSpan.style.margin = '0px 8px 0px 0px';
		iconSpan.style.fontSize = '16px';
		iconSpan.style.cursor = 'pointer';
		iconSpan.style.setProperty('cursor', 'pointer', 'important');

		const textSpan = $('span');
		this.setButtonTextStyle(textSpan, text);

		const button = $('button');
		button.classList.add('void-watermark-button');
		button.style.display = 'flex';
		button.style.alignItems = 'center';
		button.style.justifyContent = 'flex-start';
		button.style.width = '180px';
		button.style.height = '56px';
		button.style.margin = `0px 0px 0px ${marginLeft}`;
		button.style.padding = '8px 12px';
		button.style.background = 'rgba(200,200,200,.1)';
		button.style.borderRadius = '8px';
		button.style.color = this.isDark ? "#d7d7d7" : "#333";
		button.style.border = 'none';
		button.style.cursor = 'pointer';

		button.title = title || text;

		button.appendChild(iconSpan);
		button.appendChild(textSpan);
		button.onclick = onClick;

		return button;
	}

	// 创建New Project按钮的函数
	private async createNewProjectButton(): Promise<HTMLElement> {
		try {
			const isLoggedIn = await this.loginService.isLoggedIn();

			const iconId = isLoggedIn ? Codicon.folderOpened.id : Codicon.newFile.id;
			// const iconClassName = isLoggedIn ? 'iconfont icon-xinjianwenjianjia codicon codicon-joycode-xinjianwenjianjia' : '';
			const iconClassName = isLoggedIn ? 'codicon codicon-joycode-xinjianwenjianjia' : '';
			const newProjectText = localize('editorWatermark.newProject', "New Project");
			const newTextFileText = localize('editorWatermark.newTextFile', "New File");
			const text = isLoggedIn ? newProjectText : newTextFileText;
			const onClick = () => {
				if (isLoggedIn) {
					this.commandService.executeCommand(NEW_PROJECT_COMMAND_ID);
				} else {
					this.commandService.executeCommand('workbench.action.files.newUntitledFile');
				}
			};

			const newProjectTextTitle = localize('editorWatermark.newProjectTitle', "Create a new project");
			const newTextFileTitle = localize('editorWatermark.newTextFileTitle', "Create a new untitled text file");
			const title = isLoggedIn ? newProjectTextTitle : newTextFileTitle;

			const button = this.createWatermarkButton(iconId, text, onClick, '0px', title, iconClassName);

			return button;
		} catch (error) {
			// 出错时返回默认的New Text File按钮
			return this.createWatermarkButton(
				Codicon.newFile.id,
				localize('editorWatermark.newTextFile', "New File"),
				() => {
					this.commandService.executeCommand('workbench.action.files.newUntitledFile');
				},
				'0px',
				localize('editorWatermark.newTextFileTitle', "Create a new untitled text file")
			);
		}
	}

	// 创建Open按钮的函数
	private createOpenButton(): HTMLElement {
		return this.createWatermarkButton(
			Codicon.folderOpened.id,
			localize('editorWatermark.openFolder', "Open..."),
			() => {
				this.commandService.executeCommand(isMacintosh && isNative ? OpenFileFolderAction.ID : OpenFolderAction.ID);
			},
			'12px',
			localize('editorWatermark.openFolderTitle', "Open a file or folder to start working")
		);
	}

	// 创建Clone Repository按钮的函数
	private createCloneRepositoryButton(): HTMLElement {
		return this.createWatermarkButton(
			Codicon.gitBranch.id,
			localize('editorWatermark.cloneRepository', "Clone Git Repository..."),
			() => {
				this.commandService.executeCommand('git.clone');
			},
			'0px',
			localize('editorWatermark.cloneRepositoryTitle', "Clone a remote repository to a local folder")
		);
	}

	// 创建Remote Development按钮的函数
	private createRemoteDevelopmentButton(): HTMLElement {
		return this.createWatermarkButton(
			Codicon.remoteExplorer.id,
			localize('editorWatermark.remoteDevelopment', "Connect to..."),
			() => {
				this.commandService.executeCommand('workbench.view.remote');
			},
			'12px',
			localize('editorWatermark.remoteDevelopmentTitle', "Connect to a remote development workspace")
		);
	}

	// 创建Recents区域的函数
	private async createRecentsSection(container: HTMLElement): Promise<void> {
		try {
			// 获取最近打开的项目
			const recentlyOpened = await this.workspacesService.getRecentlyOpened()
				.catch(() => ({ files: [], workspaces: [] })).then(w => w.workspaces);

			// const projects = await this.newProjectService.getProjectList();
			// recentlyOpened = this.getRecetList(recentlyOpened, projects);
			if (recentlyOpened.length === 0) {
				return;
			}

			// 渲染前先移除所有"Recent Open"区块和最近项目列表，确保只保留一份
			const allRecentElements = container.querySelectorAll('div[data-recent-open="true"], div[data-recent-list="true"], li');
			allRecentElements.forEach(el => el.remove());

			// 确保没有重复的Recent Open标题
			const existingRecentTitles = container.querySelectorAll('div');
			existingRecentTitles.forEach(div => {
				if (div.textContent === localize('editorWatermark.recentOpen', "Recent Open")) {
					div.remove();
				}
			});

			// 创建Recent Open标题
			const titleSpan = $('div');
			titleSpan.textContent = localize('editorWatermark.recentOpen', "Recent Open");
			titleSpan.setAttribute('data-recent-open', 'true');
			titleSpan.style.height = "50px";
			titleSpan.style.lineHeight = "50px";
			titleSpan.style.fontSize = "14px";
			titleSpan.style.fontWeight = '500';
			container.append(titleSpan);

			// 创建最近项目列表容器
			const recentListDiv = $('div');
			recentListDiv.setAttribute('data-recent-list', 'true');
			container.append(recentListDiv);

			// 渲染最近项目列表
			recentlyOpened.slice(0, 5).forEach(w => {
				let fullPath: string;
				let windowOpenable: IWindowOpenable;
				if (isRecentFolder(w)) {
					windowOpenable = { folderUri: w.folderUri };
					fullPath = this.labelService.getWorkspaceLabel(w.folderUri, { verbose: Verbosity.LONG });
				} else {
					// 跳过无效项
					return;
				}

				const { name, parentPath } = splitRecentLabel(fullPath);

				const li = $('li');
				li.style.listStyle = "none";
				li.style.display = "flex";
				li.style.alignItems = 'center';
				li.style.justifyContent = 'flex-start';

				const li_icon = $('span', {
					class: `codicon codicon-${fullPath.includes('ssh:') ? Codicon.remoteExplorer.id : Codicon.folderOpened.id} icon-widget`,
					style: `margin: 0px 12px 0px 0px`,
				});
				li.appendChild(li_icon);

				const link = $('span');
				link.classList.add('void-link')
				link.innerText = name;
				link.title = fullPath;
				link.setAttribute('aria-label', localize('welcomePage.openFolderWithPath', "Open folder {0} with path {1}", name, parentPath));
				link.addEventListener('click', e => {
					this.hostService.openWindow([windowOpenable], {
						forceNewWindow: e.ctrlKey || e.metaKey,
						remoteAuthority: w.remoteAuthority || null
					});
					e.preventDefault();
					e.stopPropagation();
				});
				li.appendChild(link);

				const pathSpan = $('span');
				pathSpan.style.paddingLeft = '4px';
				pathSpan.classList.add('path');
				pathSpan.classList.add('detail');
				pathSpan.innerText = parentPath;
				pathSpan.title = fullPath;
				li.appendChild(pathSpan);

				recentListDiv.appendChild(li);
			});
		} catch (error) {
			console.error('Error creating recents section:', error);
		}
	}


	private render(): void {
		// 防抖：如果已经有渲染计划，先取消它
		if (this.renderTimeoutId) {
			clearTimeout(this.renderTimeoutId);
		}

		// 防抖：延迟50ms执行渲染，避免快速连续触发
		this.renderTimeoutId = setTimeout(() => {
			this.doRender();
		}, 50);
	}

	private clearAllWatermarkElements(): void {
		// 1. 清理当前实例的shortcuts容器
		if (this.shortcuts) {
			clearNode(this.shortcuts);
		}

		// 2. 全局清理：移除所有watermark相关元素
		const allWatermarkButtons = document.querySelectorAll('.void-watermark-button');
		const allWatermarkContainers = document.querySelectorAll('.watermark-box');
		const allRecentElements = document.querySelectorAll('div[data-recent-open="true"], div[data-recent-list="true"]');
		const allSettingsButtons = document.querySelectorAll('.void-settings-watermark-button');

		// 移除所有watermark按钮
		allWatermarkButtons.forEach(button => {
			if (button.parentNode) {
				button.parentNode.removeChild(button);
			}
		});

		// 移除多余的watermark容器（保留当前实例的）
		allWatermarkContainers.forEach(container => {
			if (container.parentNode && !this.shortcuts.contains(container)) {
				container.parentNode.removeChild(container);
			}
		});

		// 移除所有最近项目相关元素
		allRecentElements.forEach(element => {
			if (element.parentNode) {
				element.parentNode.removeChild(element);
			}
		});

		// 移除所有设置按钮
		allSettingsButtons.forEach(button => {
			if (button.parentNode) {
				button.parentNode.removeChild(button);
			}
		});

		// 3. 清理所有现有的disposables
		this.currentDisposables.forEach(disposable => disposable.dispose());
		this.currentDisposables.clear();

		// 4. 重置全局渲染标志
		isGloballyRendering = false;
	}

	private doRender(): void {
		// 防止重复渲染：如果正在渲染中，直接返回
		if (this.isRendering) {
			return;
		}

		this.isRendering = true;

		// 渲染前先彻底清理所有DOM元素
		this.clearAllWatermarkElements();

		try {

			const box = append(this.shortcuts, $('.watermark-box'));
			box.style.borderSpacing = '0px 0px';
			const boxBelow = append(this.shortcuts, $(''))
			boxBelow.style.display = 'flex'
			boxBelow.style.flex = 'row'
			boxBelow.style.justifyContent = 'center'

			const update = async () => {
				try {
					// 确保容器完全清空，包括所有子元素
					clearNode(box);
					clearNode(boxBelow);


					// JoyCode - if the workbench is empty, show open
					if (this.contextService.getWorkbenchState() === WorkbenchState.EMPTY && !isGloballyRendering) {
						// 设置全局渲染标志
						isGloballyRendering = true;

						// 创建2x2网格容器
						const gridContainer = h('div').root;
						gridContainer.style.display = 'grid';
						gridContainer.style.gridTemplateColumns = '180px 180px';
						gridContainer.style.gridTemplateRows = '56px 56px';
						gridContainer.style.gap = '12px';
						gridContainer.style.width = '372px';

						// 创建并添加按钮
						const newProjectButton = await this.createNewProjectButton();
						newProjectButton.style.margin = '0px'; // 重置margin，使用grid gap
						gridContainer.appendChild(newProjectButton);

						const openButton = this.createOpenButton();
						openButton.style.margin = '0px'; // 重置margin，使用grid gap
						gridContainer.appendChild(openButton);

						const cloneButton = this.createCloneRepositoryButton();
						cloneButton.style.margin = '0px'; // 重置margin，使用grid gap
						gridContainer.appendChild(cloneButton);

						const remoteButton = this.createRemoteDevelopmentButton();
						remoteButton.style.margin = '0px'; // 重置margin，使用grid gap
						gridContainer.appendChild(remoteButton);

						box.appendChild(gridContainer);

						// 添加Recents区域
						await this.createRecentsSection(box);
					}
					// 非空工作区时不显示任何内容
				} catch (error) {
					console.error('Error during watermark update:', error);
				} finally {
					// 确保渲染状态被重置
					this.isRendering = false;
					// 重置全局渲染标志
					isGloballyRendering = false;
				}
			};

			update();
		} catch (error) {
			console.error('Error during watermark render:', error);
			this.isRendering = false;
			// 重置全局渲染标志
			isGloballyRendering = false;
		}
	}

	private clear(): void {
		// 使用统一的清理方法
		this.clearAllWatermarkElements();

		this.transientDisposables.clear();

		// 清理渲染定时器
		if (this.renderTimeoutId) {
			clearTimeout(this.renderTimeoutId);
			this.renderTimeoutId = null;
		}
	}

	override dispose(): void {
		super.dispose();
		this.clear();
		this.currentDisposables.forEach(label => label.dispose());
		this.isRendering = false;
	}
}


CommandsRegistry.registerCommand(EDITOR_WATER_MARK_RENDER_ID, () => {
	onForceRenderWatermark.fire();
});
