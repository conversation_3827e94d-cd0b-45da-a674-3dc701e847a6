/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

// Login action constants
export const LOGIN_ACTION_ID = 'workbench.action.joycoderLogin';
export const IS_LOGGED_IN_COMMAND_ID = 'workbench.action.joycoderIsLoggedIn';
export const GET_LOGIN_INFO_COMMAND_ID = 'workbench.action.joycoderGetLoginInfo';
export const LOGOUT_ACTION_ID = 'workbench.action.joycoderLogout';

// Context keys
export const NOT_LOGGED_IN_KEY = 'joycoderNotLoggedIn';

// Storage keys
export const CLOSE_REMOTE_PTKEY_STORAGE_KEY = 'close-remote-ptkey';
