/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

import { Categories } from '../../../../../platform/action/common/actionCommonCategories.js';
import { ServicesAccessor, IInstantiationService } from '../../../../../platform/instantiation/common/instantiation.js';
import { registerAction2, Action2 } from '../../../../../platform/actions/common/actions.js';
import { DoubleShiftDialog } from '../doubleShiftDialog.js';
import { JoyCoderLogger } from '../joycoderLogger.js';

// 注册打开双击Shift弹窗的命令
registerAction2(class OpenDoubleShiftDialogAction extends Action2 {
    constructor() {
        super({
            id: 'joycoder.openDoubleShiftDialog',
            title: { value: '打开双击Shift弹窗', original: 'Open Double Shift Dialog' },
            category: Categories.View,
            f1: true
        });
    }

    async run(accessor: ServicesAccessor): Promise<void> {
        try {
            JoyCoderLogger.debug('OpenDoubleShiftDialogAction: 开始创建弹窗');

            // 创建弹窗实例
            const instantiationService = accessor.get(IInstantiationService);
            JoyCoderLogger.debug('OpenDoubleShiftDialogAction: 成功获取instantiationService');

            JoyCoderLogger.debug('OpenDoubleShiftDialogAction: 开始创建DoubleShiftDialog实例');
            const dialog = instantiationService.createInstance(DoubleShiftDialog);
            JoyCoderLogger.debug('OpenDoubleShiftDialogAction: DoubleShiftDialog实例创建成功');

            dialog.show();
            JoyCoderLogger.debug('OpenDoubleShiftDialogAction: 已调用dialog.show()方法');

            JoyCoderLogger.debug('OpenDoubleShiftDialogAction: 弹窗已显示');
        } catch (error) {
            JoyCoderLogger.error('OpenDoubleShiftDialogAction: 创建或显示弹窗失败', error);
        }
    }
});
