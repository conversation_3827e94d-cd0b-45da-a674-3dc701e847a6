/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

import { Action2, registerAction2 } from '../../../../../platform/actions/common/actions.js';
import { ServicesAccessor } from '../../../../../platform/instantiation/common/instantiation.js';
import { localize } from '../../../../../nls.js';
import { Categories } from '../../../../../platform/action/common/actionCommonCategories.js';
import { INotificationService } from '../../../../../platform/notification/common/notification.js';
import { ContextKeyExpr } from '../../../../../platform/contextkey/common/contextkey.js';
import { NOT_LOGGED_IN_KEY } from '../constants/loginConstants.js';
import { INewProjectService } from '../../common/newProject.js';
import { IAuxiliaryWindowService, AuxiliaryWindowMode } from '../../../../services/auxiliaryWindow/browser/auxiliaryWindowService.js';
import { IFileService } from '../../../../../platform/files/common/files.js';
import { URI } from '../../../../../base/common/uri.js';
import { IWebviewService, WebviewContentPurpose } from '../../../webview/browser/webview.js';
import { FileAccess } from '../../../../../base/common/network.js';
import { DisposableStore } from '../../../../../base/common/lifecycle.js';
import { IHostService } from '../../../../services/host/browser/host.js';
import { process } from '../../../../../base/parts/sandbox/electron-sandbox/globals.js';
import { CLOUDDEV_REFRESH } from './newProjectCommands.js';
import { ICommandService } from '../../../../../platform/commands/common/commands.js';

// 命令ID
export const PROJECT_SETTINGS_COMMAND_ID = 'workbench.action.projectSettings';

// 项目设置页面的国际化文本
interface IProjectSettingsLocalizations {
    // 页面标题
    projectSettings: string;

    // 表单字段标签
    serverInfo: string;
    projectName: string;
    devMode: string;
    templates: string;
    database: string;
    domain: string;
    exportPorts: string;
    description: string;

    // 开发模式选项
    remote: string;
    local: string;

    // 数据库选项
    noDatabase: string;
    selectExisting: string;
    custom: string;

    // 数据库配置字段
    type: string;
    url: string;
    userName: string;
    password: string;

    // 按钮文本
    cancel: string;
    save: string;
    testConnection: string;

    // 占位符文本
    hostPortDatabase: string;
    userNamePlaceholder: string;
    nameHostPort: string;

    // 状态和消息文本
    savingSettings: string;
    noDataAvailable: string;
    succeeded: string;
    failed: string;

    // 通知消息
    projectSettingsWindowExists: string;
    projectSettingsSaved: string;
    projectSettingsSaveError: string;
    htmlReadError: string;
    projectSettingsError: string;
}

// 项目设置数据接口
export interface IProjectSettingsData {
    id: number;
    serverInfo?: string;
    name?: string;
    devMode?: number;
    projectTemplateName?: string;
    databaseInfo?: string;
    databaseUsername?: string;
    databasePassword?: string;
    databaseHost?: string;
    databasePort?: string;
    description?: string;
}

// 项目设置命令类
export class ProjectSettingsAction extends Action2 {
    private static currentProjectSettingsWindow: { window: any, focus: () => void } | null = null;
    private static projectSettingsLocalizations: IProjectSettingsLocalizations;

    /**
     * 初始化国际化文本
     */
    private static initLanguageLocalizations() {
        ProjectSettingsAction.projectSettingsLocalizations = {
            // 页面标题
            projectSettings: localize('joycode.projectSettings.projectSettings', "Project Settings"),

            // 表单字段标签
            serverInfo: localize('joycode.projectSettings.serverInfo', "Server Info:"),
            projectName: localize('joycode.projectSettings.projectName', "Project Name:"),
            devMode: localize('joycode.projectSettings.devMode', "Dev Mode:"),
            templates: localize('joycode.projectSettings.templates', "Templates:"),
            database: localize('joycode.projectSettings.database', "Database:"),
            domain: localize('joycode.projectSettings.domain', "Domain:"),
            exportPorts: localize('joycode.projectSettings.exportPorts', "Export Ports:"),
            description: localize('joycode.projectSettings.description', "Description:"),

            // 开发模式选项
            remote: localize('joycode.projectSettings.remote', "Remote"),
            local: localize('joycode.projectSettings.local', "Local"),

            // 数据库选项
            noDatabase: localize('joycode.projectSettings.noDatabase', "No Database"),
            selectExisting: localize('joycode.projectSettings.selectExisting', "Select Existing"),
            custom: localize('joycode.projectSettings.custom', "Custom"),

            // 数据库配置字段
            type: localize('joycode.projectSettings.type', "Type:"),
            url: localize('joycode.projectSettings.url', "URL:"),
            userName: localize('joycode.projectSettings.userName', "User Name:"),
            password: localize('joycode.projectSettings.password', "Password:"),

            // 按钮文本
            cancel: localize('joycode.projectSettings.cancel', "Cancel"),
            save: localize('joycode.projectSettings.save', "Save"),
            testConnection: localize('joycode.projectSettings.testConnection', "Test Connection"),

            // 占位符文本
            hostPortDatabase: localize('joycode.projectSettings.hostPortDatabase', "host:port/database"),
            userNamePlaceholder: localize('joycode.projectSettings.userNamePlaceholder', "user name"),
            nameHostPort: localize('joycode.projectSettings.nameHostPort', "name host:port"),

            // 状态和消息文本
            savingSettings: localize('joycode.projectSettings.savingSettings', "Saving settings..."),
            noDataAvailable: localize('joycode.projectSettings.noDataAvailable', "No data available."),
            succeeded: localize('joycode.projectSettings.succeeded', "Succeeded"),
            failed: localize('joycode.projectSettings.failed', "Failed"),

            // 通知消息
            projectSettingsWindowExists: localize('joycode.projectSettings.projectSettingsWindowExists', "Project settings window is already open"),
            projectSettingsSaved: localize('joycode.projectSettings.projectSettingsSaved', "Project settings saved successfully"),
            projectSettingsSaveError: localize('joycode.projectSettings.projectSettingsSaveError', "Failed to save project settings: {0}"),
            htmlReadError: localize('joycode.projectSettings.htmlReadError', "Failed to read HTML file: {0}"),
            projectSettingsError: localize('joycode.projectSettings.projectSettingsError', "Failed to create project settings window: {0}")
        };
    }

    /**
     * 将国际化文本注入到HTML中
     */
    private static injectLocalizationsToHtml(htmlString: string): string {
        // 如果国际化文本还没有初始化，使用默认值
        const localizations = ProjectSettingsAction.projectSettingsLocalizations || {
            projectSettings: "Project Settings",
            serverInfo: "Server Info:",
            projectName: "Project Name:",
            devMode: "Dev Mode:",
            templates: "Templates:",
            database: "Database:",
            domain: "Domain:",
            exportPorts: "Export Ports:",
            description: "Description:",
            remote: "Remote",
            local: "Local",
            noDatabase: "No Database",
            selectExisting: "Select Existing",
            custom: "Custom",
            type: "Type:",
            url: "URL:",
            userName: "User Name:",
            password: "Password:",
            cancel: "Cancel",
            save: "Save",
            testConnection: "Test Connection",
            hostPortDatabase: "host:port/database",
            userNamePlaceholder: "user name",
            nameHostPort: "name host:port",
            savingSettings: "Saving settings...",
            noDataAvailable: "No data available.",
            succeeded: "Succeeded",
            failed: "Failed",
            projectSettingsWindowExists: "Project settings window is already open",
            projectSettingsSaved: "Project settings saved successfully",
            projectSettingsSaveError: "Failed to save project settings: {0}",
            htmlReadError: "Failed to read HTML file: {0}",
            projectSettingsError: "Failed to create project settings window: {0}"
        };

        // 创建一个脚本标签，将国际化文本作为全局变量注入
        const localizationScript = `
            <script>
                window.projectSettingsLocalizations = ${JSON.stringify(localizations)};
            </script>
        `;

        // 在 </head> 标签前插入脚本
        return htmlString.replace('</head>', `${localizationScript}</head>`);
    }

    constructor() {
        super({
            id: PROJECT_SETTINGS_COMMAND_ID,
            title: {
                value: localize('joycode.projectSettings.projectSettings', "Project Settings"),
                original: 'Project Settings'
            },
            category: Categories.View,
            f1: true,
            precondition: ContextKeyExpr.equals(NOT_LOGGED_IN_KEY, false)
        });
    }

    async run(accessor: ServicesAccessor, projectData: IProjectSettingsData): Promise<void> {
        // 初始化国际化文本
        ProjectSettingsAction.initLanguageLocalizations();

        const notificationService = accessor.get(INotificationService);
        const newProjectService = accessor.get(INewProjectService);
        const auxiliaryWindowService = accessor.get(IAuxiliaryWindowService);
        const fileService = accessor.get(IFileService);
        const webviewService = accessor.get(IWebviewService);
        const hostService = accessor.get(IHostService);
        const commandService = accessor.get(ICommandService);

        if (ProjectSettingsAction.currentProjectSettingsWindow) {
            try {
                if (ProjectSettingsAction.currentProjectSettingsWindow.window &&
                    !ProjectSettingsAction.currentProjectSettingsWindow.window.closed) {
                    ProjectSettingsAction.currentProjectSettingsWindow.focus();
                    const localizations = ProjectSettingsAction.projectSettingsLocalizations;
                    const message = localizations?.projectSettingsWindowExists || "Project settings window is already open";
                    notificationService.info(message);
                    return;
                } else {
                    ProjectSettingsAction.currentProjectSettingsWindow = null;
                }
            } catch (e) {
                ProjectSettingsAction.currentProjectSettingsWindow = null;
            }
        }

        try {
            const width = 750;
            const height = 650;
            const mainWin = window;
            const isFullScreen = document.fullscreenElement !== null ||
                (mainWin.screen.width === mainWin.outerWidth &&
                    mainWin.screen.height === mainWin.outerHeight);

            let x = mainWin.screenX + Math.floor((mainWin.outerWidth - width) / 2);
            let y = isFullScreen ? mainWin.screenY + 150 : mainWin.screenY + Math.floor((mainWin.outerHeight - height) / 2);

            const auxiliaryWindow = await auxiliaryWindowService.open({
                mode: AuxiliaryWindowMode.Normal,
                bounds: { width, height, x, y },
                nativeTitlebar: true,
                disableFullscreen: true
            });

            ProjectSettingsAction.currentProjectSettingsWindow = {
                window: auxiliaryWindow.window,
                focus: () => {
                    if (auxiliaryWindow.window) {
                        auxiliaryWindow.window.focus();
                        hostService.focus(auxiliaryWindow.window);
                    }
                }
            };

            const windowLocalizations = ProjectSettingsAction.projectSettingsLocalizations;
            const windowTitle = windowLocalizations?.projectSettings || "Project Settings";
            auxiliaryWindow.window.document.title = windowTitle;

            const container = auxiliaryWindow.container;

            const webview = webviewService.createWebviewElement({
                providedViewType: 'projectSettings',
                title: windowTitle,
                options: {
                    purpose: WebviewContentPurpose.WebviewView,
                    enableFindWidget: false
                },
                contentOptions: {
                    allowScripts: true,
                    localResourceRoots: [URI.file(process.cwd())]
                },
                extension: undefined
            });

            const htmlFileUri = FileAccess.asFileUri('vs/workbench/contrib/JoyCoder/browser/project/media/projectSettings.html');

            try {
                const htmlContent = await fileService.readFile(htmlFileUri);
                const htmlString = htmlContent.value.toString();

                // 处理HTML内容，注入国际化文本
                const processedHtml = ProjectSettingsAction.injectLocalizationsToHtml(htmlString);

                webview.setHtml(processedHtml);
                webview.mountTo(container, auxiliaryWindow.window);

                const disposables = new DisposableStore();

                let databases = [];
                try {
                    databases = await newProjectService.getProjectDatabaseList();
                    webview.postMessage({
                        type: 'setDatabases',
                        databases,
                    });
                } catch (error) {
                    console.log('获取数据库列表出错')
                }

                // 发送初始项目数据到webview
                webview.postMessage({
                    type: 'setInitialData',
                    data: projectData
                });

                disposables.add(webview.onMessage(async event => {
                    const message = event.message;

                    if (typeof message !== 'object' || !message || !('type' in message)) {
                        return;
                    }

                    switch (message.type) {
                        case 'save':
                            try {
                                if (message.data) {
                                    console.log(message.data, 'message.data')
                                    const description = message.data.description.trim();
                                    const databaseConfig = message.data.databaseConfig;
                                    const params: any = {
                                        projectId: projectData.id,
                                        description,
                                        databaseType: Number(message.data.dbMode)
                                    }

                                    if (Number(message.data.dbMode) === 2) {
                                        params.databaseId = message.data.databaseConfig.id;
                                    }
                                    if (Number(message.data.dbMode) === 3 && databaseConfig) {
                                        params.databaseCustomHost = databaseConfig.url;
                                        const url = databaseConfig.url; // 数据库连接字符串
                                        if (url) {
                                            // host:port/database
                                            const lastColon = url.lastIndexOf(':');
                                            const slash = url.indexOf('/', lastColon);
                                            if (lastColon !== -1 && slash !== -1) {
                                                params.databaseCustomHost = url.substring(0, lastColon);
                                                params.databaseCustomPort = Number(url.substring(lastColon + 1, slash));
                                                params.databaseCustomName = url.substring(slash + 1);
                                            }
                                        }
                                        params.databaseCustomUsername = databaseConfig.username;
                                        params.databaseCustomPassword = databaseConfig.password;
                                    }
                                    const res = await newProjectService.setProjectInfo(params);
                                    if (res.code === 200) {
                                        const saveLocalizations = ProjectSettingsAction.projectSettingsLocalizations;
                                        const successMessage = saveLocalizations?.projectSettingsSaved || "Project settings saved successfully";
                                        notificationService.info(successMessage);
                                        auxiliaryWindow.dispose();
                                        ProjectSettingsAction.currentProjectSettingsWindow = null;
                                        commandService.executeCommand(CLOUDDEV_REFRESH);
                                    } else {
                                        const errorLocalizations = ProjectSettingsAction.projectSettingsLocalizations;
                                        const errorMessage = errorLocalizations?.projectSettingsSaveError || "Failed to save project settings: {0}";
                                        notificationService.error(errorMessage.replace('{0}', res?.message || ''));
                                    }
                                }
                            } catch (error) {
                                console.error('保存项目设置失败:', error);
                                const catchErrorLocalizations = ProjectSettingsAction.projectSettingsLocalizations;
                                const catchErrorMessage = catchErrorLocalizations?.projectSettingsSaveError || "Failed to save project settings: {0}";
                                notificationService.error(catchErrorMessage.replace('{0}', error.message));
                            }
                            break;

                        case 'cancel':
                            auxiliaryWindow.dispose();
                            ProjectSettingsAction.currentProjectSettingsWindow = null;
                            break;
                    }
                }));

                disposables.add(auxiliaryWindow.onUnload(() => {
                    disposables.dispose();
                    ProjectSettingsAction.currentProjectSettingsWindow = null;
                }));
            } catch (error) {
                console.error('ProjectSettingsAction: 读取HTML文件失败', error);
                const htmlErrorLocalizations = ProjectSettingsAction.projectSettingsLocalizations;
                const htmlErrorMessage = htmlErrorLocalizations?.htmlReadError || "Failed to read HTML file: {0}";
                notificationService.error(htmlErrorMessage.replace('{0}', error.message));
                auxiliaryWindow.dispose();
                ProjectSettingsAction.currentProjectSettingsWindow = null;
            }
        } catch (error) {
            console.error('ProjectSettingsAction: 创建或显示窗口失败', error);
            const finalErrorLocalizations = ProjectSettingsAction.projectSettingsLocalizations;
            const finalErrorMessage = finalErrorLocalizations?.projectSettingsError || "Failed to create project settings window: {0}";
            notificationService.error(finalErrorMessage.replace('{0}', error));
        }
    }
}

// 注册项目设置命令
registerAction2(ProjectSettingsAction);
