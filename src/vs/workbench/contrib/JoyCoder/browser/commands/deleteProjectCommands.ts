/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/
import { Action2, registerAction2 } from '../../../../../platform/actions/common/actions.js';
import { ServicesAccessor } from '../../../../../platform/instantiation/common/instantiation.js';
import { localize } from '../../../../../nls.js';
import { Categories } from '../../../../../platform/action/common/actionCommonCategories.js';
import { INotificationService } from '../../../../../platform/notification/common/notification.js';
import { ContextKeyExpr } from '../../../../../platform/contextkey/common/contextkey.js';
import { NOT_LOGGED_IN_KEY } from '../constants/loginConstants.js';
import { ILogService } from '../../../../../platform/log/common/log.js';
import { ICommandService } from '../../../../../platform/commands/common/commands.js';
import { showCustomDeleteDialog } from '../project/customDeleteDialog.js';
import { IWorkspaceContextService } from '../../../../../platform/workspace/common/workspace.js';
import { IHostService } from '../../../../services/host/browser/host.js';

// 命令ID
export const DELETE_PROJECT_COMMAND_ID = 'workbench.action.deleteProject';
export const DELETE_PROJECT_COMMAND_AFTER_ID = 'workbench.action.deleteProjectAfter';
export const TEST_DELETE_LOCAL_PROJECT_COMMAND_ID = 'workbench.action.file.testLocal';
export const TEST_DELETE_REMOTE_PROJECT_COMMAND_ID = 'workbench.action.joyCoderTestDeleteRemoteProject';


export const DELETE_PROJECT = 'clouddev.deleteProject';

// 国际化文本管理
class DeleteProjectLocalizations {
    private static _instance: DeleteProjectLocalizations;
    private _localizations: any = {};

    private constructor() {
        this.initLanguageLocalizations();
    }

    public static getInstance(): DeleteProjectLocalizations {
        if (!DeleteProjectLocalizations._instance) {
            DeleteProjectLocalizations._instance = new DeleteProjectLocalizations();
        }
        return DeleteProjectLocalizations._instance;
    }

    private initLanguageLocalizations() {
        // 延迟初始化翻译，确保语言包已经加载
        this._localizations = {
            deleteProject: localize('joycode.deleteProject.title', "Delete Project"),
            deleteProjectAfter: localize('joycode.deleteProject.after', "Delete Project After"),
            testDeleteLocalProject: localize('joycode.deleteProject.testLocal', "Test Delete Local Project"),
            testDeleteRemoteProject: localize('joycode.deleteProject.testRemote', "Test Delete Remote Project"),
            noProjectData: localize('joycode.deleteProject.noProjectData', "No project data provided"),
            preparingDelete: localize('joycode.deleteProject.preparingDelete', "Preparing to delete project \"{0}\", path: {1}, remote: {2}"),
            userCancelled: localize('joycode.deleteProject.userCancelled', "User cancelled deletion of project \"{0}\""),
            pathNotProvided: localize('joycode.deleteProject.pathNotProvided', "Not provided")
        };
    }

    public get localizations() {
        return this._localizations;
    }
}

// 项目数据接口
export interface IProjectData {
    id: number;
    name: string;
    projectPath?: string;
    isRemote?: boolean;
    localLocation?: string;
    devMode?: number; // 1为远程 2为本地
}

// 模拟项目数据
const MOCK_PROJECT_DATA: IProjectData = {
    id: 233,
    name: 'HelloWorld',
    projectPath: '/Users/<USER>/Projects/HelloWorld',
    isRemote: false
};

// 模拟远程项目数据
const MOCK_REMOTE_PROJECT_DATA: IProjectData = {
    id: 233,
    name: 'RemoteProject',
    projectPath: '/config/workspace/RemoteProject',
    isRemote: true
};

// 注册删除项目命令
registerAction2(class DeleteProjectAction extends Action2 {
    constructor() {
        super({
            id: DELETE_PROJECT_COMMAND_ID,
            title: {
                value: localize('joycode.deleteProject.title', "Delete Project"),
                original: 'Delete Project'
            },
            category: Categories.View,
            f1: true,
            // 添加前置条件：只有在已登录状态下才启用
            precondition: ContextKeyExpr.equals(NOT_LOGGED_IN_KEY, false)
        });
    }

    async run(accessor: ServicesAccessor, projectData?: IProjectData): Promise<boolean> {
        const localizations = DeleteProjectLocalizations.getInstance().localizations;

        // 如果没有提供项目数据，则无法继续
        if (!projectData || !projectData.name) {
            accessor.get(INotificationService).error(localizations.noProjectData || "No project data provided");
            return false;
        }
        const logService = accessor.get(ILogService);

        // 项目名称
        const projectName = projectData.name;
        // 项目路径
        const projectPath = projectData.localLocation;
        // 是否为远程项目
        const isRemote = projectData.devMode === 1;

        const pathText = projectPath || (localizations.pathNotProvided || "Not provided");
        const logMessage = (localizations.preparingDelete || "Preparing to delete project \"{0}\", path: {1}, remote: {2}")
            .replace('{0}', projectName)
            .replace('{1}', pathText)
            .replace('{2}', String(isRemote));

        logService.info(`DeleteProjectAction: ${logMessage}`);

        return await new Promise<any>(async (resolve) => {
            showCustomDeleteDialog({
                projectName,
                isRemote,
                onConfirm: async (checked) => {
                    resolve({
                        isRemote,// 表示是否远程
                        checked,// true 标识删除本地
                    })
                },
                onCancel: () => {
                    const cancelMessage = (localizations.userCancelled || "User cancelled deletion of project \"{0}\"")
                        .replace('{0}', projectName);
                    logService.info(`DeleteProjectAction: ${cancelMessage}`);
                    resolve(false);
                }
            });

        })
    }
});

// 注册测试删除本地项目命令
registerAction2(class TestDeleteLocalProjectAction extends Action2 {
    constructor() {
        super({
            id: TEST_DELETE_LOCAL_PROJECT_COMMAND_ID,
            title: {
                value: localize('joycode.deleteProject.testLocal', "Test Delete Local Project"),
                original: 'Test Delete Local Project'
            },
            category: Categories.View,
            f1: true
        });
    }

    async run(accessor: ServicesAccessor): Promise<void> {
        const commandService = accessor.get(ICommandService);

        // 使用本地项目模拟数据调用删除项目命令
        await commandService.executeCommand(DELETE_PROJECT_COMMAND_ID, MOCK_PROJECT_DATA);
    }
});

// 注册测试删除远程项目命令
registerAction2(class TestDeleteRemoteProjectAction extends Action2 {
    constructor() {
        super({
            id: TEST_DELETE_REMOTE_PROJECT_COMMAND_ID,
            title: {
                value: localize('joycode.deleteProject.testRemote', "Test Delete Remote Project"),
                original: 'Test Delete Remote Project'
            },
            category: Categories.View,
        });
    }
    async run(accessor: ServicesAccessor): Promise<void> {
        const commandService = accessor.get(ICommandService);

        // 使用本地项目模拟数据调用删除项目命令
        await commandService.executeCommand(DELETE_PROJECT_COMMAND_ID, MOCK_REMOTE_PROJECT_DATA);
    }
});

// 注册删除项目成功后的回调命令，供插件调用
registerAction2(class DeleteProjectAfterAction extends Action2 {
    constructor() {
        super({
            id: DELETE_PROJECT_COMMAND_AFTER_ID,
            title: {
                value: localize('joycode.deleteProject.after', "Delete Project After"),
                original: 'Delete Project After'
            },
            category: Categories.View,
            f1: false
        });
    }

    async run(accessor: ServicesAccessor, projectData: IProjectData): Promise<void> {
        // 获取当前工作区
        const contextService = accessor.get(IWorkspaceContextService);
        const hostService = accessor.get(IHostService);
        const workspace = contextService.getWorkspace();
        if (workspace?.folders?.length) {
            const folder = workspace.folders[0];
            const scheme = folder.uri.scheme;
            if (scheme === 'file' && projectData.name === folder.name) {
                // 切换到空窗口
                hostService.openWindow({ forceReuseWindow: true });
            }

            if (scheme === 'vscode-remote' && folder.uri.authority?.split('+')?.[1]) {
                const authority = folder.uri.authority.split('+')[1];
                if (`${projectData.name}_${projectData.id}` === authority) {
                    // 切换到空窗口
                    hostService.openWindow({ forceReuseWindow: true });
                };
            }
        }
    }
});
