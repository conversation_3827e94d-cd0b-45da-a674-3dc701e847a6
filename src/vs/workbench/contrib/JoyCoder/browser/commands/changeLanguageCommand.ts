/*---------------------------------------------------------------------------------------------
 *  joycode 切换语言命令，使用 registerAction2 注册
 *--------------------------------------------------------------------------------------------*/

import { registerAction2, Action2 } from '../../../../../platform/actions/common/actions.js';
import { ServicesAccessor } from '../../../../../platform/instantiation/common/instantiation.js';
import { ICommandService } from '../../../../../platform/commands/common/commands.js';
import { localize2, localize } from '../../../../../nls.js';
import { IStorageService, StorageScope, StorageTarget } from '../../../../../platform/storage/common/storage.js';
import { WELCOME_SHOWN_KEY } from '../welcomeService.js';
import { ILocaleService } from '../../../../services/localization/common/locale.js';
import { INotificationService } from '../../../../../platform/notification/common/notification.js';

export const CHANGE_LANGUAGE_COMMAND_ID = 'joycode.changeLanguage';

registerAction2(class ChangeLanguageAction extends Action2 {
	constructor() {
		super({
			id: CHANGE_LANGUAGE_COMMAND_ID,
			title: localize2('joycode.changeLanguage.title', 'Change Display Language', '切换界面语言'),
			f1: true
		});
	}

	async run(accessor: ServicesAccessor, args?: { id: string, label?: string }) {
		const lang = args?.id;
		if (!lang) return;

		const localeService = accessor.get(ILocaleService);
		const storageService = accessor.get(IStorageService);
		const notificationService = accessor.get(INotificationService);

		try {
			// 标记欢迎页面已显示，避免重新显示
			storageService.store(WELCOME_SHOWN_KEY, true, StorageScope.PROFILE, StorageTarget.USER);

			const languageId = lang.toLowerCase();
			let extensionId: string | undefined;
			let galleryExtension: any = undefined;

			// 修复：使用languageId而不是label来判断是否为中文
			if (languageId === 'zh-cn') {
				extensionId = 'ms-ceintl.vscode-language-pack-zh-hans';
			}

			console.log('切换语言', languageId, extensionId);
			console.log('当前Language.value():', (await import('../../../../../base/common/platform.js')).Language.value());
			console.log('目标语言:', languageId);

			await localeService.setLocale({
				id: languageId,
				label: languageId === 'zh-cn' ? '中文(简体)' : 'English',
				galleryExtension: galleryExtension,
				extensionId: extensionId
			}, true);

			console.log('语言切换命令执行完成');

		} catch (e) {
			console.error('切换语言失败', e);
			notificationService.error(localize('changeLanguageFailed', "Failed to change language: {0}", e instanceof Error ? e.message : String(e)));
		}
	}
});

// 测试指令：切换为中文
registerAction2(class TestLanguageZhAction extends Action2 {
	constructor() {
		super({
			id: 'joycode.testLanguage.zh',
			title: localize2('joycode.testLanguage.zh.title', 'Test Switch to Chinese', '测试切换为中文'),
			f1: true
		});
	}
	async run(accessor: ServicesAccessor) {
		const commandService = accessor.get(ICommandService);
		await commandService.executeCommand(CHANGE_LANGUAGE_COMMAND_ID, { id: 'zh-cn', label: '简体中文' });
	}
});

// 测试指令：切换为英文
registerAction2(class TestLanguageEnAction extends Action2 {
	constructor() {
		super({
			id: 'joycode.testLanguage.en',
			title: localize2('joycode.testLanguage.en.title', 'Test Switch to English', '测试切换为英文'),
			f1: true
		});
	}
	async run(accessor: ServicesAccessor) {
		const commandService = accessor.get(ICommandService);
		await commandService.executeCommand(CHANGE_LANGUAGE_COMMAND_ID, { id: 'en', label: 'English' });
	}
});
