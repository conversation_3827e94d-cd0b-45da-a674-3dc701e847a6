/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/
import { Action2, registerAction2 } from '../../../../../platform/actions/common/actions.js';
import { ServicesAccessor } from '../../../../../platform/instantiation/common/instantiation.js';
import { localize } from '../../../../../nls.js';
import { Categories } from '../../../../../platform/action/common/actionCommonCategories.js';
import { ContextKeyExpr } from '../../../../../platform/contextkey/common/contextkey.js';
import { NOT_LOGGED_IN_KEY } from '../constants/loginConstants.js';
import { ILogService } from '../../../../../platform/log/common/log.js';
import { ICommandService } from '../../../../../platform/commands/common/commands.js';

// 命令ID
export const SHOW_DEPLOYMENT_DIALOG_COMMAND_ID = 'workbench.action.showDeploymentDialog';
export const TEST_DEPLOYMENT_DIALOG_COMMAND_ID = 'workbench.action.testDeploymentDialog';

// 国际化文本管理
class DeploymentDialogLocalizations {
    private static _instance: DeploymentDialogLocalizations;
    private _localizations: any = {};

    private constructor() {
        this.initLanguageLocalizations();
    }

    public static getInstance(): DeploymentDialogLocalizations {
        if (!DeploymentDialogLocalizations._instance) {
            DeploymentDialogLocalizations._instance = new DeploymentDialogLocalizations();
        }
        return DeploymentDialogLocalizations._instance;
    }

    private initLanguageLocalizations() {
        // 延迟初始化翻译，确保语言包已经加载
        this._localizations = {
            dialogTitle: localize('joycode.deploymentDialog.title', "Deployment"),
            appNameHeader: localize('joycode.deploymentDialog.appName', "App Name"),
            currentImageHeader: localize('joycode.deploymentDialog.currentImage', "Current Image"),
            createTimeHeader: localize('joycode.deploymentDialog.createTime', "Create Time"),
            operationHeader: localize('joycode.deploymentDialog.operation', "Operation"),
            updateImageOnly: localize('joycode.deploymentDialog.updateImageOnly', "Update Image Only"),
            updateImageAndConfig: localize('joycode.deploymentDialog.updateImageAndConfig', "Update"),
            cancelButton: localize('joycode.deploymentDialog.cancel', "Cancel"),
            saveButton: localize('joycode.deploymentDialog.save', "Save"),
            closeButton: localize('joycode.deploymentDialog.close', "Close"),
            showDeploymentDialog: localize('joycode.deploymentDialog.action', "Show Deployment Dialog"),
            testDeploymentDialog: localize('joycode.deploymentDialog.test', "Test Deployment Dialog"),
            noDeployments: localize('joycode.deploymentDialog.noDeployments', "No deployment information"),
            newApp: localize('joycode.deploymentDialog.newApp', "Launch New App")
        };
    }

    public get localizations() {
        return this._localizations;
    }
}

// 部署项数据接口
export interface IDeploymentItem {
    /**
     * 应用名称
     */
    name: string;

    /**
     * 当前镜像
     */
    image: string;

    /**
     * 创建时间
     */
    createdAt: string;

    /**
     * 应用ID
     */
    id?: string;
}

// 部署对话框选项接口
export interface IDeploymentDialogOptions {
    /**
     * 部署列表数据
     */
    deployments?: IDeploymentItem[];

    /**
     * 更新镜像及配置回调
     * @param deployment 部署项
     */
    onUpdateImageAndConfig?: (deployment: IDeploymentItem) => void;

    /**
     * 创建新应用
     */
    onCreateApp?: () => void;

    /**
     * 取消回调
     */
    onCancel?: () => void;
}

// 部署对话框结果接口
export interface IDeploymentDialogResult {
    /**
     * 操作类型
     */
    action: 'cancel' | 'createApp' | 'updateImageOnly' | 'updateImageAndConfig';

    /**
     * 相关的部署项（当操作为更新时）
     */
    deployment?: IDeploymentItem;
}

/**
 * 显示部署对话框
 * @param options 对话框选项
 */
export function showDeploymentDialog(options: IDeploymentDialogOptions): void {
    const localizations = DeploymentDialogLocalizations.getInstance().localizations;

    // 默认部署数据
    const defaultDeployments: IDeploymentItem[] = options.deployments || [
        {
            name: 'test_app 1',
            image: 'test 1.0.0',
            createdAt: '2025-5-31 16:18',
            id: '1'
        },
        {
            name: 'test_app 1',
            image: 'test 1.0.0',
            createdAt: '2025-5-31 16:18',
            id: '2'
        },
        {
            name: 'test_app 1',
            image: 'test 1.0.0',
            createdAt: '2025-5-31 16:18',
            id: '3'
        }
    ];

    // 1. 创建遮罩
    const mask = document.createElement('div');
    mask.className = 'joycoder-dialog-mask';

    // 2. 创建弹窗
    const dialog = document.createElement('div');
    dialog.className = 'joycoder-dialog joycoder-deployment-dialog';

    // 3. 创建内容
    const main = document.createElement('div');
    main.className = 'joycoder-dialog-main';

    // 标题区域
    const header = document.createElement('div');
    header.className = 'joycoder-dialog-header';

    // 左侧区域：标题和新应用按钮
    const leftArea = document.createElement('div');
    leftArea.className = 'joycoder-dialog-header-left';

    // 标题文本
    const title = document.createElement('div');
    title.className = 'joycoder-dialog-title';
    title.textContent = localizations.dialogTitle;

    // 新应用按钮
    const newAppBtn = document.createElement('button');
    newAppBtn.className = 'joycoder-dialog-btn joycoder-dialog-btn-text';
    newAppBtn.textContent = localizations.newApp;
    newAppBtn.addEventListener('click', () => {
        options.onCreateApp?.();
        close();
    });

    leftArea.appendChild(title);
    leftArea.appendChild(newAppBtn);

    // 关闭按钮
    const closeBtn = document.createElement('button');
    closeBtn.className = 'joycoder-dialog-close';
    closeBtn.textContent = '×';
    closeBtn.setAttribute('aria-label', localizations.closeButton);
    closeBtn.setAttribute('title', localizations.closeButton);

    header.appendChild(leftArea);
    header.appendChild(closeBtn);
    main.appendChild(header);

    // 表格区域
    const tableContainer = document.createElement('div');
    tableContainer.className = 'joycoder-deployment-table-container';

    const table = document.createElement('table');
    table.className = 'joycoder-deployment-table';

    // 表头
    const thead = document.createElement('thead');
    const headerRow = document.createElement('tr');

    const headers = [
        localizations.appNameHeader,
        localizations.currentImageHeader,
        localizations.createTimeHeader,
        localizations.operationHeader
    ];

    headers.forEach(headerText => {
        const th = document.createElement('th');
        th.textContent = headerText;
        headerRow.appendChild(th);
    });

    thead.appendChild(headerRow);
    table.appendChild(thead);

    // 表体
    const tbody = document.createElement('tbody');

    if (defaultDeployments.length === 0) {
        const emptyRow = document.createElement('tr');
        const emptyCell = document.createElement('td');
        emptyCell.colSpan = 4;
        emptyCell.className = 'joycoder-deployment-empty';
        emptyCell.textContent = localizations.noDeployments;
        emptyRow.appendChild(emptyCell);
        tbody.appendChild(emptyRow);
    } else {
        defaultDeployments.forEach(deployment => {
            const row = document.createElement('tr');

            // 应用名称
            const appNameCell = document.createElement('td');
            appNameCell.textContent = deployment.name;
            row.appendChild(appNameCell);

            // 当前镜像
            const imageCell = document.createElement('td');
            imageCell.textContent = deployment.image;
            row.appendChild(imageCell);

            // 创建时间
            const timeCell = document.createElement('td');
            timeCell.textContent = new Date(deployment.createdAt).toLocaleString('zh-CN');
            row.appendChild(timeCell);

            // 操作按钮
            const operationCell = document.createElement('td');
            operationCell.className = 'joycoder-deployment-operations';

            // const updateImageBtn = document.createElement('button');
            // updateImageBtn.className = 'joycoder-operation-btn joycoder-operation-btn-primary';
            // updateImageBtn.textContent = localizations.updateImageOnly;
            // updateImageBtn.addEventListener('click', () => {
            //     options.onUpdateImageOnly?.(deployment);
            // });

            const updateConfigBtn = document.createElement('button');
            updateConfigBtn.className = 'joycoder-dialog-btn joycoder-dialog-btn-secondary';
            updateConfigBtn.textContent = localizations.updateImageAndConfig;
            updateConfigBtn.addEventListener('click', () => {
                options.onUpdateImageAndConfig?.(deployment);
                close();
            });

            // operationCell.appendChild(updateImageBtn);
            operationCell.appendChild(updateConfigBtn);
            row.appendChild(operationCell);

            tbody.appendChild(row);
        });
    }

    table.appendChild(tbody);
    tableContainer.appendChild(table);
    main.appendChild(tableContainer);
    dialog.appendChild(main);


    // 4. 挂载
    mask.appendChild(dialog);

    // 优先插入到带有 VSCode 主题变量的容器下，保证 CSS 变量生效
    const workbench = document.querySelector('.monaco-workbench');
    if (workbench) {
        workbench.appendChild(mask);
    } else {
        document.body.appendChild(mask);
    }

    // 5. 样式
    if (!document.getElementById('joycoder-deployment-dialog-style')) {
        const style = document.createElement('style');
        style.id = 'joycoder-deployment-dialog-style';
        style.textContent = `
            .joycoder-dialog-mask {
                position: fixed;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background: rgba(0, 0, 0, 0.5);
                display: flex;
                justify-content: center;
                align-items: center;
                z-index: 9999;
            }

            .joycoder-dialog {
                background-color: var(--vscode-editor-background);
                border-radius: 6px;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
                display: flex;
                flex-direction: column;
                max-width: 90vw;
                max-height: 90vh;
                border: 1px solid var(--vscode-widget-border);
            }

            .joycoder-dialog-main {
                padding: 20px;
                flex: 1;
                overflow-y: auto;
            }

            .joycoder-dialog-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 20px;
            }

            .joycoder-dialog-header-left {
                display: flex;
                align-items: center;
                gap: 16px;
                flex: 1;
                justify-content: space-between;
            }

            .joycoder-dialog-title {
                font-size: 16px;
                font-weight: 600;
                color: var(--vscode-foreground);
            }

            .joycoder-dialog-close {
                background: none;
                border: none;
                color: var(--vscode-foreground);
                cursor: pointer;
                font-size: 20px;
                line-height: 1;
                padding: 4px;
                border-radius: 4px;
                width: 28px;
                height: 28px;
                display: flex;
                align-items: center;
                justify-content: center;
                transition: background-color 0.2s;
            }

            .joycoder-dialog-close:hover {
                background: var(--vscode-toolbar-hoverBackground);
            }

            .joycoder-dialog-close:active {
                background: var(--vscode-toolbar-activeBackground);
            }

            .joycoder-deployment-dialog {
                min-width: 800px;
                max-width: 1000px;
                max-height: 80vh;
            }
            .joycoder-deployment-table-container {
                margin-bottom: 24px;
                max-height: 400px;
                overflow-y: auto;
                border: 1px solid var(--vscode-widget-border);
                border-radius: 4px;
            }
            .joycoder-deployment-table {
                width: 100%;
                border-collapse: collapse;
                background: var(--vscode-editor-background);
            }
            .joycoder-deployment-table th,
            .joycoder-deployment-table td {
                padding: 12px;
                text-align: left;
                border-bottom: 1px solid var(--vscode-settings-textInputBorder);
                color: var(--vscode-editor-foreground);
            }
            .joycoder-deployment-table tr {
                border-bottom: 1px solid var(--vscode-settings-textInputBorder);
            }
            .joycoder-deployment-table th {
                background: var(--vscode-editorGroupHeader-tabsBackground);
                font-weight: 600;
                font-size: 13px;
                position: sticky;
                top: 0;
                z-index: 1;
            }
            .joycoder-deployment-table td {
                font-size: 14px;
            }
            .joycoder-deployment-table tr:hover {
                background: var(--vscode-list-hoverBackground);
            }
            .joycoder-deployment-operations {
                display: flex;
                gap: 8px;
            }
            .joycoder-dialog-btn {
                padding: 6px 16px;
                border: none;
                border-radius: 4px;
                font-size: 14px;
                cursor: pointer;
                transition: background-color 0.2s;
                white-space: nowrap;
                min-width: 80px;
            }
            .joycoder-dialog-btn-text {
                background: transparent;
                color: #247FFF;
                padding: 4px 8px;
                font-size: 14px;
                min-width: auto;
                border: none;
            }
            .joycoder-dialog-btn-text:hover {
                background: transparent;
                color: #247FFF;
            }
            .joycoder-dialog-btn-primary {
                background: var(--vscode-button-background);
                color: var(--vscode-button-foreground);
            }
            .joycoder-dialog-btn-primary:hover {
                background: var(--vscode-button-hoverBackground);
            }
            .joycoder-dialog-btn-secondary {
                background: transparent;
                color: #247FFF;
                padding: 4px 0;
                font-size: 14px;
                min-width: auto;
                border: none;
            }
            .joycoder-dialog-btn-secondary:hover {
                background: transparent;
                color: #247FFF;
            }
            .joycoder-deployment-empty {
                text-align: center;
                color: var(--vscode-descriptionForeground);
                font-style: italic;
                padding: 40px 20px;
            }
        `;
        document.head.appendChild(style);
    }

    // 6. 事件处理
    function close() {
        const workbench = document.querySelector('.monaco-workbench');
        if (workbench && workbench.contains(mask)) {
            workbench.removeChild(mask);
        } else if (document.body.contains(mask)) {
            document.body.removeChild(mask);
        }

        // 移除样式
        const style = document.getElementById('joycoder-deployment-dialog-style');
        if (style) {
            style.remove();
        }
    }

    closeBtn.addEventListener('click', () => {
        close();
        options.onCancel?.();
    });


    dialog.addEventListener('click', e => e.stopPropagation());

    // ESC键关闭
    const escListener = (ev: KeyboardEvent) => {
        if (ev.key === 'Escape') {
            close();
            options.onCancel?.();
            document.removeEventListener('keydown', escListener);
        }
    };
    document.addEventListener('keydown', escListener);
}

// 注册显示部署对话框命令
registerAction2(class ShowDeploymentDialogAction extends Action2 {
    constructor() {
        super({
            id: SHOW_DEPLOYMENT_DIALOG_COMMAND_ID,
            title: {
                value: localize('joycode.deploymentDialog.action', "Show Deployment Dialog"),
                original: 'Show Deployment Dialog'
            },
            category: Categories.View,
            f1: true,
            // 添加前置条件：只有在已登录状态下才启用
            precondition: ContextKeyExpr.equals(NOT_LOGGED_IN_KEY, false)
        });
    }

    async run(accessor: ServicesAccessor, deployments?: IDeploymentItem[]): Promise<IDeploymentDialogResult> {
        const logService = accessor.get(ILogService);

        logService.info('ShowDeploymentDialogAction: 显示部署对话框');

        return new Promise<IDeploymentDialogResult>((resolve) => {
            showDeploymentDialog({
                deployments,
                onUpdateImageAndConfig: (deployment: IDeploymentItem) => {
                    logService.info(`ShowDeploymentDialogAction: 更新镜像及配置 - ${deployment.name}`);
                    resolve({
                        action: 'updateImageAndConfig',
                        deployment
                    });
                },
                onCreateApp: () => {
                    logService.info(`ShowDeploymentDialogAction: 创建新应用`);
                    resolve({
                        action: 'createApp',
                    });
                },
                onCancel: () => {
                    logService.info('ShowDeploymentDialogAction: 用户取消部署');
                    resolve({
                        action: 'cancel'
                    });
                }
            });
        });
    }
});

// 注册测试部署对话框命令
registerAction2(class TestDeploymentDialogAction extends Action2 {
    constructor() {
        super({
            id: TEST_DEPLOYMENT_DIALOG_COMMAND_ID,
            title: {
                value: localize('joycode.deploymentDialog.test', "Test Deployment Dialog"),
                original: 'Test Deployment Dialog'
            },
            category: Categories.View,
            f1: true
        });
    }

    async run(accessor: ServicesAccessor): Promise<void> {
        const commandService = accessor.get(ICommandService);
        const logService = accessor.get(ILogService);

        logService.info('TestDeploymentDialogAction: 开始测试部署对话框');

        // 调用部署对话框命令
        const result = await commandService.executeCommand(SHOW_DEPLOYMENT_DIALOG_COMMAND_ID);

        logService.info(`TestDeploymentDialogAction: 测试完成，结果: ${JSON.stringify(result)}`);
    }
});
