/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/
import { Action2, registerAction2 } from '../../../../../platform/actions/common/actions.js';
import { ServicesAccessor } from '../../../../../platform/instantiation/common/instantiation.js';
import { localize } from '../../../../../nls.js';
import { Categories } from '../../../../../platform/action/common/actionCommonCategories.js';
import { INotificationService } from '../../../../../platform/notification/common/notification.js';
import { ContextKeyExpr } from '../../../../../platform/contextkey/common/contextkey.js';
import { NOT_LOGGED_IN_KEY } from '../constants/loginConstants.js';
import { ILogService } from '../../../../../platform/log/common/log.js';
import { ICommandService } from '../../../../../platform/commands/common/commands.js';

// 命令ID
export const SHOW_GENERATE_IMAGE_VERSION_DIALOG_COMMAND_ID = 'workbench.action.showGenerateImageVersionDialog';
export const TEST_GENERATE_IMAGE_VERSION_DIALOG_COMMAND_ID = 'workbench.action.testGenerateImageVersionDialog';

// 国际化文本管理
class GenerateImageVersionDialogLocalizations {
    private static _instance: GenerateImageVersionDialogLocalizations;
    private _localizations: any = {};

    private constructor() {
        this.initLanguageLocalizations();
    }

    public static getInstance(): GenerateImageVersionDialogLocalizations {
        if (!GenerateImageVersionDialogLocalizations._instance) {
            GenerateImageVersionDialogLocalizations._instance = new GenerateImageVersionDialogLocalizations();
        }
        return GenerateImageVersionDialogLocalizations._instance;
    }

    private initLanguageLocalizations() {
        // 延迟初始化翻译，确保语言包已经加载
        this._localizations = {
            dialogTitle: localize('joycode.generateImageVersionDialog.title', "Generate Image Version"),
            imageNameLabel: localize('joycode.generateImageVersionDialog.imageName', "Image Name:"),
            versionLabel: localize('joycode.generateImageVersionDialog.version', "Version:"),
            versionPlaceholder: localize('joycode.generateImageVersionDialog.versionPlaceholder', "Please enter version, e.g.: 1.0.0"),
            descriptionLabel: localize('joycode.generateImageVersionDialog.description', "Description:"),
            descriptionPlaceholder: localize('joycode.generateImageVersionDialog.descriptionPlaceholder', "Please enter version description"),
            cancelButton: localize('joycode.generateImageVersionDialog.cancel', "Cancel"),
            generateButton: localize('joycode.generateImageVersionDialog.generate', "Generate"),
            showGenerateImageVersionDialog: localize('joycode.generateImageVersionDialog.action', "Show Generate Image Version Dialog"),
            testGenerateImageVersionDialog: localize('joycode.generateImageVersionDialog.test', "Test Generate Image Version Dialog"),
            versionRequired: localize('joycode.generateImageVersionDialog.versionRequired', "Version is required"),
            invalidVersion: localize('joycode.generateImageVersionDialog.invalidVersion', "Please use lowercase letters, numbers, dots, underscores and hyphens, and cannot start or end with special characters"),
            versionTooLength: localize('joycode.generateImageVersionDialog.versionLength', "Image tag is too long, cannot exceed 32 characters"),
        };
    }

    public get localizations() {
        return this._localizations;
    }
}

// 生成镜像版本对话框选项接口
export interface IGenerateImageVersionDialogOptions {
    /**
     * 默认镜像名
     */
    defaultImageName?: string;

    /**
     * 确认回调
     * @param data 表单数据
     */
    onConfirm: (data: IImageVersionData) => void;

    /**
     * 取消回调
     */
    onCancel?: () => void;
}

// 镜像版本数据接口
export interface IImageVersionData {
    /**
     * 镜像名
     */
    imageName: string;

    /**
     * 版本号
     */
    imageTag: string;

    /**
     * 版本描述
     */
    description: string;
}

// 生成镜像版本对话框结果接口
export interface IGenerateImageVersionDialogResult {
    /**
     * 是否确认
     */
    confirmed: boolean;

    /**
     * 镜像版本数据
     */
    data?: IImageVersionData;
}

/**
 * 显示生成镜像版本对话框
 * @param options 对话框选项
 */
export function showGenerateImageVersionDialog(options: IGenerateImageVersionDialogOptions): void {
    const localizations = GenerateImageVersionDialogLocalizations.getInstance().localizations;

    // 1. 创建遮罩
    const mask = document.createElement('div');
    mask.className = 'joycoder-dialog-mask';

    // 2. 创建弹窗
    const dialog = document.createElement('div');
    dialog.className = 'joycoder-dialog joycoder-image-version-dialog';

    // 3. 创建内容
    const main = document.createElement('div');
    main.className = 'joycoder-dialog-main';

    // 标题区域
    const header = document.createElement('div');
    header.className = 'joycoder-dialog-header';

    // 标题文本
    const title = document.createElement('div');
    title.className = 'joycoder-dialog-title';
    title.textContent = localizations.dialogTitle;

    // 关闭按钮
    const closeBtn = document.createElement('button');
    closeBtn.className = 'joycoder-dialog-close';
    closeBtn.textContent = '×';
    closeBtn.setAttribute('aria-label', '关闭');
    closeBtn.setAttribute('title', '关闭');

    header.appendChild(title);
    header.appendChild(closeBtn);
    main.appendChild(header);

    // 表单区域
    const form = document.createElement('div');
    form.className = 'joycoder-dialog-form';

    // 镜像名字段
    const imageNameGroup = document.createElement('div');
    imageNameGroup.className = 'joycoder-form-group';

    const imageNameLabel = document.createElement('label');
    imageNameLabel.className = 'joycoder-form-label';
    imageNameLabel.textContent = localizations.imageNameLabel;

    const imageNameInput = document.createElement('input');
    imageNameInput.type = 'text';
    imageNameInput.className = 'joycoder-form-input';
    imageNameInput.value = options.defaultImageName || 'hub.hzh.sealos.run/ns-4oikiptw/test1';
    imageNameInput.readOnly = true;

    imageNameGroup.appendChild(imageNameLabel);
    imageNameGroup.appendChild(imageNameInput);
    form.appendChild(imageNameGroup);

    // 版本号字段
    const versionGroup = document.createElement('div');
    versionGroup.className = 'joycoder-form-group joycoder-image-version';

    const versionLabel = document.createElement('label');
    versionLabel.className = 'joycoder-form-label';
    versionLabel.textContent = localizations.versionLabel;

    const versionInput = document.createElement('input');
    versionInput.type = 'text';
    versionInput.className = 'joycoder-form-input';
    versionInput.placeholder = localizations.versionPlaceholder;

    versionGroup.appendChild(versionLabel);
    versionGroup.appendChild(versionInput);
    form.appendChild(versionGroup);

    // 版本描述字段
    const descriptionGroup = document.createElement('div');
    descriptionGroup.className = 'joycoder-form-group';

    const descriptionLabel = document.createElement('label');
    descriptionLabel.className = 'joycoder-form-label';
    descriptionLabel.textContent = localizations.descriptionLabel;

    const descriptionTextarea = document.createElement('textarea');
    descriptionTextarea.className = 'joycoder-form-textarea';
    descriptionTextarea.placeholder = localizations.descriptionPlaceholder;
    descriptionTextarea.rows = 4;

    descriptionGroup.appendChild(descriptionLabel);
    descriptionGroup.appendChild(descriptionTextarea);
    form.appendChild(descriptionGroup);

    main.appendChild(form);
    dialog.appendChild(main);

    // 按钮区
    const buttons = document.createElement('div');
    buttons.className = 'joycoder-dialog-buttons';

    const btnCancel = document.createElement('button');
    btnCancel.className = 'joycoder-dialog-btn joycoder-dialog-cancel';
    btnCancel.textContent = localizations.cancelButton;

    const btnGenerate = document.createElement('button');
    btnGenerate.className = 'joycoder-dialog-btn joycoder-dialog-confirm';
    btnGenerate.textContent = localizations.generateButton;

    buttons.appendChild(btnCancel);
    buttons.appendChild(btnGenerate);
    dialog.appendChild(buttons);

    // 4. 挂载
    mask.appendChild(dialog);

    // 优先插入到带有 VSCode 主题变量的容器下，保证 CSS 变量生效
    const workbench = document.querySelector('.monaco-workbench');
    if (workbench) {
        workbench.appendChild(mask);
    } else {
        document.body.appendChild(mask);
    }

    // 5. 样式
    if (!document.getElementById('joycoder-image-version-dialog-style')) {
        const style = document.createElement('style');
        style.id = 'joycoder-image-version-dialog-style';
        style.textContent = `
            .joycoder-dialog-mask {
                position: fixed;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background: rgba(0, 0, 0, 0.5);
                display: flex;
                justify-content: center;
                align-items: center;
                z-index: 9999;
            }
            .joycoder-dialog {
                background: var(--vscode-editor-background);
                border-radius: 6px;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
                padding: 24px;
                position: relative;
            }
            .joycoder-dialog-main {
                position: relative;
                z-index: 1;
            }
            .joycoder-dialog-title {
                font-size: 16px;
                font-weight: 600;
                color: var(--vscode-editor-foreground);
            }
            .joycoder-dialog-buttons {
                display: flex;
                justify-content: flex-end;
                gap: 12px;
                margin-top: 24px;
            }
            .joycoder-dialog-btn {
                padding: 8px 16px;
                border: none;
                border-radius: 4px;
                font-size: 14px;
                cursor: pointer;
                transition: background-color 0.2s;
                min-width: 80px;
            }
            .joycoder-dialog-cancel {
                background: var(--vscode-button-secondaryBackground);
                color: var(--vscode-button-secondaryForeground);
                border-radius: 8px;
            }
            .joycoder-dialog-cancel:hover {
                background: var(--vscode-button-secondaryHoverBackground);
            }
            .joycoder-dialog-confirm {
                background: #ffffff;
                color: #333333;
                border: 1px solid #e0e0e0;
                border-radius: 8px;
                font-weight: 500;
                box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            }
            .joycoder-dialog-confirm:hover {
                background: #f5f5f5;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
            }
            .joycoder-dialog-confirm:active {
                background: #e8e8e8;
                transform: translateY(1px);
            }
            .joycoder-image-version-dialog {
                min-width: 500px;
                max-width: 600px;
            }
            .joycoder-dialog-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 20px;
                padding-bottom: 12px;
                border-bottom: 1px solid var(--vscode-widget-border);
            }
            .joycoder-dialog-close {
                background: none;
                border: none;
                color: var(--vscode-foreground);
                cursor: pointer;
                font-size: 20px;
                line-height: 1;
                padding: 4px;
                border-radius: 4px;
                width: 28px;
                height: 28px;
                display: flex;
                align-items: center;
                justify-content: center;
                transition: background-color 0.2s;
            }
            .joycoder-dialog-close:hover {
                background: var(--vscode-toolbar-hoverBackground);
            }
            .joycoder-dialog-close:active {
                background: var(--vscode-toolbar-activeBackground);
            }
            .joycoder-dialog-form {
                margin-bottom: 24px;
            }
            .joycoder-form-group {
                margin-bottom: 16px;
                display: grid;
                gap: 2px 8px;
                grid-template-columns: auto 1fr;
            }
            .joycoder-form-label {
                display: block;
                width: 100px;
                font-size: 14px;
                font-weight: 500;
                height: 32px;
                line-height: 32px;
                color: var(--vscode-editor-foreground);
            }
            .joycoder-form-input, .joycoder-form-textarea {
                width: 100%;
                padding: 8px 12px;
                border: 1px solid var(--vscode-input-border);
                border-radius: 4px;
                background: var(--vscode-input-background);
                color: var(--vscode-input-foreground);
                font-size: 14px;
                font-family: inherit;
                box-sizing: border-box;
            }
            .joycoder-form-input {
                height: 32px;
                line-height: 32px;
            }
            .joycoder-form-input:focus, .joycoder-form-textarea:focus {
                outline: none;
                border-color: var(--vscode-focusBorder);
            }
            .joycoder-form-input[readonly] {
                background: var(--vscode-input-background);
                opacity: 0.7;
            }
            .joycoder-form-textarea {
                resize: vertical;
                min-height: 80px;
            }
            .joycoder-form-error {
                color: #e51400;
                font-size: 12px;
                grid-column: 2 / -1;
            }
        `;
        document.head.appendChild(style);
    }

    // 显示错误信息
    function showError(container: HTMLElement, message: string) {
        // 移除已有的错误提示
        const existingError = container.querySelector('.joycoder-form-error');
        if (existingError) {
            existingError.remove();
        }

        // 创建新的错误提示
        const errorDiv = document.createElement('div');
        errorDiv.className = 'joycoder-form-error';
        errorDiv.textContent = message;
        container.appendChild(errorDiv);
    }

    // 6. 验证函数
    function validateForm(): boolean {
        const version = versionInput.value.trim();
        const versionEl = document.querySelector('.joycoder-image-version') as HTMLElement;
        if (!versionEl) {
            return false;
        }
        if (!version) {
            showError(versionEl, localizations.versionRequired);
            versionInput.focus();
            return false;
        }

        // 版本号格式验证
        if (!/^[a-z0-9]+(?:[._-][a-z0-9]+)*$/.test(version)) {
            showError(versionEl, localizations.invalidVersion);
            versionInput.focus();
            return false;
        }
        if (version.length > 32) {
            showError(versionEl, localizations.versionTooLength);
            versionInput.focus();
            return false;
        }

        return true;
    }

    // 7. 事件处理
    function close() {
        const workbench = document.querySelector('.monaco-workbench');
        if (workbench && workbench.contains(mask)) {
            workbench.removeChild(mask);
        } else if (document.body.contains(mask)) {
            document.body.removeChild(mask);
        }

        // 移除样式
        const style = document.getElementById('joycoder-image-version-dialog-style');
        if (style) {
            style.remove();
        }
    }

    closeBtn.addEventListener('click', () => {
        close();
        options.onCancel?.();
    });

    btnCancel.addEventListener('click', () => {
        close();
        options.onCancel?.();
    });

    btnGenerate.addEventListener('click', () => {
        if (validateForm()) {
            const data: IImageVersionData = {
                imageName: imageNameInput.value.trim(),
                imageTag: versionInput.value.trim(),
                description: descriptionTextarea.value.trim()
            };
            close();
            options.onConfirm(data);
        }
    });

    dialog.addEventListener('click', e => e.stopPropagation());

    // ESC键关闭
    const escListener = (ev: KeyboardEvent) => {
        if (ev.key === 'Escape') {
            close();
            options.onCancel?.();
            document.removeEventListener('keydown', escListener);
        }
    };
    document.addEventListener('keydown', escListener);

    // 自动聚焦到版本号输入框
    setTimeout(() => {
        versionInput.focus();
    }, 100);
}

// 注册显示生成镜像版本对话框命令
registerAction2(class ShowGenerateImageVersionDialogAction extends Action2 {
    constructor() {
        super({
            id: SHOW_GENERATE_IMAGE_VERSION_DIALOG_COMMAND_ID,
            title: {
                value: localize('joycode.generateImageVersionDialog.action', "Show Generate Image Version Dialog"),
                original: 'Show Generate Image Version Dialog'
            },
            category: Categories.View,
            f1: true,
            // 添加前置条件：只有在已登录状态下才启用
            precondition: ContextKeyExpr.equals(NOT_LOGGED_IN_KEY, false)
        });
    }

    async run(accessor: ServicesAccessor, defaultImageName?: string): Promise<IGenerateImageVersionDialogResult> {
        const logService = accessor.get(ILogService);
        const notificationService = accessor.get(INotificationService);

        logService.info('ShowGenerateImageVersionDialogAction: 显示生成镜像版本对话框');

        return new Promise<IGenerateImageVersionDialogResult>((resolve) => {
            showGenerateImageVersionDialog({
                defaultImageName,
                onConfirm: (data: IImageVersionData) => {
                    logService.info(`ShowGenerateImageVersionDialogAction: 用户确认生成镜像版本: ${JSON.stringify(data)}`);
                    notificationService.info(`开始生成镜像版本: ${data.imageName}:${data.imageTag}`);
                    resolve({
                        confirmed: true,
                        data
                    });
                },
                onCancel: () => {
                    logService.info('ShowGenerateImageVersionDialogAction: 用户取消生成镜像版本');
                    resolve({
                        confirmed: false
                    });
                }
            });
        });
    }
});

// 注册测试生成镜像版本对话框命令
registerAction2(class TestGenerateImageVersionDialogAction extends Action2 {
    constructor() {
        super({
            id: TEST_GENERATE_IMAGE_VERSION_DIALOG_COMMAND_ID,
            title: {
                value: localize('joycode.generateImageVersionDialog.test', "Test Generate Image Version Dialog"),
                original: 'Test Generate Image Version Dialog'
            },
            category: Categories.View,
            f1: true
        });
    }

    async run(accessor: ServicesAccessor): Promise<void> {
        const commandService = accessor.get(ICommandService);
        const logService = accessor.get(ILogService);

        logService.info('TestGenerateImageVersionDialogAction: 开始测试生成镜像版本对话框');

        // 调用生成镜像版本对话框命令，传入默认镜像名
        const result = await commandService.executeCommand(
            SHOW_GENERATE_IMAGE_VERSION_DIALOG_COMMAND_ID,
            'hub.hzh.sealos.run/ns-4oikiptw/test1'
        );

        logService.info(`TestGenerateImageVersionDialogAction: 测试完成，结果: ${JSON.stringify(result)}`);
    }
});
