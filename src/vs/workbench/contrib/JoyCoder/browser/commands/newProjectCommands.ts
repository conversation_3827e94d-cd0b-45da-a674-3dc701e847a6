/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

import { ServicesAccessor } from '../../../../../platform/instantiation/common/instantiation.js';
import { registerAction2, Action2 } from '../../../../../platform/actions/common/actions.js';
import { Categories } from '../../../../../platform/action/common/actionCommonCategories.js';
import { <PERSON>u<PERSON>eg<PERSON>ry, MenuId } from '../../../../../platform/actions/common/actions.js';
import { localize } from '../../../../../nls.js';
import { IFileDialogService } from '../../../../../platform/dialogs/common/dialogs.js';
import { INotificationService } from '../../../../../platform/notification/common/notification.js';
import { DisposableStore } from '../../../../../base/common/lifecycle.js';
import { IAuxiliaryWindowService, AuxiliaryWindowMode } from '../../../../services/auxiliaryWindow/browser/auxiliaryWindowService.js';
import { IFileService } from '../../../../../platform/files/common/files.js';
import { URI } from '../../../../../base/common/uri.js';
import { IWebviewService, WebviewContentPurpose } from '../../../webview/browser/webview.js';
import { process } from '../../../../../base/parts/sandbox/electron-sandbox/globals.js';
import { IHostService } from '../../../../services/host/browser/host.js';
import { ContextKeyExpr } from '../../../../../platform/contextkey/common/contextkey.js';
import { NOT_LOGGED_IN_KEY } from '../constants/loginConstants.js';
import { IWorkspaceContextService } from '../../../../../platform/workspace/common/workspace.js';
import { INewProjectMessageParams, INewProjectResult, INewProjectService, ISaveTemplateDownloadInfo, ITemplateInfo } from '../../common/newProject.js';
import { INewProjectParams, IRes } from '../../common/newProject.js';
import { FileAccess } from '../../../../../base/common/network.js';
import { IWorkspaceTrustManagementService } from '../../../../../platform/workspace/common/workspaceTrust.js';
import { ICommandService } from '../../../../../platform/commands/common/commands.js';
import { IPathService } from '../../../../services/path/common/pathService.js';
import { IStorageService, StorageScope, StorageTarget } from '../../../../../platform/storage/common/storage.js';
import { Language } from '../../../../../base/common/platform.js';
import { ILoginService } from '../../../../../platform/login/common/login.js';

let interfaceFailureState = false;

// 命令ID
export const NEW_PROJECT_COMMAND_ID = 'workbench.action.newProject';
export const CONNECT_REMOTESSH = 'clouddev.connectRemoteSSH';
export const CLOUDDEV_REFRESH = 'clouddev.refresh';
export const LOCAL_PROJECT_PATH = 'joycode.userProjectPath';

export const NEW_PROJECT_LIST_STORAGE_KEY = 'storage.newProjectList';

export const TEMPLATE_CLASS_CODE = {
    0: 'engineer',
    1: 'language',
}

export const TEMPLATE_TYPE = {
    0: 'remote',
    1: 'local',
}

const newProjectLocalizations = {
    // 页面标题和基本信息
    newProject: localize('joycode.newProject.newProject', "New Project"),
    createProject: localize('joycode.newProject.createProject', "Create Project"),

    // 表单字段标签
    name: localize('joycode.newProject.name', "Name:"),
    devMode: localize('joycode.newProject.devMode', "Dev Mode:"),
    location: localize('joycode.newProject.location', "Location:"),
    templates: localize('joycode.newProject.templates', "Templates:"),
    database: localize('joycode.newProject.database', "Database:"),
    description: localize('joycode.newProject.description', "Description:"),

    // 开发模式选项
    remote: localize('joycode.newProject.remote', "Remote"),
    local: localize('joycode.newProject.local', "Local"),

    // 数据库选项
    noDatabase: localize('joycode.newProject.noDatabase', "No Database"),
    automaticallyCreate: localize('joycode.newProject.automaticallyCreate', "Automatically Create"),
    selectExisting: localize('joycode.newProject.selectExisting', "Select Existing"),
    custom: localize('joycode.newProject.custom', "Custom"),

    // 数据库配置字段
    type: localize('joycode.newProject.type', "Type:"),
    url: localize('joycode.newProject.url', "URL:"),
    userName: localize('joycode.newProject.userName', "User Name:"),
    password: localize('joycode.newProject.password', "Password:"),

    // 按钮文本
    cancel: localize('joycode.newProject.cancel', "Cancel"),
    create: localize('joycode.newProject.create', "Create"),
    retry: localize('joycode.newProject.retry', "Retry"),
    browse: localize('joycode.newProject.browse', "Browse"),
    remove: localize('joycode.newProject.remove', "Remove"),

    // 占位符文本
    searchTemplates: localize('joycode.newProject.searchTemplates', "Search templates..."),
    hostPortDatabase: localize('joycode.newProject.hostPortDatabase', "host:port/database"),
    userNamePlaceholder: localize('joycode.newProject.userNamePlaceholder', "user name"),
    nameHostPort: localize('joycode.newProject.nameHostPort', "name host:port"),

    // 状态和消息文本
    creatingProject: localize('joycode.newProject.creatingProject', "Creating Project..."),
    projectAlreadyExists: localize('joycode.newProject.projectAlreadyExists', "Project already exists"),
    noDataAvailable: localize('joycode.newProject.noDataAvailable', "No data available."),
    version: localize('joycode.newProject.version', "Version:"),

    // 错误消息前缀
    errorPrefix: localize('joycode.newProject.errorPrefix', "error:"),

    // 通知消息
    projectWindowExists: localize('joycode.newProject.projectWindowExists', "Project window is already open"),
    failedToOpenNewWindow: localize('joycode.newProject.failedToOpenNewWindow', "Failed to open new window: {0}"),
    htmlReadError: localize('joycode.newProject.htmlReadError', "Failed to read HTML file: {0}"),
    newProjectError: localize('joycode.newProject.newProjectError', "Failed to create new project window: {0}"),
    selectProjectLocation: localize('joycode.newProject.selectProjectLocation', "Select Project Location"),

    // 项目创建过程中的消息
    failedToGetProjectInfo: localize('joycode.newProject.failedToGetProjectInfo', "Failed to get project information"),
    projectStatusError: localize('joycode.newProject.projectStatusError', "Project status error: {0}"),
    timeoutRetrievingProjectInfo: localize('joycode.newProject.timeoutRetrievingProjectInfo', "Timeout while retrieving project information"),

    // 错误和警告消息
    addingToTrustedListError: localize('joycode.newProject.addingToTrustedListError', "Error adding project directory to trusted list:"),
    addingRemoteToTrustedListError: localize('joycode.newProject.addingRemoteToTrustedListError', "Error adding remote project directory to trusted list:"),
    commandNotExists: localize('joycode.newProject.commandNotExists', "Command {0} does not exist"),
    parseStoredProjectListError: localize('joycode.newProject.parseStoredProjectListError', "Failed to parse stored project list, returning empty list:"),
    persistProjectListError: localize('joycode.newProject.persistProjectListError', "Failed to persist project list:"),
    projectListPersisted: localize('joycode.newProject.projectListPersisted', "Project list persisted, total {0} projects, new project: {1}"),
    noNewProjectInfoFound: localize('joycode.newProject.noNewProjectInfoFound', "No new project information found, skipping persistence"),
    checkDirectoryExistsError: localize('joycode.newProject.checkDirectoryExistsError', "Error checking if directory exists:"),
    projectNameIncrementLimit: localize('joycode.newProject.projectNameIncrementLimit', "Project name increment check reached maximum limit, stopping check"),

    // 模板和项目列表相关错误
    failedToFetchTemplateList: localize('joycode.newProject.failedToFetchTemplateList', "Failed to fetch template list"),
    failedToFetchProjectList: localize('joycode.newProject.failedToFetchProjectList', "Failed to fetch project list:"),
    generateUniqueProjectNameFailed: localize('joycode.newProject.generateUniqueProjectNameFailed', "Failed to generate unique project name:"),
    getProjectListFailed: localize('joycode.newProject.getProjectListFailed', "Failed to get project list:"),
    getTemplateListFailed: localize('joycode.newProject.getTemplateListFailed', "Failed to get template list:"),
    readHtmlFileFailed: localize('joycode.newProject.readHtmlFileFailed', "Failed to read HTML file"),
    createOrShowWindowFailed: localize('joycode.newProject.createOrShowWindowFailed', "Failed to create or show window"),

    // 菜单项
    miNewProject: localize('joycode.newProject.miNewProject', "Project..."),

    loading: localize('joycode.newProject.loading', "Loading...")
}


// 注册新建项目命令
registerAction2(class NewProjectAction extends Action2 {
    // 静态变量，用于跟踪当前是否已经打开了新建项目窗口
    private static currentNewProjectWindow: { window: any, focus: () => void } | null = null;
    private static templateList: ITemplateInfo[];
    private static saveTemplateDownloadInfo: ISaveTemplateDownloadInfo;
    private static createProjectParams: INewProjectMessageParams;
    private static createProjectResultInfo: INewProjectResult;

    /**
     * 将国际化文本注入到HTML中
     */
    private static injectLocalizationsToHtml(htmlString: string): string {
        // 如果国际化文本还没有初始化，使用默认值
        const localizations = newProjectLocalizations;

        // 创建一个脚本标签，将国际化文本作为全局变量注入
        const currentLanguage = Language.value();

        const localizationScript = `
            <script>
                console.log('[NewProject Script] Setting up localizations');
                window.newProjectLocalizations = ${JSON.stringify(localizations)};
                window.language = '${currentLanguage}';
            </script>
        `;

        // 在 </head> 标签前插入脚本
        return htmlString.replace('</head>', `${localizationScript}</head>`);
    }

    private static async createProjectData(newProjectService: INewProjectService, data: any): Promise<IRes | null> {
        let params: INewProjectParams = {
            name: data.projectName,
            description: data.projectDescription,
            devMode: data.devMode,
        }
        if (data.templateInfo) {
            const selectTem = NewProjectAction.templateList?.find(f => f.id === Number(data.templateInfo.id));
            params.projectTemplateId = data.templateInfo.id;
            params.projectTemplateName = selectTem?.templateName || data.templateInfo.templateName;
            params.projectTemplateVersion = data.templateInfo.version ?? '1.0.0';
            params.projectTemplateUrl = data.templateInfo.downloadUrl;
            // const isLan = data.templateInfo.category === '语言类模版';
            if (data.templateInfo.componentVersions?.length) {
                const projectTemplateEnvs = [];
                for (const item of data.templateInfo.componentVersions) {
                    if (data.templateInfo.templateClassCode === TEMPLATE_CLASS_CODE[0]) {
                        projectTemplateEnvs.push({
                            name: item.componentName,
                            version: item.versionName
                        })
                    }
                    if (params.projectTemplateVersion === item.versionName && data.templateInfo.templateClassCode === TEMPLATE_CLASS_CODE[1]) {
                        projectTemplateEnvs.push({
                            name: item.componentName,
                            version: item.versionName
                        })
                    }
                }
                params.projectTemplateEnvs = projectTemplateEnvs;
            }
        }
        if (data.devMode === 2) {
            params.localLocation = data.projectLocation;
        }
        params.databaseType = data.databaseMode; // 1:自动初始化共享库 2:选择已有数据库实例 3:自定义数据库配置
        if (data.databaseMode === 2 && data.databaseConfig) {
            params.databaseId = data.databaseConfig.instanceId;
        }
        if (data.databaseMode === 3 && data.databaseConfig) {
            const url = data.databaseConfig.url; // 数据库连接字符串
            if (url) {
                // host:port/database
                const lastColon = url.lastIndexOf(':');
                const slash = url.indexOf('/', lastColon);
                if (lastColon !== -1 && slash !== -1) {
                    params.databaseCustomHost = url.substring(0, lastColon);
                    params.databaseCustomPort = Number(url.substring(lastColon + 1, slash));
                    params.databaseCustomName = url.substring(slash + 1);
                }
            }
            params.databaseCustomUsername = data.databaseConfig.user;
            params.databaseCustomPassword = data.databaseConfig.password;
        }
        // console.log(params, 'params')
        const res = await newProjectService.createProject(params);
        return res;
    }

    // 新增：抽离 createProjectAndMaybeOpenWindow 函数
    private static async createProjectAndMaybeOpenWindow(
        {
            message,
            hasWorkspace,
            fileService,
            newProjectService,
            notificationService,
            workspaceTrustManagementService,
            hostService,
            webview,
            disposables,
            auxiliaryWindow,
            commandService,
            storageService
        }: {
            message: any;
            hasWorkspace: boolean;
            fileService: IFileService;
            newProjectService: INewProjectService;
            notificationService: INotificationService;
            workspaceTrustManagementService: IWorkspaceTrustManagementService;
            hostService: IHostService;
            webview: any;
            disposables: DisposableStore;
            auxiliaryWindow: any;
            commandService: ICommandService;
            storageService: IStorageService;
        }
    ) {
        try {
            const { projectName, projectLocation, templateInfo } = message.data;

            // 确保prompt透传
            const selectTempInfo = NewProjectAction.templateList.find(item => item.id === Number(templateInfo?.id));
            if (!templateInfo?.prompt && selectTempInfo?.prompt) templateInfo.prompt = selectTempInfo?.prompt || '';
            if (!templateInfo?.templateClassCode && selectTempInfo?.templateClassCode) templateInfo.templateClassCode = selectTempInfo.templateClassCode;
            let saveTemplateDownloadInfo: ISaveTemplateDownloadInfo = {
                type: 'local',
                url: templateInfo?.downloadUrl,
                name: projectName ?? templateInfo?.templateName,
                targetPath: projectLocation,
                projectInfo: null,
                templateInfo,
            };

            let project: any = null;

            // 先创建项目
            const res = await NewProjectAction.createProjectData(newProjectService, message.data);
            if (res?.code !== 200) {
                webview.postMessage({ type: 'error', message: res?.message });
                return;
            }
            saveTemplateDownloadInfo.projectInfo = res?.data;
            NewProjectAction.createProjectResultInfo = res?.data;
            NewProjectAction.saveTemplateDownloadInfo = saveTemplateDownloadInfo;

            if (res?.data?.status === 'Pending' && res?.data?.id) {
                const infoRes = await NewProjectAction.getProjectInfo(res.data?.id, newProjectService, webview);
                if (infoRes?.code === 200) {
                    saveTemplateDownloadInfo.projectInfo = infoRes?.data || null;
                    project = {
                        ...infoRes?.data
                    }
                }
            }
            NewProjectAction.openWindow({ project, commandService, fileService, newProjectService, workspaceTrustManagementService, hostService, hasWorkspace, webview, disposables, auxiliaryWindow, storageService })
        } catch (error) {
            console.log(error)
        }
    }

    private static async handleCreateProject(
        message: any,
        {
            notificationService,
            fileService,
            hostService,
            newProjectService,
            workspaceTrustManagementService,
            webview,
            hasWorkspace,
            disposables,
            auxiliaryWindow,
            commandService,
            storageService
        }: {
            notificationService: INotificationService,
            fileService: IFileService,
            hostService: IHostService,
            newProjectService: INewProjectService,
            workspaceTrustManagementService: IWorkspaceTrustManagementService,
            webview: any,
            hasWorkspace: boolean,
            disposables: DisposableStore,
            auxiliaryWindow: any,
            commandService: ICommandService,
            storageService: IStorageService
        }
    ) {
        if (!message.data || typeof message.data !== 'object') {
            return;
        }

        try {
            await NewProjectAction.createProjectAndMaybeOpenWindow({
                message,
                hasWorkspace,
                fileService,
                newProjectService,
                notificationService,
                workspaceTrustManagementService,
                hostService,
                webview,
                disposables,
                auxiliaryWindow,
                commandService,
                storageService
            });
        } catch (error) {
            const localizations = newProjectLocalizations;
            const errorMessage = localizations?.failedToOpenNewWindow || "Failed to open new window: {0}";
            notificationService.error(errorMessage.replace('{0}', error.message));
            NewProjectAction.clearResources(webview, disposables, auxiliaryWindow);
        }
    }

    //  如果创建返回的status 是Pending的话，需要轮询，直到status的值是running
    private static async getProjectInfo(id: number, newProjectService: INewProjectService, webview: any): Promise<IRes<INewProjectResult> | null> {
        const startTime = Date.now();
        const timeout = 60 * 1000 * 2; // 180秒超时

        while (Date.now() - startTime < timeout) {
            const result = await newProjectService.getProjectInfo(id);

            if (!result) {
                throw new Error(newProjectLocalizations.failedToGetProjectInfo);
            }

            // 如果状态是running，直接返回结果
            if (result?.data?.status === 'Running') {
                return result;
            }

            // 如果状态不是Pending，说明出错了 Pending
            if (result?.data?.status !== 'Pending') {
                throw new Error(newProjectLocalizations.projectStatusError.replace('{0}', result?.data?.status || 'unknown'));
            }

            // 等待1秒后继续轮询
            await new Promise(resolve => setTimeout(resolve, 500));
        }

        webview.postMessage({ type: 'error', message: newProjectLocalizations.timeoutRetrievingProjectInfo });
        webview.postMessage({ type: 'loading', loading: false });
        webview.postMessage({ type: 'retry' });
        throw new Error(newProjectLocalizations.timeoutRetrievingProjectInfo);
    }

    private static async retryProject({
        hasWorkspace,
        fileService,
        newProjectService,
        workspaceTrustManagementService,
        hostService,
        webview,
        disposables,
        auxiliaryWindow,
        commandService,
        storageService
    }: {
        hasWorkspace: boolean;
        fileService: IFileService;
        newProjectService: INewProjectService;
        workspaceTrustManagementService: IWorkspaceTrustManagementService;
        hostService: IHostService;
        webview: any;
        disposables: DisposableStore;
        auxiliaryWindow: any;
        commandService: ICommandService;
        storageService: IStorageService;
    }) {
        let project: any;
        if (NewProjectAction.createProjectParams && NewProjectAction.createProjectResultInfo) {
            const projectId = NewProjectAction.createProjectResultInfo.id;

            const infoRes = await NewProjectAction.getProjectInfo(projectId, newProjectService, webview);
            if (infoRes?.code === 200) {
                NewProjectAction.saveTemplateDownloadInfo.projectInfo = infoRes?.data || null;
                project = {
                    ...infoRes?.data
                }
            }
            NewProjectAction.openWindow({ project, commandService, fileService, newProjectService, workspaceTrustManagementService, hostService, hasWorkspace, webview, disposables, auxiliaryWindow, storageService })

        } else {
            webview.postMessage({ type: 'loading', loading: false });
        }
    }

    private static async openWindow({
        project,
        commandService,
        fileService,
        newProjectService,
        workspaceTrustManagementService,
        hostService,
        hasWorkspace,
        webview,
        disposables,
        auxiliaryWindow,
        storageService
    }: {
        project: any;
        commandService: ICommandService;
        fileService: IFileService;
        newProjectService: INewProjectService;
        workspaceTrustManagementService: IWorkspaceTrustManagementService;
        hostService: IHostService;
        hasWorkspace: boolean;
        webview: any;
        disposables: DisposableStore;
        auxiliaryWindow: any;
        storageService: IStorageService;
    }) {
        // 在打开新窗口之前持久化存储项目列表
        await NewProjectAction.persistNewProjectList(storageService, newProjectService);

        // 刷新列表
        try {
            commandService.executeCommand(CLOUDDEV_REFRESH);
        } catch (error) {
            console.log(error)
        }
        if (NewProjectAction.saveTemplateDownloadInfo && NewProjectAction.createProjectParams) {
            const { devMode = 0, projectLocation, templateInfo } = NewProjectAction.createProjectParams;
            const { projectInfo } = NewProjectAction.saveTemplateDownloadInfo;

            const projectId = projectInfo?.id;
            const projectName = projectInfo?.name;

            if (!projectId) return;
            // 本地
            if (devMode === 2 && projectLocation) {
                const projectFolderPath = URI.file(projectLocation);
                if (!await fileService.exists(projectFolderPath)) {
                    await fileService.createFolder(projectFolderPath);
                }

                const windowOpenOptions = {
                    forceNewWindow: hasWorkspace,
                    forceReuseWindow: !hasWorkspace,
                };

                if (templateInfo && templateInfo.downloadUrl) {
                    await newProjectService.saveTemplateDownloadInfo(NewProjectAction.saveTemplateDownloadInfo);
                }

                try {
                    if (workspaceTrustManagementService) {
                        await workspaceTrustManagementService.setUrisTrust([projectFolderPath], true);
                    }
                } catch (trustError) {
                    console.error(newProjectLocalizations.addingToTrustedListError, trustError);
                }

                NewProjectAction.clearResources(webview, disposables, auxiliaryWindow);
                const uriWithProjectId = projectFolderPath.with({ query: `projectId=${projectId}` });
                await hostService.openWindow([{ folderUri: uriWithProjectId }], windowOpenOptions);
            } else if (devMode === 1) {
                NewProjectAction.saveTemplateDownloadInfo.type = 'remote';
                const sshConfigRes = await newProjectService.getProjectSSHConfig(projectId);
                if (sshConfigRes?.code === 200) {
                    project = {
                        ...project,
                        sshConfig: {
                            ...sshConfigRes?.data
                        }
                    }
                    const remotePath = '/config/workspace';
                    const remoteHost = sshConfigRes?.data?.host;
                    const sshHostLabel = `${projectName}_${sshConfigRes?.data?.projectId}`;
                    const remoteAuthority = `ssh-remote+${sshHostLabel}`;
                    const uri = URI.parse(`vscode-remote://${remoteAuthority}${remotePath}`);
                    NewProjectAction.saveTemplateDownloadInfo.remoteHost = remoteHost
                    NewProjectAction.saveTemplateDownloadInfo.targetPath = remotePath.replace(/^~/, process.env.HOME || '/config') as string;
                    NewProjectAction.saveTemplateDownloadInfo.sshConfigRes = sshConfigRes;
                    NewProjectAction.saveTemplateDownloadInfo.sshHostLabel = sshHostLabel;
                    NewProjectAction.saveTemplateDownloadInfo.remotePath = remotePath;
                    if (templateInfo && templateInfo.downloadUrl) {
                        await newProjectService.saveTemplateDownloadInfo(NewProjectAction.saveTemplateDownloadInfo);
                    }

                    try {
                        if (workspaceTrustManagementService) {
                            await workspaceTrustManagementService.setUrisTrust([uri], true);
                        }
                    } catch (trustError) {
                        console.error(newProjectLocalizations.addingRemoteToTrustedListError, trustError);
                    }

                    try {
                        const timer3 = setTimeout(() => {
                            NewProjectAction.clearResources(webview, disposables, auxiliaryWindow);
                            commandService.executeCommand(CONNECT_REMOTESSH, {
                                sshHost: sshConfigRes?.data?.host,
                                sshUser: sshConfigRes?.data?.user,
                                sshPort: sshConfigRes?.data?.port,
                                base64PrivateKey: sshConfigRes?.data?.privateKey,
                                sshHostLabel,
                                workingDir: remotePath,
                                forceNewWindow: hasWorkspace,
                            });
                            clearTimeout(timer3)
                        }, 1000)
                    } catch (error) {
                        console.warn(newProjectLocalizations.commandNotExists.replace('{0}', CONNECT_REMOTESSH));
                    }
                }
            }
        }
    }

    private static clearResources(webview: any, disposables: DisposableStore, auxiliaryWindow: any) {
        // 恢复界面状态（如果出错）
        webview.postMessage({ type: 'loading', loading: false });
        // // 清理资源
        disposables.dispose();

        // 关闭窗口
        auxiliaryWindow.dispose();
        // 清除窗口引用
        (NewProjectAction as any).currentNewProjectWindow = null;
    }

    private static async getUserHomePath(pathService: IPathService): Promise<string> {
        const userHomeUri = await pathService.userHome({ preferLocal: true });
        return userHomeUri?.fsPath || '';
    }

    private static saveUserPath(path: string, storageService: any) {
        if (!path || !storageService) return;
        storageService.store(LOCAL_PROJECT_PATH, path, StorageScope.APPLICATION);
    }

    private static getUserPath(storageService: any) {
        if (!storageService) return;
        const path = storageService.get(LOCAL_PROJECT_PATH, StorageScope.APPLICATION, '');
        return path;
    }


    private static async getUserContext(loginService: ILoginService, storageService: IStorageService) {
        const context: any = {
            loginInfo: null,
            userConfig: null,
            isLoggedIn: false,
            permissions: {
                hasRemoteAccess: false
            }
        };

        try {
            context.loginInfo = await loginService.getLoginInfo();
            context.isLoggedIn = await loginService.isLoggedIn();

            const resources = context.loginInfo?.resources || {};
            context.permissions.hasRemoteAccess = NewProjectAction.checkRemotePermission(resources);

        } catch (error) {
            console.error('Failed to get user context:', error);
        }
        return context;
    }

    /**
     * 检查用户是否有远程访问权限
     * @param resources 用户资源权限
     * @returns 是否有远程访问权限
     */
    private static checkRemotePermission(resources: any): boolean {
        try {
            if (!resources || typeof resources !== 'object') {
                return false;
            }

            if (Object.keys(resources).length === 0) {
                return false;
            }

            // 检查 project:create 权限中是否包含 remote
            const projectCreatePermission = resources['project:create'];
            if (Array.isArray(projectCreatePermission)) {
                const hasRemote = projectCreatePermission.includes('remote');
                return hasRemote;
            }

            // 如果是字符串形式，检查是否包含 remote
            if (typeof projectCreatePermission === 'string') {
                const hasRemote = projectCreatePermission.includes('remote');
                return hasRemote;
            }
            return false;
        } catch (error) {
            return false;
        }
    }

    private static getStoredProjectList(storageService: IStorageService): INewProjectResult[] {
        try {
            const storedProjectListStr = storageService.get(NEW_PROJECT_LIST_STORAGE_KEY, StorageScope.APPLICATION, '[]');
            return JSON.parse(storedProjectListStr);
        } catch (parseError) {
            console.warn(newProjectLocalizations.parseStoredProjectListError, parseError);
            return [];
        }
    }

    private static async persistNewProjectList(storageService: IStorageService, newProjectService: INewProjectService): Promise<void> {
        try {
            let currentProjectList: INewProjectResult[] = NewProjectAction.getStoredProjectList(storageService);

            const templateDownloadInfo = newProjectService.getTemplateDownloadInfo() as ISaveTemplateDownloadInfo | undefined;
            let newProject: INewProjectResult | null = null;

            if (templateDownloadInfo?.projectInfo) {
                newProject = templateDownloadInfo.projectInfo;
            } else if (NewProjectAction.createProjectResultInfo) {
                newProject = NewProjectAction.createProjectResultInfo;
            }

            if (newProject) {
                const existingIndex = currentProjectList.findIndex(p => p.id === newProject!.id);

                if (existingIndex >= 0) {
                    // 更新现有项目信息
                    currentProjectList[existingIndex] = { ...currentProjectList[existingIndex], ...newProject };
                } else {
                    // 添加新项目到列表开头
                    currentProjectList.unshift(newProject);
                }

                // 存储更新后的项目列表
                storageService.store(NEW_PROJECT_LIST_STORAGE_KEY, JSON.stringify(currentProjectList), StorageScope.APPLICATION, StorageTarget.MACHINE);

                console.log(newProjectLocalizations.projectListPersisted
                    .replace('{0}', currentProjectList.length.toString())
                    .replace('{1}', newProject.name));
            } else {
                console.warn(newProjectLocalizations.noNewProjectInfoFound);
            }
        } catch (error) {
            console.error(newProjectLocalizations.persistProjectListError, error);
        }
    }

    private static async generateUniqueProjectName(
        projectList: any[],
        userPath: string,
        newProjectService: INewProjectService
    ): Promise<string> {
        const baseName = 'project';
        let counter = 0;
        let projectName = baseName;

        function isNameConflictedInProjectList(name: string): boolean {
            return projectList.some(project =>
                project.name && project.name.toLowerCase() === name.toLowerCase()
            );
        }

        // 检查项目目录是否存在（仅在有用户路径时检查）
        async function isDirectoryExists(name: string): Promise<boolean> {
            if (!userPath) return false;
            const fullPath = `${userPath}/${name}`;
            try {
                return await newProjectService.checkProjectExists(fullPath);
            } catch (error) {
                console.error(newProjectLocalizations.checkDirectoryExistsError, error);
                return false;
            }
        }

        // 生成不与项目列表和目录冲突的项目名称
        while (true) {
            // 检查项目列表冲突
            const nameConflicted = isNameConflictedInProjectList(projectName);

            // 检查目录冲突
            const directoryExists = await isDirectoryExists(projectName);

            // 如果都没有冲突，则使用这个名称
            if (!nameConflicted && !directoryExists) {
                break;
            }

            // 有冲突，继续自增
            counter++;
            projectName = baseName + counter;

            // 安全机制，防止无限循环
            if (counter > 1000) {
                console.warn(newProjectLocalizations.projectNameIncrementLimit);
                break;
            }
        }

        return projectName;
    }

    constructor() {
        super({
            id: NEW_PROJECT_COMMAND_ID,
            title: {
                value: localize('joycode.newProject.newProject', "New Project"),
                original: 'New Project'
            },
            category: Categories.File,
            f1: true,
            // 添加前置条件：只有在已登录状态下才启用
            precondition: ContextKeyExpr.equals(NOT_LOGGED_IN_KEY, false)
        });
    }

    async run(accessor: ServicesAccessor): Promise<void> {
        // 重置接口失败状态（新窗口开始时重置）
        interfaceFailureState = false;

        // 获取服务
        const fileDialogService = accessor.get(IFileDialogService);
        const notificationService = accessor.get(INotificationService);
        const auxiliaryWindowService = accessor.get(IAuxiliaryWindowService);
        const fileService = accessor.get(IFileService);
        const webviewService = accessor.get(IWebviewService);
        const hostService = accessor.get(IHostService);
        const workspaceContextService = accessor.get(IWorkspaceContextService);
        const newProjectService = accessor.get(INewProjectService);
        const workspaceTrustManagementService = accessor.get(IWorkspaceTrustManagementService);
        const commandService = accessor.get(ICommandService);
        const pathService = accessor.get(IPathService);
        const storageService = accessor.get(IStorageService);
        const loginService = accessor.get(ILoginService);

        // 获取用户信息的完整方案
        const userContext = await NewProjectAction.getUserContext(loginService, storageService);

        // 如果已经存在新建项目窗口，则聚焦到该窗口并返回
        if (NewProjectAction.currentNewProjectWindow) {
            try {
                // 检查窗口是否仍然存在（可能已被关闭）
                if (NewProjectAction.currentNewProjectWindow.window &&
                    !NewProjectAction.currentNewProjectWindow.window.closed) {
                    // 聚焦到已存在的窗口
                    NewProjectAction.currentNewProjectWindow.focus();

                    return;
                } else {
                    // 窗口已关闭，重置引用
                    NewProjectAction.currentNewProjectWindow = null;
                }
            } catch (e) {
                // 如果访问窗口属性出错，说明窗口可能已关闭
                NewProjectAction.currentNewProjectWindow = null;
            }
        }

        // 检查当前窗口是否有工作目录
        const hasWorkspace = !!workspaceContextService.getWorkspace().folders.length;
        // const nativeHostService = accessor.get(INativeHostService);
        try {
            // 设置固定窗口大小
            const width = 840;
            const height = 600;

            // 获取当前窗口信息
            const mainWin = window;

            // 检测是否处于全屏模式
            // 使用VS Code内部的isFullscreen函数或检查document.fullscreenElement
            const isFullScreen = document.fullscreenElement !== null ||
                (mainWin.screen.width === mainWin.outerWidth &&
                    mainWin.screen.height === mainWin.outerHeight);

            let x, y;

            // 获取当前窗口的位置和尺寸
            const mainX = mainWin.screenX;
            const mainY = mainWin.screenY;
            const mainWidth = mainWin.outerWidth;
            const mainHeight = mainWin.outerHeight;

            // 水平居中 - 相对于当前窗口
            x = mainX + Math.floor((mainWidth - width) / 2);

            // 垂直位置计算
            if (isFullScreen) {
                // 全屏模式下，使用固定的垂直偏移量
                const verticalOffset = 150; // 固定偏移量，可以根据需要调整
                y = mainY + verticalOffset;
            } else {
                // 非全屏模式下，使用居中逻辑
                y = mainY + Math.floor((mainHeight - height) / 2);
            }

            // 创建一个新窗口，并设置初始位置和固定大小
            const auxiliaryWindow = await auxiliaryWindowService.open({
                mode: AuxiliaryWindowMode.Normal,
                bounds: {
                    width: width,
                    height,
                    x: x,
                    y: y
                },
                nativeTitlebar: true, // 使用原生标题栏，确保窗口可以拖动
                disableFullscreen: true // 禁用全屏功能，确保窗口保持固定大小
            });

            // 保存窗口引用
            NewProjectAction.currentNewProjectWindow = {
                window: auxiliaryWindow.window,
                focus: () => {
                    if (auxiliaryWindow.window) {
                        // 尝试使用多种方法确保窗口前置
                        try {
                            // 方法1: 使用标准focus方法
                            auxiliaryWindow.window.focus();

                            // 方法2: 尝试使用其他方法前置窗口
                            if (auxiliaryWindow.window.parent) {
                                auxiliaryWindow.window.parent.focus();
                            }

                            // 方法3: 尝试使用hostService前置窗口
                            hostService.focus(auxiliaryWindow.window, { force: true });

                            // 方法4: 尝试闪烁窗口以引起注意
                            const flashWindow = () => {
                                try {
                                    // 临时修改标题以引起注意
                                    const originalTitle = auxiliaryWindow.window.document.title;
                                    auxiliaryWindow.window.document.title = "● " + originalTitle;

                                    // 500毫秒后恢复原标题
                                    setTimeout(() => {
                                        auxiliaryWindow.window.document.title = originalTitle;
                                    }, 500);
                                } catch (e) {
                                    console.error('Error while flashing window title:', e);
                                }
                            };

                            // 闪烁两次
                            flashWindow();
                            setTimeout(flashWindow, 1000);
                        } catch (e) {
                            console.error('Error while trying to focus window:', e);
                        }
                    }
                }
            };

            // 设置窗口标题
            const localizations = newProjectLocalizations;
            const windowTitle = localizations?.newProject || "New Project";
            auxiliaryWindow.window.document.title = windowTitle;

            // 获取容器元素
            const container = auxiliaryWindow.container;

            // 创建webview
            const webview = webviewService.createWebviewElement({
                providedViewType: 'newProject',
                title: windowTitle,
                options: {
                    purpose: WebviewContentPurpose.WebviewView,
                    enableFindWidget: false
                },
                contentOptions: {
                    allowScripts: true,
                    localResourceRoots: [URI.file(process.cwd())]
                },
                extension: undefined // 不需要扩展ID
            });

            // 使用FileAccess API获取正确的文件URI
            const htmlFileUri = FileAccess.asFileUri('vs/workbench/contrib/JoyCoder/browser/project/media/newProject.html');

            try {
                const htmlContent = await fileService.readFile(htmlFileUri);
                const htmlString = htmlContent.value.toString();

                // 处理HTML内容，注入国际化文本
                const processedHtml = NewProjectAction.injectLocalizationsToHtml(htmlString);

                // 设置HTML内容
                webview.setHtml(processedHtml);

                // 将webview挂载到窗口
                webview.mountTo(container, auxiliaryWindow.window);

                const disposables = new DisposableStore();

                // 处理webview消息
                disposables.add(webview.onMessage(async event => {
                    const message = event.message;

                    // 确保消息是一个对象并且有type属性
                    if (typeof message !== 'object' || !message || !('type' in message)) {
                        return;
                    }

                    switch (message.type) {
                        case 'retry':
                            // 重置接口失败状态
                            interfaceFailureState = false;
                            NewProjectAction.retryProject({
                                hasWorkspace,
                                fileService,
                                newProjectService,
                                workspaceTrustManagementService,
                                hostService,
                                webview,
                                disposables,
                                auxiliaryWindow,
                                commandService,
                                storageService
                            })
                            break;
                        case 'setLocalPath':
                            const localPath = message.localPath || '';
                            NewProjectAction.saveUserPath(localPath, storageService)
                            break;
                        case 'getUserPath':
                            let userPath = NewProjectAction.getUserPath(storageService);
                            userPath = userPath || await NewProjectAction.getUserHomePath(pathService);
                            webview.postMessage({ type: 'setUserPath', userPath });
                            break;
                        case 'getUserPermissions':
                            // 发送用户权限信息到前端
                            webview.postMessage({
                                type: 'setUserPermissions',
                                permissions: userContext.permissions
                            });
                            break;
                        // 请求模板数据
                        case 'requestTemplates':
                            // 获取查询参数
                            const query = message.query || '';
                            try {
                                // 根据查询参数获取模板列表
                                newProjectService.getTemplateList({ templateName: query })
                                    .then(templates => {
                                        // 发送模板数据到WebView
                                        webview.postMessage({
                                            type: 'setTemplates',
                                            templates: templates,
                                            query: query // 返回原始查询参数，方便WebView处理
                                        });
                                        NewProjectAction.templateList = templates;
                                    })
                                    .catch(error => {
                                        console.error(newProjectLocalizations.getTemplateListFailed, error);
                                    });
                            } catch (error) {
                                // 发送错误消息到WebView
                                webview.postMessage({
                                    type: 'templatesError',
                                    error: error.message || newProjectLocalizations.failedToFetchTemplateList,
                                    query: query
                                });
                            }
                            break;
                        // 获取数据库模版
                        case 'getProjectDatabaseList':
                            try {
                                newProjectService.getProjectDatabaseList()
                                    .then(list => {
                                        // 发送模板数据到WebView
                                        webview.postMessage({
                                            type: 'setDatabases',
                                            databases: list,
                                        });
                                    })
                                    .catch(error => {
                                        console.error(newProjectLocalizations.failedToFetchProjectList, error);
                                    });
                            } catch (error) {

                            }
                            break;
                        // 获取项目列表
                        case 'getProjectList':
                            try {
                                newProjectService.getProjectList()
                                    .then(list => {
                                        // 发送项目列表数据到WebView
                                        webview.postMessage({
                                            type: 'setProjectList',
                                            projectList: list,
                                        });
                                    })
                                    .catch(error => {
                                        console.error(newProjectLocalizations.failedToFetchProjectList, error);
                                    });
                            } catch (error) {
                                console.error(newProjectLocalizations.failedToFetchProjectList, error);
                            }
                            break;
                        // 选择文件
                        case 'browse':
                            const browseLocalizations = newProjectLocalizations;
                            const browseTitle = browseLocalizations?.selectProjectLocation || "Select Project Location";
                            const uri = await fileDialogService.showOpenDialog({
                                title: browseTitle,
                                canSelectFiles: false,
                                canSelectFolders: true,
                                canSelectMany: false,
                                availableFileSystems: ['file'] // 只允许本地文件系统
                            });

                            if (uri && uri.length > 0) {
                                webview.postMessage({ type: 'setLocation', location: uri[0].fsPath });
                            }
                            break;

                        // 点击创建项目
                        case 'create':
                            NewProjectAction.createProjectParams = message.data;
                            await NewProjectAction.handleCreateProject(message, {
                                notificationService,
                                fileService,
                                hostService,
                                newProjectService,
                                workspaceTrustManagementService,
                                webview,
                                hasWorkspace,
                                disposables,
                                auxiliaryWindow,
                                commandService,
                                storageService
                            });
                            break;

                        case 'cancel':
                            // 清理资源
                            disposables.dispose();

                            // 关闭窗口
                            auxiliaryWindow.dispose();
                            // 清除窗口引用
                            NewProjectAction.currentNewProjectWindow = null;
                            break;

                        case 'error':
                            if (typeof message.message === 'string') {
                                notificationService.warn(message.message);
                            }
                            break;

                        case 'debug':
                            if (typeof message.message === 'string') {
                                // console.log('[NewProject Debug]:', message.message);
                            }
                            break;

                        // 生成唯一的项目名称
                        case 'generateUniqueProjectName':
                            try {
                                const userPath = message.userPath || '';
                                const projectList = message.projectList || [];

                                const uniqueName = await NewProjectAction.generateUniqueProjectName(
                                    projectList,
                                    userPath,
                                    newProjectService
                                );

                                webview.postMessage({
                                    type: 'setUniqueProjectName',
                                    projectName: uniqueName
                                });
                            } catch (error) {
                                console.error(newProjectLocalizations.generateUniqueProjectNameFailed, error);
                                // 如果生成失败，使用默认名称
                                webview.postMessage({
                                    type: 'setUniqueProjectName',
                                    projectName: 'project'
                                });
                            }
                            break;

                        case 'checkProjectExists':
                            if (typeof message.path === 'string') {
                                // 如果接口已经失败，直接返回不检查项目列表
                                if (interfaceFailureState) {
                                    // 只检查本地路径是否存在
                                    const pathExists = await newProjectService.checkProjectExists(message.path);

                                    const response: any = {
                                        type: 'projectExistsResult',
                                        exists: pathExists,
                                        pathExists,
                                        nameExistsInProjectList: false,
                                        interfaceFailed: true // 标记接口失败状态
                                    };

                                    // 如果有额外的参数，一并传递回去
                                    if (message.projectName) response.projectName = message.projectName;
                                    if (message.baseName) response.baseName = message.baseName;
                                    if (message.hasOwnProperty('counter')) response.counter = message.counter;
                                    if (message.currentPath) response.currentPath = message.currentPath;

                                    webview.postMessage(response);
                                    break;
                                }

                                // 检查本地路径是否存在
                                const pathExists = await newProjectService.checkProjectExists(message.path);

                                // 检查项目列表中是否存在同名项目
                                let nameExistsInProjectList = false;
                                if (message.projectName) {
                                    try {
                                        const projectList = await newProjectService.getProjectList();
                                        nameExistsInProjectList = projectList.some(project =>
                                            project.name && project.name.toLowerCase() === message.projectName.toLowerCase()
                                        );
                                    } catch (error) {
                                        console.error(newProjectLocalizations.getProjectListFailed, error);
                                        // 设置接口失败状态
                                        interfaceFailureState = true;

                                        // 发送失败响应，只返回本地路径检查结果
                                        const response: any = {
                                            type: 'projectExistsResult',
                                            exists: pathExists,
                                            pathExists,
                                            nameExistsInProjectList: false,
                                            interfaceFailed: true
                                        };

                                        // 如果有额外的参数，一并传递回去
                                        if (message.projectName) response.projectName = message.projectName;
                                        if (message.baseName) response.baseName = message.baseName;
                                        if (message.hasOwnProperty('counter')) response.counter = message.counter;
                                        if (message.currentPath) response.currentPath = message.currentPath;

                                        webview.postMessage(response);
                                        break;
                                    }
                                }

                                // 如果路径存在或项目名称在列表中存在，则认为冲突
                                const exists = pathExists || nameExistsInProjectList;

                                const response: any = {
                                    type: 'projectExistsResult',
                                    exists,
                                    pathExists,
                                    nameExistsInProjectList
                                };

                                // 如果有额外的参数，一并传递回去
                                if (message.projectName) response.projectName = message.projectName;
                                if (message.baseName) response.baseName = message.baseName;
                                if (message.hasOwnProperty('counter')) response.counter = message.counter;
                                if (message.currentPath) response.currentPath = message.currentPath;

                                console.log('checkProjectExists:', response);
                                webview.postMessage(response);
                            }
                            break;
                    }
                }));
                // 窗口关闭时清理资源
                disposables.add(auxiliaryWindow.onUnload(() => {
                    disposables.dispose();
                    // 清除窗口引用
                    NewProjectAction.currentNewProjectWindow = null;
                }));
            } catch (error) {
                console.error('NewProjectAction:', newProjectLocalizations.readHtmlFileFailed, error);
                const errorLocalizations = newProjectLocalizations;
                const errorMessage = errorLocalizations?.htmlReadError || "Failed to read HTML file: {0}";
                notificationService.error(errorMessage.replace('{0}', error.message));

                // 关闭窗口
                auxiliaryWindow.dispose();
                // 清除窗口引用
                NewProjectAction.currentNewProjectWindow = null;
            }
        } catch (error) {
            console.error('NewProjectAction:', newProjectLocalizations.createOrShowWindowFailed, error);
            const finalLocalizations = newProjectLocalizations;
            const finalErrorMessage = finalLocalizations?.newProjectError || "Failed to create new project window: {0}";
            notificationService.error(finalErrorMessage.replace('{0}', error));
        }
    }
});

// 在File菜单中添加"New Project"选项
MenuRegistry.appendMenuItem(MenuId.MenubarNewMenu, {
    group: '1_new',
    command: {
        id: NEW_PROJECT_COMMAND_ID,
        title: localize('joycode.newProject.miNewProject', "Project..."),
        // 添加前置条件：只有在已登录状态下才启用
        precondition: ContextKeyExpr.equals(NOT_LOGGED_IN_KEY, false)
    },
    // 不添加when条件，确保菜单项始终显示
    order: 1 // 放在"New Text File"之后
});
