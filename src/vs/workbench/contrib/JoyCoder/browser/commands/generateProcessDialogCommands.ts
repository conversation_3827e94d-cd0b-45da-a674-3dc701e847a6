/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/
import { Action2, registerAction2 } from '../../../../../platform/actions/common/actions.js';
import { ServicesAccessor } from '../../../../../platform/instantiation/common/instantiation.js';
import { localize } from '../../../../../nls.js';
import { Categories } from '../../../../../platform/action/common/actionCommonCategories.js';
import { ContextKeyExpr } from '../../../../../platform/contextkey/common/contextkey.js';
import { NOT_LOGGED_IN_KEY } from '../constants/loginConstants.js';
import { ILogService } from '../../../../../platform/log/common/log.js';
import { ICommandService } from '../../../../../platform/commands/common/commands.js';

// 命令ID
export const SHOW_GENERATE_PROCESS_DIALOG_COMMAND_ID = 'workbench.action.showGenerateProcessDialog';
export const TEST_GENERATE_PROCESS_DIALOG_COMMAND_ID = 'workbench.action.testGenerateProcessDialog';

// 国际化文本管理
class GenerateProcessDialogLocalizations {
    private static _instance: GenerateProcessDialogLocalizations;
    private _localizations: any = {};

    private constructor() {
        this.initLanguageLocalizations();
    }

    public static getInstance(): GenerateProcessDialogLocalizations {
        if (!GenerateProcessDialogLocalizations._instance) {
            GenerateProcessDialogLocalizations._instance = new GenerateProcessDialogLocalizations();
        }
        return GenerateProcessDialogLocalizations._instance;
    }

    private initLanguageLocalizations() {
        // 延迟初始化翻译，确保语言包已经加载
        this._localizations = {
            dialogTitle: localize('joycode.generateProcessDialog.title', "Payment Reminder"),
            dialogMessage: localize('joycode.generateProcessDialog.message', "Your account balance is insufficient to continue the operation. Please recharge as soon as possible."),
            confirmButton: localize('joycode.generateProcessDialog.confirm', "Go to Recharge"),
            closeButton: localize('joycode.generateProcessDialog.close', "Close"),
            showGenerateProcessDialog: localize('joycode.generateProcessDialog.action', "Show Generate Process Dialog"),
            testGenerateProcessDialog: localize('joycode.generateProcessDialog.test', "Test Generate Process Dialog")
        };
    }

    public get localizations() {
        return this._localizations;
    }
}

// 生成过程对话框选项接口
export interface IGenerateProcessDialogOptions {
    /**
     * 确认
     */
    onConfirm: () => void;

    /**
     * 取消回调
     */
    onCancel?: () => void;
}

// 生成过程对话框结果接口
export interface IGenerateProcessDialogResult {
    /**
     * 是否确认
     */
    confirmed: boolean;

    /**
     * 是否自动启动机器
     */
    autoStart?: boolean;
}

/**
 * 显示生成过程确认对话框
 * @param options 对话框选项
 */
export function showGenerateProcessDialog(options: IGenerateProcessDialogOptions): void {
    const localizations = GenerateProcessDialogLocalizations.getInstance().localizations;

    // 1. 创建遮罩
    const mask = document.createElement('div');
    mask.className = 'joycoder-dialog-mask';

    // 2. 创建弹窗
    const dialog = document.createElement('div');
    dialog.className = 'joycoder-dialog';

    // 3. 创建内容
    const main = document.createElement('div');
    main.className = 'joycoder-dialog-main';

    // 标题区域
    const header = document.createElement('div');
    header.className = 'joycoder-dialog-header';

    // 左侧标题区域
    const titleArea = document.createElement('div');
    titleArea.className = 'joycoder-dialog-title-area';

    // 图标
    const icon = document.createElement('div');
    icon.className = 'joycoder-dialog-icon joycoder-dialog-icon-info';
    icon.textContent = '!'; // 感叹号图标

    // 标题文本
    const title = document.createElement('div');
    title.className = 'joycoder-dialog-title';
    title.textContent = localizations.dialogTitle;

    titleArea.appendChild(icon);
    titleArea.appendChild(title);

    // 关闭按钮
    const closeBtn = document.createElement('button');
    closeBtn.className = 'joycoder-dialog-close';
    closeBtn.textContent = '×';
    closeBtn.setAttribute('aria-label', localizations.closeButton);
    closeBtn.setAttribute('title', localizations.closeButton);

    header.appendChild(titleArea);
    header.appendChild(closeBtn);
    main.appendChild(header);

    // 内容区域
    const contentWrap = document.createElement('div');
    contentWrap.className = 'joycoder-dialog-content';

    // 消息
    const message = document.createElement('div');
    message.className = 'joycoder-dialog-message';
    message.textContent = localizations.dialogMessage;
    contentWrap.appendChild(message);

    main.appendChild(contentWrap);
    dialog.appendChild(main);

    // 按钮区
    const buttons = document.createElement('div');
    buttons.className = 'joycoder-dialog-buttons';

    const btnConfirm = document.createElement('button');
    btnConfirm.className = 'joycoder-dialog-btn joycoder-dialog-confirm';
    btnConfirm.textContent = localizations.confirmButton;

    buttons.appendChild(btnConfirm);
    dialog.appendChild(buttons);

    // 4. 挂载
    mask.appendChild(dialog);

    // 优先插入到带有 VSCode 主题变量的容器下，保证 CSS 变量生效
    const workbench = document.querySelector('.monaco-workbench');
    if (workbench) {
        workbench.appendChild(mask);
    } else {
        document.body.appendChild(mask);
    }

    // 5. 样式
    if (!document.getElementById('joycoder-generate-process-dialog-style')) {
        const style = document.createElement('style');
        style.id = 'joycoder-generate-process-dialog-style';
        style.textContent = `
            .joycoder-dialog-mask {
                position: fixed; left: 0; top: 0; right: 0; bottom: 0;
                background: var(--vscode-widget-shadow); z-index: 9999;
                display: flex; align-items: center; justify-content: center;
            }
            .joycoder-dialog {
                background: var(--vscode-editor-background);
                border-radius: 10px;
                box-shadow: 0 8px 32px rgba(0,0,0,0.25);
                color: var(--vscode-editor-foreground);
                min-width: 400px;
                max-width: 500px;
                padding: 24px;
                font-family: inherit;
                animation: joycoder-dialog-in 0.18s;
                display: flex;
                flex-direction: column;
            }
            @keyframes joycoder-dialog-in {
                from { opacity: 0; transform: scale(0.9); }
                to { opacity: 1; transform: scale(1); }
            }
            .joycoder-dialog-header {
                display: flex;
                align-items: center;
                justify-content: space-between;
                margin-bottom: 16px;
            }
            .joycoder-dialog-title-area {
                display: flex;
                align-items: center;
            }
            .joycoder-dialog-icon {
                width: 24px;
                height: 24px;
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
                font-weight: bold;
                font-size: 14px;
                margin-right: 12px;
                color: white;
            }
            .joycoder-dialog-icon-info {
                background: var(--vscode-notificationsInfoIcon-foreground, #007acc);
            }
            .joycoder-dialog-title {
                font-size: 16px;
                font-weight: 600;
                color: var(--vscode-editor-foreground);
            }
            .joycoder-dialog-content {
                margin-bottom: 20px;
            }
            .joycoder-dialog-message {
                font-size: 14px;
                line-height: 1.5;
                color: var(--vscode-editor-foreground);
                margin-bottom: 16px;
            }
            .joycoder-dialog-checkbox-row {
                display: flex;
                align-items: center;
                margin-top: 12px;
            }
            .joycoder-checkbox-wrapper {
                display: flex;
                align-items: center;
                cursor: pointer;
                user-select: none;
            }
            .joycoder-checkbox {
                margin-right: 8px;
                cursor: pointer;
            }
            .joycoder-checkbox-label {
                font-size: 14px;
                color: var(--vscode-editor-foreground);
                cursor: pointer;
            }
            .joycoder-dialog-buttons {
                display: flex;
                justify-content: flex-end;
                gap: 12px;
            }
            .joycoder-dialog-btn {
                padding: 8px 16px;
                border: none;
                border-radius: 4px;
                font-size: 14px;
                cursor: pointer;
                transition: background-color 0.2s;
                min-width: 80px;
            }
            .joycoder-dialog-cancel {
                background: var(--vscode-button-secondaryBackground);
                color: var(--vscode-button-secondaryForeground);
            }
            .joycoder-dialog-cancel:hover {
                background: var(--vscode-button-secondaryHoverBackground);
            }
            .joycoder-dialog-confirm {
                background: #ffffff;
                color: #333333;
                border: 1px solid #e0e0e0;
                border-radius: 8px;
                font-weight: 500;
                box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            }
            .joycoder-dialog-confirm:hover {
                background: #f5f5f5;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
            }
            .joycoder-dialog-confirm:active {
                background: #e8e8e8;
                transform: translateY(1px);
            }
            .joycoder-dialog-close {
                background: none;
                border: none;
                color: var(--vscode-editor-foreground);
                cursor: pointer;
                font-size: 20px;
                line-height: 1;
                padding: 4px;
                border-radius: 4px;
                width: 28px;
                height: 28px;
                display: flex;
                align-items: center;
                justify-content: center;
                transition: background-color 0.2s;
            }
            .joycoder-dialog-close:hover {
                background: var(--vscode-toolbar-hoverBackground);
            }
            .joycoder-dialog-close:active {
                background: var(--vscode-toolbar-activeBackground);
            }
        `;
        document.head.appendChild(style);
    }

    // 6. 事件处理
    function close() {
        const workbench = document.querySelector('.monaco-workbench');
        if (workbench && workbench.contains(mask)) {
            workbench.removeChild(mask);
        } else if (document.body.contains(mask)) {
            document.body.removeChild(mask);
        }

        // 移除样式
        const style = document.getElementById('joycoder-generate-process-dialog-style');
        if (style) {
            style.remove();
        }
    }

    btnConfirm.addEventListener('click', () => {
        close();
        options.onConfirm();
    });

    // 关闭按钮点击事件
    closeBtn.addEventListener('click', () => {
        close();
        options.onCancel?.();
    });

    dialog.addEventListener('click', e => e.stopPropagation());

    // 点击遮罩层关闭
    mask.addEventListener('click', () => {
        close();
        options.onCancel?.();
    });

    // ESC键关闭
    const escListener = (ev: KeyboardEvent) => {
        if (ev.key === 'Escape') {
            close();
            options.onCancel?.();
            document.removeEventListener('keydown', escListener);
        }
    };
    document.addEventListener('keydown', escListener);
}

// 注册显示生成过程对话框命令
registerAction2(class ShowGenerateProcessDialogAction extends Action2 {
    constructor() {
        super({
            id: SHOW_GENERATE_PROCESS_DIALOG_COMMAND_ID,
            title: {
                value: localize('joycode.generateProcessDialog.action', "Show Generate Process Dialog"),
                original: 'Show Generate Process Dialog'
            },
            category: Categories.View,
            f1: true,
            // 添加前置条件：只有在已登录状态下才启用
            precondition: ContextKeyExpr.equals(NOT_LOGGED_IN_KEY, false)
        });
    }

    async run(accessor: ServicesAccessor): Promise<IGenerateProcessDialogResult> {
        const logService = accessor.get(ILogService);

        logService.info('ShowGenerateProcessDialogAction: 显示用户欠费弹框');

        return new Promise<IGenerateProcessDialogResult>((resolve) => {
            showGenerateProcessDialog({
                onConfirm: () => {
                    logService.info(`ShowGenerateProcessDialogAction: 用户欠费去充值`);
                    resolve({
                        confirmed: true,
                    });
                },
                onCancel: () => {
                    logService.info('ShowGenerateProcessDialogAction: 用户关闭欠费弹框');
                    resolve({
                        confirmed: false
                    });
                }
            });
        });
    }
});

// 注册测试生成过程对话框命令
registerAction2(class TestGenerateProcessDialogAction extends Action2 {
    constructor() {
        super({
            id: TEST_GENERATE_PROCESS_DIALOG_COMMAND_ID,
            title: {
                value: localize('joycode.generateProcessDialog.test', "Test Generate Process Dialog"),
                original: 'Test Generate Process Dialog'
            },
            category: Categories.View,
            f1: true
        });
    }

    async run(accessor: ServicesAccessor): Promise<void> {
        const commandService = accessor.get(ICommandService);
        const logService = accessor.get(ILogService);

        logService.info('TestGenerateProcessDialogAction: 开始测试生成过程对话框');

        // 调用生成过程对话框命令
        const result = await commandService.executeCommand(SHOW_GENERATE_PROCESS_DIALOG_COMMAND_ID);

        logService.info(`TestGenerateProcessDialogAction: 测试完成，结果: ${JSON.stringify(result)}`);
    }
});
