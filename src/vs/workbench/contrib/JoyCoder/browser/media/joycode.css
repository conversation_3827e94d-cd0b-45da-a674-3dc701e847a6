/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

/**
 * JoyCode View Containers CSS
 * This file provides styling for JoyCode view containers registered in joycode.contribution.ts
 * Loads the JoyCode iconfont for custom icons.
 */

/* Load the JoyCode iconfont */
@font-face {
  font-family: "joycode-iconfont";
  /* // src/vs/workbench/browser/parts/editor/media/iconfont.ttf */
  src: url('../../../../browser/parts/editor/media/iconfont.ttf?t=1752672610201') format('truetype');
  /* src: url('../../../../../../../extensions/joycode-common/iconfont/iconfont.woff') format('woff'),
       url('../../../../../../../extensions/joycode-common/iconfont/iconfont.ttf') format('truetype'); */
  font-display: block;
}

/* Apply JoyCode font to JoyCode icons */
.codicon.codicon-clouddev-view-icon,
.codicon.codicon-ai-resource-view-icon {
  font-family: "joycode-iconfont" !important;
}
