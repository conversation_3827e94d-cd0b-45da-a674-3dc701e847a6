# 源文件示例说明

## 源文件要求

为了生成高质量的BMP图标文件，请准备以下源文件：

### 1. source-big.png (大图标源文件)
- **用途**: 生成安装向导的背景图
- **推荐尺寸**: 410x785 像素或更大
- **宽高比**: 约 1:1.9 (宽:高)
- **格式**: PNG (支持透明背景)
- **内容建议**:
  - JoyCoder的主要标志或品牌图像
  - 可以包含产品名称
  - 适合作为安装向导的侧边栏背景

### 2. source-small.png (小图标源文件)
- **用途**: 生成安装向导的小图标
- **推荐尺寸**: 120x120 像素或更大
- **宽高比**: 1:1 (正方形)
- **格式**: PNG (支持透明背景)
- **内容建议**:
  - JoyCoder的图标或Logo
  - 简洁明了，在小尺寸下仍然清晰可辨
  - 适合作为安装向导的标题图标
  - 正方形设计，避免在不同DPI下变形

## 设计建议

### 颜色方案
- 使用与JoyCoder品牌一致的颜色
- 考虑在不同背景下的可见性
- 避免过于复杂的渐变（在小尺寸下可能失真）

### 图像质量
- 使用矢量图形或高分辨率位图
- 确保在缩放后仍然清晰
- 避免过细的线条和文字（在小尺寸下可能模糊）

### 透明背景处理
- PNG文件可以使用透明背景
- 透明区域在转换为BMP时会变成白色
- 如果需要特定背景色，请在源文件中直接设置

## 文件放置

将准备好的源文件放置在 `resources/win32/` 目录下：

```
resources/win32/
├── source-big.png      # 大图标源文件
├── source-small.png    # 小图标源文件
├── generate-bmp.sh     # Linux/macOS脚本
├── generate-bmp.bat    # Windows脚本
├── generate-bmp.py     # Python脚本
└── README-BMP-Generator.md
```

## 生成流程

1. 准备源文件并放置在正确位置
2. 选择合适的生成脚本
3. 运行脚本生成BMP文件
4. 检查生成的文件质量
5. 如有需要，调整源文件并重新生成

## 质量检查

生成BMP文件后，请检查：

- [ ] 文件是否正确生成（14个BMP文件）
- [ ] 图像是否清晰，没有明显的失真
- [ ] 颜色是否正确，透明区域是否正确转换
- [ ] 不同DPI版本的图像是否保持一致性
- [ ] 文件大小是否合理

## 示例命令

```bash
# 使用Python脚本（推荐）
python generate-bmp.py

# 使用Bash脚本（Linux/macOS）
./generate-bmp.sh

# 使用批处理脚本（Windows）
generate-bmp.bat

# 指定自定义源文件
python generate-bmp.py my-logo-big.png my-logo-small.png
```

## 注意事项

1. **备份**: 脚本会自动备份现有的BMP文件
2. **覆盖**: 新生成的文件会覆盖同名的现有文件
3. **格式**: 只支持常见的图像格式作为源文件（PNG, JPG, BMP等）
4. **依赖**: 确保安装了必要的图像处理工具（ImageMagick或Pillow）
