#!/bin/bash

# JoyCoder BMP图标生成脚本
# 用于生成安装程序所需的不同DPI的BMP图标文件
#
# 使用方法:
# 1. 将源图像文件放在当前目录下，命名为 source-big.png 和 source-small.png
# 2. 运行脚本: ./generate-bmp.sh
# 3. 或者指定源文件: ./generate-bmp.sh custom-big.png custom-small.png

set -e  # 遇到错误时退出

# 颜色输出函数
print_info() {
    echo -e "\033[32m[INFO]\033[0m $1"
}

print_error() {
    echo -e "\033[31m[ERROR]\033[0m $1"
}

print_warning() {
    echo -e "\033[33m[WARNING]\033[0m $1"
}

# 检查ImageMagick是否安装
check_imagemagick() {
    if ! command -v convert &> /dev/null; then
        print_error "ImageMagick未安装，请先安装ImageMagick"
        print_info "macOS: brew install imagemagick"
        print_info "Ubuntu: sudo apt-get install imagemagick"
        print_info "Windows: 下载并安装 https://imagemagick.org/script/download.php#windows"
        exit 1
    fi
    print_info "ImageMagick已安装: $(convert -version | head -n1)"
}

# 生成大图标 (安装向导背景图)
generate_big_icons() {
    local source_file="$1"

    if [[ ! -f "$source_file" ]]; then
        print_error "源文件不存在: $source_file"
        return 1
    fi

    print_info "开始生成大图标 (安装向导背景图)..."

    # 大图标尺寸定义 (宽x高)
    declare -A big_sizes=(
        ["100"]="164x314"
        ["125"]="205x393"
        ["150"]="246x471"
        ["175"]="287x550"
        ["200"]="328x628"
        ["225"]="369x707"
        ["250"]="410x785"
    )

    for dpi in "${!big_sizes[@]}"; do
        local size="${big_sizes[$dpi]}"
        local output_file="inno-big-${dpi}.bmp"

        print_info "生成 ${output_file} (${size})"

        # 转换图像，保持宽高比，居中裁剪，保持透明度
        convert "$source_file" \
            -resize "${size}^" \
            -gravity center \
            -extent "$size" \
            -depth 24 \
            -compress None \
            "BMP3:$output_file"

        if [[ $? -eq 0 ]]; then
            print_info "✓ 成功生成: $output_file"
        else
            print_error "✗ 生成失败: $output_file"
        fi
    done
}

# 生成小图标 (安装向导小图标)
generate_small_icons() {
    local source_file="$1"

    if [[ ! -f "$source_file" ]]; then
        print_error "源文件不存在: $source_file"
        return 1
    fi

    print_info "开始生成小图标 (安装向导小图标)..."

    # 小图标尺寸定义 (宽x高) - 调整为更小的尺寸以适应安装向导
    declare -A small_sizes=(
        ["100"]="48x48"
        ["125"]="60x60"
        ["150"]="72x72"
        ["175"]="84x84"
        ["200"]="96x96"
        ["225"]="108x108"
        ["250"]="120x120"
    )

    for dpi in "${!small_sizes[@]}"; do
        local size="${small_sizes[$dpi]}"
        local output_file="inno-small-${dpi}.bmp"

        print_info "生成 ${output_file} (${size})"

        # 转换图像，保持宽高比，居中裁剪，保持透明度
        convert "$source_file" \
            -resize "${size}^" \
            -gravity center \
            -extent "$size" \
            -depth 24 \
            -compress None \
            "BMP3:$output_file"

        if [[ $? -eq 0 ]]; then
            print_info "✓ 成功生成: $output_file"
        else
            print_error "✗ 生成失败: $output_file"
        fi
    done
}

# 备份现有文件
backup_existing_files() {
    local backup_dir="backup_$(date +%Y%m%d_%H%M%S)"

    # 检查是否有现有的BMP文件
    if ls inno-*.bmp 1> /dev/null 2>&1; then
        print_info "发现现有BMP文件，创建备份..."
        mkdir -p "$backup_dir"
        mv inno-*.bmp "$backup_dir/"
        print_info "备份已保存到: $backup_dir"
    fi
}

# 验证生成的文件
verify_generated_files() {
    print_info "验证生成的文件..."

    local total_files=0
    local success_files=0

    for file in inno-*.bmp; do
        if [[ -f "$file" ]]; then
            total_files=$((total_files + 1))
            local file_info=$(file "$file")
            if [[ "$file_info" == *"PC bitmap"* ]]; then
                success_files=$((success_files + 1))
                local size=$(identify -format "%wx%h" "$file" 2>/dev/null || echo "unknown")
                print_info "✓ $file ($size)"
            else
                print_error "✗ $file (格式错误)"
            fi
        fi
    done

    print_info "验证完成: $success_files/$total_files 文件成功生成"
}

# 显示使用帮助
show_help() {
    echo "JoyCoder BMP图标生成脚本"
    echo ""
    echo "使用方法:"
    echo "  $0                                    # 使用默认源文件"
    echo "  $0 <big_source> <small_source>       # 指定源文件"
    echo "  $0 --help                            # 显示帮助"
    echo ""
    echo "默认源文件:"
    echo "  source-big.png    - 用于生成大图标 (推荐尺寸: 410x785 或更大)"
    echo "  source-small.png  - 用于生成小图标 (推荐尺寸: 138x145 或更大)"
    echo ""
    echo "生成的文件:"
    echo "  inno-big-*.bmp    - 安装向导背景图 (7个不同DPI)"
    echo "  inno-small-*.bmp  - 安装向导小图标 (7个不同DPI)"
    echo ""
    echo "注意事项:"
    echo "  - 需要安装 ImageMagick"
    echo "  - 源图像建议使用PNG格式，支持透明背景"
    echo "  - 生成的BMP文件为24位真彩色，无压缩"
}

# 主函数
main() {
    # 检查参数
    if [[ "$1" == "--help" || "$1" == "-h" ]]; then
        show_help
        exit 0
    fi

    # 检查依赖
    check_imagemagick

    # 确定源文件
    local big_source="${1:-source-big.png}"
    local small_source="${2:-source-small.png}"

    print_info "JoyCoder BMP图标生成脚本启动"
    print_info "大图标源文件: $big_source"
    print_info "小图标源文件: $small_source"

    # 检查源文件是否存在
    if [[ ! -f "$big_source" ]]; then
        print_error "大图标源文件不存在: $big_source"
        print_info "请提供源文件或将文件命名为 source-big.png"
        exit 1
    fi

    if [[ ! -f "$small_source" ]]; then
        print_error "小图标源文件不存在: $small_source"
        print_info "请提供源文件或将文件命名为 source-small.png"
        exit 1
    fi

    # 备份现有文件
    backup_existing_files

    # 生成图标
    generate_big_icons "$big_source"
    generate_small_icons "$small_source"

    # 验证生成的文件
    verify_generated_files

    print_info "BMP图标生成完成！"
}

# 运行主函数
main "$@"
