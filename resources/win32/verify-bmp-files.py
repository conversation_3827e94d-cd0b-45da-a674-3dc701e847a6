#!/usr/bin/env python3
"""
BMP文件验证脚本
用于验证生成的BMP文件是否符合Windows安装程序要求
"""

import sys
from pathlib import Path

try:
    from PIL import Image
except ImportError:
    print("[ERROR] 请安装Pillow库: pip install Pillow")
    sys.exit(1)


class Colors:
    GREEN = '\033[32m'
    RED = '\033[31m'
    YELLOW = '\033[33m'
    RESET = '\033[0m'
    BOLD = '\033[1m'


def print_info(message):
    print(f"{Colors.GREEN}[INFO]{Colors.RESET} {message}")


def print_error(message):
    print(f"{Colors.RED}[ERROR]{Colors.RESET} {message}")


def print_success(message):
    print(f"{Colors.GREEN}✓{Colors.RESET} {message}")


def print_failure(message):
    print(f"{Colors.RED}✗{Colors.RESET} {message}")


def verify_bmp_file(bmp_path):
    """验证单个BMP文件"""
    try:
        with Image.open(bmp_path) as img:
            # 检查基本信息
            if img.format != 'BMP':
                return False, f"格式错误: {img.format}"
            
            if img.mode != 'RGB':
                return False, f"颜色模式错误: {img.mode} (应为RGB)"
            
            # 检查是否有透明背景（应该没有）
            if 'transparency' in img.info:
                return False, "包含透明信息"
            
            return True, f"{img.width}x{img.height}, {img.mode}, {img.format}"
            
    except Exception as e:
        return False, f"读取失败: {str(e)}"


def main():
    """主函数"""
    print(f"{Colors.BOLD}BMP文件验证脚本{Colors.RESET}")
    print("=" * 40)
    
    # 期望的文件列表
    expected_files = [
        'inno-big-100.bmp',
        'inno-big-125.bmp', 
        'inno-big-150.bmp',
        'inno-big-175.bmp',
        'inno-big-200.bmp',
        'inno-big-225.bmp',
        'inno-big-250.bmp',
        'inno-small-100.bmp',
        'inno-small-125.bmp',
        'inno-small-150.bmp',
        'inno-small-175.bmp',
        'inno-small-200.bmp',
        'inno-small-225.bmp',
        'inno-small-250.bmp',
    ]
    
    # 期望的尺寸
    expected_sizes = {
        'inno-big-100.bmp': (164, 314),
        'inno-big-125.bmp': (205, 393),
        'inno-big-150.bmp': (246, 471),
        'inno-big-175.bmp': (287, 550),
        'inno-big-200.bmp': (328, 628),
        'inno-big-225.bmp': (369, 707),
        'inno-big-250.bmp': (410, 785),
        'inno-small-100.bmp': (55, 58),
        'inno-small-125.bmp': (69, 73),
        'inno-small-150.bmp': (83, 87),
        'inno-small-175.bmp': (96, 101),
        'inno-small-200.bmp': (110, 116),
        'inno-small-225.bmp': (124, 131),
        'inno-small-250.bmp': (138, 145),
    }
    
    success_count = 0
    total_count = len(expected_files)
    
    print_info("检查BMP文件...")
    
    for filename in expected_files:
        file_path = Path(filename)
        
        if not file_path.exists():
            print_failure(f"{filename} - 文件不存在")
            continue
        
        is_valid, info = verify_bmp_file(file_path)
        
        if is_valid:
            # 检查尺寸是否正确
            expected_size = expected_sizes[filename]
            with Image.open(file_path) as img:
                actual_size = (img.width, img.height)
                
            if actual_size == expected_size:
                print_success(f"{filename} - {info}")
                success_count += 1
            else:
                print_failure(f"{filename} - 尺寸错误: {actual_size} (期望: {expected_size})")
        else:
            print_failure(f"{filename} - {info}")
    
    print()
    print_info(f"验证结果: {success_count}/{total_count} 文件通过验证")
    
    if success_count == total_count:
        print_success("所有BMP文件验证通过！")
        print_info("文件已准备好用于Windows安装程序")
        return True
    else:
        print_error("部分文件验证失败")
        return False


if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
