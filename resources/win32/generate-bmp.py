#!/usr/bin/env python3
"""
JoyCoder BMP图标生成脚本 (Python版本)
用于生成安装程序所需的不同DPI的BMP图标文件

使用方法:
1. 安装依赖: pip install Pillow
2. 将源图像文件放在当前目录下，命名为 source-big.png 和 source-small.png
3. 运行脚本: python generate-bmp.py
4. 或者指定源文件: python generate-bmp.py custom-big.png custom-small.png
"""

import os
import sys
import shutil
import argparse
from datetime import datetime
from pathlib import Path

try:
    from PIL import Image, ImageOps
except ImportError:
    print("[ERROR] 请安装Pillow库: pip install Pillow")
    sys.exit(1)


class Colors:
    """终端颜色输出"""
    GREEN = '\033[32m'
    RED = '\033[31m'
    YELLOW = '\033[33m'
    BLUE = '\033[34m'
    RESET = '\033[0m'
    BOLD = '\033[1m'


def print_info(message):
    """打印信息"""
    print(f"{Colors.GREEN}[INFO]{Colors.RESET} {message}")


def print_error(message):
    """打印错误"""
    print(f"{Colors.RED}[ERROR]{Colors.RESET} {message}")


def print_warning(message):
    """打印警告"""
    print(f"{Colors.YELLOW}[WARNING]{Colors.RESET} {message}")


def print_success(message):
    """打印成功"""
    print(f"{Colors.GREEN}✓{Colors.RESET} {message}")


def print_failure(message):
    """打印失败"""
    print(f"{Colors.RED}✗{Colors.RESET} {message}")


def backup_existing_files():
    """备份现有的BMP文件"""
    bmp_files = list(Path('.').glob('inno-*.bmp'))

    if bmp_files:
        backup_dir = f"backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        print_info(f"发现现有BMP文件，创建备份...")

        Path(backup_dir).mkdir(exist_ok=True)

        for bmp_file in bmp_files:
            shutil.move(str(bmp_file), backup_dir)

        print_info(f"备份已保存到: {backup_dir}")


def resize_and_fit(image, target_size, is_big_icon=False):
    """调整图像大小并适配到目标尺寸"""
    target_width, target_height = target_size

    if is_big_icon:
        # 对于大图标，使用适配模式而不是裁剪，并添加白色背景
        # 计算缩放比例，确保图像完全显示
        img_ratio = image.width / image.height
        target_ratio = target_width / target_height

        if img_ratio > target_ratio:
            # 图像更宽，按宽度缩放
            new_width = target_width
            new_height = int(target_width / img_ratio)
        else:
            # 图像更高，按高度缩放
            new_height = target_height
            new_width = int(target_height * img_ratio)

        # 调整大小
        resized = image.resize((new_width, new_height), Image.Resampling.LANCZOS)

        # 创建白色背景
        result = Image.new('RGB', target_size, (255, 255, 255))

        # 计算居中位置
        x = (target_width - new_width) // 2
        y = (target_height - new_height) // 2

        # 如果原图有透明度，需要特殊处理
        if resized.mode == 'RGBA':
            result.paste(resized, (x, y), resized)
        else:
            result.paste(resized, (x, y))

        return result
    else:
        # 对于小图标，也使用适配模式并添加透明或白色背景
        img_ratio = image.width / image.height
        target_ratio = target_width / target_height

        if img_ratio > target_ratio:
            # 图像更宽，按宽度缩放
            new_width = target_width
            new_height = int(target_width / img_ratio)
        else:
            # 图像更高，按高度缩放
            new_height = target_height
            new_width = int(target_height * img_ratio)

        # 调整大小
        resized = image.resize((new_width, new_height), Image.Resampling.LANCZOS)

        # 创建白色背景（BMP格式不支持透明度）
        result = Image.new('RGB', target_size, (255, 255, 255))

        # 计算居中位置
        x = (target_width - new_width) // 2
        y = (target_height - new_height) // 2

        # 粘贴图像
        if resized.mode == 'RGBA':
            # 将RGBA图像粘贴到RGB背景上，使用alpha通道作为蒙版
            result.paste(resized, (x, y), resized)
        else:
            result.paste(resized, (x, y))

        return result


def generate_big_icons(source_file):
    """生成大图标 (安装向导背景图)"""
    if not Path(source_file).exists():
        print_error(f"源文件不存在: {source_file}")
        return False

    print_info("开始生成大图标 (安装向导背景图)...")

    # 大图标尺寸定义 (宽x高)
    big_sizes = {
        '100': (164, 314),
        '125': (205, 393),
        '150': (246, 471),
        '175': (287, 550),
        '200': (328, 628),
        '225': (369, 707),
        '250': (410, 785),
    }

    try:
        with Image.open(source_file) as source_image:
            # 保持原始图像模式，不添加背景色
            # 如果是RGBA模式，保持透明度信息
            if source_image.mode == 'RGBA':
                # 保持RGBA模式用于后续处理
                pass
            elif source_image.mode in ('LA', 'P'):
                # 转换为RGBA以保持透明度
                source_image = source_image.convert('RGBA')
            elif source_image.mode != 'RGB':
                source_image = source_image.convert('RGB')

            success_count = 0
            for dpi, size in big_sizes.items():
                output_file = f"inno-big-{dpi}.bmp"

                print_info(f"生成 {output_file} ({size[0]}x{size[1]})")

                try:
                    # 调整大小并适配（大图标使用适配模式）
                    processed_image = resize_and_fit(source_image, size, is_big_icon=True)

                    # 保存为BMP格式
                    processed_image.save(output_file, 'BMP')

                    print_success(f"成功生成: {output_file}")
                    success_count += 1

                except Exception as e:
                    print_failure(f"生成失败: {output_file} - {str(e)}")

            print_info(f"大图标生成完成: {success_count}/{len(big_sizes)} 个文件成功")
            return success_count == len(big_sizes)

    except Exception as e:
        print_error(f"打开源文件失败: {str(e)}")
        return False


def generate_small_icons(source_file):
    """生成小图标 (安装向导小图标)"""
    if not Path(source_file).exists():
        print_error(f"源文件不存在: {source_file}")
        return False

    print_info("开始生成小图标 (安装向导小图标)...")

    # 小图标尺寸定义 (宽x高) - 调整为更小的尺寸以适应安装向导
    small_sizes = {
        '100': (48, 48),
        '125': (60, 60),
        '150': (72, 72),
        '175': (84, 84),
        '200': (96, 96),
        '225': (108, 108),
        '250': (120, 120),
    }

    try:
        with Image.open(source_file) as source_image:
            # 保持原始图像模式，不添加背景色
            # 如果是RGBA模式，保持透明度信息
            if source_image.mode == 'RGBA':
                # 保持RGBA模式用于后续处理
                pass
            elif source_image.mode in ('LA', 'P'):
                # 转换为RGBA以保持透明度
                source_image = source_image.convert('RGBA')
            elif source_image.mode != 'RGB':
                source_image = source_image.convert('RGB')

            success_count = 0
            for dpi, size in small_sizes.items():
                output_file = f"inno-small-{dpi}.bmp"

                print_info(f"生成 {output_file} ({size[0]}x{size[1]})")

                try:
                    # 调整大小并裁剪（小图标使用裁剪模式）
                    processed_image = resize_and_fit(source_image, size, is_big_icon=False)

                    # 保存为BMP格式
                    processed_image.save(output_file, 'BMP')

                    print_success(f"成功生成: {output_file}")
                    success_count += 1

                except Exception as e:
                    print_failure(f"生成失败: {output_file} - {str(e)}")

            print_info(f"小图标生成完成: {success_count}/{len(small_sizes)} 个文件成功")
            return success_count == len(small_sizes)

    except Exception as e:
        print_error(f"打开源文件失败: {str(e)}")
        return False


def verify_generated_files():
    """验证生成的文件"""
    print_info("验证生成的文件...")

    bmp_files = list(Path('.').glob('inno-*.bmp'))
    total_files = len(bmp_files)
    success_files = 0

    for bmp_file in bmp_files:
        try:
            with Image.open(bmp_file) as img:
                if img.format == 'BMP':
                    success_files += 1
                    print_success(f"{bmp_file.name} ({img.width}x{img.height})")
                else:
                    print_failure(f"{bmp_file.name} (格式错误)")
        except Exception as e:
            print_failure(f"{bmp_file.name} (无法读取: {str(e)})")

    print_info(f"验证完成: {success_files}/{total_files} 文件成功生成")
    return success_files, total_files


def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description='JoyCoder BMP图标生成脚本',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例:
  python generate-bmp.py                           # 使用默认源文件
  python generate-bmp.py big.png small.png        # 指定源文件
  python generate-bmp.py --help                   # 显示帮助

默认源文件:
  source-big.png    - 用于生成大图标 (推荐尺寸: 410x785 或更大)
  source-small.png  - 用于生成小图标 (推荐尺寸: 120x120 或更大，正方形)

生成的文件:
  inno-big-*.bmp    - 安装向导背景图 (7个不同DPI)
  inno-small-*.bmp  - 安装向导小图标 (7个不同DPI)

注意事项:
  - 需要安装 Pillow 库: pip install Pillow
  - 源图像建议使用PNG格式，支持透明背景
  - 生成的BMP文件为24位真彩色，无压缩
        """
    )

    parser.add_argument('big_source', nargs='?', default='source-big.png',
                       help='大图标源文件 (默认: source-big.png)')
    parser.add_argument('small_source', nargs='?', default='source-small.png',
                       help='小图标源文件 (默认: source-small.png)')

    args = parser.parse_args()

    print(f"{Colors.BOLD}JoyCoder BMP图标生成脚本 (Python版本){Colors.RESET}")
    print("=" * 50)

    print_info(f"大图标源文件: {args.big_source}")
    print_info(f"小图标源文件: {args.small_source}")

    # 检查源文件是否存在
    if not Path(args.big_source).exists():
        print_error(f"大图标源文件不存在: {args.big_source}")
        print_info("请提供源文件或将文件命名为 source-big.png")
        sys.exit(1)

    if not Path(args.small_source).exists():
        print_error(f"小图标源文件不存在: {args.small_source}")
        print_info("请提供源文件或将文件命名为 source-small.png")
        sys.exit(1)

    # 备份现有文件
    backup_existing_files()

    # 生成图标
    big_success = generate_big_icons(args.big_source)
    small_success = generate_small_icons(args.small_source)

    # 验证生成的文件
    success_files, total_files = verify_generated_files()

    if big_success and small_success and success_files == total_files:
        print_info(f"{Colors.BOLD}BMP图标生成完成！{Colors.RESET}")
        sys.exit(0)
    else:
        print_error("部分文件生成失败，请检查错误信息")
        sys.exit(1)


if __name__ == '__main__':
    main()
