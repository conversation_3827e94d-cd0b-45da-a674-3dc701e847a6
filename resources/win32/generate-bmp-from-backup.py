#!/usr/bin/env python3
"""
JoyCoder BMP图标生成脚本 - 使用backup_resources中的PNG文件
用于从backup_resources目录中的特定尺寸PNG文件生成对应的BMP文件
"""

import os
import sys
import shutil
from datetime import datetime
from pathlib import Path

try:
    from PIL import Image
except ImportError:
    print("[ERROR] 请安装Pillow库: pip install Pillow")
    sys.exit(1)


class Colors:
    """终端颜色输出"""
    GREEN = '\033[32m'
    RED = '\033[31m'
    YELLOW = '\033[33m'
    BLUE = '\033[34m'
    RESET = '\033[0m'
    BOLD = '\033[1m'


def print_info(message):
    """打印信息"""
    print(f"{Colors.GREEN}[INFO]{Colors.RESET} {message}")


def print_error(message):
    """打印错误"""
    print(f"{Colors.RED}[ERROR]{Colors.RESET} {message}")


def print_success(message):
    """打印成功"""
    print(f"{Colors.GREEN}✓{Colors.RESET} {message}")


def print_failure(message):
    """打印失败"""
    print(f"{Colors.RED}✗{Colors.RESET} {message}")


def backup_existing_files():
    """备份现有的BMP文件"""
    bmp_files = list(Path('.').glob('inno-*.bmp'))
    
    if bmp_files:
        backup_dir = f"backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        print_info(f"发现现有BMP文件，创建备份...")
        
        Path(backup_dir).mkdir(exist_ok=True)
        
        for bmp_file in bmp_files:
            shutil.move(str(bmp_file), backup_dir)
        
        print_info(f"备份已保存到: {backup_dir}")


def convert_png_to_bmp(png_file, bmp_file):
    """将PNG文件转换为BMP文件，处理透明背景"""
    try:
        with Image.open(png_file) as image:
            # 转换为RGB模式（BMP不支持透明度）
            if image.mode in ('RGBA', 'LA'):
                # 创建白色背景
                background = Image.new('RGB', image.size, (255, 255, 255))
                if image.mode == 'RGBA':
                    background.paste(image, mask=image.split()[-1])
                else:
                    background.paste(image, mask=image.split()[-1])
                image = background
            elif image.mode != 'RGB':
                image = image.convert('RGB')
            
            # 保存为BMP格式
            image.save(bmp_file, 'BMP')
            return True
            
    except Exception as e:
        print_error(f"转换失败 {png_file} -> {bmp_file}: {str(e)}")
        return False


def main():
    """主函数"""
    print(f"{Colors.BOLD}JoyCoder BMP图标生成脚本 - 使用backup_resources{Colors.RESET}")
    print("=" * 60)
    
    backup_dir = Path('backup_resources')
    if not backup_dir.exists():
        print_error("backup_resources目录不存在")
        sys.exit(1)
    
    # 定义PNG文件到BMP文件的映射
    file_mappings = {
        # 大图标映射
        '164_314.png': 'inno-big-100.bmp',
        '205_393.png': 'inno-big-125.bmp',
        '246_471.png': 'inno-big-150.bmp',
        '287_550.png': 'inno-big-175.bmp',
        '328_628.png': 'inno-big-200.bmp',
        '369_707.png': 'inno-big-225.bmp',
        '410_785.png': 'inno-big-250.bmp',
        
        # 小图标映射
        '55_58.png': 'inno-small-100.bmp',
        '69_73.png': 'inno-small-125.bmp',
        '83_87.png': 'inno-small-150.bmp',
        '96_101.png': 'inno-small-175.bmp',
        '110_116.png': 'inno-small-200.bmp',
        '124_131.png': 'inno-small-225.bmp',
        '138_145.png': 'inno-small-250.bmp',
    }
    
    # 备份现有文件
    backup_existing_files()
    
    print_info("开始从backup_resources转换PNG文件为BMP...")
    
    success_count = 0
    total_count = len(file_mappings)
    
    for png_name, bmp_name in file_mappings.items():
        png_path = backup_dir / png_name
        bmp_path = Path(bmp_name)
        
        if not png_path.exists():
            print_failure(f"源文件不存在: {png_path}")
            continue
        
        print_info(f"转换 {png_name} -> {bmp_name}")
        
        if convert_png_to_bmp(png_path, bmp_path):
            print_success(f"成功生成: {bmp_name}")
            success_count += 1
        else:
            print_failure(f"生成失败: {bmp_name}")
    
    print_info(f"转换完成: {success_count}/{total_count} 个文件成功")
    
    # 验证生成的文件
    print_info("验证生成的文件...")
    
    bmp_files = list(Path('.').glob('inno-*.bmp'))
    verified_count = 0
    
    for bmp_file in sorted(bmp_files):
        try:
            with Image.open(bmp_file) as img:
                if img.format == 'BMP':
                    verified_count += 1
                    print_success(f"{bmp_file.name} ({img.width}x{img.height})")
                else:
                    print_failure(f"{bmp_file.name} (格式错误)")
        except Exception as e:
            print_failure(f"{bmp_file.name} (无法读取: {str(e)})")
    
    print_info(f"验证完成: {verified_count}/{len(bmp_files)} 文件验证成功")
    
    if success_count == total_count and verified_count == len(bmp_files):
        print_info(f"{Colors.BOLD}BMP图标生成完成！{Colors.RESET}")
        sys.exit(0)
    else:
        print_error("部分文件生成或验证失败，请检查错误信息")
        sys.exit(1)


if __name__ == '__main__':
    main()
