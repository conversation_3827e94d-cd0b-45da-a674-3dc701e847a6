@echo off
setlocal enabledelayedexpansion

REM JoyCoder BMP图标生成脚本 (Windows版本)
REM 用于生成安装程序所需的不同DPI的BMP图标文件
REM
REM 使用方法:
REM 1. 将源图像文件放在当前目录下，命名为 source-big.png 和 source-small.png
REM 2. 运行脚本: generate-bmp.bat
REM 3. 或者指定源文件: generate-bmp.bat custom-big.png custom-small.png

echo JoyCoder BMP图标生成脚本 (Windows版本)
echo ==========================================

REM 检查参数
if "%1"=="--help" goto :show_help
if "%1"=="-h" goto :show_help

REM 检查ImageMagick是否安装
where convert >nul 2>&1
if %errorlevel% neq 0 (
    echo [ERROR] ImageMagick未安装，请先安装ImageMagick
    echo [INFO] 下载地址: https://imagemagick.org/script/download.php#windows
    echo [INFO] 或使用 Chocolatey: choco install imagemagick
    pause
    exit /b 1
)

echo [INFO] ImageMagick已安装
for /f "tokens=*" %%i in ('convert -version ^| findstr /r "^Version"') do echo [INFO] %%i

REM 确定源文件
set "big_source=%1"
set "small_source=%2"
if "%big_source%"=="" set "big_source=source-big.png"
if "%small_source%"=="" set "small_source=source-small.png"

echo [INFO] 大图标源文件: %big_source%
echo [INFO] 小图标源文件: %small_source%

REM 检查源文件是否存在
if not exist "%big_source%" (
    echo [ERROR] 大图标源文件不存在: %big_source%
    echo [INFO] 请提供源文件或将文件命名为 source-big.png
    pause
    exit /b 1
)

if not exist "%small_source%" (
    echo [ERROR] 小图标源文件不存在: %small_source%
    echo [INFO] 请提供源文件或将文件命名为 source-small.png
    pause
    exit /b 1
)

REM 备份现有文件
set "backup_dir=backup_%date:~0,4%%date:~5,2%%date:~8,2%_%time:~0,2%%time:~3,2%%time:~6,2%"
set "backup_dir=%backup_dir: =0%"

if exist "inno-*.bmp" (
    echo [INFO] 发现现有BMP文件，创建备份...
    mkdir "%backup_dir%" 2>nul
    move inno-*.bmp "%backup_dir%\" >nul 2>&1
    echo [INFO] 备份已保存到: %backup_dir%
)

REM 生成大图标
echo [INFO] 开始生成大图标 (安装向导背景图)...

REM 大图标尺寸定义
set "big_100=164x314"
set "big_125=205x393"
set "big_150=246x471"
set "big_175=287x550"
set "big_200=328x628"
set "big_225=369x707"
set "big_250=410x785"

for %%d in (100 125 150 175 200 225 250) do (
    set "size=!big_%%d!"
    set "output_file=inno-big-%%d.bmp"

    echo [INFO] 生成 !output_file! (!size!)

    convert "%big_source%" -resize "!size!^" -gravity center -extent "!size!" -depth 24 -type TrueColor "!output_file!"

    if !errorlevel! equ 0 (
        echo [INFO] ✓ 成功生成: !output_file!
    ) else (
        echo [ERROR] ✗ 生成失败: !output_file!
    )
)

REM 生成小图标
echo [INFO] 开始生成小图标 (安装向导小图标)...

REM 小图标尺寸定义 - 调整为更小的尺寸以适应安装向导
set "small_100=48x48"
set "small_125=60x60"
set "small_150=72x72"
set "small_175=84x84"
set "small_200=96x96"
set "small_225=108x108"
set "small_250=120x120"

for %%d in (100 125 150 175 200 225 250) do (
    set "size=!small_%%d!"
    set "output_file=inno-small-%%d.bmp"

    echo [INFO] 生成 !output_file! (!size!)

    convert "%small_source%" -resize "!size!^" -gravity center -extent "!size!" -depth 24 -type TrueColor "!output_file!"

    if !errorlevel! equ 0 (
        echo [INFO] ✓ 成功生成: !output_file!
    ) else (
        echo [ERROR] ✗ 生成失败: !output_file!
    )
)

REM 验证生成的文件
echo [INFO] 验证生成的文件...
set "total_files=0"
set "success_files=0"

for %%f in (inno-*.bmp) do (
    if exist "%%f" (
        set /a total_files+=1

        REM 检查文件是否为有效的BMP文件
        file "%%f" | findstr /i "bitmap" >nul 2>&1
        if !errorlevel! equ 0 (
            set /a success_files+=1

            REM 获取文件尺寸
            for /f "tokens=*" %%s in ('identify -format "%%wx%%h" "%%f" 2^>nul') do set "file_size=%%s"
            if "!file_size!"=="" set "file_size=unknown"

            echo [INFO] ✓ %%f (!file_size!)
        ) else (
            echo [ERROR] ✗ %%f (格式错误)
        )
    )
)

echo [INFO] 验证完成: %success_files%/%total_files% 文件成功生成
echo [INFO] BMP图标生成完成！
pause
exit /b 0

:show_help
echo JoyCoder BMP图标生成脚本 (Windows版本)
echo.
echo 使用方法:
echo   %0                                    # 使用默认源文件
echo   %0 ^<big_source^> ^<small_source^>       # 指定源文件
echo   %0 --help                            # 显示帮助
echo.
echo 默认源文件:
echo   source-big.png    - 用于生成大图标 (推荐尺寸: 410x785 或更大)
echo   source-small.png  - 用于生成小图标 (推荐尺寸: 138x145 或更大)
echo.
echo 生成的文件:
echo   inno-big-*.bmp    - 安装向导背景图 (7个不同DPI)
echo   inno-small-*.bmp  - 安装向导小图标 (7个不同DPI)
echo.
echo 注意事项:
echo   - 需要安装 ImageMagick
echo   - 源图像建议使用PNG格式，支持透明背景
echo   - 生成的BMP文件为24位真彩色，无压缩
pause
exit /b 0
