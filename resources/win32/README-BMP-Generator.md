# JoyCoder BMP图标生成工具

这个目录包含了用于生成JoyCoder Windows安装程序所需BMP图标文件的脚本。

## 文件说明

- `generate-bmp.sh` - Linux/macOS Bash脚本
- `generate-bmp.bat` - Windows批处理脚本
- `generate-bmp.py` - Python跨平台脚本
- `README-BMP-Generator.md` - 本说明文件

## 生成的文件

脚本会生成以下BMP文件，用于不同DPI的安装向导：

### 大图标 (安装向导背景图)
- `inno-big-100.bmp` - 164x314 (100% DPI)
- `inno-big-125.bmp` - 205x393 (125% DPI)
- `inno-big-150.bmp` - 246x471 (150% DPI)
- `inno-big-175.bmp` - 287x550 (175% DPI)
- `inno-big-200.bmp` - 328x628 (200% DPI)
- `inno-big-225.bmp` - 369x707 (225% DPI)
- `inno-big-250.bmp` - 410x785 (250% DPI)

### 小图标 (安装向导小图标)
- `inno-small-100.bmp` - 48x48 (100% DPI)
- `inno-small-125.bmp` - 60x60 (125% DPI)
- `inno-small-150.bmp` - 72x72 (150% DPI)
- `inno-small-175.bmp` - 84x84 (175% DPI)
- `inno-small-200.bmp` - 96x96 (200% DPI)
- `inno-small-225.bmp` - 108x108 (225% DPI)
- `inno-small-250.bmp` - 120x120 (250% DPI)

## 使用方法

### 1. 准备源文件

在 `resources/win32` 目录下放置两个源图像文件：

- `source-big.png` - 用于生成大图标（推荐尺寸：410x785或更大）
- `source-small.png` - 用于生成小图标（推荐尺寸：138x145或更大）

**注意：**
- 建议使用PNG格式，支持透明背景
- 源图像应该有足够的分辨率以保证缩放后的质量
- 透明背景会被转换为白色背景（BMP不支持透明度）

### 2. 选择合适的脚本

#### Python脚本 (推荐，跨平台)

**安装依赖：**
```bash
pip install Pillow
```

**使用默认源文件：**
```bash
python generate-bmp.py
```

**指定源文件：**
```bash
python generate-bmp.py my-big-icon.png my-small-icon.png
```

**查看帮助：**
```bash
python generate-bmp.py --help
```

#### Linux/macOS Bash脚本

**安装依赖：**
```bash
# macOS
brew install imagemagick

# Ubuntu/Debian
sudo apt-get install imagemagick

# CentOS/RHEL
sudo yum install ImageMagick
```

**使用：**
```bash
# 给脚本执行权限
chmod +x generate-bmp.sh

# 使用默认源文件
./generate-bmp.sh

# 指定源文件
./generate-bmp.sh my-big-icon.png my-small-icon.png

# 查看帮助
./generate-bmp.sh --help
```

#### Windows批处理脚本

**安装依赖：**
1. 下载并安装 ImageMagick: https://imagemagick.org/script/download.php#windows
2. 或使用 Chocolatey: `choco install imagemagick`

**使用：**
```cmd
REM 使用默认源文件
generate-bmp.bat

REM 指定源文件
generate-bmp.bat my-big-icon.png my-small-icon.png

REM 查看帮助
generate-bmp.bat --help
```

## 功能特性

### 自动备份
- 脚本会自动备份现有的BMP文件到带时间戳的备份目录
- 避免意外覆盖现有文件

### 智能缩放
- 保持源图像的宽高比
- 使用居中裁剪确保目标尺寸
- 高质量的图像重采样

### 格式转换
- 自动处理透明背景（转换为白色背景）
- 生成24位真彩色BMP文件
- 无压缩，保证最佳质量

### 验证检查
- 自动验证生成的BMP文件
- 显示文件尺寸和格式信息
- 统计成功/失败的文件数量

## 故障排除

### 常见问题

1. **ImageMagick未安装**
   - 按照上述说明安装ImageMagick
   - 确保`convert`命令在PATH中

2. **Python Pillow未安装**
   - 运行 `pip install Pillow`
   - 如果使用虚拟环境，确保在正确的环境中安装

3. **源文件不存在**
   - 检查文件名和路径
   - 确保文件格式正确（推荐PNG）

4. **权限问题（Linux/macOS）**
   - 给脚本执行权限：`chmod +x generate-bmp.sh`
   - 确保对目录有写权限

5. **生成的图像质量不佳**
   - 使用更高分辨率的源图像
   - 检查源图像的宽高比是否合适

### 调试信息

所有脚本都提供详细的输出信息：
- 绿色 `[INFO]` - 一般信息
- 红色 `[ERROR]` - 错误信息
- 黄色 `[WARNING]` - 警告信息
- ✓ - 成功操作
- ✗ - 失败操作

## 技术细节

### 图像处理算法
1. **缩放**: 使用Lanczos重采样算法保证高质量
2. **裁剪**: 居中裁剪确保目标尺寸精确
3. **颜色转换**: RGBA → RGB，透明度转换为白色背景

### BMP格式规范
- 24位真彩色
- 无压缩
- Windows标准BMP格式
- 兼容Inno Setup安装程序

### 文件命名规范
- `inno-big-{dpi}.bmp` - 大图标
- `inno-small-{dpi}.bmp` - 小图标
- `{dpi}` 为 100, 125, 150, 175, 200, 225, 250

## 更新日志

- **v1.0** - 初始版本，支持基本的BMP生成功能
- 支持三种平台的脚本（Bash, Batch, Python）
- 自动备份和验证功能
- 智能图像处理和格式转换
