/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

import { constants, statSync } from 'fs';
import { additionalDeps } from './dep-lists';
import { SecureExec } from '../../lib/secureExec';

export function generatePackageDeps(files: string[]): Set<string>[] {
	const dependencies: Set<string>[] = files.map(file => calculatePackageDeps(file));
	const additionalDepsSet = new Set(additionalDeps);
	dependencies.push(additionalDepsSet);
	return dependencies;
}

// Based on https://source.chromium.org/chromium/chromium/src/+/main:chrome/installer/linux/rpm/calculate_package_deps.py.
function calculatePackageDeps(binaryPath: string): Set<string> {
	try {
		if (!(statSync(binaryPath).mode & constants.S_IXUSR)) {
			throw new Error(`Binary ${binaryPath} needs to have an executable bit set.`);
		}
	} catch (e) {
		// The package might not exist. Don't re-throw the error here.
		console.error('Tried to stat ' + binaryPath + ' but failed.');
	}

	// 注意：/usr/lib/rpm/find-requires 需要从标准输入读取文件路径
	// 我们使用 echo 命令来提供输入，然后通过管道传递给 find-requires
	let findRequiresOutput: string;
	try {
		// 使用 sh -c 来执行管道命令，但确保路径是安全的
		const safeBinaryPath = binaryPath.replace(/[;&|`$(){}[\]<>'"]/g, ''); // 清理路径中的危险字符
		findRequiresOutput = SecureExec.execSync('sh', ['-c', `echo "${safeBinaryPath}" | /usr/lib/rpm/find-requires`]);
	} catch (error) {
		throw new Error(`find-requires failed. Error: ${error.message}`);
	}

	const requires = new Set(findRequiresOutput.trimEnd().split('\n'));
	return requires;
}
