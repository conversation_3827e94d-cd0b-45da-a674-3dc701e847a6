"use strict";
/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.getVSCodeSysroot = getVSCodeSysroot;
exports.getChromiumSysroot = getChromiumSysroot;
// import { spawnSync, execSync } from 'child_process'; // 已替换为 SecureExec
const os_1 = require("os");
const fs_1 = __importDefault(require("fs"));
const https_1 = __importDefault(require("https"));
const path_1 = __importDefault(require("path"));
const crypto_1 = require("crypto");
const ansi_colors_1 = __importDefault(require("ansi-colors"));
const SecureExec = require('../../lib/secureExec');
// Based on https://source.chromium.org/chromium/chromium/src/+/main:build/linux/sysroot_scripts/install-sysroot.py.
const URL_PREFIX = 'https://msftelectronbuild.z5.web.core.windows.net';
const URL_PATH = 'sysroots/toolchain';
const REPO_ROOT = path_1.default.dirname(path_1.default.dirname(path_1.default.dirname(__dirname)));
/**
 * 安全地获取临时目录路径
 * @returns 验证后的安全临时目录路径
 */
function getSafeTmpDir() {
    const tmpDir = (0, os_1.tmpdir)();
    // 清理路径
    const sanitized = tmpDir
        .replace(/[<>"'&;|`$(){}[\]]/g, '') // 移除危险字符
        .replace(/\.\./g, '') // 防止路径遍历
        .trim();
    // 检查长度限制
    if (sanitized.length === 0 || sanitized.length > 500) {
        throw new Error('Invalid temporary directory path');
    }
    // 标准化路径
    const normalizedPath = path_1.default.resolve(sanitized);
    // 检查是否为合法的临时目录
    const allowedTmpDirs = [
        '/tmp',
        '/var/tmp'
    ];
    // 在 Windows 和 macOS 上，允许系统临时目录
    if (process.platform === 'win32') {
        allowedTmpDirs.push('C:\\Windows\\Temp', 'C:\\temp', 'C:\\tmp');
    }
    else if (process.platform === 'darwin') {
        allowedTmpDirs.push('/private/tmp', '/private/var/tmp');
    }
    const isAllowed = allowedTmpDirs.some(allowedDir => normalizedPath.startsWith(allowedDir)) || normalizedPath.includes('Temp'); // Windows 临时目录通常包含 Temp
    if (!isAllowed) {
        console.warn(`Warning: Using potentially unsafe temporary directory: ${normalizedPath}`);
    }
    return normalizedPath;
}
const ghApiHeaders = {
    Accept: 'application/vnd.github.v3+json',
    'User-Agent': 'VSCode Build',
};
console.log('process.env.GITHUB_TOKEN', process.env.GITHUB_TOKEN);
if (process.env.GITHUB_TOKEN) {
    ghApiHeaders.Authorization = 'Basic ' + Buffer.from(process.env.GITHUB_TOKEN).toString('base64');
}
const ghDownloadHeaders = {
    ...ghApiHeaders,
    Accept: 'application/octet-stream',
};
/**
 * 验证 sysroot 目录路径的安全性
 * @param sysrootDir
 * @returns
 */
function validateSysrootDir(sysrootDir) {
    if (!sysrootDir || typeof sysrootDir !== 'string') {
        return null;
    }
    // 清理路径
    const sanitized = sysrootDir
        .replace(/[<>"'&;|`$(){}[\]]/g, '') // 移除危险字符
        .replace(/\.\./g, '') // 防止路径遍历
        .trim();
    // 检查长度限制
    if (sanitized.length === 0 || sanitized.length > 500) {
        throw new Error('Invalid sysroot directory path');
    }
    // 确保路径是绝对路径或相对于安全目录
    const normalizedPath = path_1.default.resolve(sanitized);
    // 检查是否在允许的目录范围内
    const allowedPrefixes = [
        path_1.default.resolve(getSafeTmpDir()),
        path_1.default.resolve(REPO_ROOT),
        '/tmp',
        '/var/tmp'
    ];
    const isAllowed = allowedPrefixes.some(prefix => normalizedPath.startsWith(prefix));
    if (!isAllowed) {
        throw new Error(`Sysroot directory outside allowed paths: ${normalizedPath}`);
    }
    return normalizedPath;
}
function getElectronVersion() {
    const npmrc = fs_1.default.readFileSync(path_1.default.join(REPO_ROOT, '.npmrc'), 'utf8');
    const electronVersion = /^target="(.*)"$/m.exec(npmrc)[1];
    const msBuildId = /^ms_build_id="(.*)"$/m.exec(npmrc)[1];
    return { electronVersion, msBuildId };
}
function getSha(filename) {
    const hash = (0, crypto_1.createHash)('sha256');
    // Read file 1 MB at a time
    const fd = fs_1.default.openSync(filename, 'r');
    const buffer = Buffer.alloc(1024 * 1024);
    let position = 0;
    let bytesRead = 0;
    while ((bytesRead = fs_1.default.readSync(fd, buffer, 0, buffer.length, position)) === buffer.length) {
        hash.update(buffer);
        position += bytesRead;
    }
    hash.update(buffer.slice(0, bytesRead));
    return hash.digest('hex');
}
function getVSCodeSysrootChecksum(expectedName) {
    const checksums = fs_1.default.readFileSync(path_1.default.join(REPO_ROOT, 'build', 'checksums', 'vscode-sysroot.txt'), 'utf8');
    for (const line of checksums.split('\n')) {
        const [checksum, name] = line.split(/\s+/);
        if (name === expectedName) {
            return checksum;
        }
    }
    return undefined;
}
/*
 * Do not use the fetch implementation from build/lib/fetch as it relies on vinyl streams
 * and vinyl-fs breaks the symlinks in the compiler toolchain sysroot. We use the native
 * tar implementation for that reason.
 */
async function fetchUrl(options, retries = 10, retryDelay = 1000) {
    try {
        const controller = new AbortController();
        const timeout = setTimeout(() => controller.abort(), 30 * 1000);
        const version = '20240129-253798';
        try {
            const response = await fetch(`https://api.github.com/repos/Microsoft/vscode-linux-build-agent/releases/tags/v${version}`, {
                headers: ghApiHeaders,
                signal: controller.signal /* Typings issue with lib.dom.d.ts */
            });
            if (response.ok && (response.status >= 200 && response.status < 300)) {
                console.log(`Fetch completed: Status ${response.status}.`);
                const contents = Buffer.from(await response.arrayBuffer());
                const asset = JSON.parse(contents.toString()).assets.find((a) => a.name === options.assetName);
                if (!asset) {
                    throw new Error(`Could not find asset in release of Microsoft/vscode-linux-build-agent @ ${version}`);
                }
                console.log(`Found asset ${options.assetName} @ ${asset.url}.`);
                const assetResponse = await fetch(asset.url, {
                    headers: ghDownloadHeaders
                });
                if (assetResponse.ok && (assetResponse.status >= 200 && assetResponse.status < 300)) {
                    const assetContents = Buffer.from(await assetResponse.arrayBuffer());
                    console.log(`Fetched response body buffer: ${ansi_colors_1.default.magenta(`${assetContents.byteLength} bytes`)}`);
                    if (options.checksumSha256) {
                        const actualSHA256Checksum = (0, crypto_1.createHash)('sha256').update(assetContents).digest('hex');
                        if (actualSHA256Checksum !== options.checksumSha256) {
                            throw new Error(`Checksum mismatch for ${ansi_colors_1.default.cyan(asset.url)} (expected ${options.checksumSha256}, actual ${actualSHA256Checksum}))`);
                        }
                    }
                    console.log(`Verified SHA256 checksums match for ${ansi_colors_1.default.cyan(asset.url)}`);
                    // 使用安全的命令执行
                    SecureExec.execSync('tar', ['-xz', '-C', options.dest], { input: assetContents });
                    console.log(`Fetch complete!`);
                    return;
                }
                throw new Error(`Request ${ansi_colors_1.default.magenta(asset.url)} failed with status code: ${assetResponse.status}`);
            }
            throw new Error(`Request ${ansi_colors_1.default.magenta('https://api.github.com')} failed with status code: ${response.status}`);
        }
        finally {
            clearTimeout(timeout);
        }
    }
    catch (e) {
        if (retries > 0) {
            console.log(`Fetching failed: ${e}`);
            await new Promise(resolve => setTimeout(resolve, retryDelay));
            return fetchUrl(options, retries - 1, retryDelay);
        }
        throw e;
    }
}
async function getVSCodeSysroot(arch) {
    let expectedName;
    let triple;
    const prefix = process.env['VSCODE_SYSROOT_PREFIX'] ?? '-glibc-2.28';
    switch (arch) {
        case 'amd64':
            expectedName = `x86_64-linux-gnu${prefix}.tar.gz`;
            triple = 'x86_64-linux-gnu';
            break;
        case 'arm64':
            expectedName = `aarch64-linux-gnu${prefix}.tar.gz`;
            triple = 'aarch64-linux-gnu';
            break;
        case 'armhf':
            expectedName = `arm-rpi-linux-gnueabihf${prefix}.tar.gz`;
            triple = 'arm-rpi-linux-gnueabihf';
            break;
    }
    console.log(`Fetching ${expectedName} for ${triple}`);
    const checksumSha256 = getVSCodeSysrootChecksum(expectedName);
    if (!checksumSha256) {
        throw new Error(`Could not find checksum for ${expectedName}`);
    }
    const sysroot = validateSysrootDir(process.env['VSCODE_SYSROOT_DIR']) ?? path_1.default.join(getSafeTmpDir(), `vscode-${arch}-sysroot`);
    const stamp = path_1.default.join(sysroot, '.stamp');
    const result = `${sysroot}/${triple}/${triple}/sysroot`;
    if (fs_1.default.existsSync(stamp) && fs_1.default.readFileSync(stamp).toString() === expectedName) {
        return result;
    }
    console.log(`Installing ${arch} root image: ${sysroot}`);
    fs_1.default.rmSync(sysroot, { recursive: true, force: true });
    fs_1.default.mkdirSync(sysroot);
    await fetchUrl({
        checksumSha256,
        assetName: expectedName,
        dest: sysroot
    });
    fs_1.default.writeFileSync(stamp, expectedName);
    return result;
}
async function getChromiumSysroot(arch) {
    const sysrootJSONUrl = `https://raw.githubusercontent.com/electron/electron/v${getElectronVersion().electronVersion}/script/sysroots.json`;
    const safeTmpDir = validateSysrootDir(getSafeTmpDir()) ?? getSafeTmpDir();
    const sysrootDictLocation = path_1.default.join(safeTmpDir, 'sysroots.json');
    // 使用安全的命令执行
    try {
        SecureExec.execSync('curl', [sysrootJSONUrl, '-o', sysrootDictLocation]);
    }
    catch (error) {
        throw new Error('Cannot retrieve sysroots.json. Error: ' + error.message);
    }
    const sysrootInfo = require(sysrootDictLocation);
    const sysrootArch = `bullseye_${arch}`;
    const sysrootDict = sysrootInfo[sysrootArch];
    const tarballFilename = sysrootDict['Tarball'];
    const tarballSha = sysrootDict['Sha256Sum'];
    const sysroot = path_1.default.join(safeTmpDir, sysrootDict['SysrootDir']);
    const url = [URL_PREFIX, URL_PATH, tarballSha].join('/');
    const stamp = path_1.default.join(sysroot, '.stamp');
    if (fs_1.default.existsSync(stamp) && fs_1.default.readFileSync(stamp).toString() === url) {
        return sysroot;
    }
    console.log(`Installing Debian ${arch} root image: ${sysroot}`);
    fs_1.default.rmSync(sysroot, { recursive: true, force: true });
    fs_1.default.mkdirSync(sysroot);
    const tarball = path_1.default.join(sysroot, tarballFilename);
    console.log(`Downloading ${url}`);
    let downloadSuccess = false;
    for (let i = 0; i < 3 && !downloadSuccess; i++) {
        fs_1.default.writeFileSync(tarball, '');
        await new Promise((c) => {
            https_1.default.get(url, (res) => {
                res.on('data', (chunk) => {
                    fs_1.default.appendFileSync(tarball, chunk);
                });
                res.on('end', () => {
                    downloadSuccess = true;
                    c();
                });
            }).on('error', (err) => {
                console.error('Encountered an error during the download attempt: ' + err.message);
                c();
            });
        });
    }
    if (!downloadSuccess) {
        fs_1.default.rmSync(tarball);
        throw new Error('Failed to download ' + url);
    }
    const sha = getSha(tarball);
    if (sha !== tarballSha) {
        throw new Error(`Tarball sha1sum is wrong. Expected ${tarballSha}, actual ${sha}`);
    }
    // 使用安全的命令执行
    try {
        SecureExec.execSync('tar', ['xf', tarball, '-C', sysroot]);
    }
    catch (error) {
        throw new Error('Tarball extraction failed: ' + error.message);
    }
    fs_1.default.rmSync(tarball);
    fs_1.default.writeFileSync(stamp, url);
    return sysroot;
}
