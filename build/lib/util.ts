/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

import es from 'event-stream';
import _debounce from 'debounce';
import _filter from 'gulp-filter';
import rename from 'gulp-rename';
import path from 'path';
import fs from 'fs';
import _rimraf from 'rimraf';
const VinylFile = require('vinyl');
import { ThroughStream } from 'through';
import sm from 'source-map';
import { pathToFileURL } from 'url';
import ternaryStream from 'ternary-stream';

const root = path.dirname(path.dirname(__dirname));

export interface ICancellationToken {
	isCancellationRequested(): boolean;
}

const NoCancellationToken: ICancellationToken = { isCancellationRequested: () => false };

export interface IStreamProvider {
	(cancellationToken?: ICancellationToken): NodeJS.ReadWriteStream;
}

export function incremental(streamProvider: IStreamProvider, initial: NodeJS.ReadWriteStream, supportsCancellation?: boolean): NodeJS.ReadWriteStream {
	const input = es.through();
	const output = es.through();
	let state = 'idle';
	let buffer = Object.create(null);

	const token: ICancellationToken | undefined = !supportsCancellation ? undefined : { isCancellationRequested: () => Object.keys(buffer).length > 0 };

	const run = (input: NodeJS.ReadWriteStream, isCancellable: boolean) => {
		state = 'running';

		const stream = !supportsCancellation ? streamProvider() : streamProvider(isCancellable ? token : NoCancellationToken);

		input
			.pipe(stream)
			.pipe(es.through(undefined, () => {
				state = 'idle';
				eventuallyRun();
			}))
			.pipe(output);
	};

	if (initial) {
		run(initial, false);
	}

	const eventuallyRun = _debounce(() => {
		const paths = Object.keys(buffer);

		if (paths.length === 0) {
			return;
		}

		const data = paths.map(path => buffer[path]);
		buffer = Object.create(null);
		run(es.readArray(data), true);
	}, 500);

	input.on('data', (f: any) => {
		buffer[f.path] = f;

		if (state === 'idle') {
			eventuallyRun();
		}
	});

	return es.duplex(input, output);
}

export function debounce(task: () => NodeJS.ReadWriteStream, duration = 500): NodeJS.ReadWriteStream {
	const input = es.through();
	const output = es.through();
	let state = 'idle';

	const run = () => {
		state = 'running';

		task()
			.pipe(es.through(undefined, () => {
				const shouldRunAgain = state === 'stale';
				state = 'idle';

				if (shouldRunAgain) {
					eventuallyRun();
				}
			}))
			.pipe(output);
	};

	run();

	const eventuallyRun = _debounce(() => run(), duration);

	input.on('data', () => {
		if (state === 'idle') {
			eventuallyRun();
		} else {
			state = 'stale';
		}
	});

	return es.duplex(input, output);
}

export function fixWin32DirectoryPermissions(): NodeJS.ReadWriteStream {
	if (!/win32/.test(process.platform)) {
		return es.through();
	}

	return es.mapSync<typeof VinylFile, typeof VinylFile>(f => {
		if (f.stat && f.stat.isDirectory && f.stat.isDirectory()) {
			f.stat.mode = 16877;
		}

		return f;
	});
}

export function setExecutableBit(pattern?: string | string[]): NodeJS.ReadWriteStream {
	const setBit = es.mapSync<typeof VinylFile, typeof VinylFile>(f => {
		if (!f.stat) {
			f.stat = { isFile() { return true; } } as any;
		}
		f.stat.mode = /* 100755 */ 33261;
		return f;
	});

	if (!pattern) {
		return setBit;
	}

	const input = es.through();
	const filter = _filter(pattern, { restore: true });
	const output = input
		.pipe(filter)
		.pipe(setBit)
		.pipe(filter.restore);

	return es.duplex(input, output);
}

export function toFileUri(filePath: string): string {
	const match = filePath.match(/^([a-z])\:(.*)$/i);

	if (match) {
		filePath = '/' + match[1].toUpperCase() + ':' + match[2];
	}

	return 'file://' + filePath.replace(/\\/g, '/');
}

export function skipDirectories(): NodeJS.ReadWriteStream {
	return es.mapSync<typeof VinylFile, typeof VinylFile | undefined>(f => {
		if (!f.isDirectory()) {
			return f;
		}
	});
}

/**
 * 验证文件路径的安全性
 * @param filePath 要验证的文件路径
 * @returns 验证后的安全路径
 */
function validateFilePath(filePath: string): string {
	if (!filePath || typeof filePath !== 'string') {
		throw new Error('Invalid file path');
	}

	// 清理路径，移除危险字符
	const sanitized = filePath
		.replace(/[<>"'&;|`$(){}[\]]/g, '') // 移除危险字符
		.trim();

	if (!sanitized || sanitized.length === 0) {
		throw new Error('Invalid sanitized file path');
	}

	// 检查路径长度限制
	if (sanitized.length > 1000) {
		throw new Error('File path too long');
	}

	// 标准化路径
	const normalizedPath = path.resolve(sanitized);

	// 检查路径遍历
	if (normalizedPath.includes('..')) {
		throw new Error('Path traversal detected');
	}

	// 检查是否为合法的文件路径（应该在项目目录下）
	const projectRoot = path.resolve(__dirname, '../..');
	if (!normalizedPath.startsWith(projectRoot)) {
		throw new Error(`File path outside project directory: ${normalizedPath}`);
	}

	// 检查文件是否存在
	if (!fs.existsSync(normalizedPath)) {
		throw new Error(`File does not exist: ${normalizedPath}`);
	}

	return normalizedPath;
}

export function cleanNodeModules(rulePath: string): NodeJS.ReadWriteStream {
	// 验证并清理输入路径
	const safeRulePath = validateFilePath(rulePath);

	const rules = fs.readFileSync(safeRulePath, 'utf8')
		.split(/\r?\n/g)
		.map(line => line.trim())
		.filter(line => line && !/^#/.test(line));

	const excludes = rules.filter(line => !/^!/.test(line)).map(line => `!**/node_modules/${line}`);
	const includes = rules.filter(line => /^!/.test(line)).map(line => `**/node_modules/${line.substring(1)}`);

	const input = es.through();
	const output = es.merge(
		input.pipe(_filter(['**', ...excludes])),
		input.pipe(_filter(includes))
	);

	return es.duplex(input, output);
}

interface FileSourceMap extends InstanceType<typeof VinylFile> {
	sourceMap: sm.RawSourceMap;
}

export function loadSourcemaps(): NodeJS.ReadWriteStream {
	const input = es.through();

	const output = input
		.pipe(es.map<FileSourceMap, FileSourceMap | undefined>((f, cb): FileSourceMap | undefined => {
			if (f.sourceMap) {
				cb(undefined, f);
				return;
			}

			if (!f.contents) {
				cb(undefined, f);
				return;
			}

			const contents = (<Buffer>f.contents).toString('utf8');

			const reg = /\/\/# sourceMappingURL=(.*)$/g;
			let lastMatch: RegExpExecArray | null = null;
			let match: RegExpExecArray | null = null;

			while (match = reg.exec(contents)) {
				lastMatch = match;
			}

			if (!lastMatch) {
				f.sourceMap = {
					version: '3',
					names: [],
					mappings: '',
					sources: [f.relative.replace(/\\/g, '/')],
					sourcesContent: [contents]
				};

				cb(undefined, f);
				return;
			}

			f.contents = Buffer.from(contents.replace(/\/\/# sourceMappingURL=(.*)$/g, ''), 'utf8');

			fs.readFile(path.join(path.dirname(f.path), lastMatch[1]), 'utf8', (err, contents) => {
				if (err) { return cb(err); }

				f.sourceMap = JSON.parse(contents);
				cb(undefined, f);
			});
		}));

	return es.duplex(input, output);
}

export function stripSourceMappingURL(): NodeJS.ReadWriteStream {
	const input = es.through();

	const output = input
		.pipe(es.mapSync<typeof VinylFile, typeof VinylFile>(f => {
			const contents = (<Buffer>f.contents).toString('utf8');
			f.contents = Buffer.from(contents.replace(/\n\/\/# sourceMappingURL=(.*)$/gm, ''), 'utf8');
			return f;
		}));

	return es.duplex(input, output);
}

/** Splits items in the stream based on the predicate, sending them to onTrue if true, or onFalse otherwise */
export function $if(test: boolean | ((f: typeof VinylFile) => boolean), onTrue: NodeJS.ReadWriteStream, onFalse: NodeJS.ReadWriteStream = es.through()) {
	if (typeof test === 'boolean') {
		return test ? onTrue : onFalse;
	}

	return ternaryStream(test, onTrue, onFalse);
}

/** Operator that appends the js files' original path a sourceURL, so debug locations map */
export function appendOwnPathSourceURL(): NodeJS.ReadWriteStream {
	const input = es.through();

	const output = input
		.pipe(es.mapSync<typeof VinylFile, typeof VinylFile>(f => {
			if (!(f.contents instanceof Buffer)) {
				throw new Error(`contents of ${f.path} are not a buffer`);
			}

			f.contents = Buffer.concat([f.contents, Buffer.from(`\n//# sourceURL=${pathToFileURL(f.path)}`)]);
			return f;
		}));

	return es.duplex(input, output);
}

export function rewriteSourceMappingURL(sourceMappingURLBase: string): NodeJS.ReadWriteStream {
	const input = es.through();

	const output = input
		.pipe(es.mapSync<typeof VinylFile, typeof VinylFile>(f => {
			const contents = (<Buffer>f.contents).toString('utf8');
			const str = `//# sourceMappingURL=${sourceMappingURLBase}/${path.dirname(f.relative).replace(/\\/g, '/')}/$1`;
			f.contents = Buffer.from(contents.replace(/\n\/\/# sourceMappingURL=(.*)$/gm, str));
			return f;
		}));

	return es.duplex(input, output);
}

export function rimraf(dir: string): () => Promise<void> {
	const result = () => new Promise<void>((c, e) => {
		let retries = 0;

		const retry = () => {
			_rimraf(dir, { maxBusyTries: 1 }, (err: any) => {
				if (!err) {
					return c();
				}

				if (err.code === 'ENOTEMPTY' && ++retries < 5) {
					return setTimeout(() => retry(), 10);
				}

				return e(err);
			});
		};

		retry();
	});

	result.taskName = `clean-${path.basename(dir).toLowerCase()}`;
	return result;
}

function _rreaddir(dirPath: string, prepend: string, result: string[]): void {
	const entries = fs.readdirSync(dirPath, { withFileTypes: true });
	for (const entry of entries) {
		if (entry.isDirectory()) {
			_rreaddir(path.join(dirPath, entry.name), `${prepend}/${entry.name}`, result);
		} else {
			result.push(`${prepend}/${entry.name}`);
		}
	}
}

export function rreddir(dirPath: string): string[] {
	const result: string[] = [];
	_rreaddir(dirPath, '', result);
	return result;
}

export function ensureDir(dirPath: string): void {
	if (fs.existsSync(dirPath)) {
		return;
	}
	ensureDir(path.dirname(dirPath));
	fs.mkdirSync(dirPath);
}

export function rebase(count: number): NodeJS.ReadWriteStream {
	return rename(f => {
		const parts = f.dirname ? f.dirname.split(/[\/\\]/) : [];
		f.dirname = parts.slice(count).join(path.sep);
	});
}

export interface FilterStream extends NodeJS.ReadWriteStream {
	restore: ThroughStream;
}

export function filter(fn: (data: any) => boolean): FilterStream {
	const result = <FilterStream><any>es.through(function (data) {
		if (fn(data)) {
			this.emit('data', data);
		} else {
			result.restore.push(data);
		}
	});

	result.restore = es.through();
	return result;
}

export function versionStringToNumber(versionStr: string) {
	const semverRegex = /(\d+)\.(\d+)\.(\d+)/;
	const match = versionStr.match(semverRegex);
	if (!match) {
		throw new Error('Version string is not properly formatted: ' + versionStr);
	}

	return parseInt(match[1], 10) * 1e4 + parseInt(match[2], 10) * 1e2 + parseInt(match[3], 10);
}

export function streamToPromise(stream: NodeJS.ReadWriteStream): Promise<void> {
	return new Promise((c, e) => {
		stream.on('error', err => e(err));
		stream.on('end', () => c());
	});
}

export function getElectronVersion(): Record<string, string> {
	// 构建安全的 .npmrc 文件路径
	const npmrcPath = path.join(root, '.npmrc');

	// 验证文件路径的安全性
	const safeNpmrcPath = validateFilePath(npmrcPath);

	const npmrc = fs.readFileSync(safeNpmrcPath, 'utf8');
	const electronVersion = /^target="(.*)"$/m.exec(npmrc)![1];
	const msBuildId = /^ms_build_id="(.*)"$/m.exec(npmrc)![1];
	return { electronVersion, msBuildId };
}

export function acquireWebNodePaths() {
	const root = path.join(__dirname, '..', '..');
	const webPackageJSON = path.join(root, '/remote/web', 'package.json');

	// 验证文件路径的安全性
	const safeWebPackageJSON = validateFilePath(webPackageJSON);
	const webPackages = JSON.parse(fs.readFileSync(safeWebPackageJSON, 'utf8')).dependencies;

	const distroWebPackageJson = path.join(root, '.build/distro/npm/remote/web/package.json');
	if (fs.existsSync(distroWebPackageJson)) {
		// 验证文件路径的安全性
		const safeDistroWebPackageJson = validateFilePath(distroWebPackageJson);
		const distroWebPackages = JSON.parse(fs.readFileSync(safeDistroWebPackageJson, 'utf8')).dependencies;
		Object.assign(webPackages, distroWebPackages);
	}

	const nodePaths: { [key: string]: string } = {};
	for (const key of Object.keys(webPackages)) {
		const packageJSON = path.join(root, 'node_modules', key, 'package.json');

		// 验证文件路径的安全性
		const safePackageJSON = validateFilePath(packageJSON);
		const packageData = JSON.parse(fs.readFileSync(safePackageJSON, 'utf8'));
		// Only cases where the browser is a string are handled
		let entryPoint: string = typeof packageData.browser === 'string' ? packageData.browser : packageData.main;

		// On rare cases a package doesn't have an entrypoint so we assume it has a dist folder with a min.js
		if (!entryPoint) {
			// TODO @lramos15 remove this when jschardet adds an entrypoint so we can warn on all packages w/out entrypoint
			if (key !== 'jschardet') {
				console.warn(`No entry point for ${key} assuming dist/${key}.min.js`);
			}

			entryPoint = `dist/${key}.min.js`;
		}

		// Remove any starting path information so it's all relative info
		if (entryPoint.startsWith('./')) {
			entryPoint = entryPoint.substring(2);
		} else if (entryPoint.startsWith('/')) {
			entryPoint = entryPoint.substring(1);
		}

		// Search for a minified entrypoint as well
		if (/(?<!\.min)\.js$/i.test(entryPoint)) {
			const minEntryPoint = entryPoint.replace(/\.js$/i, '.min.js');

			if (fs.existsSync(path.join(root, 'node_modules', key, minEntryPoint))) {
				entryPoint = minEntryPoint;
			}
		}

		nodePaths[key] = entryPoint;
	}

	// @TODO lramos15 can we make this dynamic like the rest of the node paths
	// Add these paths as well for 1DS SDK dependencies.
	// Not sure why given the 1DS entrypoint then requires these modules
	// they are not fetched from the right location and instead are fetched from out/
	nodePaths['@microsoft/dynamicproto-js'] = 'lib/dist/umd/dynamicproto-js.min.js';
	nodePaths['@microsoft/applicationinsights-shims'] = 'dist/umd/applicationinsights-shims.min.js';
	nodePaths['@microsoft/applicationinsights-core-js'] = 'browser/applicationinsights-core-js.min.js';
	return nodePaths;
}

export interface IExternalLoaderInfo {
	baseUrl: string;
	paths: { [moduleId: string]: string };
	[key: string]: any;
}

export function createExternalLoaderConfig(webEndpoint?: string, commit?: string, quality?: string): IExternalLoaderInfo | undefined {
	if (!webEndpoint || !commit || !quality) {
		return undefined;
	}
	webEndpoint = webEndpoint + `/${quality}/${commit}`;
	const nodePaths = acquireWebNodePaths();
	Object.keys(nodePaths).map(function (key, _) {
		nodePaths[key] = `../node_modules/${key}/${nodePaths[key]}`;
	});
	const externalLoaderConfig: IExternalLoaderInfo = {
		baseUrl: `${webEndpoint}/out`,
		recordStats: true,
		paths: nodePaths
	};
	return externalLoaderConfig;
}

export function buildWebNodePaths(outDir: string) {
	const result = () => new Promise<void>((resolve, _) => {
		const root = path.join(__dirname, '..', '..');
		const nodePaths = acquireWebNodePaths();
		// Now we write the node paths to out/vs
		const outDirectory = path.join(root, outDir, 'vs');
		fs.mkdirSync(outDirectory, { recursive: true });
		const headerWithGeneratedFileWarning = `/*---------------------------------------------------------------------------------------------
	 *  Copyright (c) Microsoft Corporation. All rights reserved.
	 *  Licensed under the MIT License. See License.txt in the project root for license information.
	 *--------------------------------------------------------------------------------------------*/

	// This file is generated by build/npm/postinstall.js. Do not edit.`;
		const fileContents = `${headerWithGeneratedFileWarning}\nself.webPackagePaths = ${JSON.stringify(nodePaths, null, 2)};`;
		fs.writeFileSync(path.join(outDirectory, 'webPackagePaths.js'), fileContents, 'utf8');
		resolve();
	});
	result.taskName = 'build-web-node-paths';
	return result;
}
