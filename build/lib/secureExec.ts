/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

import * as cp from 'child_process';
import * as path from 'path';

/**
 * 安全的命令执行选项
 */
interface SecureExecOptions {
	cwd?: string;
	stdio?: cp.StdioOptions;
	env?: NodeJS.ProcessEnv;
	timeout?: number;
	maxBuffer?: number;
	encoding?: BufferEncoding;
	shell?: boolean;
}

/**
 * 安全的命令执行工具，防止命令注入攻击
 * 支持自动编译 TypeScript 到 JavaScript
 */
class SecureExec {
	/**
	 * 验证命令是否在允许列表中
	 */
	static isAllowedCommand(command: string): boolean {
		const allowedCommands = [
			'git',
			'cargo',
			'dpkg-deb',
			'fakeroot',
			'chmod',
			'mkdir',
			'node',
			'npm',
			'npm.cmd',
			'yarn',
			'pnpm',
			'python',
			'python3',
			'make',
			'cmake',
			'gcc',
			'g++',
			'clang',
			'rustc',
			'tsc',
			'esbuild',
			'mksnapshot',
			'mksnapshot.cmd',
			'rg',
			'rg.exe',
			'iscc',
			'iscc.exe',
			'ISCC.exe',  // 添加大写版本的 ISCC.exe
			'innosetup',
			'innosetup.exe',
			'node-gyp',
			'node-gyp.cmd',
			'tar',
			'curl',
			'wget',
			'unzip',
			'7z',
			'7za',
			'openssl',
			'gpg',
			'gpg2',
			'dotnet',
			'dotnet.exe',
			'find',
			'grep',
			'awk',
			'sed',
			'sort',
			'uniq',
			'head',
			'tail',
			'cat',
			'ls',
			'cp',
			'mv',
			'rm',
			'mkdir',
			'rmdir',
			'docker',
			'podman',
			'rpmbuild',
			'snapcraft'
		];

		// 检查是否是允许的命令名
		if (allowedCommands.includes(command)) {
			return true;
		}

		// 对于完整路径，检查文件名是否在允许列表中
		// 特别处理 ISCC.exe 的完整路径
		if (command.includes('/') || command.includes('\\')) {
			const basename = path.basename(command);
			if (allowedCommands.includes(basename)) {
				// 额外安全检查：确保是项目内的路径或 node_modules 中的工具
				const projectRoot = path.resolve(__dirname, '../..');
				const resolvedCommand = path.resolve(command);

				// 允许项目根目录内的文件或 node_modules 中的工具
				if (resolvedCommand.startsWith(projectRoot) ||
					resolvedCommand.includes('node_modules')) {
					return true;
				}
			}
		}

		return false;
	}

	/**
	 * 清理和验证命令参数
	 */
	static sanitizeArg(arg: string): string | null {
		if (typeof arg !== 'string') {
			return null;
		}

		// 对于常见的npm命令参数，采用更宽松的清理策略
		const commonNpmArgs = ['run', 'install', 'ci', 'compile', 'electron', 'build', 'watch', 'test'];
		if (commonNpmArgs.includes(arg)) {
			return arg;
		}

		// 对于路径参数，保留更多字符
		if (arg.includes('/') || arg.includes('\\') || arg.includes(':')) {
			// 这可能是路径，只移除最危险的字符
			const sanitized = arg
				.replace(/[;&|`$(){}[\]<>]/g, '') // 移除命令注入字符，但保留路径字符
				.trim();
			if (sanitized.length > 1000) {
				throw new Error('Argument too long');
			}
			return sanitized;
		}

		// 对于其他参数，使用原来的清理策略
		const sanitized = arg
			.replace(/[;&|`$(){}[\]<>]/g, '') // 移除命令注入字符
			.replace(/\.\./g, '') // 防止路径遍历
			.trim();

		// 检查长度限制
		if (sanitized.length > 1000) {
			throw new Error('Argument too long');
		}

		return sanitized;
	}

	/**
	 * 验证工作目录的安全性
	 */
	static validateCwd(cwd?: string): string {
		if (!cwd || typeof cwd !== 'string') {
			return process.cwd();
		}

		// 标准化路径
		const normalizedCwd = path.resolve(cwd);

		// 确保在项目根目录内
		const projectRoot = path.resolve(__dirname, '../..');
		if (!normalizedCwd.startsWith(projectRoot)) {
			throw new Error('Working directory outside project root');
		}

		return normalizedCwd;
	}

	/**
	 * 安全地执行命令
	 */
	static async exec(command: string, args: string[] = [], options: SecureExecOptions = {}): Promise<string> {
		// 验证命令
		if (!this.isAllowedCommand(command)) {
			throw new Error(`Command not allowed: ${command}`);
		}

		// 清理参数
		const sanitizedArgs = args.map(arg => {
			const sanitized = this.sanitizeArg(arg);
			if (sanitized === null) {
				throw new Error(`Invalid argument: ${arg}`);
			}
			return sanitized;
		});

		// 验证工作目录
		const safeCwd = this.validateCwd(options.cwd);

		// 设置安全的执行选项
		const safeOptions: cp.SpawnOptions = {
			cwd: safeCwd,
			stdio: options.stdio || 'pipe',
			env: options.env || process.env,
			timeout: options.timeout || 300000 // 5分钟超时
		};

		console.log(`[SECURE_EXEC] Executing: ${command} ${sanitizedArgs.join(' ')}`);

		return new Promise((resolve, reject) => {
			const proc = cp.spawn(command, sanitizedArgs, safeOptions);
			let stdout = '';
			let stderr = '';

			if (proc.stdout) {
				proc.stdout.on('data', (data: Buffer) => {
					stdout += data.toString();
				});
			}

			if (proc.stderr) {
				proc.stderr.on('data', (data: Buffer) => {
					stderr += data.toString();
				});
			}

			proc.on('close', (code: number | null) => {
				if (code === 0) {
					resolve(stdout);
				} else {
					reject(new Error(`Command failed with code ${code}: ${stderr}`));
				}
			});

			proc.on('error', (error: Error) => {
				reject(new Error(`Failed to execute command: ${error.message}`));
			});
		});
	}

	/**
	 * 安全地执行同步命令
	 */
	static execSync(command: string, args: string[] = [], options: SecureExecOptions = {}): string {
		// 验证命令
		if (!this.isAllowedCommand(command)) {
			throw new Error(`Command not allowed: ${command}`);
		}

		// 清理参数
		const sanitizedArgs = args.map(arg => {
			const sanitized = this.sanitizeArg(arg);
			if (sanitized === null) {
				throw new Error(`Invalid argument: ${arg}`);
			}
			return sanitized;
		});

		// 验证工作目录
		const safeCwd = this.validateCwd(options.cwd);

		// 设置安全的执行选项
		const safeOptions: cp.SpawnSyncOptions = {
			cwd: safeCwd,
			stdio: options.stdio || 'pipe',
			env: options.env || process.env,
			timeout: options.timeout || 300000, // 5分钟超时
			maxBuffer: options.maxBuffer || 1024 * 1024 * 10, // 10MB 缓冲区限制
			encoding: options.encoding || 'utf8',
			shell: options.shell
		};

		// Windows特殊处理
		if (process.platform === 'win32') {
			if (command.endsWith('.cmd') || command.endsWith('.bat') || options.shell) {
				safeOptions.shell = true;
			}
			// 对于npm.cmd，确保使用shell执行
			if (command === 'npm.cmd') {
				safeOptions.shell = true;
			}
		}

		console.log(`[SECURE_EXEC_SYNC] Executing: ${command} ${sanitizedArgs.join(' ')}`);

		try {
			const result = cp.spawnSync(command, sanitizedArgs, safeOptions);
			if (result.error) {
				throw result.error;
			}
			if (result.status !== 0) {
				throw new Error(`Command failed with code ${result.status}: ${result.stderr?.toString()}`);
			}
			return result.stdout?.toString() || '';
		} catch (error) {
			throw new Error(`Failed to execute command: ${error instanceof Error ? error.message : String(error)}`);
		}
	}

	/**
	 * 安全地创建子进程
	 */
	static spawn(command: string, args: string[] = [], options: SecureExecOptions = {}): cp.ChildProcess {
		// 验证命令
		if (!this.isAllowedCommand(command)) {
			throw new Error(`Command not allowed: ${command}`);
		}

		// 清理参数
		const sanitizedArgs = args.map(arg => {
			const sanitized = this.sanitizeArg(arg);
			if (sanitized === null) {
				throw new Error(`Invalid argument: ${arg}`);
			}
			return sanitized;
		});

		// 验证工作目录
		const safeCwd = this.validateCwd(options.cwd);

		// Windows特殊处理：确保使用正确的shell选项
		const safeOptions: cp.SpawnOptions = {
			...options,
			cwd: safeCwd,
			env: options.env || process.env
		};

		// 在Windows上，对于.cmd文件需要特殊处理
		if (process.platform === 'win32') {
			if (command.endsWith('.cmd') || command.endsWith('.bat') || options.shell) {
				safeOptions.shell = true;
			}
			// Windows特殊处理：确保正确的命令路径
			if (command === 'npm.cmd' && !command.includes('\\') && !command.includes('/')) {
				// 让系统自动查找npm.cmd
				safeOptions.shell = true;
			}
		}

		console.log(`[SECURE_SPAWN] Spawning: ${command} ${sanitizedArgs.join(' ')}`);
		console.log(`[SECURE_SPAWN] Options:`, { cwd: safeOptions.cwd, shell: safeOptions.shell });

		try {
			return cp.spawn(command, sanitizedArgs, safeOptions);
		} catch (error) {
			console.error(`[SECURE_SPAWN] Error spawning process:`, error);
			throw error;
		}
	}
}

// 为了向后兼容，同时支持 CommonJS 和 ES6 导入
export { SecureExec };
export default SecureExec;
