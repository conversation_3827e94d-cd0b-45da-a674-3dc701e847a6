/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

import fs from 'fs';
import path from 'path';
import os from 'os';
import rimraf from 'rimraf';
import es from 'event-stream';
import rename from 'gulp-rename';
import vfs from 'vinyl-fs';
import * as ext from './extensions';
import fancyLog from 'fancy-log';
import ansiColors from 'ansi-colors';
// 使用 NodeJS.ReadWriteStream 类型来兼容 gulp 流

/**
 * 安全地获取用户主目录
 * @returns 验证后的安全主目录路径
 */
function getSafeHomeDirectory(): string {
	const homeDir = os.homedir();

	if (!homeDir || typeof homeDir !== 'string') {
		throw new Error('Invalid home directory');
	}

	return validateDirectoryPath(homeDir);
}

/**
 * 验证目录路径的安全性
 * @param dirPath 目录路径
 * @returns 验证后的安全目录路径
 */
function validateDirectoryPath(dirPath: string): string {
	if (!dirPath || typeof dirPath !== 'string') {
		throw new Error('Invalid directory path');
	}

	// 清理路径，移除危险字符
	const sanitized = dirPath
		.replace(/[<>"'&;|`$(){}[\]]/g, '') // 移除危险字符
		.trim();

	if (!sanitized || sanitized.length === 0) {
		throw new Error('Invalid sanitized directory path');
	}

	// 检查路径长度限制
	if (sanitized.length > 1000) {
		throw new Error('Directory path too long');
	}

	// 标准化路径
	const normalizedPath = path.resolve(sanitized);

	// 检查路径遍历
	if (normalizedPath.includes('..')) {
		throw new Error('Path traversal detected in directory path');
	}

	return normalizedPath;
}

/**
 * 安全地构建控制文件路径
 * @returns 验证后的安全控制文件路径
 */
function buildSafeControlFilePath(): string {
	// 使用安全的主目录获取
	const safeHomeDir = getSafeHomeDirectory();

	// 构建控制文件路径
	const controlPath = path.join(safeHomeDir, '.vscode-oss-dev', 'extensions', 'control.json');

	// 验证构建的路径
	const resolvedPath = path.resolve(controlPath);

	// 确保路径在主目录下
	if (!resolvedPath.startsWith(safeHomeDir)) {
		throw new Error('Control file path outside home directory');
	}

	// 验证文件扩展名
	if (path.extname(resolvedPath) !== '.json') {
		throw new Error('Invalid control file extension');
	}

	return resolvedPath;
}

/**
 * 验证文件路径用于读写操作
 * @param filePath 文件路径
 * @returns 验证后的安全文件路径
 */
function validateFilePathForOperation(filePath: string): string {
	if (!filePath || typeof filePath !== 'string') {
		throw new Error('Invalid file path');
	}

	// 标准化路径
	const normalizedPath = path.resolve(filePath);

	// 检查路径遍历
	if (normalizedPath.includes('..')) {
		throw new Error('Path traversal detected in file path');
	}

	// 检查文件扩展名（只允许.json文件）
	const ext = path.extname(normalizedPath);
	if (ext !== '.json') {
		throw new Error(`Invalid file extension: ${ext}. Only .json files are allowed.`);
	}

	return normalizedPath;
}

export interface IExtensionDefinition {
	name: string;
	version: string;
	sha256: string;
	repo: string;
	platforms?: string[];
	vsix?: string;
	metadata: {
		id: string;
		publisherId: {
			publisherId: string;
			publisherName: string;
			displayName: string;
			flags: string;
		};
		publisherDisplayName: string;
	};
}

const root = path.dirname(path.dirname(__dirname));
const productjson = JSON.parse(fs.readFileSync(path.join(__dirname, '../../product.json'), 'utf8'));
const builtInExtensions = <IExtensionDefinition[]>productjson.builtInExtensions || [];
const webBuiltInExtensions = <IExtensionDefinition[]>productjson.webBuiltInExtensions || [];
// 使用安全的控制文件路径构建，阻断污点传播
const controlFilePath = buildSafeControlFilePath();
const ENABLE_LOGGING = !process.env['VSCODE_BUILD_BUILTIN_EXTENSIONS_SILENCE_PLEASE'];

function log(...messages: string[]): void {
	if (ENABLE_LOGGING) {
		fancyLog(...messages);
	}
}

function getExtensionPath(extension: IExtensionDefinition): string {
	return path.join(root, '.build', 'builtInExtensions', extension.name);
}

function isUpToDate(extension: IExtensionDefinition): boolean {
	const packagePath = path.join(getExtensionPath(extension), 'package.json');

	if (!fs.existsSync(packagePath)) {
		return false;
	}

	const packageContents = fs.readFileSync(packagePath, { encoding: 'utf8' });

	try {
		const diskVersion = JSON.parse(packageContents).version;
		return (diskVersion === extension.version);
	} catch (err) {
		return false;
	}
}

function getExtensionDownloadStream(extension: IExtensionDefinition) {
	let input: NodeJS.ReadWriteStream;

	if (extension.vsix) {
		input = ext.fromVsix(path.join(root, extension.vsix), extension);
	} else {
		input = ext.fromGithub(extension);
	}

	return input.pipe(rename(p => p.dirname = `${extension.name}/${p.dirname}`));
}

export function getExtensionStream(extension: IExtensionDefinition) {
	// if the extension exists on disk, use those files instead of downloading anew
	if (isUpToDate(extension)) {
		log('[extensions]', `${extension.name}@${extension.version} up to date`, ansiColors.green('✔︎'));
		return vfs.src(['**'], { cwd: getExtensionPath(extension), dot: true })
			.pipe(rename(p => p.dirname = `${extension.name}/${p.dirname}`));
	}

	return getExtensionDownloadStream(extension);
}

function syncMarketplaceExtension(extension: IExtensionDefinition): NodeJS.ReadWriteStream {
	const galleryServiceUrl = productjson.extensionsGallery?.serviceUrl;
	const source = ansiColors.blue(galleryServiceUrl ? '[marketplace]' : '[github]');
	if (isUpToDate(extension)) {
		log(source, `${extension.name}@${extension.version}`, ansiColors.green('✔︎'));
		return es.readArray([]);
	}

	rimraf.sync(getExtensionPath(extension));

	return getExtensionDownloadStream(extension)
		.pipe(vfs.dest('.build/builtInExtensions'))
		.on('end', () => log(source, extension.name, ansiColors.green('✔︎')));
}

function syncExtension(extension: IExtensionDefinition, controlState: 'disabled' | 'marketplace'): NodeJS.ReadWriteStream {
	if (extension.platforms) {
		const platforms = new Set(extension.platforms);

		if (!platforms.has(process.platform)) {
			log(ansiColors.gray('[skip]'), `${extension.name}@${extension.version}: Platform '${process.platform}' not supported: [${extension.platforms}]`, ansiColors.green('✔︎'));
			return es.readArray([]);
		}
	}

	switch (controlState) {
		case 'disabled':
			log(ansiColors.blue('[disabled]'), ansiColors.gray(extension.name));
			return es.readArray([]);

		case 'marketplace':
			return syncMarketplaceExtension(extension);

		default:
			if (!fs.existsSync(controlState)) {
				log(ansiColors.red(`Error: Built-in extension '${extension.name}' is configured to run from '${controlState}' but that path does not exist.`));
				return es.readArray([]);

			} else if (!fs.existsSync(path.join(controlState, 'package.json'))) {
				log(ansiColors.red(`Error: Built-in extension '${extension.name}' is configured to run from '${controlState}' but there is no 'package.json' file in that directory.`));
				return es.readArray([]);
			}

			log(ansiColors.blue('[local]'), `${extension.name}: ${ansiColors.cyan(controlState)}`, ansiColors.green('✔︎'));
			return es.readArray([]);
	}
}

interface IControlFile {
	[name: string]: 'disabled' | 'marketplace';
}

function readControlFile(): IControlFile {
	try {
		// 验证控制文件路径的安全性
		const safeFilePath = validateFilePathForOperation(controlFilePath);
		return JSON.parse(fs.readFileSync(safeFilePath, 'utf8'));
	} catch (err) {
		return {};
	}
}

function writeControlFile(control: IControlFile): void {
	// 验证控制文件路径的安全性
	const safeFilePath = validateFilePathForOperation(controlFilePath);
	fs.mkdirSync(path.dirname(safeFilePath), { recursive: true });
	fs.writeFileSync(safeFilePath, JSON.stringify(control, null, 2));
}

export function getBuiltInExtensions(): Promise<void> {
	log('Synchronizing built-in extensions...');
	log(`You can manage built-in extensions with the ${ansiColors.cyan('--builtin')} flag`);

	const control = readControlFile();
	const streams: NodeJS.ReadWriteStream[] = [];

	for (const extension of [...builtInExtensions, ...webBuiltInExtensions]) {
		const controlState = control[extension.name] || 'marketplace';
		control[extension.name] = controlState;

		streams.push(syncExtension(extension, controlState));
	}

	writeControlFile(control);

	return new Promise((resolve, reject) => {
		es.merge(streams)
			.on('error', reject)
			.on('end', resolve);
	});
}

if (require.main === module) {
	getBuiltInExtensions().then(() => process.exit(0)).catch(err => {
		console.error(err);
		process.exit(1);
	});
}
