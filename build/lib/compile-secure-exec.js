/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

const fs = require('fs');
const path = require('path');

/**
 * 简单的 TypeScript 到 JavaScript 编译器
 * 专门用于编译 secureExec.ts 文件
 */
function compileSecureExec() {
	const tsFile = path.join(__dirname, 'secureExec.ts');
	const jsFile = path.join(__dirname, 'secureExec.js');
	
	console.log('🔨 Compiling secureExec.ts to secureExec.js...');
	
	try {
		// 读取 TypeScript 文件
		let content = fs.readFileSync(tsFile, 'utf8');
		
		// 简单的 TypeScript 到 JavaScript 转换
		content = content
			// 移除 import 语句并替换为 require
			.replace(/import \* as (\w+) from ['"]([^'"]+)['"];/g, 'const $1 = require(\'$2\');')
			.replace(/import \{ ([^}]+) \} from ['"]([^'"]+)['"];/g, 'const { $1 } = require(\'$2\');')
			.replace(/import (\w+) from ['"]([^'"]+)['"];/g, 'const $1 = require(\'$2\');')
			
			// 移除类型注解
			.replace(/: cp\.StdioOptions/g, '')
			.replace(/: NodeJS\.ProcessEnv/g, '')
			.replace(/: BufferEncoding/g, '')
			.replace(/: boolean/g, '')
			.replace(/: string/g, '')
			.replace(/: number/g, '')
			.replace(/: cp\.ChildProcess/g, '')
			.replace(/: cp\.SpawnOptions/g, '')
			.replace(/: cp\.SpawnSyncOptions/g, '')
			.replace(/: SecureExecOptions/g, '')
			.replace(/: Promise<string>/g, '')
			.replace(/: string\[\]/g, '')
			.replace(/: string \| null/g, '')
			.replace(/: Error/g, '')
			.replace(/: Buffer/g, '')
			.replace(/: number \| null/g, '')
			
			// 移除接口定义
			.replace(/interface SecureExecOptions \{[\s\S]*?\}/g, '')
			
			// 移除 export 关键字
			.replace(/export class/g, 'class')
			.replace(/export \{ testSecureExec \};/g, '')
			.replace(/export default SecureExec;/g, '')
			
			// 替换 module.exports 部分
			.replace(/module\.exports = SecureExec;[\s\S]*$/g, `// 为了向后兼容，同时支持 CommonJS 和 ES6 导入
module.exports = SecureExec;
module.exports.SecureExec = SecureExec;
module.exports.default = SecureExec;`)
			
			// 清理多余的空行
			.replace(/\n\s*\n\s*\n/g, '\n\n')
			.trim();
		
		// 添加 JSDoc 注释的类型信息
		content = content
			.replace(/static isAllowedCommand\(command\)/g, 'static isAllowedCommand(command)')
			.replace(/static sanitizeArg\(arg\)/g, 'static sanitizeArg(arg)')
			.replace(/static validateCwd\(cwd\)/g, 'static validateCwd(cwd)')
			.replace(/static async exec\(command, args = \[\], options = \{\}\)/g, 'static async exec(command, args = [], options = {})')
			.replace(/static execSync\(command, args = \[\], options = \{\}\)/g, 'static execSync(command, args = [], options = {})')
			.replace(/static spawn\(command, args = \[\], options = \{\}\)/g, 'static spawn(command, args = [], options = {})');
		
		// 写入 JavaScript 文件
		fs.writeFileSync(jsFile, content, 'utf8');
		
		console.log('✅ Successfully compiled secureExec.ts to secureExec.js');
		
		// 验证编译结果
		try {
			const SecureExec = require(jsFile);
			if (typeof SecureExec.isAllowedCommand === 'function') {
				console.log('✅ Compiled module loads correctly');
			} else {
				throw new Error('Compiled module does not export expected functions');
			}
		} catch (error) {
			console.error('❌ Compiled module validation failed:', error.message);
			throw error;
		}
		
	} catch (error) {
		console.error('❌ Compilation failed:', error.message);
		throw error;
	}
}

// 如果直接运行此脚本
if (require.main === module) {
	compileSecureExec();
}

module.exports = { compileSecureExec };
