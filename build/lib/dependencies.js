"use strict";
/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.getProductionDependencies = getProductionDependencies;
const fs_1 = __importDefault(require("fs"));
const path_1 = __importDefault(require("path"));
const secureExec_1 = require("./secureExec");
const root = fs_1.default.realpathSync(path_1.default.dirname(path_1.default.dirname(__dirname)));
function getNpmProductionDependencies(folder) {
    let raw;
    try {
        // Windows环境下使用npm.cmd
        const npmCommand = process.platform === 'win32' ? 'npm.cmd' : 'npm';
        raw = secureExec_1.SecureExec.execSync(npmCommand, ['ls', '--all', '--omit=dev', '--parseable'], {
            cwd: folder,
            env: { ...process.env, NODE_ENV: 'production' },
            shell: process.platform === 'win32' // Windows下需要shell
        });
    }
    catch (err) {
        console.warn(`[DEPENDENCIES] npm ls failed in ${folder}:`, err.message);
        const regex = /^npm ERR! .*$/gm;
        let match;
        while (match = regex.exec(err.message)) {
            if (/ELSPROBLEMS/.test(match[0])) {
                continue;
            }
            else if (/invalid: xterm/.test(match[0])) {
                continue;
            }
            else if (/A complete log of this run/.test(match[0])) {
                continue;
            }
            else if (/peer dep missing/.test(match[0])) {
                continue; // 忽略peer dependency警告
            }
            else if (/ERESOLVE/.test(match[0])) {
                continue; // 忽略依赖解析警告
            }
            else {
                console.error(`[DEPENDENCIES] Unhandled npm error: ${match[0]}`);
                // 不要抛出错误，而是返回空结果
                return [];
            }
        }
        raw = err.stdout || '';
    }
    if (!raw) {
        console.warn(`[DEPENDENCIES] No output from npm ls in ${folder}`);
        return [];
    }
    return raw.split(/\r?\n/).filter(line => {
        return !!line.trim() && path_1.default.relative(root, line) !== path_1.default.relative(root, folder);
    });
}
function getProductionDependencies(folderPath) {
    try {
        console.log(`[DEPENDENCIES] Getting production dependencies for: ${folderPath}`);
        const result = getNpmProductionDependencies(folderPath);
        // Account for distro npm dependencies
        const realFolderPath = fs_1.default.realpathSync(folderPath);
        const relativeFolderPath = path_1.default.relative(root, realFolderPath);
        const distroFolderPath = `${root}/.build/distro/npm/${relativeFolderPath}`;
        if (fs_1.default.existsSync(distroFolderPath)) {
            console.log(`[DEPENDENCIES] Found distro folder: ${distroFolderPath}`);
            const distroResult = getNpmProductionDependencies(distroFolderPath);
            result.push(...distroResult);
        }
        const uniqueResult = [...new Set(result)];
        console.log(`[DEPENDENCIES] Found ${uniqueResult.length} unique dependencies`);
        return uniqueResult;
    }
    catch (error) {
        console.error(`[DEPENDENCIES] Error getting production dependencies for ${folderPath}:`, error);
        // 返回空数组而不是抛出错误，让构建继续进行
        return [];
    }
}
if (require.main === module) {
    console.log(JSON.stringify(getProductionDependencies(root), null, '  '));
}
