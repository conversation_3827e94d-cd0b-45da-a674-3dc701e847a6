/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

// @ts-check

import path from 'path';
import { promises as fs } from 'fs';
import { SecureExec } from './secureExec';

const npm = process.platform === 'win32' ? 'npm.cmd' : 'npm';
const rootDir = path.resolve(__dirname, '..', '..');

function runProcess(command: string, args: string[] = []) {
	return new Promise<void>((resolve, reject) => {
		console.log(`[PRELAUNCH] Running: ${command} ${args.join(' ')}`);
		console.log(`[PRELAUNCH] Working directory: ${rootDir}`);

		// Windows特殊处理
		const spawnOptions = {
			cwd: rootDir,
			stdio: 'inherit' as const,
			env: process.env,
			// 在Windows上，对于npm.cmd需要使用shell
			shell: process.platform === 'win32' && command === 'npm.cmd'
		};

		try {
			const child = SecureExec.spawn(command, args, spawnOptions);

			child.on('exit', (code) => {
				console.log(`[PRELAUNCH] Process exited with code: ${code}`);
				if (code === 0) {
					resolve();
				} else {
					process.exit(code ?? 1);
				}
			});

			child.on('error', (error) => {
				console.error(`[PRELAUNCH] Process error:`, error);
				reject(error);
			});
		} catch (error) {
			console.error(`[PRELAUNCH] Spawn error:`, error);
			reject(error);
		}
	});
}

async function exists(subdir: string) {
	try {
		await fs.stat(path.join(rootDir, subdir));
		return true;
	} catch {
		return false;
	}
}

async function ensureNodeModules() {
	if (!(await exists('node_modules'))) {
		await runProcess(npm, ['ci']);
	}
}

async function getElectron() {
	await runProcess(npm, ['run', 'electron']);
}

async function ensureCompiled() {
	if (!(await exists('out'))) {
		await runProcess(npm, ['run', 'compile']);
	}
}

/**
 * 内置扩展模块的类型定义
 */
interface BuiltInExtensionsModule {
	getBuiltInExtensions: () => Promise<void>;
}

/**
 * 安全地导入和调用内置扩展函数
 * @returns Promise<void>
 */
async function safeGetBuiltInExtensions(): Promise<void> {
	try {
		// 使用静态导入路径，避免动态require的污点传播
		// 添加 .js 扩展名以符合 Node.js ES 模块解析要求
		const builtInExtensionsModule = await import('./builtInExtensions.js') as BuiltInExtensionsModule;

		// 验证导入的模块和函数
		if (!builtInExtensionsModule || typeof builtInExtensionsModule !== 'object') {
			throw new Error('Invalid builtInExtensions module');
		}

		const { getBuiltInExtensions } = builtInExtensionsModule;

		if (typeof getBuiltInExtensions !== 'function') {
			throw new Error('getBuiltInExtensions is not a function');
		}

		// 调用验证后的安全函数
		await getBuiltInExtensions();
	} catch (error) {
		console.error('Failed to load or execute built-in extensions:', error);
		throw error;
	}
}

async function main() {
	await ensureNodeModules();
	await getElectron();
	await ensureCompiled();

	// 使用安全的内置扩展加载函数
	await safeGetBuiltInExtensions();
}

if (require.main === module) {
	main().catch(err => {
		console.error(err);
		process.exit(1);
	});
}
