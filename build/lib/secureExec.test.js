"use strict";
/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/
Object.defineProperty(exports, "__esModule", { value: true });
exports.testSecureExec = testSecureExec;
const secureExec_1 = require("./secureExec");
/**
 * 简单的测试函数，验证 SecureExec 的基本功能
 */
async function testSecureExec() {
    console.log('🧪 Testing SecureExec TypeScript implementation...');
    try {
        // 测试命令白名单验证
        console.log('✅ Testing command whitelist...');
        console.assert(secureExec_1.SecureExec.isAllowedCommand('git'), 'git should be allowed');
        console.assert(secureExec_1.SecureExec.isAllowedCommand('node'), 'node should be allowed');
        console.assert(!secureExec_1.SecureExec.isAllowedCommand('rm -rf /'), 'dangerous command should not be allowed');
        console.assert(!secureExec_1.SecureExec.isAllowedCommand('evil-command'), 'unknown command should not be allowed');
        // 测试参数清理
        console.log('✅ Testing argument sanitization...');
        console.assert(secureExec_1.SecureExec.sanitizeArg('safe-arg') === 'safe-arg', 'safe argument should pass through');
        console.assert(secureExec_1.SecureExec.sanitizeArg('arg;rm -rf /') === 'argrm -rf /', 'dangerous characters should be removed');
        console.assert(secureExec_1.SecureExec.sanitizeArg('../../../etc/passwd') === 'etc/passwd', 'path traversal should be prevented');
        // 测试工作目录验证
        console.log('✅ Testing working directory validation...');
        const safeCwd = secureExec_1.SecureExec.validateCwd('./');
        console.assert(safeCwd.includes('JoyCoder-IDE'), 'working directory should be within project root');
        // 测试简单的安全命令执行
        console.log('✅ Testing safe command execution...');
        const result = await secureExec_1.SecureExec.exec('node', ['--version']);
        console.assert(result.includes('v'), 'node --version should return version string');
        console.log('🎉 All tests passed! SecureExec TypeScript implementation is working correctly.');
    }
    catch (error) {
        console.error('❌ Test failed:', error);
        throw error;
    }
}
// 如果直接运行此文件，执行测试
if (require.main === module) {
    testSecureExec().catch(error => {
        console.error('Test execution failed:', error);
        process.exit(1);
    });
}
