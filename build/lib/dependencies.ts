/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

import fs from 'fs';
import path from 'path';
import { SecureExec } from './secureExec';
const root = fs.realpathSync(path.dirname(path.dirname(__dirname)));

function getNpmProductionDependencies(folder: string): string[] {
	let raw: string;

	try {
		// Windows环境下使用npm.cmd
		const npmCommand = process.platform === 'win32' ? 'npm.cmd' : 'npm';
		raw = SecureExec.execSync(npmCommand, ['ls', '--all', '--omit=dev', '--parseable'], {
			cwd: folder,
			env: { ...process.env, NODE_ENV: 'production' },
			shell: process.platform === 'win32' // Windows下需要shell
		});
	} catch (err: any) {
		console.warn(`[DEPENDENCIES] npm ls failed in ${folder}:`, err.message);
		const regex = /^npm ERR! .*$/gm;
		let match: RegExpExecArray | null;

		while (match = regex.exec(err.message)) {
			if (/ELSPROBLEMS/.test(match[0])) {
				continue;
			} else if (/invalid: xterm/.test(match[0])) {
				continue;
			} else if (/A complete log of this run/.test(match[0])) {
				continue;
			} else if (/peer dep missing/.test(match[0])) {
				continue; // 忽略peer dependency警告
			} else if (/ERESOLVE/.test(match[0])) {
				continue; // 忽略依赖解析警告
			} else {
				console.error(`[DEPENDENCIES] Unhandled npm error: ${match[0]}`);
				// 不要抛出错误，而是返回空结果
				return [];
			}
		}

		raw = err.stdout || '';
	}

	if (!raw) {
		console.warn(`[DEPENDENCIES] No output from npm ls in ${folder}`);
		return [];
	}

	return raw.split(/\r?\n/).filter(line => {
		return !!line.trim() && path.relative(root, line) !== path.relative(root, folder);
	});
}

export function getProductionDependencies(folderPath: string): string[] {
	try {
		console.log(`[DEPENDENCIES] Getting production dependencies for: ${folderPath}`);
		const result = getNpmProductionDependencies(folderPath);

		// Account for distro npm dependencies
		const realFolderPath = fs.realpathSync(folderPath);
		const relativeFolderPath = path.relative(root, realFolderPath);
		const distroFolderPath = `${root}/.build/distro/npm/${relativeFolderPath}`;

		if (fs.existsSync(distroFolderPath)) {
			console.log(`[DEPENDENCIES] Found distro folder: ${distroFolderPath}`);
			const distroResult = getNpmProductionDependencies(distroFolderPath);
			result.push(...distroResult);
		}

		const uniqueResult = [...new Set(result)];
		console.log(`[DEPENDENCIES] Found ${uniqueResult.length} unique dependencies`);
		return uniqueResult;
	} catch (error) {
		console.error(`[DEPENDENCIES] Error getting production dependencies for ${folderPath}:`, error);
		// 返回空数组而不是抛出错误，让构建继续进行
		return [];
	}
}

if (require.main === module) {
	console.log(JSON.stringify(getProductionDependencies(root), null, '  '));
}
