"use strict";
/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.getSafeTmpDir = getSafeTmpDir;
exports.validateWorkingDirectory = validateWorkingDirectory;
exports.validateTmpDir = validateTmpDir;
exports.validateAgentTmpDir = validateAgentTmpDir;
exports.validateQuality = validateQuality;
const os_1 = require("os");
const path_1 = __importDefault(require("path"));
/**
 * 安全地获取临时目录路径
 * @returns 验证后的安全临时目录路径
 */
function getSafeTmpDir() {
    const tmpDir = (0, os_1.tmpdir)();
    // 清理路径
    const sanitized = tmpDir
        .replace(/[<>"'&;|`$(){}[\]]/g, '') // 移除危险字符
        .replace(/\.\./g, '') // 防止路径遍历
        .trim();
    // 检查长度限制
    if (sanitized.length === 0 || sanitized.length > 500) {
        throw new Error('Invalid temporary directory path');
    }
    // 标准化路径
    const normalizedPath = path_1.default.resolve(sanitized);
    // 检查是否为合法的临时目录
    const allowedTmpDirs = [
        '/tmp',
        '/var/tmp'
    ];
    // 在 Windows 和 macOS 上，允许系统临时目录
    if (process.platform === 'win32') {
        allowedTmpDirs.push('C:\\Windows\\Temp', 'C:\\temp', 'C:\\tmp');
    }
    else if (process.platform === 'darwin') {
        allowedTmpDirs.push('/private/tmp', '/private/var/tmp');
    }
    const isAllowed = allowedTmpDirs.some(allowedDir => normalizedPath.startsWith(allowedDir)) || normalizedPath.includes('Temp'); // Windows 临时目录通常包含 Temp
    if (!isAllowed) {
        console.warn(`Warning: Using potentially unsafe temporary directory: ${normalizedPath}`);
    }
    return normalizedPath;
}
/**
 * 验证工作目录的安全性
 * @param workingDir 工作目录路径
 * @returns 安全的工作目录路径
 */
function validateWorkingDirectory(workingDir) {
    if (!workingDir || typeof workingDir !== 'string') {
        throw new Error('Invalid working directory');
    }
    // 清理路径
    const sanitized = workingDir
        .replace(/[<>"'&;|`$(){}[\]]/g, '') // 移除危险字符
        .replace(/\.\./g, '') // 防止路径遍历
        .trim();
    // 检查长度限制
    if (sanitized.length === 0 || sanitized.length > 1000) {
        throw new Error('Invalid working directory path');
    }
    // 标准化路径
    const normalizedPath = path_1.default.resolve(sanitized);
    // 检查是否为合法的工作目录（通常应该在项目目录或系统目录下）
    const allowedBaseDirs = [
        path_1.default.resolve(__dirname, '../..'), // 项目根目录
        '/home', // Linux 用户目录
        '/Users', // macOS 用户目录
        'C:\\', // Windows 根目录
        'D:\\' // Windows 其他驱动器
    ];
    const isAllowed = allowedBaseDirs.some(allowedDir => normalizedPath.startsWith(allowedDir));
    if (!isAllowed) {
        throw new Error(`Working directory outside allowed paths: ${normalizedPath}`);
    }
    return normalizedPath;
}
/**
 * 验证临时目录路径的安全性
 * @param tmpDir 临时目录路径
 * @returns 安全的临时目录路径
 */
function validateTmpDir(tmpDir) {
    if (!tmpDir || typeof tmpDir !== 'string') {
        throw new Error('Invalid temporary directory');
    }
    // 清理路径
    const sanitized = tmpDir
        .replace(/[<>"'&;|`$(){}[\]]/g, '') // 移除危险字符
        .replace(/\.\./g, '') // 防止路径遍历
        .trim();
    // 检查长度限制
    if (sanitized.length === 0 || sanitized.length > 500) {
        throw new Error('Invalid temporary directory path');
    }
    // 标准化路径
    const normalizedPath = path_1.default.resolve(sanitized);
    // 检查是否为合法的临时目录
    const allowedTmpDirs = [
        '/tmp',
        '/var/tmp',
        path_1.default.resolve(getSafeTmpDir())
    ];
    const isAllowed = allowedTmpDirs.some(allowedDir => normalizedPath.startsWith(allowedDir));
    if (!isAllowed) {
        throw new Error(`Temporary directory outside allowed paths: ${normalizedPath}`);
    }
    return normalizedPath;
}
/**
 * 验证代理临时目录的安全性
 * @param agentTmpDir 代理临时目录路径
 * @returns 安全的代理临时目录路径
 */
function validateAgentTmpDir(agentTmpDir) {
    if (!agentTmpDir || typeof agentTmpDir !== 'string') {
        throw new Error('Invalid agent temporary directory');
    }
    // 清理路径
    const sanitized = agentTmpDir
        .replace(/[<>"'&;|`$(){}[\]]/g, '') // 移除危险字符
        .replace(/\.\./g, '') // 防止路径遍历
        .trim();
    // 检查长度限制
    if (sanitized.length === 0 || sanitized.length > 1000) {
        throw new Error('Invalid agent temporary directory path');
    }
    // 标准化路径
    const normalizedPath = path_1.default.resolve(sanitized);
    // 检查是否为合法的代理临时目录（通常在项目目录或系统临时目录下）
    const allowedBaseDirs = [
        '/tmp',
        '/var/tmp',
        path_1.default.resolve(getSafeTmpDir()),
        path_1.default.resolve(__dirname, '../..'), // 项目根目录
        '/home', // Linux 用户目录
        '/Users', // macOS 用户目录
        'C:\\', // Windows 根目录
        'D:\\' // Windows 其他驱动器
    ];
    const isAllowed = allowedBaseDirs.some(allowedDir => normalizedPath.startsWith(allowedDir));
    if (!isAllowed) {
        throw new Error(`Agent temporary directory outside allowed paths: ${normalizedPath}`);
    }
    return normalizedPath;
}
/**
 * 验证 VSCODE_QUALITY 环境变量的安全性
 * @param quality 质量标识符
 * @returns 安全的质量标识符
 */
function validateQuality(quality) {
    if (!quality || typeof quality !== 'string') {
        throw new Error('Invalid or missing VSCODE_QUALITY environment variable');
    }
    // 清理输入，移除危险字符
    const sanitized = quality
        .replace(/[<>"'&;|`$(){}[\]]/g, '') // 移除危险字符
        .replace(/\.\./g, '') // 防止路径遍历
        .replace(/[\/\\]/g, '') // 移除路径分隔符
        .trim();
    // 检查长度限制
    if (sanitized.length === 0 || sanitized.length > 50) {
        throw new Error('Invalid VSCODE_QUALITY: length must be between 1 and 50 characters');
    }
    // 只允许字母数字、连字符和下划线
    if (!/^[a-zA-Z0-9_-]+$/.test(sanitized)) {
        throw new Error('Invalid VSCODE_QUALITY: only alphanumeric characters, hyphens, and underscores are allowed');
    }
    // 检查是否为已知的有效质量值
    const validQualities = ['stable', 'insider', 'exploration', 'oss', 'dev', 'test'];
    if (!validQualities.includes(sanitized)) {
        // 如果不在预定义列表中，至少确保它是安全的格式
        console.warn(`Warning: Unknown VSCODE_QUALITY value: ${sanitized}`);
    }
    return sanitized;
}
