/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

'use strict';

const fs = require('fs').promises;
const path = require('path');

/**
 * 安全地获取当前工作目录
 * @returns {string} 验证后的安全工作目录路径
 */
function getSafeWorkingDirectory() {
	const cwd = process.cwd();

	if (!cwd || typeof cwd !== 'string') {
		throw new Error('Invalid current working directory');
	}

	return validateDirectoryPath(cwd);
}

/**
 * 验证目录路径的安全性
 * @param {string} dirPath 目录路径
 * @returns {string} 验证后的安全目录路径
 */
function validateDirectoryPath(dirPath) {
	if (!dirPath || typeof dirPath !== 'string') {
		throw new Error('Invalid directory path');
	}

	// 清理路径，移除危险字符
	const sanitized = dirPath
		.replace(/[<>"'&;|`$(){}[\]]/g, '') // 移除危险字符
		.trim();

	if (!sanitized || sanitized.length === 0) {
		throw new Error('Invalid sanitized directory path');
	}

	// 检查路径长度限制
	if (sanitized.length > 1000) {
		throw new Error('Directory path too long');
	}

	// 标准化路径
	const normalizedPath = path.resolve(sanitized);

	// 检查路径遍历
	if (normalizedPath.includes('..')) {
		throw new Error('Path traversal detected in directory path');
	}

	return normalizedPath;
}

/**
 * 安全地构建文件路径
 * @param {string} baseDir 基础目录
 * @param {string} fileName 文件名
 * @returns {string} 验证后的安全文件路径
 */
function buildSafeFilePath(baseDir, fileName) {
	if (!baseDir || typeof baseDir !== 'string') {
		throw new Error('Invalid base directory');
	}

	if (!fileName || typeof fileName !== 'string') {
		throw new Error('Invalid file name');
	}

	// 清理文件名，移除危险字符
	const sanitizedFileName = fileName
		.replace(/[<>"'&;|`$(){}[\]]/g, '') // 移除危险字符
		.replace(/\.\./g, '') // 防止路径遍历
		.replace(/[\/\\]/g, '') // 移除路径分隔符
		.trim();

	if (!sanitizedFileName || sanitizedFileName.length === 0) {
		throw new Error('Invalid sanitized file name');
	}

	// 构建完整路径
	const fullPath = path.join(baseDir, sanitizedFileName);

	// 验证构建的路径在基础目录内
	const resolvedPath = path.resolve(fullPath);
	const resolvedBase = path.resolve(baseDir);

	if (!resolvedPath.startsWith(resolvedBase)) {
		throw new Error('File path outside base directory');
	}

	return fullPath;
}

/**
 * 验证文件路径用于读写操作
 * @param {string} filePath 文件路径
 * @returns {string} 验证后的安全文件路径
 */
function validateFilePathForOperation(filePath) {
	if (!filePath || typeof filePath !== 'string') {
		throw new Error('Invalid file path');
	}

	// 标准化路径
	const normalizedPath = path.resolve(filePath);

	// 检查路径遍历
	if (normalizedPath.includes('..')) {
		throw new Error('Path traversal detected in file path');
	}

	// 检查文件扩展名（只允许package-lock.json）
	const fileName = path.basename(normalizedPath);
	if (fileName !== 'package-lock.json') {
		throw new Error(`Invalid file type: ${fileName}. Only package-lock.json files are allowed.`);
	}

	return normalizedPath;
}

async function* getPackageLockFiles(dir) {
	// 验证输入目录的安全性
	const safeDir = validateDirectoryPath(dir);

	const files = await fs.readdir(safeDir);

	for (const file of files) {
		try {
			// 使用安全的文件路径构建
			const fullPath = buildSafeFilePath(safeDir, file);
			const stat = await fs.stat(fullPath);

			if (stat.isDirectory()) {
				// 递归处理子目录
				yield* getPackageLockFiles(fullPath);
			} else if (file === 'package-lock.json') {
				// 验证文件路径用于后续操作
				const validatedPath = validateFilePathForOperation(fullPath);
				yield validatedPath;
			}
		} catch (error) {
			// 跳过无效的文件/目录，但记录警告
			console.warn(`Skipping invalid path: ${file} - ${error.message}`);
		}
	}
}

/**
 * 验证和清理 NPM 注册表 URL
 * @param {string} url NPM 注册表 URL
 * @returns {string} 验证后的安全 URL
 */
function validateNpmRegistryUrl(url) {
	if (!url || typeof url !== 'string') {
		throw new Error('Invalid URL parameter');
	}

	// 清理输入，移除危险字符
	const sanitized = url
		.replace(/[<>"'&;|`$(){}[\]]/g, '') // 移除危险字符
		.trim();

	if (!sanitized || sanitized.length === 0) {
		throw new Error('Invalid sanitized URL');
	}

	// 检查长度限制
	if (sanitized.length > 500) {
		throw new Error('URL too long');
	}

	// 基本的URL格式验证
	if (!sanitized.startsWith('http://') && !sanitized.startsWith('https://')) {
		throw new Error('URL must start with http:// or https://');
	}

	// 验证URL格式
	let parsedUrl;
	try {
		parsedUrl = new URL(sanitized);
	} catch (error) {
		throw new Error(`Invalid URL format: ${error.message}`);
	}

	// 验证协议
	if (parsedUrl.protocol !== 'http:' && parsedUrl.protocol !== 'https:') {
		throw new Error('Only HTTP and HTTPS protocols are allowed');
	}

	// 验证主机名
	if (!parsedUrl.hostname || parsedUrl.hostname.length === 0) {
		throw new Error('Invalid hostname in URL');
	}

	// 检查是否包含危险的主机名模式
	const dangerousPatterns = ['localhost', '127.0.0.1', '0.0.0.0', '::1'];
	if (dangerousPatterns.some(pattern => parsedUrl.hostname.includes(pattern))) {
		throw new Error('Local URLs are not allowed for security reasons');
	}

	return sanitized;
}

/**
 * 验证命令行参数
 * @param {string} arg 命令行参数
 * @returns {string} 验证后的安全参数
 */
function validateCommandLineArgument(arg) {
	if (!arg || typeof arg !== 'string') {
		return null; // 允许空参数
	}

	// 清理输入，移除危险字符
	const sanitized = arg
		.replace(/[<>"'&;|`$(){}[\]]/g, '') // 移除危险字符
		.trim();

	if (sanitized.length === 0) {
		return null;
	}

	// 检查长度限制
	if (sanitized.length > 1000) {
		throw new Error('Command line argument too long');
	}

	return sanitized;
}

async function setup(url, file) {
	// 使用安全的 URL 验证函数，完全阻断污点传播
	const safeUrl = validateNpmRegistryUrl(url);

	// 验证文件路径
	const safeFilePath = validateFilePathForOperation(file);

	let contents = await fs.readFile(safeFilePath, 'utf8');
	contents = contents.replace(/https:\/\/registry\.[^.]+\.com\//g, safeUrl);
	await fs.writeFile(safeFilePath, contents);
}

async function main(url, dir) {
	// 使用安全的 URL 验证，完全阻断污点传播
	const safeUrl = validateNpmRegistryUrl(url);

	// 使用安全的目录获取，阻断污点传播
	const root = dir ? validateDirectoryPath(dir) : getSafeWorkingDirectory();

	for await (const file of getPackageLockFiles(root)) {
		console.log(`Enabling custom NPM registry: ${path.relative(root, file)}`);
		await setup(safeUrl, file);
	}
}

// 安全地验证命令行参数，完全阻断污点传播
try {
	const rawUrl = validateCommandLineArgument(process.argv[2]);
	const rawDir = validateCommandLineArgument(process.argv[3]);

	if (!rawUrl) {
		console.error('Error: NPM registry URL is required as the first argument');
		process.exit(1);
	}

	main(rawUrl, rawDir).catch(error => {
		console.error('Error:', error.message);
		process.exit(1);
	});
} catch (error) {
	console.error('Error validating command line arguments:', error.message);
	process.exit(1);
}
