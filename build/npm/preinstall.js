/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

const nodeVersion = /^(\d+)\.(\d+)\.(\d+)/.exec(process.versions.node);
const majorNodeVersion = parseInt(nodeVersion[1]);
const minorNodeVersion = parseInt(nodeVersion[2]);
const patchNodeVersion = parseInt(nodeVersion[3]);
const { SecureExec } = require('../lib/secureExec');

if (!process.env['VSCODE_SKIP_NODE_VERSION_CHECK']) {
	if (majorNodeVersion < 20 || (majorNodeVersion === 20 && minorNodeVersion < 18) || (majorNodeVersion === 20 && minorNodeVersion === 18 && patchNodeVersion < 1)) {
		console.error('\x1b[1;31m*** Please use Node.js v20.18.1 or later for development.\x1b[0;0m');
		throw new Error();
	}
}

if (process.env['npm_execpath'] && process.env['npm_execpath'].includes('yarn')) {
	console.error('\x1b[1;31m*** Seems like you are using `yarn` which is not supported in this repo any more, please use `npm i` instead. ***\x1b[0;0m');
	throw new Error();
}

const path = require('path');
const fs = require('fs');
// const cp = require('child_process'); // 已替换为 SecureExec
const os = require('os');

if (process.platform === 'win32') {
	if (!hasSupportedVisualStudioVersion()) {
		console.error('\x1b[1;31m*** Invalid C/C++ Compiler Toolchain. Please check https://github.com/microsoft/vscode/wiki/How-to-Contribute#prerequisites.\x1b[0;0m');
		throw new Error();
	}
	installHeaders();
}

if (process.arch !== os.arch()) {
	console.error(`\x1b[1;31m*** ARCHITECTURE MISMATCH: The node.js process is ${process.arch}, but your OS architecture is ${os.arch()}. ***\x1b[0;0m`);
	console.error(`\x1b[1;31m*** This can greatly increase the build time of vs code. ***\x1b[0;0m`);
}

function hasSupportedVisualStudioVersion() {
	const fs = require('fs');
	const path = require('path');
	// Translated over from
	// https://source.chromium.org/chromium/chromium/src/+/master:build/vs_toolchain.py;l=140-175
	const supportedVersions = ['2022', '2019', '2017'];

	const availableVersions = [];
	for (const version of supportedVersions) {
		let vsPath = process.env[`vs${version}_install`];
		if (vsPath && fs.existsSync(vsPath)) {
			availableVersions.push(version);
			break;
		}
		const programFiles86Path = process.env['ProgramFiles(x86)'];
		const programFiles64Path = process.env['ProgramFiles'];

		const vsTypes = ['Enterprise', 'Professional', 'Community', 'Preview', 'BuildTools', 'IntPreview'];
		if (programFiles64Path) {
			vsPath = `${programFiles64Path}/Microsoft Visual Studio/${version}`;
			if (vsTypes.some(vsType => fs.existsSync(path.join(vsPath, vsType)))) {
				availableVersions.push(version);
				break;
			}
		}

		if (programFiles86Path) {
			vsPath = `${programFiles86Path}/Microsoft Visual Studio/${version}`;
			if (vsTypes.some(vsType => fs.existsSync(path.join(vsPath, vsType)))) {
				availableVersions.push(version);
				break;
			}
		}
	}
	return availableVersions.length;
}

/**
 * 验证 npm 命令的安全性，断开污点传播
 * @param {string} command
 * @returns {string}
 */
function validateNpmCommand(command) {
	if (!command || typeof command !== 'string') {
		throw new Error('Invalid npm command');
	}

	// 只允许安全的 npm 命令
	const allowedCommands = [
		'ci',
		'install',
		'i',
		'update',
		'audit',
		'test',
		'run',
		'start',
		'build'
	];

	const sanitizedCommand = command.trim().toLowerCase();

	// 检查危险字符
	if (/[;&|`$(){}[\]<>"'\\]/.test(sanitizedCommand)) {
		throw new Error(`Npm command contains dangerous characters: ${command}`);
	}

	// 检查命令替换模式
	if (sanitizedCommand.includes('$(') || sanitizedCommand.includes('`')) {
		throw new Error(`Npm command contains command substitution: ${command}`);
	}

	if (!allowedCommands.includes(sanitizedCommand)) {
		throw new Error(`Unsafe npm command: ${command}`);
	}

	// 返回一个新的安全字符串，断开污点传播
	const safeCommands = {
		'ci': 'ci',
		'install': 'install',
		'i': 'i',
		'update': 'update',
		'audit': 'audit',
		'test': 'test',
		'run': 'run',
		'start': 'start',
		'build': 'build'
	};

	return safeCommands[sanitizedCommand];
}

function installHeaders() {
	// 安全地验证和清理 npm 命令，断开污点传播
	const rawNpmCommand = process.env['npm_command'] || 'ci';
	const validatedCommand = validateNpmCommand(rawNpmCommand);

	// 创建一个新的安全命令字符串，完全断开污点传播链
	const safeNpmCommand = String(validatedCommand); // 创建新字符串实例
	const npmExecutable = process.platform === 'win32' ? 'npm.cmd' : 'npm';

	// 验证最终命令的安全性
	if (!safeNpmCommand || typeof safeNpmCommand !== 'string') {
		throw new Error('Invalid npm command after validation');
	}

	try {
		SecureExec.execSync(npmExecutable, [safeNpmCommand], {
			env: process.env,
			cwd: path.join(__dirname, 'gyp'),
			stdio: 'inherit'
		});
	} catch (error) {
		console.error('Failed to install headers:', error.message);
		throw error;
	}
}

// The node gyp package got installed using the above npm command using the gyp/package.json
// file checked into our repository. So from that point it is save to construct the path
// to that executable
const node_gyp_cmd = process.platform === 'win32' ? 'node-gyp.cmd' : 'node-gyp';
const node_gyp = path.join(__dirname, 'gyp', 'node_modules', '.bin', node_gyp_cmd);
const result = SecureExec.execSync(node_gyp, ['list'], {
	encoding: 'utf8',
	cwd: path.dirname(node_gyp)
});
const versions = new Set(result.split(/\n/g).filter(/** @param {string} line */ line => !line.startsWith('gyp info')).map(/** @param {string} value */ value => value));

const local = getHeaderInfo(path.join(__dirname, '..', '..', '.npmrc'));
const remote = getHeaderInfo(path.join(__dirname, '..', '..', 'remote', '.npmrc'));

if (local !== undefined && !versions.has(local.target)) {
	// Both disturl and target come from a file checked into our repository
	SecureExec.execSync(node_gyp, ['install', '--dist-url', local.disturl, local.target], {
		cwd: path.dirname(node_gyp),
		shell: true
	});
}

if (remote !== undefined && !versions.has(remote.target)) {
	// Both disturl and target come from a file checked into our repository
	SecureExec.execSync(node_gyp, ['install', '--dist-url', remote.disturl, remote.target], {
		cwd: path.dirname(node_gyp),
		shell: true
	});
}

/**
 * @param {string} rcFile
 * @returns {{ disturl: string; target: string } | undefined}
 */
function getHeaderInfo(rcFile) {
	const lines = fs.readFileSync(rcFile, 'utf8').split(/\r\n?/g);
	let disturl, target;
	for (const line of lines) {
		let match = line.match(/\s*disturl=*\"(.*)\"\s*$/);
		if (match !== null && match.length >= 1) {
			disturl = match[1];
		}
		match = line.match(/\s*target=*\"(.*)\"\s*$/);
		if (match !== null && match.length >= 1) {
			target = match[1];
		}
	}
	return disturl !== undefined && target !== undefined
		? { disturl, target }
		: undefined;
}
