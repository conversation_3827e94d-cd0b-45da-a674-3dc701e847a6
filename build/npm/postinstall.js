/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

const fs = require('fs');
const path = require('path');
const os = require('os');
const { dirs } = require('./dirs');
const { SecureExec } = require('../lib/secureExec');
const npm = process.platform === 'win32' ? 'npm.cmd' : 'npm';
const root = path.dirname(path.dirname(__dirname));

/**
 * 验证 npm 命令的安全性，断开污点传播
 * @param {string} command
 * @returns {string}
 */
function validateNpmCommand(command) {
	if (!command || typeof command !== 'string') {
		throw new Error('Invalid npm command');
	}

	// 只允许安全的 npm 命令
	const allowedCommands = [
		'ci',
		'install',
		'i',
		'update',
		'audit',
		'test',
		'run',
		'start',
		'build'
	];

	const sanitizedCommand = command.trim().toLowerCase();

	// 检查危险字符
	if (/[;&|`$(){}[\]<>"'\\]/.test(sanitizedCommand)) {
		throw new Error(`Npm command contains dangerous characters: ${command}`);
	}

	// 检查命令替换模式
	if (sanitizedCommand.includes('$(') || sanitizedCommand.includes('`')) {
		throw new Error(`Npm command contains command substitution: ${command}`);
	}

	if (!allowedCommands.includes(sanitizedCommand)) {
		throw new Error(`Unsafe npm command: ${command}`);
	}

	// 返回一个新的安全字符串，断开污点传播
	const safeCommands = {
		'ci': 'ci',
		'install': 'install',
		'i': 'i',
		'update': 'update',
		'audit': 'audit',
		'test': 'test',
		'run': 'run',
		'start': 'start',
		'build': 'build'
	};

	return safeCommands[sanitizedCommand];
}

/**
 * 记录日志信息
 * @param {string} dir - 目录名
 * @param {string} message - 消息内容
 */
function log(dir, message) {
	if (process.stdout.isTTY) {
		console.log(`\x1b[34m[${dir}]\x1b[0m`, message);
	} else {
		console.log(`[${dir}]`, message);
	}
}

function run(command, args, opts) {
	log(opts.cwd || '.', '$ ' + command + ' ' + args.join(' '));

	try {
		SecureExec.execSync(command, args, {
			cwd: opts.cwd,
			env: opts.env || process.env,
			stdio: opts.stdio || 'inherit'
		});
	} catch (error) {
		console.error(`ERR Failed to execute command: ${error.message}`);
		process.exit(1);
	}
}

/**
 * @param {string} dir
 * @param {*} [opts]
 */
function npmInstall(dir, opts) {
	opts = {
		env: { ...process.env },
		...(opts ?? {}),
		cwd: dir,
		stdio: 'inherit',
		shell: true
	};

	// 安全地验证和清理 npm 命令，断开污点传播
	const rawCommand = process.env['npm_command'] || 'install';
	const validatedCommand = validateNpmCommand(rawCommand);

	// 创建一个新的安全命令字符串，完全断开污点传播链
	const command = String(validatedCommand);

	if (process.env['VSCODE_REMOTE_DEPENDENCIES_CONTAINER_NAME'] && /^(.build\/distro\/npm\/)?remote$/.test(dir)) {
		const userinfo = os.userInfo();
		log(dir, `Installing dependencies inside container ${process.env['VSCODE_REMOTE_DEPENDENCIES_CONTAINER_NAME']}...`);

		opts.cwd = root;
		if (process.env['npm_config_arch'] === 'arm64') {
			run('sudo', ['docker', 'run', '--rm', '--privileged', 'multiarch/qemu-user-static', '--reset', '-p', 'yes'], opts);
		}
		run('sudo', ['docker', 'run', '-e', 'GITHUB_TOKEN', '-v', `${process.env['VSCODE_HOST_MOUNT']}:/root/vscode`, '-v', `${process.env['VSCODE_HOST_MOUNT']}/.build/.netrc:/root/.netrc`, '-w', path.resolve('/root/vscode', dir), process.env['VSCODE_REMOTE_DEPENDENCIES_CONTAINER_NAME'], 'sh', '-c', `\"chown -R root:root ${path.resolve('/root/vscode', dir)} && npm i -g node-gyp-build && npm ci\"`], opts);
		run('sudo', ['chown', '-R', `${userinfo.uid}:${userinfo.gid}`, `${path.resolve(root, dir)}`], opts);
	} else {
		log(dir, 'Installing dependencies...');
		run(npm, command.split(' '), opts);
	}
	removeParcelWatcherPrebuild(dir);
}

function setNpmrcConfig(dir, env) {
	const npmrcPath = path.join(root, dir, '.npmrc');
	const lines = fs.readFileSync(npmrcPath, 'utf8').split('\n');

	for (const line of lines) {
		const trimmedLine = line.trim();
		if (trimmedLine && !trimmedLine.startsWith('#')) {
			const [key, value] = trimmedLine.split('=');
			env[`npm_config_${key}`] = value.replace(/^"(.*)"$/, '$1');
		}
	}

	// Force node-gyp to use process.config on macOS
	// which defines clang variable as expected. Otherwise we
	// run into compilation errors due to incorrect compiler
	// configuration.
	// NOTE: This means the process.config should contain
	// the correct clang variable. So keep the version check
	// in preinstall sync with this logic.
	// Change was first introduced in https://github.com/nodejs/node/commit/6e0a2bb54c5bbeff0e9e33e1a0c683ed980a8a0f
	if (dir === 'remote' && process.platform === 'darwin') {
		env['npm_config_force_process_config'] = 'true';
	} else {
		delete env['npm_config_force_process_config'];
	}

	if (dir === 'build') {
		env['npm_config_target'] = process.versions.node;
		env['npm_config_arch'] = process.arch;
	}
}

function removeParcelWatcherPrebuild(dir) {
	const parcelModuleFolder = path.join(root, dir, 'node_modules', '@parcel');
	if (!fs.existsSync(parcelModuleFolder)) {
		return;
	}

	const parcelModules = fs.readdirSync(parcelModuleFolder);
	for (const moduleName of parcelModules) {
		if (moduleName.startsWith('watcher-')) {
			const modulePath = path.join(parcelModuleFolder, moduleName);
			fs.rmSync(modulePath, { recursive: true, force: true });
			log(dir, `Removed @parcel/watcher prebuilt module ${modulePath}`);
		}
	}
}

for (let dir of dirs) {

	if (dir === '') {
		removeParcelWatcherPrebuild(dir);
		continue; // already executed in root
	}

	let opts;

	if (dir === 'build') {
		opts = {
			env: {
				...process.env
			},
		}
		if (process.env['CC']) { opts.env['CC'] = 'gcc'; }
		if (process.env['CXX']) { opts.env['CXX'] = 'g++'; }
		if (process.env['CXXFLAGS']) { opts.env['CXXFLAGS'] = ''; }
		if (process.env['LDFLAGS']) { opts.env['LDFLAGS'] = ''; }

		setNpmrcConfig('build', opts.env);
		npmInstall('build', opts);
		continue;
	}

	if (/^(.build\/distro\/npm\/)?remote$/.test(dir)) {
		// node modules used by vscode server
		opts = {
			env: {
				...process.env
			},
		}
		if (process.env['VSCODE_REMOTE_CC']) {
			opts.env['CC'] = process.env['VSCODE_REMOTE_CC'];
		} else {
			delete opts.env['CC'];
		}
		if (process.env['VSCODE_REMOTE_CXX']) {
			opts.env['CXX'] = process.env['VSCODE_REMOTE_CXX'];
		} else {
			delete opts.env['CXX'];
		}
		if (process.env['CXXFLAGS']) { delete opts.env['CXXFLAGS']; }
		if (process.env['CFLAGS']) { delete opts.env['CFLAGS']; }
		if (process.env['LDFLAGS']) { delete opts.env['LDFLAGS']; }
		if (process.env['VSCODE_REMOTE_CXXFLAGS']) { opts.env['CXXFLAGS'] = process.env['VSCODE_REMOTE_CXXFLAGS']; }
		if (process.env['VSCODE_REMOTE_LDFLAGS']) { opts.env['LDFLAGS'] = process.env['VSCODE_REMOTE_LDFLAGS']; }
		if (process.env['VSCODE_REMOTE_NODE_GYP']) { opts.env['npm_config_node_gyp'] = process.env['VSCODE_REMOTE_NODE_GYP']; }

		const globalGypPath = path.join(os.homedir(), '.gyp');
		const globalInclude = path.join(globalGypPath, 'include.gypi');
		const tempGlobalInclude = path.join(globalGypPath, 'include.gypi.bak');
		if (process.platform === 'linux' &&
			(process.env['CI'] || process.env['BUILD_ARTIFACTSTAGINGDIRECTORY'])) {
			// Following include file rename should be removed
			// when `Override gnu target for arm64 and arm` step
			// is removed from the product build pipeline.
			if (fs.existsSync(globalInclude)) {
				fs.renameSync(globalInclude, tempGlobalInclude);
			}
		}
		setNpmrcConfig('remote', opts.env);
		npmInstall(dir, opts);
		if (process.platform === 'linux' &&
			(process.env['CI'] || process.env['BUILD_ARTIFACTSTAGINGDIRECTORY'])) {
			if (fs.existsSync(tempGlobalInclude)) {
				fs.renameSync(tempGlobalInclude, globalInclude);
			}
		}
		continue;
	}

	npmInstall(dir, opts);
}

// 使用安全的命令执行
try {
	SecureExec.execSync('git', ['config', 'pull.rebase', 'merges']);
	SecureExec.execSync('git', ['config', 'blame.ignoreRevsFile', '.git-blame-ignore-revs']);
} catch (error) {
	console.warn('Failed to configure git settings:', error.message);
}
