/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/
//@ts-check

const fs = require('fs');
const path = require('path');
const os = require('os');
const { ipcRenderer } = require('electron');

/**
 * 安全地获取用户主目录
 * @returns {string} 验证后的安全主目录路径
 */
function getSafeHomeDirectory() {
	const homeDir = os.homedir();

	if (!homeDir || typeof homeDir !== 'string') {
		throw new Error('Invalid home directory');
	}

	// 清理路径，移除危险字符
	const sanitized = homeDir
		.replace(/[<>"'&;|`$(){}[\]]/g, '') // 移除危险字符
		.trim();

	if (!sanitized || sanitized.length === 0) {
		throw new Error('Invalid sanitized home directory');
	}

	// 检查路径长度限制
	if (sanitized.length > 1000) {
		throw new Error('Home directory path too long');
	}

	// 标准化路径
	const normalizedPath = path.resolve(sanitized);

	// 检查路径遍历
	if (normalizedPath.includes('..')) {
		throw new Error('Path traversal detected in home directory');
	}

	return normalizedPath;
}

/**
 * 安全地构建控制文件路径
 * @returns {string} 验证后的安全控制文件路径
 */
function buildSafeControlFilePath() {
	const safeHomeDir = getSafeHomeDirectory();

	// 构建控制文件路径
	const controlFilePath = path.join(safeHomeDir, '.vscode-oss-dev', 'extensions', 'control.json');

	// 验证构建的路径
	const normalizedPath = path.resolve(controlFilePath);

	// 确保路径在主目录下
	if (!normalizedPath.startsWith(safeHomeDir)) {
		throw new Error('Control file path outside home directory');
	}

	// 验证文件扩展名
	if (!normalizedPath.endsWith('.json')) {
		throw new Error('Invalid control file extension');
	}

	return normalizedPath;
}

/**
 * 验证文件路径用于读取操作
 * @param {string} filePath 文件路径
 * @returns {string} 验证后的安全文件路径
 */
function validateFilePathForReading(filePath) {
	if (!filePath || typeof filePath !== 'string') {
		throw new Error('Invalid file path');
	}

	// 标准化路径
	const normalizedPath = path.resolve(filePath);

	// 检查路径遍历
	if (normalizedPath.includes('..')) {
		throw new Error('Path traversal detected in file path');
	}

	// 检查路径长度
	if (normalizedPath.length > 1000) {
		throw new Error('File path too long');
	}

	// 检查文件是否存在
	if (!fs.existsSync(normalizedPath)) {
		throw new Error(`File does not exist: ${normalizedPath}`);
	}

	return normalizedPath;
}

const builtInExtensionsPath = path.join(__dirname, '..', '..', 'product.json');
const controlFilePath = buildSafeControlFilePath();

/**
 * @param {string} filePath
 */
function readJson(filePath) {
	// 验证文件路径的安全性
	const safeFilePath = validateFilePathForReading(filePath);
	return JSON.parse(fs.readFileSync(safeFilePath, { encoding: 'utf8' }));
}

/**
 * @param {string} filePath
 * @param {any} obj
 */
function writeJson(filePath, obj) {
	// 验证文件路径的安全性（对于写入操作，不需要检查文件是否存在）
	if (!filePath || typeof filePath !== 'string') {
		throw new Error('Invalid file path');
	}

	// 标准化路径
	const normalizedPath = path.resolve(filePath);

	// 检查路径遍历
	if (normalizedPath.includes('..')) {
		throw new Error('Path traversal detected in file path');
	}

	// 检查路径长度
	if (normalizedPath.length > 1000) {
		throw new Error('File path too long');
	}

	fs.writeFileSync(normalizedPath, JSON.stringify(obj, null, 2));
}

/**
 * @param {HTMLFormElement} form
 * @param {string} id
 * @param {string} title
 * @param {string} value
 * @param {boolean} checked
 */
function renderOption(form, id, title, value, checked) {
	const input = document.createElement('input');
	input.type = 'radio';
	input.id = id;
	input.name = 'choice';
	input.value = value;
	input.checked = !!checked;
	form.appendChild(input);

	const label = document.createElement('label');
	label.setAttribute('for', id);
	label.textContent = title;
	form.appendChild(label);

	return input;
}

/**
 * @param {HTMLElement} el
 * @param {any} state
 */
function render(el, state) {
	/**
	 * @param {any} state
	 */
	function setState(state) {
		try {
			writeJson(controlFilePath, state.control);
		} catch (err) {
			console.error(err);
		}

		el.innerHTML = '';
		render(el, state);
	}

	const ul = document.createElement('ul');
	const { builtin, control } = state;

	for (const ext of builtin) {
		const controlState = control[ext.name] || 'marketplace';

		const li = document.createElement('li');
		ul.appendChild(li);

		const name = document.createElement('code');
		name.textContent = ext.name;
		li.appendChild(name);

		const form = document.createElement('form');
		li.appendChild(form);

		const marketplaceInput = renderOption(form, `marketplace-${ext.name}`, 'Marketplace', 'marketplace', controlState === 'marketplace');
		marketplaceInput.onchange = function () {
			control[ext.name] = 'marketplace';
			setState({ builtin, control });
		};

		const disabledInput = renderOption(form, `disabled-${ext.name}`, 'Disabled', 'disabled', controlState === 'disabled');
		disabledInput.onchange = function () {
			control[ext.name] = 'disabled';
			setState({ builtin, control });
		};

		let local = undefined;

		if (controlState !== 'marketplace' && controlState !== 'disabled') {
			local = controlState;
		}

		const localInput = renderOption(form, `local-${ext.name}`, 'Local', 'local', !!local);
		localInput.onchange = async function () {
			const result = await ipcRenderer.invoke('pickdir');

			if (result) {
				control[ext.name] = result;
				setState({ builtin, control });
			}
		};

		if (local) {
			const localSpan = document.createElement('code');
			localSpan.className = 'local';
			localSpan.textContent = local;
			form.appendChild(localSpan);
		}
	}

	el.appendChild(ul);
}

function main() {
	const el = document.getElementById('extensions');
	const builtin = readJson(builtInExtensionsPath).builtInExtensions;
	let control;

	try {
		control = readJson(controlFilePath);
	} catch (err) {
		control = {};
	}

	if (el) {
		render(el, { builtin, control });
	}
}

window.onload = main;
