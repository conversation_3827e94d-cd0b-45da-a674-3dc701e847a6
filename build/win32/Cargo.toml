[package]
name = "inno_updater"
version = "0.12.1"
authors = ["JoyCode <<EMAIL>>"]
build = "build.rs"

[dependencies]
byteorder = "1.4.3"
crc = "3.0.1"
slog = "2.7.0"
slog-async = "2.7.0"
slog-term = "2.9.1"

[target.'cfg(windows)'.dependencies.windows-sys]
version = "0.42"
features = [
    "Win32_Foundation",
    "Win32_System_Shutdown",
    "Win32_UI_WindowsAndMessaging",
    "Win32_System_Threading",
    "Win32_System_LibraryLoader",
    "Win32_System_Diagnostics_Debug",
    "Win32_Storage_FileSystem",
    "Win32_Security",
    "Win32_System_ProcessStatus",
    "Win32_System_Diagnostics_ToolHelp"
]

[profile.release]
lto = true
panic = 'abort'
