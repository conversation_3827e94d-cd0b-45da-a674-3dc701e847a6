/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

const fs = require('fs');
const path = require('path');

/**
 * 测试 TypeScript 自动编译功能
 */
function testAutoCompile() {
	console.log('🧪 Testing TypeScript auto-compilation...');

	try {
		// 检查 TypeScript 源文件是否存在
		const tsFile = path.join(__dirname, 'lib/secureExec.ts');
		const jsFile = path.join(__dirname, 'lib/secureExec.js');

		if (!fs.existsSync(tsFile)) {
			throw new Error('TypeScript source file not found: ' + tsFile);
		}

		if (!fs.existsSync(jsFile)) {
			throw new Error('Compiled JavaScript file not found: ' + jsFile);
		}

		// 检查编译后的文件是否可以加载
		const { SecureExec } = require('./lib/secureExec');

		if (typeof SecureExec.isAllowedCommand !== 'function') {
			throw new Error('SecureExec.isAllowedCommand is not a function');
		}

		// 测试基本功能
		const testResults = {
			gitAllowed: SecureExec.isAllowedCommand('git'),
			evilBlocked: !SecureExec.isAllowedCommand('rm -rf /'),
			sanitizeWorks: SecureExec.sanitizeArg('safe-arg') === 'safe-arg',
			sanitizeCleans: SecureExec.sanitizeArg('arg;rm -rf /') === 'argrm -rf /'
		};

		const allPassed = Object.values(testResults).every(result => result === true);

		if (allPassed) {
			console.log('✅ All tests passed!');
			console.log('✅ TypeScript auto-compilation is working correctly');
			console.log('✅ SecureExec module is functioning properly');
			return true;
		} else {
			console.error('❌ Some tests failed:', testResults);
			return false;
		}

	} catch (error) {
		console.error('❌ Test failed:', error.message);
		return false;
	}
}

// 如果直接运行此脚本
if (require.main === module) {
	const success = testAutoCompile();
	process.exit(success ? 0 : 1);
}

module.exports = { testAutoCompile };
