"use strict";
/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const fs_1 = __importDefault(require("fs"));
const path_1 = __importDefault(require("path"));
const secureExec_1 = require("../../lib/secureExec");
/**
 * 安全地获取当前工作目录
 * @returns 验证后的安全工作目录路径
 */
function getSafeWorkingDirectory() {
    const cwd = process.cwd();
    if (!cwd || typeof cwd !== 'string') {
        throw new Error('Invalid current working directory');
    }
    return validateWorkingDirectory(cwd);
}
/**
 * 验证工作目录的安全性
 * @param workingDir 工作目录路径
 * @returns 安全的工作目录路径
 */
function validateWorkingDirectory(workingDir) {
    if (!workingDir || typeof workingDir !== 'string') {
        throw new Error('Invalid working directory');
    }
    // 清理路径
    const sanitized = workingDir
        .replace(/[<>"'&;|`$(){}[\]]/g, '') // 移除危险字符
        .replace(/\.\./g, '') // 防止路径遍历
        .trim();
    // 检查长度限制
    if (sanitized.length === 0 || sanitized.length > 1000) {
        throw new Error('Invalid working directory path');
    }
    // 标准化路径
    const normalizedPath = path_1.default.resolve(sanitized);
    // 确保在合理的工作目录范围内（例如项目根目录或其子目录）
    const projectRoot = path_1.default.resolve(__dirname, '../../..');
    if (!normalizedPath.startsWith(projectRoot)) {
        throw new Error(`Working directory outside project root: ${normalizedPath}`);
    }
    return normalizedPath;
}
/**
 * 安全地构建输出文件路径
 * @param baseDir 基础目录
 * @param relativePath 相对路径
 * @returns 验证后的安全文件路径
 */
function buildSafeOutputPath(baseDir, relativePath) {
    if (!baseDir || typeof baseDir !== 'string') {
        throw new Error('Invalid base directory');
    }
    if (!relativePath || typeof relativePath !== 'string') {
        throw new Error('Invalid relative path');
    }
    // 清理相对路径
    const sanitizedRelativePath = relativePath
        .replace(/[<>"'&;|`$(){}[\]]/g, '') // 移除危险字符
        .replace(/\.\./g, '') // 防止路径遍历
        .trim();
    if (!sanitizedRelativePath || sanitizedRelativePath.length === 0) {
        throw new Error('Invalid sanitized relative path');
    }
    // 构建完整路径
    const fullPath = path_1.default.resolve(baseDir, sanitizedRelativePath);
    // 验证路径在基础目录内
    if (!fullPath.startsWith(baseDir)) {
        throw new Error('Output path outside base directory');
    }
    // 验证文件扩展名
    const ext = path_1.default.extname(fullPath).toLowerCase();
    const allowedExtensions = ['.ts', '.d.ts', '.js', '.json'];
    if (!allowedExtensions.includes(ext)) {
        throw new Error(`Invalid file extension: ${ext}`);
    }
    return fullPath;
}
/**
 * 安全地验证文件路径用于读取操作
 * @param filePath 文件路径
 * @returns 验证后的安全文件路径
 */
function validateFilePathForReading(filePath) {
    if (!filePath || typeof filePath !== 'string') {
        throw new Error('Invalid file path');
    }
    // 标准化路径
    const normalizedPath = path_1.default.resolve(filePath);
    // 检查路径遍历
    if (normalizedPath.includes('..')) {
        throw new Error('Path traversal detected');
    }
    // 确保文件在项目目录内
    const projectRoot = path_1.default.resolve(__dirname, '../../..');
    if (!normalizedPath.startsWith(projectRoot)) {
        throw new Error('File path outside project directory');
    }
    // 检查文件是否存在
    if (!fs_1.default.existsSync(normalizedPath)) {
        throw new Error(`File does not exist: ${normalizedPath}`);
    }
    return normalizedPath;
}
let tag = '';
try {
    // 使用安全的命令执行获取最新标签
    const revListOutput = secureExec_1.SecureExec.execSync('git', ['rev-list', '--tags', '--max-count=1']).toString().trim();
    tag = secureExec_1.SecureExec.execSync('git', ['describe', '--tags', revListOutput]).toString().trim();
    const dtsUri = `https://raw.githubusercontent.com/microsoft/vscode/${tag}/src/vscode-dts/vscode.d.ts`;
    // 使用安全的工作目录获取函数，阻断污点传播
    const safeWorkingDir = getSafeWorkingDirectory();
    // 使用安全的路径构建函数
    const outPath = buildSafeOutputPath(safeWorkingDir, 'DefinitelyTyped/types/vscode/index.d.ts');
    // 使用安全的命令执行下载文件
    secureExec_1.SecureExec.execSync('curl', [dtsUri, '--output', outPath]);
    updateDTSFile(outPath, tag);
    console.log(`Done updating vscode.d.ts at ${outPath}`);
}
catch (err) {
    console.error(err);
    console.error('Failed to update types');
    process.exit(1);
}
function updateDTSFile(outPath, tag) {
    // 验证文件路径的安全性
    const safeFilePath = validateFilePathForReading(outPath);
    const oldContent = fs_1.default.readFileSync(safeFilePath, 'utf-8');
    const newContent = getNewFileContent(oldContent, tag);
    fs_1.default.writeFileSync(safeFilePath, newContent);
}
function repeat(str, times) {
    const result = new Array(times);
    for (let i = 0; i < times; i++) {
        result[i] = str;
    }
    return result.join('');
}
function convertTabsToSpaces(str) {
    return str.replace(/\t/gm, value => repeat('    ', value.length));
}
function getNewFileContent(content, tag) {
    const oldheader = [
        `/*---------------------------------------------------------------------------------------------`,
        ` *  Copyright (c) Microsoft Corporation. All rights reserved.`,
        ` *  Licensed under the MIT License. See License.txt in the project root for license information.`,
        ` *--------------------------------------------------------------------------------------------*/`
    ].join('\n');
    return convertTabsToSpaces(getNewFileHeader(tag) + content.slice(oldheader.length));
}
function getNewFileHeader(tag) {
    const [major, minor] = tag.split('.');
    const shorttag = `${major}.${minor}`;
    const header = [
        `// Type definitions for Visual Studio Code ${shorttag}`,
        `// Project: https://github.com/microsoft/vscode`,
        `// Definitions by: Visual Studio Code Team, Microsoft <https://github.com/microsoft>`,
        `// Definitions: https://github.com/DefinitelyTyped/DefinitelyTyped`,
        ``,
        `/*---------------------------------------------------------------------------------------------`,
        ` *  Copyright (c) Microsoft Corporation. All rights reserved.`,
        ` *  Licensed under the MIT License.`,
        ` *  See https://github.com/microsoft/vscode/blob/main/LICENSE.txt for license information.`,
        ` *--------------------------------------------------------------------------------------------*/`,
        ``,
        `/**`,
        ` * Type Definition for Visual Studio Code ${shorttag} Extension API`,
        ` * See https://code.visualstudio.com/api for more information`,
        ` */`
    ].join('\n');
    return header;
}
