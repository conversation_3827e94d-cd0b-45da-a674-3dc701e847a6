"use strict";
/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const path_1 = __importDefault(require("path"));
const event_stream_1 = __importDefault(require("event-stream"));
const vinyl_fs_1 = __importDefault(require("vinyl-fs"));
const util = __importStar(require("../lib/util"));
const dependencies_1 = require("../lib/dependencies");
const identity_1 = require("@azure/identity");
const azure = require('gulp-azure-storage');
/**
 * 安全地获取和验证平台标识符
 * @returns 验证后的安全平台标识符
 */
function getSafePlatform() {
    const platform = process.platform;
    // 验证平台值是否为已知的有效值
    const validPlatforms = ['win32', 'darwin', 'linux', 'freebsd', 'openbsd', 'sunos', 'aix'];
    if (!validPlatforms.includes(platform)) {
        throw new Error(`Invalid or unsupported platform: ${platform}`);
    }
    // 额外的安全检查：确保平台字符串不包含危险字符
    if (!/^[a-z0-9]+$/.test(platform)) {
        throw new Error(`Platform contains invalid characters: ${platform}`);
    }
    return platform;
}
/**
 * 安全地构建模块忽略文件路径
 * @param basePath 基础路径
 * @param filename 文件名
 * @returns 验证后的安全路径
 */
function buildSafeModuleIgnorePath(basePath, filename) {
    // 验证输入参数
    if (!basePath || typeof basePath !== 'string') {
        throw new Error('Invalid base path');
    }
    if (!filename || typeof filename !== 'string') {
        throw new Error('Invalid filename');
    }
    // 清理文件名，移除危险字符
    const sanitizedFilename = filename
        .replace(/[<>"'&;|`$(){}[\]]/g, '') // 移除危险字符
        .replace(/\.\./g, '') // 防止路径遍历
        .replace(/[\/\\]/g, ''); // 移除路径分隔符
    if (!sanitizedFilename || sanitizedFilename.length === 0) {
        throw new Error('Invalid sanitized filename');
    }
    // 构建路径
    const fullPath = path_1.default.join(basePath, 'build', sanitizedFilename);
    // 验证构建的路径是否在预期的基础目录下
    const resolvedPath = path_1.default.resolve(fullPath);
    const resolvedBase = path_1.default.resolve(basePath);
    if (!resolvedPath.startsWith(resolvedBase)) {
        throw new Error('Path traversal detected');
    }
    return fullPath;
}
const root = path_1.default.dirname(path_1.default.dirname(__dirname));
const commit = process.env['BUILD_SOURCEVERSION'];
const credential = new identity_1.ClientAssertionCredential(process.env['AZURE_TENANT_ID'], process.env['AZURE_CLIENT_ID'], () => Promise.resolve(process.env['AZURE_ID_TOKEN']));
// optionally allow to pass in explicit base/maps to upload
const [, , base, maps] = process.argv;
function src(base, maps = `${base}/**/*.map`) {
    return vinyl_fs_1.default.src(maps, { base })
        .pipe(event_stream_1.default.mapSync((f) => {
        f.path = `${f.base}/core/${f.relative}`;
        return f;
    }));
}
function main() {
    const sources = [];
    // vscode client maps (default)
    if (!base) {
        const vs = src('out-vscode-min'); // client source-maps only
        sources.push(vs);
        const productionDependencies = (0, dependencies_1.getProductionDependencies)(root);
        const productionDependenciesSrc = productionDependencies.map((d) => path_1.default.relative(root, d)).map((d) => `./${d}/**/*.map`);
        // 使用安全的平台标识符和路径构建
        const safePlatform = getSafePlatform();
        const baseModuleIgnorePath = buildSafeModuleIgnorePath(root, '.moduleignore');
        const platformModuleIgnorePath = buildSafeModuleIgnorePath(root, `.moduleignore.${safePlatform}`);
        const nodeModules = vinyl_fs_1.default.src(productionDependenciesSrc, { base: '.' })
            .pipe(util.cleanNodeModules(baseModuleIgnorePath))
            .pipe(util.cleanNodeModules(platformModuleIgnorePath));
        sources.push(nodeModules);
        const extensionsOut = vinyl_fs_1.default.src(['.build/extensions/**/*.js.map', '!**/node_modules/**'], { base: '.build' });
        sources.push(extensionsOut);
    }
    // specific client base/maps
    else {
        sources.push(src(base, maps));
    }
    return new Promise((c, e) => {
        event_stream_1.default.merge(...sources)
            .pipe(event_stream_1.default.through(function (data) {
            console.log('Uploading Sourcemap', data.relative); // debug
            this.emit('data', data);
        }))
            .pipe(azure.upload({
            account: process.env.AZURE_STORAGE_ACCOUNT,
            credential,
            container: '$web',
            prefix: `sourcemaps/${commit}/`
        }))
            .on('end', () => c())
            .on('error', (err) => e(err));
    });
}
main().catch(err => {
    console.error(err);
    process.exit(1);
});
