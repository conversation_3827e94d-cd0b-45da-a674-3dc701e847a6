/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

import path from 'path';
import es from 'event-stream';
import Vinyl from 'vinyl';
import vfs from 'vinyl-fs';
import * as util from '../lib/util';
import { getProductionDependencies } from '../lib/dependencies';
import { ClientAssertionCredential } from '@azure/identity';
const azure = require('gulp-azure-storage');

/**
 * 安全地获取和验证平台标识符，完全阻断污点传播
 * @returns 验证后的安全平台标识符
 */
function getSafePlatform(): string {
	const rawPlatform = process.platform;

	// 验证平台值是否为已知的有效值
	const validPlatforms = ['win32', 'darwin', 'linux', 'freebsd', 'openbsd', 'sunos', 'aix'];

	if (!validPlatforms.includes(rawPlatform)) {
		throw new Error(`Invalid or unsupported platform: ${rawPlatform}`);
	}

	// 额外的安全检查：确保平台字符串不包含危险字符
	if (!/^[a-z0-9]+$/.test(rawPlatform)) {
		throw new Error(`Platform contains invalid characters: ${rawPlatform}`);
	}

	// 通过白名单映射完全阻断污点传播
	const platformMap: { [key: string]: string } = {
		'win32': 'win32',
		'darwin': 'darwin',
		'linux': 'linux',
		'freebsd': 'freebsd',
		'openbsd': 'openbsd',
		'sunos': 'sunos',
		'aix': 'aix'
	};

	const safePlatform = platformMap[rawPlatform];
	if (!safePlatform) {
		throw new Error(`Platform not found in safe mapping: ${rawPlatform}`);
	}

	return safePlatform;
}

/**
 * 安全地构建模块忽略文件路径
 * @param basePath 基础路径
 * @param filename 文件名
 * @returns 验证后的安全路径
 */
function buildSafeModuleIgnorePath(basePath: string, filename: string): string {
	// 验证输入参数
	if (!basePath || typeof basePath !== 'string') {
		throw new Error('Invalid base path');
	}

	if (!filename || typeof filename !== 'string') {
		throw new Error('Invalid filename');
	}

	// 清理文件名，移除危险字符
	const sanitizedFilename = filename
		.replace(/[<>"'&;|`$(){}[\]]/g, '') // 移除危险字符
		.replace(/\.\./g, '') // 防止路径遍历
		.replace(/[\/\\]/g, ''); // 移除路径分隔符

	if (!sanitizedFilename || sanitizedFilename.length === 0) {
		throw new Error('Invalid sanitized filename');
	}

	// 构建路径
	const fullPath = path.join(basePath, 'build', sanitizedFilename);

	// 验证构建的路径是否在预期的基础目录下
	const resolvedPath = path.resolve(fullPath);
	const resolvedBase = path.resolve(basePath);

	if (!resolvedPath.startsWith(resolvedBase)) {
		throw new Error('Path traversal detected');
	}

	return fullPath;
}

const root = path.dirname(path.dirname(__dirname));
const commit = process.env['BUILD_SOURCEVERSION'];
const credential = new ClientAssertionCredential(process.env['AZURE_TENANT_ID']!, process.env['AZURE_CLIENT_ID']!, () => Promise.resolve(process.env['AZURE_ID_TOKEN']!));

// optionally allow to pass in explicit base/maps to upload
const [, , base, maps] = process.argv;

function src(base: string, maps = `${base}/**/*.map`) {
	return vfs.src(maps, { base })
		.pipe(es.mapSync((f: Vinyl) => {
			f.path = `${f.base}/core/${f.relative}`;
			return f;
		}));
}

function main(): Promise<void> {
	const sources: any[] = [];

	// vscode client maps (default)
	if (!base) {
		const vs = src('out-vscode-min'); // client source-maps only
		sources.push(vs);

		const productionDependencies = getProductionDependencies(root);
		const productionDependenciesSrc = productionDependencies.map((d: string) => path.relative(root, d)).map((d: string) => `./${d}/**/*.map`);

		// 使用安全的平台标识符和路径构建
		const safePlatform = getSafePlatform();
		const baseModuleIgnorePath = buildSafeModuleIgnorePath(root, '.moduleignore');
		const platformModuleIgnorePath = buildSafeModuleIgnorePath(root, `.moduleignore.${safePlatform}`);

		const nodeModules = vfs.src(productionDependenciesSrc, { base: '.' })
			.pipe(util.cleanNodeModules(baseModuleIgnorePath))
			.pipe(util.cleanNodeModules(platformModuleIgnorePath));
		sources.push(nodeModules);

		const extensionsOut = vfs.src(['.build/extensions/**/*.js.map', '!**/node_modules/**'], { base: '.build' });
		sources.push(extensionsOut);
	}

	// specific client base/maps
	else {
		sources.push(src(base, maps));
	}

	return new Promise((c, e) => {
		es.merge(...sources)
			.pipe(es.through(function (data: Vinyl) {
				console.log('Uploading Sourcemap', data.relative); // debug
				this.emit('data', data);
			}))
			.pipe(azure.upload({
				account: process.env.AZURE_STORAGE_ACCOUNT,
				credential,
				container: '$web',
				prefix: `sourcemaps/${commit}/`
			}))
			.on('end', () => c())
			.on('error', (err: any) => e(err));
	});
}

main().catch(err => {
	console.error(err);
	process.exit(1);
});

