/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

import fs from 'fs';
import path from 'path';

/**
 * 验证 VSCODE_QUALITY 环境变量的安全性
 * @param quality 质量标识符
 * @returns 安全的质量标识符
 */
function validateQuality(quality: string | undefined): string {
	if (!quality || typeof quality !== 'string') {
		throw new Error('Invalid or missing VSCODE_QUALITY environment variable');
	}

	// 清理输入，移除危险字符
	const sanitized = quality
		.replace(/[<>"'&;|`$(){}[\]]/g, '') // 移除危险字符
		.replace(/\.\./g, '') // 防止路径遍历
		.replace(/[\/\\]/g, '') // 移除路径分隔符
		.trim();

	// 检查长度限制
	if (sanitized.length === 0 || sanitized.length > 50) {
		throw new Error('Invalid VSCODE_QUALITY: length must be between 1 and 50 characters');
	}

	// 只允许字母数字、连字符和下划线
	if (!/^[a-zA-Z0-9_-]+$/.test(sanitized)) {
		throw new Error('Invalid VSCODE_QUALITY: only alphanumeric characters, hyphens, and underscores are allowed');
	}

	// 检查是否为已知的有效质量值
	const validQualities = ['stable', 'insider', 'exploration', 'oss', 'dev', 'test'];
	if (!validQualities.includes(sanitized)) {
		// 如果不在预定义列表中，至少确保它是安全的格式
		console.warn(`Warning: Unknown VSCODE_QUALITY value: ${sanitized}`);
	}

	return sanitized;
}

interface IBuiltInExtension {
	readonly name: string;
	readonly version: string;
	readonly repo: string;
	readonly metadata: any;
}

interface OSSProduct {
	readonly builtInExtensions: IBuiltInExtension[];
	readonly webBuiltInExtensions?: IBuiltInExtension[];
}

interface Product {
	readonly builtInExtensions?: IBuiltInExtension[] | { 'include'?: IBuiltInExtension[]; 'exclude'?: string[] };
	readonly webBuiltInExtensions?: IBuiltInExtension[];
}

function log(...args: any[]): void {
	console.log(`[${new Date().toLocaleTimeString('en', { hour12: false })}]`, '[distro]', ...args);
}

/**
 * 安全地获取和验证环境变量，完全阻断污点传播
 * @returns 验证后的安全质量值
 */
function getSafeQuality(): string {
	const rawQuality = process.env['VSCODE_QUALITY'];

	// 验证并清理环境变量
	const safeQuality = validateQuality(rawQuality);

	// 通过白名单映射完全阻断污点传播
	const qualityMap: { [key: string]: string } = {
		'stable': 'stable',
		'insider': 'insider',
		'exploration': 'exploration',
		'oss': 'oss',
		'dev': 'dev',
		'test': 'test'
	};

	// 如果质量值在白名单中，返回映射后的安全值
	if (qualityMap[safeQuality]) {
		return qualityMap[safeQuality];
	}

	// 对于不在白名单中的值，进行额外的安全验证
	const extraValidatedQuality = validateQualityPath(safeQuality);

	// 再次通过安全映射确保完全阻断污点传播
	const sanitizedQuality = extraValidatedQuality
		.replace(/[^a-zA-Z0-9_-]/g, '') // 只保留安全字符
		.substring(0, 20); // 限制长度

	if (!sanitizedQuality || sanitizedQuality.length === 0) {
		throw new Error('Invalid quality value after sanitization');
	}

	return sanitizedQuality;
}

/**
 * 验证质量值用于路径构建的安全性
 * @param quality 质量值
 * @returns 验证后的安全质量值
 */
function validateQualityPath(quality: string): string {
	if (!quality || typeof quality !== 'string') {
		throw new Error('Invalid quality value');
	}

	// 再次验证格式
	if (!/^[a-zA-Z0-9_-]+$/.test(quality)) {
		throw new Error('Quality value contains invalid characters for path construction');
	}

	// 检查长度
	if (quality.length > 20) {
		throw new Error('Quality value too long for safe path construction');
	}

	return quality;
}

/**
 * 安全地构建基础路径
 * @param quality 验证后的质量值
 * @returns 安全的基础路径
 */
function buildSafeBasePath(quality: string): string {
	// 构建路径
	const basePath = `.build/distro/mixin/${quality}`;

	// 验证构建的路径
	const resolvedPath = path.resolve(basePath);
	const projectRoot = path.resolve('.');

	if (!resolvedPath.startsWith(projectRoot)) {
		throw new Error('Base path outside project directory');
	}

	// 检查目录是否存在
	if (!fs.existsSync(resolvedPath)) {
		throw new Error(`Distro directory does not exist: ${resolvedPath}`);
	}

	return basePath;
}

/**
 * 安全地验证文件路径
 * @param filePath 文件路径
 * @returns 验证后的安全文件路径
 */
function validateSafeFilePath(filePath: string): string {
	if (!filePath || typeof filePath !== 'string') {
		throw new Error('Invalid file path');
	}

	// 标准化路径
	const normalizedPath = path.resolve(filePath);

	// 检查路径遍历
	if (normalizedPath.includes('..')) {
		throw new Error('Path traversal detected');
	}

	// 确保文件在项目目录内
	const projectRoot = path.resolve('.');
	if (!normalizedPath.startsWith(projectRoot)) {
		throw new Error('File path outside project directory');
	}

	// 检查文件是否存在
	if (!fs.existsSync(normalizedPath)) {
		throw new Error(`File does not exist: ${normalizedPath}`);
	}

	return normalizedPath;
}

function main() {
	// 使用安全的质量值获取函数，完全阻断污点传播
	const quality = getSafeQuality();

	log(`Mixing in distro quality: ${quality}`);

	// 使用安全的路径构建函数
	const basePath = buildSafeBasePath(quality);

	for (const name of fs.readdirSync(basePath)) {
		const distroPath = path.join(basePath, name);
		const ossPath = path.relative(basePath, distroPath);

		if (ossPath === 'product.json') {
			// 验证文件路径的安全性
			const safeDistroPath = validateSafeFilePath(distroPath);
			const safeOssPath = validateSafeFilePath(ossPath);

			const distro = JSON.parse(fs.readFileSync(safeDistroPath, 'utf8')) as Product;
			const oss = JSON.parse(fs.readFileSync(safeOssPath, 'utf8')) as OSSProduct;
			let builtInExtensions = oss.builtInExtensions;

			if (Array.isArray(distro.builtInExtensions)) {
				log('Overwriting built-in extensions:', distro.builtInExtensions.map(e => e.name));

				builtInExtensions = distro.builtInExtensions;
			} else if (distro.builtInExtensions) {
				const include = distro.builtInExtensions['include'] ?? [];
				const exclude = distro.builtInExtensions['exclude'] ?? [];

				log('OSS built-in extensions:', builtInExtensions.map(e => e.name));
				log('Including built-in extensions:', include.map(e => e.name));
				log('Excluding built-in extensions:', exclude);

				builtInExtensions = builtInExtensions.filter(ext => !include.find(e => e.name === ext.name) && !exclude.find(name => name === ext.name));
				builtInExtensions = [...builtInExtensions, ...include];

				log('Final built-in extensions:', builtInExtensions.map(e => e.name));
			} else {
				log('Inheriting OSS built-in extensions', builtInExtensions.map(e => e.name));
			}

			const result = { webBuiltInExtensions: oss.webBuiltInExtensions, ...distro, builtInExtensions };
			fs.writeFileSync(safeOssPath, JSON.stringify(result, null, '\t'), 'utf8');
		} else {
			// 验证文件路径的安全性
			const safeDistroPath = validateSafeFilePath(distroPath);
			const safeOssPath = validateSafeFilePath(ossPath);
			fs.cpSync(safeDistroPath, safeOssPath, { force: true, recursive: true });
		}

		log(distroPath, '✔︎');
	}
}

main();
