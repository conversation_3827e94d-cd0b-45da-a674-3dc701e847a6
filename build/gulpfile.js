/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

// 启用严格模式，捕获常见的编程错误
'use strict';

// 增加事件发射器的最大监听器数量，防止内存泄漏警告
// 由于构建过程中会创建大量事件监听器，默认值(10)可能不够
require('events').EventEmitter.defaultMaxListeners = 100;

// 导入gulp，这是一个基于流的构建系统
const gulp = require('gulp');
// 导入工具函数库，包含一些通用的构建工具函数
const util = require('./lib/util');
// 导入任务定义和管理模块
const task = require('./lib/task');
// 导入编译相关的任务函数
// transpileClientSWC: 使用SWC进行客户端代码转译
// transpileTask: 通用转译任务
// compileTask: 编译任务
// watchTask: 监视文件变化的任务
// compileApiProposalNamesTask: 编译API提案名称的任务
// watchApiProposalNamesTask: 监视API提案名称变化的任务
const { transpileClientSWC, transpileTask, compileTask, watchTask, compileApiProposalNamesTask, watchApiProposalNamesTask } = require('./lib/compilation');

// 导入Monaco编辑器相关的类型检查任务
const { monacoTypecheckTask/* , monacoTypecheckWatchTask */ } = require('./gulpfile.editor');
// 导入扩展编译相关的任务
// compileExtensionsTask: 编译扩展的任务
// watchExtensionsTask: 监视扩展变化的任务
// compileExtensionMediaTask: 编译扩展媒体资源的任务
const { compileExtensionsTask, watchExtensionsTask, compileExtensionMediaTask } = require('./gulpfile.extensions');

// 注册API提案名称相关的任务
// 这些任务负责处理VS Code API提案的名称生成和监视
gulp.task(compileApiProposalNamesTask); // 注册编译API提案名称的任务
gulp.task(watchApiProposalNamesTask);   // 注册监视API提案名称变化的任务

// 定义使用SWC(一个Rust编写的快速JavaScript/TypeScript编译器)进行客户端代码转译的任务
// 1. 首先清空'out'目录
// 2. 然后使用SWC转译'src'目录中的代码到'out'目录
const transpileClientSWCTask = task.define('transpile-client-esbuild', task.series(util.rimraf('out'), transpileTask('src', 'out', true)));
gulp.task(transpileClientSWCTask); // 注册SWC客户端转译任务

// 定义仅进行转译的任务(不使用SWC)
// 1. 首先清空'out'目录
// 2. 然后转译'src'目录中的代码到'out'目录
const transpileClientTask = task.define('transpile-client', task.series(util.rimraf('out'), transpileTask('src', 'out')));
gulp.task(transpileClientTask); // 注册客户端转译任务

// 定义构建脚本编译任务
// 编译 build 目录中的 TypeScript 文件
const compileBuildScriptsTask = task.define('compile-build-scripts', (done) => {
	const typescript = require('typescript');
	const fs = require('fs');
	const path = require('path');

	try {
		// 读取 TypeScript 配置
		const configPath = path.join(__dirname, 'tsconfig.build.json');
		const configFile = typescript.readConfigFile(configPath, typescript.sys.readFile);
		const compilerOptions = typescript.parseJsonConfigFileContent(
			configFile.config,
			typescript.sys,
			path.dirname(configPath)
		);

		// 编译 TypeScript 文件
		const program = typescript.createProgram(compilerOptions.fileNames, compilerOptions.options);
		const emitResult = program.emit();

		// 检查编译错误（只显示严重错误，忽略警告）
		const allDiagnostics = typescript.getPreEmitDiagnostics(program).concat(emitResult.diagnostics);
		const errors = allDiagnostics.filter(d => d.category === typescript.DiagnosticCategory.Error);

		if (errors.length > 0) {
			errors.forEach(diagnostic => {
				if (diagnostic.file) {
					const { line, character } = typescript.getLineAndCharacterOfPosition(diagnostic.file, diagnostic.start);
					const message = typescript.flattenDiagnosticMessageText(diagnostic.messageText, '\n');
					console.error(`❌ ${diagnostic.file.fileName} (${line + 1},${character + 1}): ${message}`);
				} else {
					console.error(`❌ ${typescript.flattenDiagnosticMessageText(diagnostic.messageText, '\n')}`);
				}
			});
			done(new Error('TypeScript compilation failed'));
			return;
		}

		console.log('✅ Build scripts compiled successfully');
		done();
	} catch (error) {
		done(error);
	}
});
gulp.task(compileBuildScriptsTask);

// 定义监视构建脚本变化的任务
const watchBuildScriptsTask = task.define('watch-build-scripts', () => {
	return gulp.watch(['build/**/*.ts'], compileBuildScriptsTask);
});
gulp.task(watchBuildScriptsTask);

// 定义用于开发时的快速编译任务
// 1. 首先清空'out'目录
// 2. 编译API提案名称
// 3. 编译'src'目录中的代码到'out'目录
const compileClientTask = task.define('compile-client', task.series(util.rimraf('out'), compileApiProposalNamesTask, compileTask('src', 'out', false)));
gulp.task(compileClientTask); // 注册客户端编译任务

// 定义监视客户端代码变化的任务
// 1. 首先清空'out'目录
// 2. 并行执行两个任务：监视代码变化并编译到'out'目录，以及监视API提案名称变化
const watchClientTask = task.define('watch-client', task.series(util.rimraf('out'), task.parallel(watchTask('out', false), watchApiProposalNamesTask)));
gulp.task(watchClientTask); // 注册客户端监视任务

// 定义完整的编译任务，并行执行以下任务：
// 1. Monaco编辑器类型检查
// 2. 客户端代码编译
// 3. 扩展编译
// 4. 扩展媒体资源编译
// 5. 构建脚本编译
const _compileTask = task.define('compile', task.parallel(monacoTypecheckTask, compileClientTask, compileExtensionsTask, compileExtensionMediaTask, compileBuildScriptsTask));
gulp.task(_compileTask); // 注册完整编译任务

// 定义并注册监视任务，先构建扩展到.build目录，然后并行执行以下任务：
// 1. 客户端代码监视
// 2. 扩展监视
// 3. 构建脚本监视
// 注意：Monaco编辑器类型检查监视任务被注释掉了
gulp.task(task.define('watch', task.series(
	// 首先构建扩展到.build/extensions目录，确保开发模式下扩展可以正常加载
	task.define('compile-extensions-build-for-dev', () => {
		const { compileAllExtensionsBuildTask } = require('./gulpfile.extensions');
		return compileAllExtensionsBuildTask();
	}),
	// 然后开始监视
	task.parallel(/* monacoTypecheckWatchTask, */ watchClientTask, watchExtensionsTask, watchBuildScriptsTask)
)));

// 导入移除console语句的工具
const { removeConsoleStatements } = require('./lib/remove-console');

// 定义一个移除console语句的任务
const removeConsoleTask = task.define('remove-console', async (done) => {
	try {
		// 主要处理JoyCoder目录下的文件，因为这些文件是通过TypeScript编译器处理的
		// 而不是通过esbuild处理的
		const count = await removeConsoleStatements('out/vs/workbench/contrib/JoyCoder');
		console.log(`成功移除 ${count} 个文件中的console语句`);
		done();
	} catch (error) {
		// 即使出错也不应该导致整个构建失败
		console.error('移除console语句时出错，但构建将继续:', error);
		done();
	}
});

// 定义一个生产构建任务
// 注意：esbuild处理的文件中的console语句已经在optimize.ts中配置移除
// 而这里我们添加了一个额外的任务来移除TypeScript编译器处理的文件中的console语句

// 设置生产环境变量的任务
const setProductionEnvTask = task.define('set-production-env', (done) => {
	// 设置 NODE_ENV 环境变量为 production
	process.env.NODE_ENV = 'production';
	console.log('\u8bbe置生产环境变量: NODE_ENV =', process.env.NODE_ENV);
	done();
});

// 定义一个生产构建任务
// 使用更稳健的方式，先清空输出目录，然后执行各个子任务
const buildProductionTask = task.define('build-production', task.series(
	setProductionEnvTask,
	util.rimraf('out'),
	compileApiProposalNamesTask,
	compileTask('src', 'out', false),
	compileExtensionsTask,
	compileExtensionMediaTask,
	removeConsoleTask
));
gulp.task(buildProductionTask);

// 设置默认任务为完整编译任务
// 当运行'gulp'命令而不指定任务名称时，将执行此任务
gulp.task('default', _compileTask);

// 添加未处理的Promise拒绝事件处理器
// 这确保了在构建过程中如果有Promise被拒绝但没有被捕获，程序会退出并显示错误
process.on('unhandledRejection', (reason, p) => {
	console.log('Unhandled Rejection at: Promise', p, 'reason:', reason);
	process.exit(1); // 以错误代码1退出进程
});

// 动态加载所有其他的gulpfile文件
// 使用glob模式匹配当前目录下所有gulpfile.*.js文件
// 这允许模块化组织构建任务
require('glob').sync('gulpfile.*.js', { cwd: __dirname }) // 查找所有匹配的gulpfile
	.forEach(f => require(`./${f}`)); // 导入每个找到的gulpfile
