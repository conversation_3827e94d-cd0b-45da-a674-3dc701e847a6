"use strict";
/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.SecureExec = void 0;
const cp = __importStar(require("child_process"));
const path = __importStar(require("path"));
/**
 * 安全的命令执行工具，防止命令注入攻击
 * 支持自动编译 TypeScript 到 JavaScript
 */
class SecureExec {
    /**
     * 验证命令是否在允许列表中
     */
    static isAllowedCommand(command) {
        const allowedCommands = [
            'git',
            'cargo',
            'dpkg-deb',
            'fakeroot',
            'chmod',
            'mkdir',
            'node',
            'npm',
            'npm.cmd',
            'yarn',
            'pnpm',
            'python',
            'python3',
            'make',
            'cmake',
            'gcc',
            'g++',
            'clang',
            'rustc',
            'tsc',
            'esbuild',
            'mksnapshot',
            'mksnapshot.cmd',
            'rg',
            'rg.exe',
            'iscc',
            'iscc.exe',
            'innosetup',
            'innosetup.exe',
            'node-gyp',
            'node-gyp.cmd',
            'tar',
            'curl',
            'wget',
            'unzip',
            '7z',
            '7za',
            'openssl',
            'gpg',
            'gpg2',
            'dotnet',
            'dotnet.exe',
            'find',
            'grep',
            'awk',
            'sed',
            'sort',
            'uniq',
            'head',
            'tail',
            'cat',
            'ls',
            'cp',
            'mv',
            'rm',
            'mkdir',
            'rmdir',
            'docker',
            'podman',
            'rpmbuild',
            'snapcraft'
        ];
        return allowedCommands.includes(command);
    }
    /**
     * 清理和验证命令参数
     */
    static sanitizeArg(arg) {
        if (typeof arg !== 'string') {
            return null;
        }
        // 移除危险字符
        const sanitized = arg
            .replace(/[;&|`$(){}[\]<>]/g, '') // 移除命令注入字符
            .replace(/\.\./g, '') // 防止路径遍历
            .trim();
        // 检查长度限制
        if (sanitized.length > 1000) {
            throw new Error('Argument too long');
        }
        return sanitized;
    }
    /**
     * 验证工作目录的安全性
     */
    static validateCwd(cwd) {
        if (!cwd || typeof cwd !== 'string') {
            return process.cwd();
        }
        // 标准化路径
        const normalizedCwd = path.resolve(cwd);
        // 确保在项目根目录内
        const projectRoot = path.resolve(__dirname, '../..');
        if (!normalizedCwd.startsWith(projectRoot)) {
            throw new Error('Working directory outside project root');
        }
        return normalizedCwd;
    }
    /**
     * 安全地执行命令
     */
    static async exec(command, args = [], options = {}) {
        // 验证命令
        if (!this.isAllowedCommand(command)) {
            throw new Error(`Command not allowed: ${command}`);
        }
        // 清理参数
        const sanitizedArgs = args.map(arg => {
            const sanitized = this.sanitizeArg(arg);
            if (sanitized === null) {
                throw new Error(`Invalid argument: ${arg}`);
            }
            return sanitized;
        });
        // 验证工作目录
        const safeCwd = this.validateCwd(options.cwd);
        // 设置安全的执行选项
        const safeOptions = {
            cwd: safeCwd,
            stdio: options.stdio || 'pipe',
            env: options.env || process.env,
            timeout: options.timeout || 300000 // 5分钟超时
        };
        console.log(`[SECURE_EXEC] Executing: ${command} ${sanitizedArgs.join(' ')}`);
        return new Promise((resolve, reject) => {
            const proc = cp.spawn(command, sanitizedArgs, safeOptions);
            let stdout = '';
            let stderr = '';
            if (proc.stdout) {
                proc.stdout.on('data', (data) => {
                    stdout += data.toString();
                });
            }
            if (proc.stderr) {
                proc.stderr.on('data', (data) => {
                    stderr += data.toString();
                });
            }
            proc.on('close', (code) => {
                if (code === 0) {
                    resolve(stdout);
                }
                else {
                    reject(new Error(`Command failed with code ${code}: ${stderr}`));
                }
            });
            proc.on('error', (error) => {
                reject(new Error(`Failed to execute command: ${error.message}`));
            });
        });
    }
    /**
     * 安全地执行同步命令
     */
    static execSync(command, args = [], options = {}) {
        // 验证命令
        if (!this.isAllowedCommand(command)) {
            throw new Error(`Command not allowed: ${command}`);
        }
        // 清理参数
        const sanitizedArgs = args.map(arg => {
            const sanitized = this.sanitizeArg(arg);
            if (sanitized === null) {
                throw new Error(`Invalid argument: ${arg}`);
            }
            return sanitized;
        });
        // 验证工作目录
        const safeCwd = this.validateCwd(options.cwd);
        // 设置安全的执行选项
        const safeOptions = {
            cwd: safeCwd,
            stdio: options.stdio || 'pipe',
            env: options.env || process.env,
            timeout: options.timeout || 300000, // 5分钟超时
            maxBuffer: options.maxBuffer || 1024 * 1024 * 10, // 10MB 缓冲区限制
            encoding: options.encoding || 'utf8',
            shell: options.shell
        };
        console.log(`[SECURE_EXEC_SYNC] Executing: ${command} ${sanitizedArgs.join(' ')}`);
        try {
            const result = cp.spawnSync(command, sanitizedArgs, safeOptions);
            if (result.error) {
                throw result.error;
            }
            if (result.status !== 0) {
                throw new Error(`Command failed with code ${result.status}: ${result.stderr?.toString()}`);
            }
            return result.stdout?.toString() || '';
        }
        catch (error) {
            throw new Error(`Failed to execute command: ${error instanceof Error ? error.message : String(error)}`);
        }
    }
    /**
     * 安全地创建子进程
     */
    static spawn(command, args = [], options = {}) {
        // 验证命令
        if (!this.isAllowedCommand(command)) {
            throw new Error(`Command not allowed: ${command}`);
        }
        // 清理参数
        const sanitizedArgs = args.map(arg => {
            const sanitized = this.sanitizeArg(arg);
            if (sanitized === null) {
                throw new Error(`Invalid argument: ${arg}`);
            }
            return sanitized;
        });
        // 验证工作目录
        const safeCwd = this.validateCwd(options.cwd);
        // 设置安全的执行选项
        const safeOptions = {
            ...options,
            cwd: safeCwd,
            env: options.env || process.env
        };
        console.log(`[SECURE_SPAWN] Spawning: ${command} ${sanitizedArgs.join(' ')}`);
        return cp.spawn(command, sanitizedArgs, safeOptions);
    }
}
exports.SecureExec = SecureExec;
exports.default = SecureExec;
