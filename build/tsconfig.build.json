{"extends": "./tsconfig.json", "compilerOptions": {"allowJs": false, "checkJs": false, "noEmit": false, "skipLibCheck": true, "noUnusedLocals": false, "noUnusedParameters": false, "strict": false, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "removeComments": false, "module": "commonjs", "target": "es2022"}, "include": ["lib/secureExec.ts", "lib/optimize.ts", "lib/dependencies.ts", "lib/tsb/**/*.ts"], "exclude": ["lib/eslint-plugin-vscode/**/*", "node_modules/**"]}