"use strict";
/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const path = __importStar(require("path"));
const fs = __importStar(require("fs"));
const minimatch_1 = __importDefault(require("minimatch"));
const vscode_universal_bundler_1 = require("vscode-universal-bundler");
/**
 * 验证架构值的安全性
 * @param arch 架构值
 * @returns 验证后的安全架构值
 */
function validateArchitecture(arch) {
    if (!arch || typeof arch !== 'string') {
        throw new Error('Invalid or missing VSCODE_ARCH environment variable');
    }
    // 清理输入，移除危险字符
    const sanitized = arch
        .replace(/[<>"'&;|`$(){}[\]]/g, '') // 移除危险字符
        .replace(/\.\./g, '') // 防止路径遍历
        .replace(/[\/\\]/g, '') // 移除路径分隔符
        .trim();
    // 检查长度限制
    if (sanitized.length === 0 || sanitized.length > 20) {
        throw new Error('Invalid VSCODE_ARCH: length must be between 1 and 20 characters');
    }
    // 只允许已知的有效架构值
    const validArchitectures = ['x64', 'arm64', 'universal'];
    if (!validArchitectures.includes(sanitized)) {
        throw new Error(`Invalid VSCODE_ARCH: ${sanitized}. Must be one of: ${validArchitectures.join(', ')}`);
    }
    return sanitized;
}
/**
 * 安全地构建应用路径
 * @param buildDir 构建目录
 * @param arch 架构
 * @param appName 应用名称
 * @returns 验证后的安全应用路径
 */
function buildSafeAppPath(buildDir, arch, appName) {
    if (!buildDir || typeof buildDir !== 'string') {
        throw new Error('Invalid build directory');
    }
    if (!arch || typeof arch !== 'string') {
        throw new Error('Invalid architecture');
    }
    if (!appName || typeof appName !== 'string') {
        throw new Error('Invalid app name');
    }
    // 构建路径
    const appPath = path.join(buildDir, `VSCode-darwin-${arch}`, appName);
    // 验证构建的路径
    const resolvedPath = path.resolve(appPath);
    const resolvedBuildDir = path.resolve(buildDir);
    if (!resolvedPath.startsWith(resolvedBuildDir)) {
        throw new Error('App path outside build directory');
    }
    return appPath;
}
/**
 * 安全地构建产品JSON路径
 * @param appPath 应用路径
 * @returns 验证后的安全产品JSON路径
 */
function buildSafeProductJsonPath(appPath) {
    if (!appPath || typeof appPath !== 'string') {
        throw new Error('Invalid app path');
    }
    // 构建产品JSON路径
    const productJsonPath = path.resolve(appPath, 'Contents', 'Resources', 'app', 'product.json');
    // 验证构建的路径
    const resolvedAppPath = path.resolve(appPath);
    if (!productJsonPath.startsWith(resolvedAppPath)) {
        throw new Error('Product JSON path outside app directory');
    }
    // 验证文件扩展名
    if (path.extname(productJsonPath) !== '.json') {
        throw new Error('Invalid product file extension');
    }
    return productJsonPath;
}
/**
 * 验证构建目录的安全性
 * @param buildDir 构建目录路径
 * @returns 验证后的安全构建目录路径
 */
function validateBuildDirectory(buildDir) {
    if (!buildDir || typeof buildDir !== 'string') {
        throw new Error('Invalid or missing build directory argument');
    }
    // 清理输入，移除危险字符
    const sanitized = buildDir
        .replace(/[<>"'&;|`$(){}[\]]/g, '') // 移除危险字符
        .trim();
    // 检查长度限制
    if (sanitized.length === 0 || sanitized.length > 500) {
        throw new Error('Invalid build directory: length must be between 1 and 500 characters');
    }
    // 标准化路径
    const normalizedPath = path.resolve(sanitized);
    // 检查路径遍历
    if (normalizedPath.includes('..')) {
        throw new Error('Path traversal detected in build directory');
    }
    // 检查是否为有效的目录路径
    if (!fs.existsSync(normalizedPath)) {
        throw new Error('Build directory does not exist');
    }
    // 检查是否为目录
    const stats = fs.statSync(normalizedPath);
    if (!stats.isDirectory()) {
        throw new Error('Build directory is not a directory');
    }
    // 检查是否可读写
    try {
        fs.accessSync(normalizedPath, fs.constants.R_OK | fs.constants.W_OK);
    }
    catch {
        throw new Error('Build directory is not readable/writable');
    }
    return normalizedPath;
}
const root = path.dirname(path.dirname(__dirname));
async function main(buildDir) {
    // 验证并清理架构值，完全阻断污点传播
    const rawArch = process.env['VSCODE_ARCH'];
    const arch = validateArchitecture(rawArch);
    // 验证并清理构建目录，完全阻断污点传播
    const safeBuildDir = validateBuildDirectory(buildDir);
    const product = JSON.parse(fs.readFileSync(path.join(root, 'product.json'), 'utf8'));
    const appName = product.nameLong + '.app';
    // 使用安全的路径构建函数
    const x64AppPath = buildSafeAppPath(safeBuildDir, 'x64', appName);
    const arm64AppPath = buildSafeAppPath(safeBuildDir, 'arm64', appName);
    const asarRelativePath = path.join('Contents', 'Resources', 'app', 'node_modules.asar');
    const outAppPath = buildSafeAppPath(safeBuildDir, arch, appName);
    const productJsonPath = buildSafeProductJsonPath(outAppPath);
    const filesToSkip = [
        '**/CodeResources',
        '**/Credits.rtf',
        // TODO: Should we consider expanding this to other files in this area?
        '**/node_modules/@parcel/node-addon-api/nothing.target.mk'
    ];
    await (0, vscode_universal_bundler_1.makeUniversalApp)({
        x64AppPath,
        arm64AppPath,
        asarPath: asarRelativePath,
        outAppPath,
        force: true,
        mergeASARs: true,
        x64ArchFiles: '*/kerberos.node',
        filesToSkipComparison: (file) => {
            for (const expected of filesToSkip) {
                if ((0, minimatch_1.default)(file, expected)) {
                    return true;
                }
            }
            return false;
        }
    });
    const productJson = JSON.parse(fs.readFileSync(productJsonPath, 'utf8'));
    Object.assign(productJson, {
        darwinUniversalAssetId: 'darwin-universal'
    });
    fs.writeFileSync(productJsonPath, JSON.stringify(productJson, null, '\t'));
}
if (require.main === module) {
    // 安全地验证命令行参数，完全阻断污点传播
    try {
        const rawBuildDir = process.argv[2];
        const safeBuildDir = validateBuildDirectory(rawBuildDir);
        main(safeBuildDir).catch(err => {
            console.error(err);
            process.exit(1);
        });
    }
    catch (err) {
        console.error('Invalid build directory argument:', err);
        process.exit(1);
    }
}
