/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

'use strict';

const { ESLint } = require('eslint');
const { Transform } = require('stream');
const { relative, resolve, normalize } = require('path');
const fancyLog = require('fancy-log');

/**
 * 安全地获取当前工作目录
 * @returns {string} 验证后的安全工作目录路径
 */
function getSafeWorkingDirectory() {
	const cwd = process.cwd();

	if (!cwd || typeof cwd !== 'string') {
		throw new Error('Invalid current working directory');
	}

	// 标准化路径
	const normalizedCwd = normalize(cwd);

	// 检查路径长度限制
	if (normalizedCwd.length > 1000) {
		throw new Error('Working directory path too long');
	}

	// 检查路径遍历
	if (normalizedCwd.includes('..')) {
		throw new Error('Path traversal detected in working directory');
	}

	return normalizedCwd;
}

/**
 * 安全地构建相对文件路径
 * @param {string} filePath 文件路径
 * @returns {string} 验证后的安全相对路径
 */
function buildSafeRelativePath(filePath) {
	if (!filePath || typeof filePath !== 'string') {
		throw new Error('Invalid file path');
	}

	// 清理文件路径，移除危险字符
	const sanitizedPath = filePath
		.replace(/[<>"'&;|`$(){}[\]]/g, '') // 移除危险字符
		.trim();

	if (!sanitizedPath || sanitizedPath.length === 0) {
		throw new Error('Invalid sanitized file path');
	}

	// 检查路径长度限制
	if (sanitizedPath.length > 2000) {
		throw new Error('File path too long');
	}

	// 标准化文件路径
	const normalizedFilePath = normalize(sanitizedPath);

	// 检查路径遍历
	if (normalizedFilePath.includes('..')) {
		throw new Error('Path traversal detected in file path');
	}

	// 获取安全的工作目录
	const safeWorkingDir = getSafeWorkingDirectory();

	// 构建相对路径
	const relativePath = relative(safeWorkingDir, normalizedFilePath);

	// 验证相对路径不会逃出工作目录
	if (relativePath.startsWith('..')) {
		throw new Error('File path outside working directory');
	}

	// 检查文件扩展名白名单（对于ESLint）
	const allowedExtensions = [
		'.js', '.jsx', '.ts', '.tsx', '.vue', '.svelte',
		'.json', '.mjs', '.cjs', '.es6', '.es'
	];

	const ext = relativePath.substring(relativePath.lastIndexOf('.')).toLowerCase();
	if (ext && !allowedExtensions.includes(ext)) {
		console.warn(`Warning: Linting file with non-standard extension: ${ext}`);
	}

	return relativePath;
}

/**
 * @param {Function} action - A function to handle all ESLint results
 * @returns {stream} gulp file stream
 */
function eslint(action) {
	const linter = new ESLint({});
	const formatter = linter.loadFormatter('compact');

	const results = [];
	results.errorCount = 0;
	results.warningCount = 0;

	return transform(
		async (file, enc, cb) => {
			try {
				// 使用安全的路径构建
				const filePath = buildSafeRelativePath(file.path);

				if (file.isNull()) {
					cb(null, file);
					return;
				}

				if (file.isStream()) {
					cb(new Error('vinyl files with Stream contents are not supported'));
					return;
				}

				// TODO: Should this be checked?
				if (await linter.isPathIgnored(filePath)) {
					cb(null, file);
					return;
				}

				const result = (await linter.lintText(file.contents.toString(), { filePath }))[0];
				results.push(result);
				results.errorCount += result.errorCount;
				results.warningCount += result.warningCount;

				const message = (await formatter).format([result]);
				if (message) {
					fancyLog(message);
				}
				cb(null, file);
			} catch (error) {
				cb(error);
			}
		},
		(done) => {
			try {
				action(results);
				done();
			} catch (error) {
				done(error);
			}
		});
}

function transform(transform, flush) {
	return new Transform({
		objectMode: true,
		transform,
		flush
	});
}

module.exports = eslint;
