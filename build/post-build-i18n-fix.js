#!/usr/bin/env node

/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

'use strict';

const fs = require('fs');
const path = require('path');

// 实现jsonc解析器，可以处理包含注释的JSON文件
// 从src/vs/base/common/jsonc.ts复制而来
const jsonc = (function() {
    // First group matches a double quoted string
    // Second group matches a single quoted string
    // Third group matches a multi line comment
    // Forth group matches a single line comment
    // Fifth group matches a trailing comma
    const regexp = /("[^"\\]*(?:\\.[^"\\]*)*")|('[^'\\]*(?:\\.[^'\\]*)*')|(\/\*[^\/\*]*(?:(?:\*|\/)[^\/\*]*)*?\*\/)|(\/{2,}.*?(?:(?:\r?\n)|$))|(,\s*[}\]])/g;

    /**
     * Strips single and multi line JavaScript comments from JSON
     * content. Ignores characters in strings BUT doesn't support
     * string continuation across multiple lines since it is not
     * supported in JSON.
     *
     * @param content the content to strip comments from
     * @returns the content without comments
    */
    function stripComments(content) {
        return content.replace(regexp, function (match, _m1, _m2, m3, m4, m5) {
            // Only one of m1, m2, m3, m4, m5 matches
            if (m3) {
                // A block comment. Replace with nothing
                return '';
            } else if (m4) {
                // Since m4 is a single line comment is is at least of length 2 (e.g. //)
                // If it ends in \r?\n then keep it.
                const length = m4.length;
                if (m4[length - 1] === '\n') {
                    return m4[length - 2] === '\r' ? '\r\n' : '\n';
                }
                else {
                    return '';
                }
            } else if (m5) {
                // Remove the trailing comma
                return match.substring(1);
            } else {
                // We match a string
                return match;
            }
        });
    }

    /**
     * A drop-in replacement for JSON.parse that can parse
     * JSON with comments and trailing commas.
     *
     * @param content the content to strip comments from
     * @returns the parsed content as JSON
    */
    function parse(content) {
        const commentsStripped = stripComments(content);

        try {
            return JSON.parse(commentsStripped);
        } catch (error) {
            const trailingCommasStriped = commentsStripped.replace(/,\s*([}\]])/g, '$1');
            return JSON.parse(trailingCommasStriped);
        }
    }

    return { parse, stripComments };
})();

/**
 * 构建后国际化修复脚本
 * 确保生成的国际化文件使用正确的编码
 */

const REPO_ROOT = path.join(__dirname, '..');

function log(message) {
    console.log(`[i18n-fix] ${message}`);
}

function validateJsonFile(filePath) {
    try {
        const content = fs.readFileSync(filePath, { encoding: 'utf8' });

        // 使用jsonc解析器解析可能包含注释的JSON
        let parsed;
        try {
            parsed = jsonc.parse(content);
        } catch (jsoncError) {
            // 如果jsonc解析失败，尝试标准JSON解析
            try {
                parsed = JSON.parse(content);
            } catch (jsonError) {
                throw jsoncError; // 如果两种方式都失败，抛出原始的jsonc错误
            }
        }

        // 检查是否包含中文字符
        const hasChineseChars = /[\u4e00-\u9fff]/.test(content);
        if (hasChineseChars) {
            log(`验证中文JSON文件: ${path.relative(REPO_ROOT, filePath)}`);

            // 重新写入文件，确保UTF-8编码
            fs.writeFileSync(filePath, JSON.stringify(parsed, null, 2), { encoding: 'utf8' });
            log(`✓ 修复编码: ${path.relative(REPO_ROOT, filePath)}`);
        }

        return true;
    } catch (error) {
        log(`✗ 验证失败: ${path.relative(REPO_ROOT, filePath)} - ${error.message}`);
        return false;
    }
}

function validateJsFile(filePath) {
    try {
        const content = fs.readFileSync(filePath, { encoding: 'utf8' });

        // 检查是否包含中文字符
        const hasChineseChars = /[\u4e00-\u9fff]/.test(content);
        if (hasChineseChars) {
            log(`验证中文JS文件: ${path.relative(REPO_ROOT, filePath)}`);

            // 重新写入文件，确保UTF-8编码
            fs.writeFileSync(filePath, content, { encoding: 'utf8' });
            log(`✓ 修复编码: ${path.relative(REPO_ROOT, filePath)}`);
        }

        return true;
    } catch (error) {
        log(`✗ 验证失败: ${path.relative(REPO_ROOT, filePath)} - ${error.message}`);
        return false;
    }
}

function walkDirectory(dir, callback) {
    if (!fs.existsSync(dir)) {
        return;
    }

    const files = fs.readdirSync(dir);

    for (const file of files) {
        const filePath = path.join(dir, file);
        const stat = fs.statSync(filePath);

        if (stat.isDirectory()) {
            walkDirectory(filePath, callback);
        } else {
            callback(filePath);
        }
    }
}

function fixI18nFiles() {
    log('开始修复国际化文件编码...');

    let fixedCount = 0;
    let errorCount = 0;

    // 1. 修复语言包扩展中的文件
    const languagePackDir = path.join(REPO_ROOT, 'extensions', 'ms-ceintl.vscode-language-pack-zh-hans');
    if (fs.existsSync(languagePackDir)) {
        log('修复中文语言包文件...');

        walkDirectory(languagePackDir, (filePath) => {
            if (filePath.endsWith('.json')) {
                if (validateJsonFile(filePath)) {
                    fixedCount++;
                } else {
                    errorCount++;
                }
            }
        });
    }

    // 2. 修复构建输出中的NLS文件
    const outDir = path.join(REPO_ROOT, 'out');
    if (fs.existsSync(outDir)) {
        log('修复构建输出中的NLS文件...');

        walkDirectory(outDir, (filePath) => {
            const fileName = path.basename(filePath);

            // 修复nls.messages.*.js文件
            if (fileName.match(/^nls\.messages\..+\.js$/)) {
                if (validateJsFile(filePath)) {
                    fixedCount++;
                } else {
                    errorCount++;
                }
            }

            // 修复其他包含中文的JSON文件
            if (fileName.endsWith('.json')) {
                if (validateJsonFile(filePath)) {
                    fixedCount++;
                } else {
                    errorCount++;
                }
            }
        });
    }

    // 3. 修复产品构建中的文件
    const buildDir = path.join(REPO_ROOT, 'build');
    const i18nFiles = [
        path.join(buildDir, 'win32', 'i18n', 'Default.zh-cn.isl'),
        path.join(buildDir, 'win32', 'i18n', 'Default.zh-tw.isl')
    ];

    for (const filePath of i18nFiles) {
        if (fs.existsSync(filePath)) {
            try {
                const content = fs.readFileSync(filePath, { encoding: 'utf8' });
                fs.writeFileSync(filePath, content, { encoding: 'utf8' });
                log(`✓ 修复ISL文件: ${path.relative(REPO_ROOT, filePath)}`);
                fixedCount++;
            } catch (error) {
                log(`✗ 修复ISL文件失败: ${path.relative(REPO_ROOT, filePath)} - ${error.message}`);
                errorCount++;
            }
        }
    }

    log(`修复完成: ${fixedCount} 个文件已修复, ${errorCount} 个文件出错`);

    return errorCount === 0;
}

function validateLanguagePackIntegrity() {
    log('验证语言包完整性...');

    const mainI18nPath = path.join(
        REPO_ROOT,
        'extensions',
        'ms-ceintl.vscode-language-pack-zh-hans',
        'translations',
        'main.i18n.json'
    );

    if (!fs.existsSync(mainI18nPath)) {
        log('✗ 主要中文语言包文件不存在');
        return false;
    }

    try {
        const content = fs.readFileSync(mainI18nPath, { encoding: 'utf8' });
        const parsed = JSON.parse(content);

        // 验证基本结构
        if (!parsed.contents || typeof parsed.contents !== 'object') {
            log('✗ 语言包结构无效');
            return false;
        }

        // 验证是否包含中文内容
        const contentStr = JSON.stringify(parsed.contents);
        const hasChineseChars = /[\u4e00-\u9fff]/.test(contentStr);

        if (!hasChineseChars) {
            log('✗ 语言包不包含中文内容');
            return false;
        }

        log('✓ 语言包完整性验证通过');
        return true;
    } catch (error) {
        log(`✗ 语言包验证失败: ${error.message}`);
        return false;
    }
}

function main() {
    log('开始构建后国际化修复...');

    const success1 = fixI18nFiles();
    const success2 = validateLanguagePackIntegrity();

    if (success1 && success2) {
        log('✓ 国际化修复完成');
        process.exit(0);
    } else {
        log('✗ 国际化修复失败');
        process.exit(1);
    }
}

if (require.main === module) {
    main();
}

module.exports = { fixI18nFiles, validateLanguagePackIntegrity };
