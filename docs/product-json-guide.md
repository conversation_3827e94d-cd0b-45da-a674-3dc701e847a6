# product.json 文件字段详解

`product.json` 是 VS Code/JoyCode-IDE 中的一个重要配置文件，它定义了产品的各种特性、品牌和行为。以下是该文件中常见字段的详细解释：

## 基本信息字段

| 字段名 | 描述 |
|-------|------|
| `nameShort` | 产品的简短名称，通常用于标题栏和菜单 |
| `nameLong` | 产品的完整名称，用于关于对话框和其他正式场合 |
| `applicationName` | 应用程序的标识符，用于文件关联和系统集成 |
| `version` | 产品版本号 |
| `commit` | 构建产品的源代码提交哈希 |
| `date` | 构建日期 |
| `quality` | 构建质量，如 "stable"、"insider" 或 "dev" |

## 品牌和外观

| 字段名 | 描述 |
|-------|------|
| `win32AppUserModelId` | Windows 上的应用程序用户模型 ID |
| `win32MutexName` | Windows 上用于确保单实例运行的互斥体名称 |
| `win32RegValueName` | Windows 注册表值名称 |
| `darwinBundleIdentifier` | macOS 上的应用程序包标识符 |
| `linuxIconName` | Linux 上的图标名称 |
| `applicationIcon` | 应用程序图标路径 |
| `icons` | 不同场景使用的图标集合 |
| `favicon` | 网页图标 |
| `windowsBackgroundColor` | Windows 上的背景颜色 |
| `themeColor` | 主题颜色，用于 PWA 和其他场景 |

## 功能和服务配置

| 字段名 | 描述 |
|-------|------|
| `extensionsGallery` | 扩展市场的配置，包括服务 URL 和发布者 |
| `extensionAllowedProposedApi` | 允许使用提议 API 的扩展列表 |
| `extensionEnabledApiProposals` | 启用的 API 提议列表 |
| `extensionsDownloadUrl` | 扩展下载的基本 URL |
| `updateUrl` | 应用程序更新检查的 URL |
| `releaseNotesUrl` | 发行说明的 URL |
| `keyboardShortcutsUrlMac` | macOS 键盘快捷键参考的 URL |
| `keyboardShortcutsUrlLinux` | Linux 键盘快捷键参考的 URL |
| `keyboardShortcutsUrlWin` | Windows 键盘快捷键参考的 URL |
| `introductoryVideosUrl` | 介绍视频的 URL |
| `tipsAndTricksUrl` | 提示和技巧的 URL |
| `newsletterSignupUrl` | 通讯订阅的 URL |
| `documentationUrl` | 文档的 URL |
| `reportIssueUrl` | 问题报告的 URL |
| `licenseUrl` | 许可证的 URL |
| `privacyStatementUrl` | 隐私声明的 URL |

## 扩展相关配置

| 字段名 | 描述 |
|-------|------|
| `extensionAllowedBadgeProviders` | 允许提供徽章的扩展列表 |
| `extensionKeywords` | 扩展关键词映射，用于搜索和分类 |
| `extensionKind` | 定义特定扩展的运行位置（本地或远程） |
| `builtInExtensions` | 内置扩展列表，这些扩展随产品一起安装 |
| `webBuiltInExtensions` | Web 版本中的内置扩展列表 |

## 遥测和反馈

| 字段名 | 描述 |
|-------|------|
| `aiConfig` | 应用洞察配置，用于遥测 |
| `telemetryEndpoint` | 遥测数据发送的端点 |
| `sendASmile` | 微笑/皱眉反馈功能的配置 |
| `enableTelemetry` | 是否启用遥测收集 |
| `npsSurveyUrl` | NPS 调查的 URL |
| `surveys` | 用户调查配置 |

## 实验性功能和开发者选项

| 字段名 | 描述 |
|-------|------|
| `experimentsUrl` | 实验配置的 URL |
| `enabledExperiments` | 启用的实验列表 |
| `enabledVSCodeWebExperiments` | 在 Web 版本中启用的实验列表 |
| `webEndpointUrlTemplate` | Web 版本的端点 URL 模板 |
| `webviewContentExternalBaseUrlTemplate` | Webview 内容的外部基本 URL 模板 |
| `debuggerFrontendUrl` | 调试器前端的 URL |

## 集成和兼容性

| 字段名 | 描述 |
|-------|------|
| `extensionRuntimeQuality` | 扩展运行时的质量设置 |
| `extensionSyncedKeys` | 在设置同步中包含的扩展设置键 |
| `msftInternalDomains` | 微软内部域名列表，用于特殊处理 |
| `linkProtectionTrustedDomains` | 链接保护中信任的域名列表 |
| `trustedExtensionAuthAccess` | 信任的扩展认证访问列表 |

## JoyCode 特定配置

| 字段名 | 描述 |
|-------|------|
| `joycoder` | JoyCode 特定的配置对象 |
| `joycoder.features` | JoyCode 特定功能的开关 |
| `joycoder.extensionPoints` | JoyCode 自定义扩展点定义 |
| `joycoder.defaultExtensions` | JoyCode 默认安装的扩展列表 |

## 示例

以下是一个简化的 `product.json` 示例：

```json
{
  "nameShort": "JoyCode",
  "nameLong": "JoyCode IDE",
  "applicationName": "joycoder",
  "version": "1.0.0",
  "commit": "abcdef123456",
  "date": "2023-04-07T12:00:00.000Z",
  "quality": "stable",
  "extensionsGallery": {
    "serviceUrl": "https://marketplace.joycoder.com/_apis/public/gallery",
    "itemUrl": "https://marketplace.joycoder.com/items"
  },
  "builtInExtensions": [
    {
      "name": "joycoder.joycoder-fe",
      "version": "1.0.0",
      "repo": "https://github.com/joycoder/joycoder-fe",
      "metadata": {
        "id": "joycoder.joycoder-fe",
        "publisherId": {
          "publisherId": "joycoder",
          "publisherName": "JoyCode",
          "displayName": "JoyCode",
          "flags": "verified"
        },
        "publisherDisplayName": "JoyCode"
      }
    }
  ],
  "extensionAllowedProposedApi": [
    "joycoder.joycoder-fe"
  ],
  "joycoder": {
    "features": {
      "welcomePage": true,
      "automaticUpdates": true
    }
  }
}
```

## 注意事项

1. 不是所有字段都是必需的，具体取决于产品的需求
2. 某些字段可能是特定于某个平台的
3. 修改此文件可能会影响产品的行为和外观
4. 在开发自定义 VS Code 衍生产品时，此文件是主要的配置点
5. 对于 JoyCode-IDE，可能有一些特定的自定义字段

修改 `product.json` 文件时应当谨慎，因为它可能会影响产品的稳定性和功能。建议在修改前备份原始文件，并在测试环境中验证更改。
