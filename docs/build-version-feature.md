# 构建版本功能实现文档

## 概述

本文档描述了在 JoyCode IDE 中实现的构建版本功能，该功能用于改进黑屏检测机制，并添加了语言包损坏检测和自动修复功能。

## 功能背景

之前的黑屏检测机制使用应用程序版本号（`version`）来判断是否需要进行检查。但是版本号在开发过程中可能保持不变，导致无法准确检测到需要清理的情况。通过引入构建版本（`buildVersion`），我们可以在每次打包时生成唯一的标识符，从而更准确地检测应用程序的变化。

## 实现的功能

### 1. 构建版本字段

- **位置**: `product.json`
- **字段名**: `buildVersion`
- **格式**: `version去掉点号 + YYYYMMDDHHMMSS` (version数字 + 14位时间戳)
- **示例**: version "0.7.0" -> buildVersion "07020250617000000"

### 2. 自动更新机制

#### 构建时更新
- **文件**: `build/gulpfile.vscode.js`
- **机制**: 在构建过程中自动生成 version去掉点号 + 时间戳 作为构建版本
- **代码位置**: 第288-298行

#### 手动更新脚本
- **文件**: `JoyCoder-release-scripts/update-commit.mjs`
- **功能**: 同时更新 commit 和 buildVersion (使用新格式)
- **使用方法**: `npm run update-commit [commit-hash]`

### 3. 黑屏检测改进

#### 版本比较机制
- **原理**: 使用 `buildVersion` 替代 `version` 进行比较
- **文件**: `src/vs/code/electron-main/app.ts`
- **方法**: `handleBlackScreenIssues()`, `shouldPerformBlackScreenCheck()`, `saveBlackScreenCheckVersion()`

#### 兼容性处理
- 支持从旧版本（使用 `version`）平滑升级到新版本（使用 `buildVersion`）
- 在读取检查文件时，优先使用 `buildVersion`，如果不存在则回退到 `version`

### 4. 语言包损坏检测和修复

#### 检测机制
- **方法**: `detectLanguagePackCorruption()`
- **检查内容**:
  - 语言包配置文件 (`languagepacks.json`) 的完整性
  - 语言包缓存目录 (`clp`) 中的损坏标记文件
  - 语言包配置的结构完整性

#### 自动修复
- **方法**: `repairLanguagePacks()`
- **修复步骤**:
  1. 清理语言包缓存目录 (`clp`)
  2. 重置或修复语言包配置文件
  3. 记录修复过程和结果

### 5. 关于弹窗显示构建版本

#### 功能说明
- 在应用程序的关于弹窗中显示构建版本信息
- 支持 Electron 版本和浏览器版本两种环境
- 提供中文国际化支持

#### 实现位置
- **Electron 版本**: `src/vs/workbench/electron-sandbox/parts/dialogs/dialogHandler.ts`
- **浏览器版本**: `src/vs/workbench/browser/parts/dialogs/dialogHandler.ts`
- **国际化文件**: `extensions/ms-ceintl.vscode-language-pack-zh-hans/translations/main.i18n.json`

#### 显示内容
**Electron 版本关于弹窗**:
```
版本: 0.7.0
构建版本: 07020250617000000
VS Code版本: 1.98.2
提交: [commit-hash]
日期: [build-date]
Electron: [version]
Chromium: [version]
Node.js: [version]
V8: [version]
OS: [system-info]
```

**浏览器版本关于弹窗**:
```
版本: 0.7.0
构建版本: 07020250617000000
提交: [commit-hash]
日期: [build-date]
浏览器: [user-agent]
```

## 类型定义更新

### IProductConfiguration 接口
- **文件**: `src/vs/base/common/product.ts`
- **新增字段**: `readonly buildVersion?: string;`
- **位置**: 第58行

## 使用方法

### 开发环境
1. 正常进行开发工作
2. 构建版本会在每次构建时自动更新

### 生产环境
1. 使用构建脚本进行打包：
   ```bash
   npm run build:arm
   npm run build:intel
   npm run build:win
   ```

2. 手动更新版本（如需要）：
   ```bash
   npm run update-commit [commit-hash]
   ```

### 测试验证
运行测试脚本验证功能：
```bash
node test-build-version.js
```

## 文件变更清单

### 新增文件
- `doc/build-version-feature.md` - 本文档
- `test-build-version.js` - 测试脚本

### 修改文件
1. `product.json` - 添加 `buildVersion` 字段
2. `build/gulpfile.vscode.js` - 构建时自动更新构建版本
3. `JoyCoder-release-scripts/update-commit.mjs` - 支持更新构建版本
4. `src/vs/code/electron-main/app.ts` - 黑屏检测和语言包修复逻辑
5. `src/vs/base/common/product.ts` - 类型定义更新
6. `src/vs/workbench/electron-sandbox/parts/dialogs/dialogHandler.ts` - Electron版本关于弹窗
7. `src/vs/workbench/browser/parts/dialogs/dialogHandler.ts` - 浏览器版本关于弹窗
8. `extensions/ms-ceintl.vscode-language-pack-zh-hans/translations/main.i18n.json` - 国际化更新

## 技术细节

### 构建版本生成
```javascript
// 生成构建版本：version去掉点号 + 时间戳
const versionWithoutDots = packageJson.version.replace(/\./g, ''); // 例如 0.7.0 -> 070
const timestamp = new Date().toISOString().replace(/[-:T.]/g, '').slice(0, 14); // YYYYMMDDHHMMSS format
const buildVersion = versionWithoutDots + timestamp; // 例如 070YYYYMMDDHHMMSS
```

### 版本比较逻辑
```javascript
const currentBuildVersion = (this.productService as any).buildVersion || this.productService.version || '1.0.0';
const storedBuildVersion = versionData.buildVersion || versionData.version; // 兼容旧版本
return storedBuildVersion !== currentBuildVersion;
```

### 语言包检测
- 检查 `corrupted.info` 标记文件
- 验证 `languagepacks.json` 的 JSON 格式
- 检查语言包配置的必要字段（hash, extensions）

## 注意事项

1. **向后兼容**: 新版本能够正确处理旧版本的检查文件
2. **错误处理**: 所有操作都有适当的错误处理和日志记录
3. **性能影响**: 检查过程是异步的，不会阻塞应用启动
4. **平台支持**: 目前主要针对 macOS 平台，其他平台可能需要调整路径

## 故障排除

### 常见问题
1. **构建版本未更新**: 检查构建脚本是否正确执行
2. **黑屏检测失效**: 检查用户数据目录权限和文件完整性
3. **语言包修复失败**: 检查文件系统权限和磁盘空间

### 调试方法
1. 查看应用日志中的相关信息
2. 检查 `blackscreen-check-version.json` 文件内容
3. 验证 `product.json` 中的 `buildVersion` 字段

## 用户体验改进

### 关于弹窗中的构建版本显示
用户现在可以通过以下方式查看构建版本信息：

1. **菜单访问**: 帮助 → 关于
2. **命令面板**: `workbench.action.showAboutDialog`
3. **快捷键**: 根据平台设置的快捷键

### 显示效果
- 构建版本显示在版本信息的第二行
- 格式清晰易读：`构建版本: 07020250617000000`
- 支持复制功能，方便用户报告问题时提供详细信息

## 未来改进

1. 支持更多平台的路径配置
2. 添加更详细的语言包验证
3. 添加构建版本的历史记录功能
4. 在其他界面中显示构建版本信息（如设置页面）
