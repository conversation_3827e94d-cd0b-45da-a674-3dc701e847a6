# 扩展版本兼容性修复文档

## 问题背景

JoyCoder 是基于 VS Code 的定制化 IDE，它维护两个不同的版本号：

- `version: "0.6.1"` - JoyCoder 自身的产品版本号
- `vscodeVersion: "1.98.2"` - 基于的 VS Code 版本号

在扩展系统中，扩展的兼容性检查应该基于 VS Code 版本号，因为扩展是针对 VS Code API 编写的。但是原始代码中多处使用了 JoyCoder 的产品版本号进行兼容性检查，导致扩展激活失败。

## 错误现象

典型的错误信息：
```
Activating extension 'vscode.json-language-features' failed: 
The language client requires VS Code version ^1.91.0 but received version 0.6.1.
```

## 根本原因分析

### 错误流程
1. JoyCoder 在扩展主机初始化时传递 `version: "0.6.1"`
2. 扩展主机将此版本暴露为 `vscode.version` API
3. `vscode-languageclient` 库检查 `vscode.version`（0.6.1）是否满足 `^1.91.0`
4. 版本检查失败，抛出兼容性错误

### 关键代码位置
在 `vscode-languageclient` 库中：
```javascript
// node_modules/vscode-languageclient/lib/node/main.js:47
const REQUIRED_VSCODE_VERSION = '^1.91.0';

// node_modules/vscode-languageclient/lib/node/main.js:145-147
if (!semver.satisfies(vscode_1.version, REQUIRED_VSCODE_VERSION)) {
    throw new Error(`The language client requires VS Code version ${REQUIRED_VSCODE_VERSION} but received version ${vscode_1.version}.`);
}
```

## 修复方案

### 核心思路
在所有涉及扩展兼容性检查和扩展主机初始化的地方，使用 `productService.vscodeVersion` 替代 `productService.version`。

### 修改文件列表

#### 1. 扩展管理服务
- `src/vs/platform/extensionManagement/node/extensionManagementService.ts`
- `src/vs/platform/extensionManagement/common/extensionGalleryService.ts`
- `src/vs/workbench/services/extensionManagement/browser/webExtensionsScannerService.ts`
- `src/vs/platform/extensionManagement/common/extensionsScannerService.ts`

#### 2. 扩展主机初始化
- `src/vs/workbench/services/extensions/common/remoteExtensionHost.ts`
- `src/vs/workbench/services/extensions/electron-sandbox/localProcessExtensionHost.ts`
- `src/vs/workbench/services/extensions/browser/webWorkerExtensionHost.ts`

## 详细修改说明

### 1. 扩展兼容性检查

**文件：** `src/vs/platform/extensionManagement/node/extensionManagementService.ts`

**修改位置：** 第151-153行
```typescript
// 修改前
if (manifest.engines && manifest.engines.vscode && !isEngineValid(manifest.engines.vscode, this.productService.version, this.productService.date)) {
    throw new Error(nls.localize('incompatible', "Unable to install extension '{0}' as it is not compatible with JoyCode '{1}'.", extensionId, this.productService.version));
}

// 修改后
if (manifest.engines && manifest.engines.vscode && !isEngineValid(manifest.engines.vscode, this.productService.vscodeVersion || this.productService.version, this.productService.date)) {
    throw new Error(nls.localize('incompatible', "Unable to install extension '{0}' as it is not compatible with JoyCode '{1}'.", extensionId, this.productService.vscodeVersion || this.productService.version));
}
```

**原因：** 扩展的 `engines.vscode` 字段指定与 VS Code API 的兼容性，应该用 VS Code 版本检查。

### 2. 扩展市场通信

**文件：** `src/vs/platform/extensionManagement/common/extensionGalleryService.ts`

**修改位置：** 第641-648行
```typescript
// 修改前
this.commonHeadersPromise = resolveMarketplaceHeaders(
    productService.version,  // 发送 0.6.1
    productService,
    // ...
);

// 修改后
this.commonHeadersPromise = resolveMarketplaceHeaders(
    productService.vscodeVersion || productService.version,  // 发送 1.98.2
    productService,
    // ...
);
```

**原因：** 扩展市场需要 VS Code 版本来返回兼容的扩展列表。

### 3. 扩展主机初始化数据

**文件：** `src/vs/workbench/services/extensions/common/remoteExtensionHost.ts`

**修改位置：** 第209-212行
```typescript
// 修改前
return {
    commit: this._productService.commit,
    version: this._productService.version,  // 传递 0.6.1 给扩展
    quality: this._productService.quality,
    // ...
};

// 修改后
return {
    commit: this._productService.commit,
    version: this._productService.vscodeVersion || this._productService.version,  // 传递 1.98.2 给扩展
    quality: this._productService.quality,
    // ...
};
```

**原因：** 这个 `version` 字段会成为扩展中 `vscode.version` API 的值，扩展库会基于此进行兼容性检查。

### 4. 国际化资源加载

**文件：** `src/vs/workbench/services/extensions/browser/webWorkerExtensionHost.ts`

**修改位置：** 第291-293行
```typescript
// 修改前
if (nlsBaseUrl && this._productService.commit && !platform.Language.isDefaultVariant()) {
    nlsUrlWithDetails = URI.joinPath(URI.parse(nlsBaseUrl), this._productService.commit, this._productService.version, platform.Language.value());
}

// 修改后
if (nlsBaseUrl && this._productService.commit && !platform.Language.isDefaultVariant()) {
    nlsUrlWithDetails = URI.joinPath(URI.parse(nlsBaseUrl), this._productService.commit, this._productService.vscodeVersion || this._productService.version, platform.Language.value());
}
```

**原因：** 语言包资源按 VS Code 版本组织，需要使用正确的版本号构建 URL。

## 修复效果

### 修复前
```
❌ 扩展激活失败
❌ vscode.version 返回 "0.6.1"
❌ 扩展兼容性检查失败
❌ 扩展市场返回不兼容的扩展
```

### 修复后
```
✅ 扩展正常激活
✅ vscode.version 返回 "1.98.2"
✅ 扩展兼容性检查通过
✅ 扩展市场返回兼容的扩展
✅ 保持 JoyCoder 独立版本号
```

## 兼容性保障

所有修改都使用了 `productService.vscodeVersion || productService.version` 的形式，确保：

1. **向后兼容：** 如果 `vscodeVersion` 不存在，回退到 `version`
2. **渐进升级：** 可以逐步在产品配置中添加 `vscodeVersion` 字段
3. **零风险：** 不会破坏现有功能

## 测试验证

### 验证步骤
1. 编译项目：`npm run compile`
2. 启动 JoyCoder
3. 检查内置扩展（如 JSON Language Features）是否正常激活
4. 验证 `vscode.version` API 返回正确版本
5. 测试扩展安装和兼容性检查

### 预期结果
- 所有内置扩展正常激活
- 扩展兼容性检查使用 VS Code 版本
- 扩展市场功能正常
- JoyCoder 版本号保持独立

## 总结

此修复确保了 JoyCoder 在保持自身独立版本号的同时，与 VS Code 扩展生态系统完全兼容。通过在关键位置使用 VS Code 版本号进行兼容性检查，解决了扩展激活失败的问题，提升了用户体验。
