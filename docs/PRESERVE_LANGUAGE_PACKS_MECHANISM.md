# 保留语言包的英文切换机制

## 🎯 **需求明确**

根据您的要求，我已经调整了策略：**构建版本不一致时，不要清空语言包，而是切换为英文配置**。

## 🔄 **策略调整**

### ❌ **之前的做法**
```typescript
// 删除所有语言包配置
await this.fileService.writeFile(languagePacksJsonPath, VSBuffer.fromString('{}'));

// 删除 argv.json 文件
await this.fileService.del(argvPath);
```

### ✅ **现在的做法**
```typescript
// 保留语言包配置，不删除
if (await this.fileService.exists(languagePacksJsonPath)) {
    this.logService.info('保留现有语言包配置，不进行删除');
}

// 修改 argv.json 为英文配置，而不是删除
const argvConfig = {
    locale: 'en',
    'force-english-on-version-change': true,
    buildVersion: currentBuildVersion,
    lastVersionSwitch: new Date().toISOString(),
    reason: 'Build version changed - switched to English for stability'
};
await this.fileService.writeFile(argvPath, VSBuffer.fromString(JSON.stringify(argvConfig, null, 4)));
```

## 📋 **具体实现**

### 1. **保留语言包配置**

#### `forceSetToEnglish()` 方法改进：
- ✅ **保留 `languagepacks.json`**：不删除现有的语言包配置
- ✅ **修改 `argv.json`**：设置 `locale: 'en'` 而不是删除文件
- ✅ **添加版本标记**：记录切换原因和时间

#### `completelyCleanOldLanguagePackData()` 方法改进：
- ✅ **保留配置文件**：不删除 `languagepacks.json` 和 `argv.json`
- ✅ **清理缓存**：仍然清理 `clp` 目录和扩展缓存
- ✅ **清理临时文件**：删除可能损坏的临时文件

### 2. **新的 argv.json 配置格式**

```json
{
    "locale": "en",
    "force-english-on-version-change": true,
    "buildVersion": "07020250619104320",
    "lastVersionSwitch": "2025-06-19T11:18:37.000Z",
    "reason": "Build version changed - switched to English for stability"
}
```

### 3. **版本切换记录文件**

创建 `.version-switch-to-english` 文件：
```json
{
    "timestamp": 1640995200000,
    "buildVersion": "07020250619104320",
    "reason": "Build version changed - switched to English configuration",
    "message": "Language packs are preserved. Only locale configuration was changed to English.",
    "languagePacksPreserved": true,
    "configurationChanged": "argv.json locale set to en"
}
```

## 🎯 **预期效果**

### 版本更新后：

1. **立即效果**：
   - ✅ 应用以英文界面启动
   - ✅ 避免中文语言错乱问题
   - ✅ 确保应用稳定性

2. **用户体验**：
   - ✅ 语言包配置被保留
   - ✅ 用户可以轻松切换回中文
   - ✅ 不需要重新下载语言包

3. **切换回中文的方法**：
   - **方法1**：在设置中修改 `locale` 为 `zh-cn`
   - **方法2**：手动编辑 `argv.json`，将 `locale` 改为 `zh-cn`
   - **方法3**：删除 `argv.json` 中的 `force-english-on-version-change` 标记

## 📊 **对比分析**

| 方面 | 删除语言包方式 | 保留语言包方式 |
|------|----------------|----------------|
| **版本更新稳定性** | ✅ 高 | ✅ 高 |
| **清理彻底性** | ✅ 完全清理 | ✅ 清理缓存 |
| **用户便利性** | ❌ 需重新安装 | ✅ 保留配置 |
| **切换回中文** | ❌ 需重新下载 | ✅ 简单设置 |
| **网络依赖** | ❌ 需要网络 | ✅ 无需网络 |
| **用户数据保护** | ❌ 丢失配置 | ✅ 保护配置 |

## 🔧 **实现细节**

### 触发条件（不变）：
```typescript
// 只有在构建版本不同时才触发
const needsCheck = await this.shouldPerformBlackScreenCheck(currentBuildVersion);
if (needsCheck) {
    // 执行英文切换
    await this.resetLanguagePacksForVersionChange(currentJoyCodeSupportDir);
}
```

### 主要操作：
1. **清理缓存**：删除 `clp` 目录和扩展缓存
2. **保留配置**：保留 `languagepacks.json` 文件
3. **修改语言**：将 `argv.json` 的 `locale` 设为 `en`
4. **记录操作**：创建版本切换记录文件

### 环境变量：
```typescript
process.env['VSCODE_VERSION_SWITCH_ENGLISH'] = currentBuildVersion;
process.env['VSCODE_LOCALE_OVERRIDE'] = 'en';
```

## 🧪 **测试场景**

### 场景1：版本更新后启动
1. 应用检测到版本变化
2. 执行英文切换机制
3. 应用以英文界面启动
4. **验证**：界面为英文，功能正常

### 场景2：用户切换回中文
1. 打开设置 (Settings)
2. 搜索 "locale"
3. 修改为 "zh-cn"
4. 重启应用
5. **验证**：成功切换到中文界面

### 场景3：再次版本更新
1. 用户当前使用中文
2. 应用版本再次更新
3. 再次触发英文切换
4. **验证**：切换到英文，但中文配置仍保留

## 📝 **日志输出示例**

```
[info] 构建版本不一致，完全替换为新版本语言包
[info] 开始强制重置为英文语言（构建版本变化）...
[info] 开始清理语言包缓存（保留配置文件）...
[info] 删除语言包缓存目录...
[info] 保留语言包配置文件 languagepacks.json
[info] 保留 argv.json 配置文件（稍后修改为英文配置）
[info] 语言包缓存清理完成（配置文件已保留）
[info] 强制切换为英文配置（保留语言包）...
[info] 保留现有语言包配置，不进行删除
[info] 已设置 argv.json 为英文配置
[info] 已创建版本切换记录文件
[info] 强制切换为英文配置完成（语言包已保留）
```

## 🎉 **总结**

这个改进后的机制完美平衡了：

1. **稳定性**：版本更新时强制英文，避免兼容性问题
2. **便利性**：保留语言包配置，用户可轻松切换回中文
3. **效率性**：不需要重新下载语言包
4. **用户友好**：保护用户的语言偏好设置

现在的机制既解决了版本更新时的稳定性问题，又保持了用户体验的连续性。
