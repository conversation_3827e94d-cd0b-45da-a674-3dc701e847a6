# IDE 与扩展的登录信息同步机制

## 概述

本文档描述了IDE与扩展之间的登录信息同步机制，包括：
- IDE登录后同步到扩展
- 扩展登录后同步到IDE
- 登录状态实时同步

## 通信架构

### 1. 登录信息接口

```typescript
// 用户信息接口
interface IUserInfo {
  userName?: string;
  userToken?: string;
  erp?: string;
  ptKey?: string;
}

// 登录状态
enum AuthState {
    LoggedIn,
    LoggedOut,
    TokenExpired
}

// 登录消息
interface IAuthMessage {
    type: 'LOGIN' | 'LOGOUT' | 'TOKEN_REFRESH';
    userInfo?: IUserInfo;
    timestamp: number;
}
```

### 2. 实现机制

通过以下机制实现登录信息同步：

1. **消息通道**：使用 `vscode.postMessage` 和 `window.addEventListener('message')`
2. **状态管理**：维护登录状态和用户信息
3. **Token管理**：处理token的刷新和过期
4. **存储机制**：安全存储认证信息

## 实现示例

### 1. IDE 端实现

```typescript
// IDE认证服务
class IDEAuthService {
    private _userInfo: IUserInfo | null = null;
    private _authState: AuthState = AuthState.LoggedOut;
    private readonly _onAuthStateChanged = new EventEmitter<AuthState>();

    constructor() {
        // 监听扩展的登录消息
        window.addEventListener('message', (event) => {
            const message: IAuthMessage = event.data;
            if (message.type === 'LOGIN') {
                this._handleExtensionLogin(message.userInfo!);
            } else if (message.type === 'LOGOUT') {
                this._handleExtensionLogout();
            }
        });
    }

    // IDE登录
    public async login(username: string, password: string): Promise<void> {
        try {
            // 执行IDE登录逻辑
            const userInfo = await this._performLogin(username, password);

            // 更新状态
            this._userInfo = userInfo;
            this._authState = AuthState.LoggedIn;
            this._onAuthStateChanged.fire(this._authState);

            // 同步到扩展
            this._syncToExtension({
                type: 'LOGIN',
                userInfo,
                timestamp: Date.now()
            });
        } catch (error) {
            console.error('IDE login failed:', error);
            throw error;
        }
    }

    // IDE登出
    public async logout(): Promise<void> {
        try {
            // 执行IDE登出逻辑
            await this._performLogout();

            // 更新状态
            this._userInfo = null;
            this._authState = AuthState.LoggedOut;
            this._onAuthStateChanged.fire(this._authState);

            // 同步到扩展
            this._syncToExtension({
                type: 'LOGOUT',
                timestamp: Date.now()
            });
        } catch (error) {
            console.error('IDE logout failed:', error);
            throw error;
        }
    }

    // 处理扩展登录
    private _handleExtensionLogin(userInfo: IUserInfo): void {
        this._userInfo = userInfo;
        this._authState = AuthState.LoggedIn;
        this._onAuthStateChanged.fire(this._authState);
        // 更新IDE UI显示
        this._updateIDEUI();
    }

    // 处理扩展登出
    private _handleExtensionLogout(): void {
        this._userInfo = null;
        this._authState = AuthState.LoggedOut;
        this._onAuthStateChanged.fire(this._authState);
        // 更新IDE UI显示
        this._updateIDEUI();
    }

    // 同步到扩展
    private _syncToExtension(message: IAuthMessage): void {
        vscode.postMessage(message);
    }

    private _updateIDEUI(): void {
        // 更新IDE界面显示登录状态
    }
}
```

### 2. 扩展端实现

```typescript
// 扩展认证服务
class ExtensionAuthService {
    private _userInfo: IUserInfo | null = null;
    private _authState: AuthState = AuthState.LoggedOut;
    private readonly _onAuthStateChanged = new vscode.EventEmitter<AuthState>();

    constructor(context: vscode.ExtensionContext) {
        // 监听IDE的登录消息
        context.subscriptions.push(
            vscode.window.registerWebviewViewProvider('authView', {
                webviewView: {
                    onDidReceiveMessage: (message: IAuthMessage) => {
                        if (message.type === 'LOGIN') {
                            this._handleIDELogin(message.userInfo!);
                        } else if (message.type === 'LOGOUT') {
                            this._handleIDELogout();
                        }
                    }
                }
            })
        );
    }

    // 扩展登录
    public async login(token: string): Promise<void> {
        try {
            // 执行扩展登录逻辑
            const userInfo = await this._performTokenLogin(token);

            // 更新状态
            this._userInfo = userInfo;
            this._authState = AuthState.LoggedIn;
            this._onAuthStateChanged.fire(this._authState);

            // 同步到IDE
            this._syncToIDE({
                type: 'LOGIN',
                userInfo,
                timestamp: Date.now()
            });
        } catch (error) {
            console.error('Extension login failed:', error);
            throw error;
        }
    }

    // 扩展登出
    public async logout(): Promise<void> {
        try {
            // 执行扩展登出逻辑
            await this._performLogout();

            // 更新状态
            this._userInfo = null;
            this._authState = AuthState.LoggedOut;
            this._onAuthStateChanged.fire(this._authState);

            // 同步到IDE
            this._syncToIDE({
                type: 'LOGOUT',
                timestamp: Date.now()
            });
        } catch (error) {
            console.error('Extension logout failed:', error);
            throw error;
        }
    }

    // 处理IDE登录
    private _handleIDELogin(userInfo: IUserInfo): void {
        this._userInfo = userInfo;
        this._authState = AuthState.LoggedIn;
        this._onAuthStateChanged.fire(this._authState);
        // 更新扩展状态
        this._updateExtensionState();
    }

    // 处理IDE登出
    private _handleIDELogout(): void {
        this._userInfo = null;
        this._authState = AuthState.LoggedOut;
        this._onAuthStateChanged.fire(this._authState);
        // 更新扩展状态
        this._updateExtensionState();
    }

    // 同步到IDE
    private _syncToIDE(message: IAuthMessage): void {
        // 发送消息到IDE
        vscode.commands.executeCommand('workbench.action.joycoderIsLoggedIn', message);
    }

    private _updateExtensionState(): void {
        // 更新扩展状态和UI
    }
}
```

### 3. 使用示例

```typescript
// IDE端
const ideAuthService = new IDEAuthService();

// IDE登录
await ideAuthService.login('username', 'password');

// IDE登出
await ideAuthService.logout();

// 扩展端
const extensionAuthService = new ExtensionAuthService(context);

// 扩展登录
await extensionAuthService.login('oauth-token');

// 扩展登出
await extensionAuthService.logout();
```

## 安全考虑

1. **Token存储**
   - IDE端使用安全存储机制
   - 扩展端使用 `SecretStorage` API
   - 避免明文存储敏感信息

2. **通信安全**
   - 使用安全的通信通道
   - 验证消息来源
   - 加密敏感数据

3. **状态管理**
   - 及时清理过期token
   - 处理token刷新
   - 保持状态一致性

## 最佳实践

1. **错误处理**
   - 完善的错误处理机制
   - 友好的错误提示
   - 自动重试机制

2. **状态同步**
   - 确保IDE和扩展状态一致
   - 处理网络异常情况
   - 实现重连机制

3. **用户体验**
   - 无缝的登录体验
   - 状态实时更新
   - 清晰的状态指示

## 注意事项

1. 避免循环同步
2. 处理并发登录情况
3. 注意内存泄漏
4. 实现优雅降级
5. 考虑多设备同步
