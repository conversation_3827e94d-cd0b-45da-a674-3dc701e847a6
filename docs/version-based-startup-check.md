# JoyCode 基于版本号的启动检查机制

## 概述

为了优化 JoyCode 的启动性能，我们实现了基于版本号的启动检查机制。该机制确保只有在版本号发生变化时才进行完整的配置验证和黑屏检测，避免每次启动都执行不必要的检查。

## 实现原理

### 版本号检查逻辑

1. **存储版本号**：在用户配置目录中存储当前应用版本号
2. **比较版本号**：启动时比较存储的版本号与当前版本号
3. **条件检查**：只有在版本号不一致时才执行完整检查
4. **更新版本号**：检查完成后更新存储的版本号

### 检查范围

该机制应用于以下两个主要检查：

1. **配置文件验证** (`validateUserConfiguration`)
2. **黑屏问题检测** (`handleBlackScreenIssues`)

## 技术实现

### 1. 配置文件验证的版本检查

#### 文件位置
- 版本文件：`~/.joycoder-editor/version.json`

#### 核心函数

```typescript
// 获取存储的版本号
async function getStoredVersion(logService: ILogService): Promise<string | null>

// 保存当前版本号
async function saveCurrentVersion(currentVersion: string, logService: ILogService): Promise<void>

// 检查是否需要进行配置检查
async function shouldPerformConfigCheck(currentVersion: string, logService: ILogService): Promise<boolean>
```

#### 工作流程

1. 获取当前产品版本号：`productService.version`
2. 读取存储的版本号文件
3. 比较版本号：
   - 如果版本号一致 → 跳过检查
   - 如果版本号不一致或文件不存在 → 执行检查
4. 检查完成后保存当前版本号

### 2. 黑屏检测的版本检查

#### 文件位置
- 版本文件：`~/Library/Application Support/JoyCode/blackscreen-check-version.json`

#### 核心函数

```typescript
// 检查是否需要进行黑屏检查
private async shouldPerformBlackScreenCheck(currentVersion: string): Promise<boolean>

// 保存黑屏检查的版本号
private async saveBlackScreenCheckVersion(currentVersion: string): Promise<void>
```

#### 工作流程

1. 检查命令行参数（强制清理会跳过版本检查）
2. 获取当前版本号并与存储版本比较
3. 根据比较结果决定是否执行黑屏检测
4. 检查完成后更新版本号

## 版本文件格式

### 配置验证版本文件
```json
{
  "version": "1.95.0",
  "lastCheck": "2024-01-01T12:00:00.000Z"
}
```

### 黑屏检测版本文件
```json
{
  "version": "1.95.0",
  "lastCheck": "2024-01-01T12:00:00.000Z"
}
```

## 日志输出

### 版本号一致时
```
[ConfigValidator] 版本号一致 (1.95.0)，跳过配置检查
[main] 版本号一致 (1.95.0)，跳过黑屏检查
```

### 版本号不一致时
```
[ConfigValidator] 版本号不一致 (存储: 1.94.0, 当前: 1.95.0)，需要进行配置检查
[main] 版本号不一致，进行黑屏检查 (当前版本: 1.95.0)
[ConfigValidator] 已保存版本号: 1.95.0
[main] 已保存黑屏检查版本号: 1.95.0
```

### 首次启动时
```
[ConfigValidator] 未找到存储的版本号，需要进行配置检查
[ConfigValidator] 检测到第一次启动，初始化配置目录
```

## 性能优化效果

### 优化前
- 每次启动都执行完整的配置验证
- 每次启动都执行黑屏检测
- 启动时间较长，特别是在配置文件较多时

### 优化后
- 只有版本更新时才执行完整检查
- 大部分启动场景下跳过检查
- 显著减少启动时间

## 强制检查机制

即使版本号一致，以下情况仍会强制执行检查：

### 配置验证
- 第一次启动：`isFirstLaunch = true`
- 版本文件损坏或不存在

### 黑屏检测
- 命令行参数：`--force-cleanup` 或 `--reset-user-data`
- 版本文件损坏或不存在

## 错误处理

### 版本文件读取失败
- 记录警告日志
- 默认执行检查（安全优先）

### 版本文件写入失败
- 记录错误日志
- 不影响应用正常启动

## 兼容性

### 向后兼容
- 旧版本升级到新版本时会自动创建版本文件
- 不影响现有用户的使用体验

### 跨平台支持
- 配置验证：支持所有平台
- 黑屏检测：目前仅在 macOS 上启用

## 维护建议

### 版本文件清理
- 版本文件较小，通常不需要清理
- 如需重置，删除对应的 `version.json` 文件即可

### 调试支持
- 可通过删除版本文件来强制执行检查
- 日志中会详细记录版本比较过程

## 相关文件

### 修改的文件
1. `src/vs/platform/JoyCoder/common/configValidator.ts` - 配置验证版本检查
2. `src/vs/code/electron-main/app.ts` - 黑屏检测版本检查

### 版本文件位置
1. `~/.joycoder-editor/version.json` - 配置验证版本
2. `~/Library/Application Support/JoyCode/blackscreen-check-version.json` - 黑屏检测版本

## 总结

基于版本号的启动检查机制有效地优化了 JoyCode 的启动性能，在保证功能完整性的同时减少了不必要的检查操作。该机制具有良好的兼容性和可维护性，为用户提供了更快的启动体验。
