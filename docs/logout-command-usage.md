# LOGOUT_ACTION_ID 命令使用说明

## 概述

`LOGOUT_ACTION_ID` 命令现在支持一个可选的布尔参数 `isExpired`，用于区分正常登出和登录失效的情况。

## 命令ID

```typescript
export const LOGOUT_ACTION_ID = 'workbench.action.joycoderLogout';
```

## 参数

- `isExpired` (boolean, 可选): 默认为 `false`
  - `false`: 正常登出，显示 "Logged out successfully"
  - `true`: 登录失效，显示 "Login expired, please log in again"

## 使用方法

### 1. 正常登出（默认行为）

```typescript
// 方法1：不传参数（默认为false）
await commandService.executeCommand('workbench.action.joycoderLogout');

// 方法2：明确传入false
await commandService.executeCommand('workbench.action.joycoderLogout', false);
```

显示消息：**"Logged out successfully"**

### 2. 登录失效登出

```typescript
// 传入true表示登录失效
await commandService.executeCommand('workbench.action.joycoderLogout', true);
```

显示消息：**"Login expired, please log in again"**

## 通知行为

无论是哪种情况，都会：

1. 执行登出操作
2. 显示相应的通知消息
3. 在通知中提供"Login"按钮
4. 点击按钮可以重新登录

## 国际化支持

### 英文（默认）
- 正常登出: "Logged out successfully"
- 登录失效: "Login expired, please log in again"
- 登录按钮: "Login"

### 中文
- 正常登出: "已退出登录"
- 登录失效: "登录失效，请重新登录"
- 登录按钮: "登录"

## 实际应用场景

### 场景1：用户主动登出
```typescript
// 用户点击登出按钮
await commandService.executeCommand('workbench.action.joycoderLogout', false);
```

### 场景2：Token过期自动登出
```typescript
// HTTP响应检测到401错误，token失效
if (response.status === 401) {
    await commandService.executeCommand('workbench.action.joycoderLogout', true);
}
```

### 场景3：服务器返回登录失效
```typescript
// 服务器返回登录失效信息
if (response.data.code === 401) {
    await commandService.executeCommand('workbench.action.joycoderLogout', true);
}
```
