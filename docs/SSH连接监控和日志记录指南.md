# SSH连接监控和日志记录指南

## 概述

JoyCoder IDE 的 `open-remote-ssh` 扩展提供了完整的SSH连接监控和日志记录功能，帮助用户诊断连接问题、监控连接状态和分析性能。

## 🖥️ 日志显示界面

### 1. **输出面板 (Output Panel)**

#### 访问方式
- **命令面板**: `Ctrl+Shift+P` → `Remote-SSH: Show Log`
- **状态栏**: 点击远程连接指示器 → `Show Log`
- **菜单**: `View` → `Output` → 选择 `Remote - SSH`

#### 日志内容格式
```
[Info  - 14:32:15.123] Resolving ssh remote authority 'ssh-remote+production-server' (attemp #1)
[Info  - 14:32:15.124] Parsing encoded destination: production-server
[Info  - 14:32:15.125] Parsed SSH destination: production-server
[Trace - 14:32:15.200] Trying publickey authentication: ~/.ssh/id_rsa RSA SHA256:abc123...
[Info  - 14:32:16.100] SSH connection established successfully
[Trace - 14:32:16.150] Opening tunnel 8080(local) => 3000(remote)
```

### 2. **日志级别**

#### 支持的日志级别
- **Trace**: 详细的调试信息，包括SSH协议细节
- **Info**: 一般信息，连接状态变化
- **Error**: 错误信息和异常

<augment_code_snippet path="extensions/open-remote-ssh/src/common/logger.ts" mode="EXCERPT">
```typescript
export default class Log {
    private output: vscode.OutputChannel;

    public trace(message: string, data?: any): void {
        this.logLevel('Trace', message, data);
    }

    public info(message: string, data?: any): void {
        this.logLevel('Info', message, data);
    }

    public error(message: string, data?: any): void {
        this.logLevel('Error', message, data);
    }
```
</augment_code_snippet>

## 📁 日志文件位置

### 1. **本地日志文件**

#### Windows 系统
```
%APPDATA%\Code\logs\{timestamp}\exthost\output_logging_remote-ssh_{timestamp}.log
```

#### macOS 系统
```
~/Library/Application Support/Code/logs/{timestamp}/exthost/output_logging_remote-ssh_{timestamp}.log
```

#### Linux 系统
```
~/.config/Code/logs/{timestamp}/exthost/output_logging_remote-ssh_{timestamp}.log
```

### 2. **远程服务器日志**

#### 服务器端日志位置
```bash
# 默认位置
~/.vscode-server/data/logs/{timestamp}/

# 具体文件
~/.vscode-server/data/logs/{timestamp}/remoteagent.log
~/.vscode-server/data/logs/{timestamp}/exthost/exthost.log
```

### 3. **日志文件结构**

#### 日志目录结构
```
logs/
├── 20241217T143215/          # 时间戳目录
│   ├── window.log            # 主窗口日志
│   ├── renderer.log          # 渲染进程日志
│   └── exthost/              # 扩展主机日志
│       ├── output_logging_remote-ssh_*.log
│       └── exthost.log
└── 20241216T091030/          # 前一天的日志
    └── ...
```

## 🔍 连接监控功能

### 1. **实时连接状态监控**

#### 连接状态指示器
- **状态栏显示**: 显示当前连接状态和远程主机信息
- **连接图标**: 绿色表示已连接，红色表示断开，黄色表示连接中

<augment_code_snippet path="extensions/open-remote-ssh/src/ssh/sshConnection.ts" mode="EXCERPT">
```typescript
const SSHConstants = {
    'CHANNEL': {
        SSH: 'ssh',
        TUNNEL: 'tunnel',
        X11: 'x11'
    },
    'STATUS': {
        BEFORECONNECT: 'beforeconnect',
        CONNECT: 'connect',
        BEFOREDISCONNECT: 'beforedisconnect',
        DISCONNECT: 'disconnect'
    }
};
```
</augment_code_snippet>

### 2. **连接事件监控**

#### 监控的事件类型
- **连接建立**: `ssh:connect`
- **连接断开**: `ssh:disconnect`
- **隧道创建**: `tunnel:connect`
- **隧道关闭**: `tunnel:disconnect`
- **认证过程**: 各种认证方法的尝试和结果

#### 事件日志示例
```
[Info] SSH connection event: beforeconnect
[Info] Trying publickey authentication: ~/.ssh/id_rsa
[Info] SSH connection event: connect
[Trace] Opening tunnel 8080(local) => 3000(remote)
[Info] Tunnel ssh_tunnel_8080_3000 established
```

### 3. **性能监控**

#### 监控指标
- **连接建立时间**: 从开始连接到成功建立的耗时
- **认证时间**: 各种认证方法的耗时
- **数据传输量**: 通过SSH隧道传输的数据量
- **重连次数**: 自动重连的次数和频率

## 🛠️ 调试和诊断工具

### 1. **详细日志记录**

#### 启用详细日志
```json
// settings.json
{
    "remote.SSH.showLoginTerminal": true,
    "remote.SSH.logLevel": "debug"
}
```

#### 认证过程日志
<augment_code_snippet path="extensions/open-remote-ssh/src/authResolver.ts" mode="EXCERPT">
```typescript
this.logger.info(`Trying publickey authentication: ${identityKey.filename} ${identityKey.parsedKey.type} SHA256:${identityKey.fingerprint}`);

if (passwordRetryCount === PASSWORD_RETRY_COUNT) {
    this.logger.info(`Trying password authentication`);
}

if (keyboardRetryCount === PASSWORD_RETRY_COUNT) {
    this.logger.info(`Trying keyboard-interactive authentication`);
}
```
</augment_code_snippet>

### 2. **连接诊断信息**

#### 连接解析日志
```
[Info] Resolving ssh remote authority 'ssh-remote+hostname' (attemp #1)
[Info] Parsing encoded destination: hostname
[Info] Parsed SSH destination: hostname
[Info] Current workspace folder: project-name, URI: vscode-remote://ssh-remote+hostname/path
```

#### 隧道管理日志
```
[Trace] Creating forwarding server 8080(local) => 1080(socks) => 3000(remote)
[Trace] Opening tunnel 8080(local) => 3000(remote)
[Trace] Tunnel ssh_tunnel_8080_3000 closed
[Trace] SOCKS forwading server closed
```

### 3. **错误诊断**

#### 错误日志格式
```
[Error] Error resolving authority: Connection timeout
[Error] Error details: ETIMEDOUT
[Error] Stack trace: Error: ETIMEDOUT at Socket.connect...
[Error] Server installation error: Failed to download server
```

#### 常见错误类型
- **认证失败**: `Authentication failed`
- **连接超时**: `ETIMEDOUT`
- **主机不可达**: `ENOTFOUND`
- **端口被拒绝**: `ECONNREFUSED`
- **服务器安装失败**: `ServerInstallError`

## 📊 日志分析和监控

### 1. **日志文件分析**

#### 使用命令行工具分析
```bash
# 查看最近的连接日志
tail -f ~/.config/Code/logs/*/exthost/output_logging_remote-ssh_*.log

# 搜索特定错误
grep -i "error" ~/.config/Code/logs/*/exthost/output_logging_remote-ssh_*.log

# 分析连接时间
grep "connection established" ~/.config/Code/logs/*/exthost/output_logging_remote-ssh_*.log
```

### 2. **性能分析**

#### 连接性能指标
```typescript
interface ConnectionMetrics {
    connectionTime: number;      // 连接建立时间 (ms)
    reconnectCount: number;      // 重连次数
    dataTransferred: number;     // 传输数据量 (bytes)
    errorCount: number;          // 错误次数
    lastActivity: Date;          // 最后活动时间
}
```

### 3. **自动化监控**

#### 日志轮转
- 日志文件按时间戳自动分组
- 旧日志文件自动清理（保留最近10个会话）
- 单个日志文件大小限制（30MB，分6个文件轮转）

## 🔧 配置选项

### 1. **日志相关配置**

```json
{
    // 显示登录终端
    "remote.SSH.showLoginTerminal": true,
    
    // 日志级别
    "remote.SSH.logLevel": "info",
    
    // 连接超时时间
    "remote.SSH.connectTimeout": 15,
    
    // 启用详细日志
    "remote.SSH.enableDynamicForwarding": true
}
```

### 2. **监控配置**

```json
{
    // 启用连接健康检查
    "remote.SSH.enableConnectionHealth": true,
    
    // 心跳间隔
    "remote.SSH.serverAliveInterval": 60,
    
    // 最大心跳失败次数
    "remote.SSH.serverAliveCountMax": 3
}
```

## 🚨 故障排除

### 1. **常见问题诊断**

#### 连接失败
1. 检查日志中的错误信息
2. 验证SSH配置文件
3. 测试网络连通性
4. 检查认证配置

#### 性能问题
1. 分析连接建立时间
2. 检查网络延迟
3. 监控数据传输量
4. 查看重连频率

### 2. **日志收集**

#### 收集完整日志信息
```bash
# 收集所有相关日志
tar -czf ssh-logs.tar.gz ~/.config/Code/logs/*/exthost/output_logging_remote-ssh_*.log
```

#### 提交问题时包含的信息
- 完整的错误日志
- SSH配置文件（去除敏感信息）
- 网络环境信息
- 操作系统和版本信息

---

通过这些监控和日志功能，用户可以全面了解SSH连接的状态，快速诊断和解决连接问题，优化远程开发体验。
