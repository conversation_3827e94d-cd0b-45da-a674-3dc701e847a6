<?xml version="1.0" encoding="UTF-8"?>
<xmap-content xmlns="urn:xmind:xmap:xmlns:content:2.0" xmlns:fo="http://www.w3.org/1999/XSL/Format" xmlns:svg="http://www.w3.org/2000/svg" xmlns:xhtml="http://www.w3.org/1999/xhtml" xmlns:xlink="http://www.w3.org/1999/xlink" version="2.0">
  <sheet id="root" theme="classic">
    <topic id="central-topic">
      <title>JoyCoder IDE 项目架构</title>
      <children>
        <!-- 项目概览 -->
        <topics type="attached">
          <topic id="overview">
            <title>项目概览</title>
            <children>
              <topics type="attached">
                <topic id="basic-info">
                  <title>基本信息</title>
                  <children>
                    <topics type="attached">
                      <topic><title>版本: 0.7.3</title></topic>
                      <topic><title>基于: VS Code 1.98.2</title></topic>
                      <topic><title>许可证: MIT</title></topic>
                      <topic><title>应用名: JoyCode</title></topic>
                      <topic><title>数据目录: .joycode-editor</title></topic>
                    </topics>
                  </children>
                </topic>
                <topic id="tech-stack">
                  <title>技术栈</title>
                  <children>
                    <topics type="attached">
                      <topic><title>Electron 34.3.2 - 跨平台桌面应用</title></topic>
                      <topic><title>React 18.3.1 - 现代化UI组件</title></topic>
                      <topic><title>TypeScript 5.8.0 - 类型安全</title></topic>
                      <topic><title>TailwindCSS 3.4.14 - 原子化CSS</title></topic>
                      <topic><title>Node.js - 后端服务</title></topic>
                    </topics>
                  </children>
                </topic>
                <topic id="core-features">
                  <title>核心特性</title>
                  <children>
                    <topics type="attached">
                      <topic><title>AI智能编程助手</title></topic>
                      <topic><title>多AI模型支持</title></topic>
                      <topic><title>智能代码补全</title></topic>
                      <topic><title>内联代码编辑(Ctrl+K)</title></topic>
                      <topic><title>VS Code扩展兼容</title></topic>
                      <topic><title>一键扩展导入</title></topic>
                    </topics>
                  </children>
                </topic>
              </topics>
            </children>
          </topic>

          <!-- 核心架构 -->
          <topic id="core-architecture">
            <title>核心架构</title>
            <children>
              <topics type="attached">
                <topic id="layered-arch">
                  <title>分层架构</title>
                  <children>
                    <topics type="attached">
                      <topic id="base-layer">
                        <title>基础层 (src/vs/base)</title>
                        <children>
                          <topics type="attached">
                            <topic><title>browser/ - 浏览器环境基础功能</title></topic>
                            <topic><title>common/ - 通用工具和类型</title></topic>
                            <topic><title>node/ - Node.js环境功能</title></topic>
                            <topic><title>worker/ - Web Worker支持</title></topic>
                          </topics>
                        </children>
                      </topic>
                      <topic id="platform-layer">
                        <title>平台层 (src/vs/platform)</title>
                        <children>
                          <topics type="attached">
                            <topic><title>JoyCoder/ - JoyCoder特有平台服务</title></topic>
                            <topic><title>configuration/ - 配置管理</title></topic>
                            <topic><title>files/ - 文件系统</title></topic>
                            <topic><title>instantiation/ - 依赖注入</title></topic>
                            <topic><title>keybinding/ - 快捷键绑定</title></topic>
                            <topic><title>extensions/ - 扩展系统</title></topic>
                          </topics>
                        </children>
                      </topic>
                      <topic id="workbench-layer">
                        <title>工作台层 (src/vs/workbench)</title>
                        <children>
                          <topics type="attached">
                            <topic><title>browser/ - 工作台浏览器实现</title></topic>
                            <topic><title>services/ - 工作台服务</title></topic>
                            <topic><title>contrib/ - 功能贡献模块</title></topic>
                            <topic><title>api/ - 扩展API</title></topic>
                          </topics>
                        </children>
                      </topic>
                      <topic id="code-layer">
                        <title>应用层 (src/vs/code)</title>
                        <children>
                          <topics type="attached">
                            <topic><title>electron-main/ - 主进程</title></topic>
                            <topic><title>electron-sandbox/ - 渲染进程</title></topic>
                            <topic><title>browser/ - 浏览器版本</title></topic>
                            <topic><title>node/ - Node.js服务</title></topic>
                          </topics>
                        </children>
                      </topic>
                    </topics>
                  </children>
                </topic>

                <topic id="startup-flow">
                  <title>启动流程</title>
                  <children>
                    <topics type="attached">
                      <topic><title>1. 主进程启动 (main.ts)</title></topic>
                      <topic><title>2. 创建应用窗口</title></topic>
                      <topic><title>3. 加载渲染进程</title></topic>
                      <topic><title>4. 初始化工作台</title></topic>
                      <topic><title>5. 加载扩展系统</title></topic>
                      <topic><title>6. 启动AI服务</title></topic>
                    </topics>
                  </children>
                </topic>

                <topic id="service-arch">
                  <title>服务架构</title>
                  <children>
                    <topics type="attached">
                      <topic><title>依赖注入容器</title></topic>
                      <topic><title>服务生命周期管理</title></topic>
                      <topic><title>事件驱动通信</title></topic>
                      <topic><title>跨进程服务代理</title></topic>
                    </topics>
                  </children>
                </topic>
              </topics>
            </children>
          </topic>

          <!-- AI集成架构 -->
          <topic id="ai-integration">
            <title>AI集成架构</title>
            <children>
              <topics type="attached">
                <topic id="ai-services">
                  <title>AI服务层</title>
                  <children>
                    <topics type="attached">
                      <topic id="llm-service">
                        <title>LLM消息服务</title>
                        <children>
                          <topics type="attached">
                            <topic><title>llmMessageService.ts - 核心服务接口</title></topic>
                            <topic><title>llmMessageChannel.ts - 进程间通信</title></topic>
                            <topic><title>流式响应处理</title></topic>
                            <topic><title>消息队列管理</title></topic>
                          </topics>
                        </children>
                      </topic>
                      <topic id="ai-providers">
                        <title>AI提供商</title>
                        <children>
                          <topics type="attached">
                            <topic><title>Anthropic Claude</title></topic>
                            <topic><title>OpenAI GPT</title></topic>
                            <topic><title>Google Gemini</title></topic>
                            <topic><title>Groq</title></topic>
                            <topic><title>Ollama (本地模型)</title></topic>
                          </topics>
                        </children>
                      </topic>
                    </topics>
                  </children>
                </topic>

                <topic id="ai-features">
                  <title>AI功能模块</title>
                  <children>
                    <topics type="attached">
                      <topic id="autocomplete">
                        <title>智能代码补全</title>
                        <children>
                          <topics type="attached">
                            <topic><title>autocompleteService.ts</title></topic>
                            <topic><title>上下文感知补全</title></topic>
                            <topic><title>实时建议生成</title></topic>
                            <topic><title>补全缓存机制</title></topic>
                          </topics>
                        </children>
                      </topic>
                      <topic id="inline-edit">
                        <title>内联编辑 (Ctrl+K)</title>
                        <children>
                          <topics type="attached">
                            <topic><title>quickEditActions.ts</title></topic>
                            <topic><title>quickEditStateService.ts</title></topic>
                            <topic><title>自然语言指令处理</title></topic>
                            <topic><title>代码差异显示</title></topic>
                          </topics>
                        </children>
                      </topic>
                      <topic id="chat-interface">
                        <title>聊天界面</title>
                        <children>
                          <topics type="attached">
                            <topic><title>侧边栏AI助手</title></topic>
                            <topic><title>代码解释和优化</title></topic>
                            <topic><title>多轮对话支持</title></topic>
                            <topic><title>历史记录管理</title></topic>
                          </topics>
                        </children>
                      </topic>
                    </topics>
                  </children>
                </topic>

                <topic id="ai-config">
                  <title>AI配置管理</title>
                  <children>
                    <topics type="attached">
                      <topic><title>joyCoderSettingsService.ts</title></topic>
                      <topic><title>API密钥安全存储</title></topic>
                      <topic><title>模型参数配置</title></topic>
                      <topic><title>提供商切换</title></topic>
                    </topics>
                  </children>
                </topic>
              </topics>
            </children>
          </topic>

          <!-- React UI系统 -->
          <topic id="react-ui">
            <title>React UI系统</title>
            <children>
              <topics type="attached">
                <topic id="react-integration">
                  <title>React集成架构</title>
                  <children>
                    <topics type="attached">
                      <topic><title>混合架构设计</title></topic>
                      <topic><title>VS Code工作台集成</title></topic>
                      <topic><title>样式隔离机制</title></topic>
                      <topic><title>服务注入系统</title></topic>
                    </topics>
                  </children>
                </topic>

                <topic id="react-components">
                  <title>核心React组件</title>
                  <children>
                    <topics type="attached">
                      <topic id="sidebar-component">
                        <title>侧边栏组件</title>
                        <children>
                          <topics type="attached">
                            <topic><title>src/sidebar-tsx/ - 侧边栏实现</title></topic>
                            <topic><title>AI聊天界面</title></topic>
                            <topic><title>历史记录显示</title></topic>
                            <topic><title>状态管理</title></topic>
                          </topics>
                        </children>
                      </topic>
                      <topic id="settings-component">
                        <title>设置组件</title>
                        <children>
                          <topics type="attached">
                            <topic><title>src/JoyCoder-settings-tsx/</title></topic>
                            <topic><title>AI模型配置</title></topic>
                            <topic><title>API密钥管理</title></topic>
                            <topic><title>功能开关</title></topic>
                          </topics>
                        </children>
                      </topic>
                      <topic id="ctrl-k-component">
                        <title>Ctrl+K组件</title>
                        <children>
                          <topics type="attached">
                            <topic><title>src/ctrl-k-tsx/</title></topic>
                            <topic><title>内联编辑界面</title></topic>
                            <topic><title>指令输入处理</title></topic>
                            <topic><title>代码差异显示</title></topic>
                          </topics>
                        </children>
                      </topic>
                      <topic id="welcome-component">
                        <title>欢迎页组件</title>
                        <children>
                          <topics type="attached">
                            <topic><title>src/welcome-tsx/</title></topic>
                            <topic><title>新用户引导</title></topic>
                            <topic><title>功能介绍</title></topic>
                            <topic><title>快速开始</title></topic>
                          </topics>
                        </children>
                      </topic>
                    </topics>
                  </children>
                </topic>

                <topic id="build-system">
                  <title>构建系统</title>
                  <children>
                    <topics type="attached">
                      <topic><title>双阶段构建流程</title></topic>
                      <topic><title>tsup.config.js - 构建配置</title></topic>
                      <topic><title>tailwind.config.js - 样式配置</title></topic>
                      <topic><title>scope-tailwind - 样式隔离</title></topic>
                      <topic><title>build.js - 构建脚本</title></topic>
                    </topics>
                  </children>
                </topic>
              </topics>
            </children>
          </topic>

          <!-- 扩展系统架构 -->
          <topic id="extension-system">
            <title>扩展系统架构</title>
            <children>
              <topics type="attached">
                <topic id="extension-overview">
                  <title>扩展系统概览</title>
                  <children>
                    <topics type="attached">
                      <topic><title>VS Code扩展兼容</title></topic>
                      <topic><title>内置扩展管理</title></topic>
                      <topic><title>扩展导入服务</title></topic>
                      <topic><title>扩展主机架构</title></topic>
                    </topics>
                  </children>
                </topic>

                <topic id="extension-import">
                  <title>扩展导入系统</title>
                  <children>
                    <topics type="attached">
                      <topic><title>extensionImportService.ts</title></topic>
                      <topic><title>从VS Code导入扩展</title></topic>
                      <topic><title>扩展兼容性检查</title></topic>
                      <topic><title>批量导入功能</title></topic>
                    </topics>
                  </children>
                </topic>

                <topic id="builtin-extensions">
                  <title>内置扩展</title>
                  <children>
                    <topics type="attached">
                      <topic><title>语言支持扩展 (50+种语言)</title></topic>
                      <topic><title>主题扩展 (10+种主题)</title></topic>
                      <topic><title>调试扩展</title></topic>
                      <topic><title>Git集成扩展</title></topic>
                      <topic><title>JoyCoder专用扩展</title></topic>
                    </topics>
                  </children>
                </topic>

                <topic id="extension-host">
                  <title>扩展主机</title>
                  <children>
                    <topics type="attached">
                      <topic><title>扩展进程隔离</title></topic>
                      <topic><title>API代理机制</title></topic>
                      <topic><title>扩展生命周期管理</title></topic>
                      <topic><title>性能监控</title></topic>
                    </topics>
                  </children>
                </topic>
              </topics>
            </children>
          </topic>

          <!-- 服务层架构 -->
          <topic id="service-layer">
            <title>服务层架构</title>
            <children>
              <topics type="attached">
                <topic id="di-system">
                  <title>依赖注入系统</title>
                  <children>
                    <topics type="attached">
                      <topic><title>服务标识符定义</title></topic>
                      <topic><title>服务接口规范</title></topic>
                      <topic><title>服务实现注册</title></topic>
                      <topic><title>生命周期管理</title></topic>
                    </topics>
                  </children>
                </topic>

                <topic id="core-services">
                  <title>核心服务</title>
                  <children>
                    <topics type="attached">
                      <topic id="sidebar-state">
                        <title>侧边栏状态服务</title>
                        <children>
                          <topics type="attached">
                            <topic><title>sidebarStateService.ts</title></topic>
                            <topic><title>UI状态管理</title></topic>
                            <topic><title>用户交互状态</title></topic>
                            <topic><title>持久化存储</title></topic>
                          </topics>
                        </children>
                      </topic>
                      <topic id="thread-history">
                        <title>线程历史服务</title>
                        <children>
                          <topics type="attached">
                            <topic><title>threadHistoryService.ts</title></topic>
                            <topic><title>对话历史管理</title></topic>
                            <topic><title>会话持久化</title></topic>
                            <topic><title>历史搜索功能</title></topic>
                          </topics>
                        </children>
                      </topic>
                      <topic id="settings-service">
                        <title>设置服务</title>
                        <children>
                          <topics type="attached">
                            <topic><title>配置读写管理</title></topic>
                            <topic><title>默认设置处理</title></topic>
                            <topic><title>配置验证</title></topic>
                            <topic><title>热更新支持</title></topic>
                          </topics>
                        </children>
                      </topic>
                    </topics>
                  </children>
                </topic>

                <topic id="service-communication">
                  <title>服务间通信</title>
                  <children>
                    <topics type="attached">
                      <topic><title>事件驱动架构</title></topic>
                      <topic><title>服务代理模式</title></topic>
                      <topic><title>跨进程通信</title></topic>
                      <topic><title>消息队列机制</title></topic>
                    </topics>
                  </children>
                </topic>
              </topics>
            </children>
          </topic>

          <!-- 构建和部署 -->
          <topic id="build-deployment">
            <title>构建和部署架构</title>
            <children>
              <topics type="attached">
                <topic id="build-tools">
                  <title>构建工具链</title>
                  <children>
                    <topics type="attached">
                      <topic><title>Gulp - 主构建系统</title></topic>
                      <topic><title>TypeScript - 类型编译</title></topic>
                      <topic><title>Webpack - 模块打包</title></topic>
                      <topic><title>Electron Builder - 应用打包</title></topic>
                    </topics>
                  </children>
                </topic>

                <topic id="build-process">
                  <title>构建流程</title>
                  <children>
                    <topics type="attached">
                      <topic id="dev-build">
                        <title>开发构建</title>
                        <children>
                          <topics type="attached">
                            <topic><title>npm run dev - 开发启动</title></topic>
                            <topic><title>npm run watch - 监听模式</title></topic>
                            <topic><title>npm run buildreact - React构建</title></topic>
                            <topic><title>热重载支持</title></topic>
                          </topics>
                        </children>
                      </topic>
                      <topic id="prod-build">
                        <title>生产构建</title>
                        <children>
                          <topics type="attached">
                            <topic><title>多平台构建脚本</title></topic>
                            <topic><title>代码压缩优化</title></topic>
                            <topic><title>资源打包</title></topic>
                            <topic><title>签名和公证</title></topic>
                          </topics>
                        </children>
                      </topic>
                    </topics>
                  </children>
                </topic>

                <topic id="platform-support">
                  <title>平台支持</title>
                  <children>
                    <topics type="attached">
                      <topic><title>macOS (Intel/Apple Silicon)</title></topic>
                      <topic><title>Windows (x64/x86)</title></topic>
                      <topic><title>Linux (x64)</title></topic>
                      <topic><title>自动更新机制</title></topic>
                    </topics>
                  </children>
                </topic>

                <topic id="release-scripts">
                  <title>发布脚本</title>
                  <children>
                    <topics type="attached">
                      <topic><title>JoyCoder-release-scripts/</title></topic>
                      <topic><title>平台特定构建脚本</title></topic>
                      <topic><title>代码签名脚本</title></topic>
                      <topic><title>扩展移动脚本</title></topic>
                    </topics>
                  </children>
                </topic>
              </topics>
            </children>
          </topic>

          <!-- 性能和安全 -->
          <topic id="performance-security">
            <title>性能和安全架构</title>
            <children>
              <topics type="attached">
                <topic id="performance">
                  <title>性能优化</title>
                  <children>
                    <topics type="attached">
                      <topic id="startup-perf">
                        <title>启动性能</title>
                        <children>
                          <topics type="attached">
                            <topic><title>延迟加载机制</title></topic>
                            <topic><title>模块按需加载</title></topic>
                            <topic><title>缓存优化</title></topic>
                            <topic><title>启动时间 &lt; 3秒</title></topic>
                          </topics>
                        </children>
                      </topic>
                      <topic id="memory-perf">
                        <title>内存管理</title>
                        <children>
                          <topics type="attached">
                            <topic><title>内存使用 &lt; 200MB</title></topic>
                            <topic><title>垃圾回收优化</title></topic>
                            <topic><title>内存泄漏检测</title></topic>
                            <topic><title>资源释放机制</title></topic>
                          </topics>
                        </children>
                      </topic>
                      <topic id="render-perf">
                        <title>渲染性能</title>
                        <children>
                          <topics type="attached">
                            <topic><title>虚拟滚动</title></topic>
                            <topic><title>组件懒加载</title></topic>
                            <topic><title>DOM优化</title></topic>
                            <topic><title>动画性能优化</title></topic>
                          </topics>
                        </children>
                      </topic>
                    </topics>
                  </children>
                </topic>

                <topic id="security">
                  <title>安全架构</title>
                  <children>
                    <topics type="attached">
                      <topic id="data-security">
                        <title>数据安全</title>
                        <children>
                          <topics type="attached">
                            <topic><title>API密钥安全存储</title></topic>
                            <topic><title>本地数据加密</title></topic>
                            <topic><title>敏感信息过滤</title></topic>
                            <topic><title>数据传输加密</title></topic>
                          </topics>
                        </children>
                      </topic>
                      <topic id="network-security">
                        <title>网络安全</title>
                        <children>
                          <topics type="attached">
                            <topic><title>HTTPS强制</title></topic>
                            <topic><title>请求验证</title></topic>
                            <topic><title>超时控制</title></topic>
                            <topic><title>证书验证</title></topic>
                          </topics>
                        </children>
                      </topic>
                      <topic id="input-validation">
                        <title>输入验证</title>
                        <children>
                          <topics type="attached">
                            <topic><title>恶意脚本检测</title></topic>
                            <topic><title>SQL注入防护</title></topic>
                            <topic><title>XSS防护</title></topic>
                            <topic><title>输入长度限制</title></topic>
                          </topics>
                        </children>
                      </topic>
                    </topics>
                  </children>
                </topic>
              </topics>
            </children>
          </topic>

          <!-- 目录结构 -->
          <topic id="directory-structure">
            <title>目录结构</title>
            <children>
              <topics type="attached">
                <topic id="root-structure">
                  <title>根目录结构</title>
                  <children>
                    <topics type="attached">
                      <topic><title>src/ - 源代码目录</title></topic>
                      <topic><title>extensions/ - 内置扩展</title></topic>
                      <topic><title>build/ - 构建脚本和工具</title></topic>
                      <topic><title>docs/ - 项目文档</title></topic>
                      <topic><title>resources/ - 资源文件</title></topic>
                      <topic><title>scripts/ - 开发脚本</title></topic>
                      <topic><title>test/ - 测试文件</title></topic>
                    </topics>
                  </children>
                </topic>

                <topic id="src-structure">
                  <title>src/vs 目录结构</title>
                  <children>
                    <topics type="attached">
                      <topic id="vs-base">
                        <title>base/ - 基础层</title>
                        <children>
                          <topics type="attached">
                            <topic><title>browser/ - 浏览器环境</title></topic>
                            <topic><title>common/ - 通用功能</title></topic>
                            <topic><title>node/ - Node.js环境</title></topic>
                            <topic><title>parts/ - 基础组件</title></topic>
                            <topic><title>worker/ - Worker支持</title></topic>
                          </topics>
                        </children>
                      </topic>
                      <topic id="vs-platform">
                        <title>platform/ - 平台层</title>
                        <children>
                          <topics type="attached">
                            <topic><title>JoyCoder/ - JoyCoder平台服务</title></topic>
                            <topic><title>configuration/ - 配置服务</title></topic>
                            <topic><title>files/ - 文件服务</title></topic>
                            <topic><title>instantiation/ - 依赖注入</title></topic>
                            <topic><title>extensions/ - 扩展平台</title></topic>
                          </topics>
                        </children>
                      </topic>
                      <topic id="vs-workbench">
                        <title>workbench/ - 工作台层</title>
                        <children>
                          <topics type="attached">
                            <topic><title>contrib/JoyCoder/ - JoyCoder功能</title></topic>
                            <topic><title>services/ - 工作台服务</title></topic>
                            <topic><title>browser/ - 浏览器实现</title></topic>
                            <topic><title>api/ - 扩展API</title></topic>
                          </topics>
                        </children>
                      </topic>
                      <topic id="vs-editor">
                        <title>editor/ - 编辑器</title>
                        <children>
                          <topics type="attached">
                            <topic><title>browser/ - 编辑器浏览器实现</title></topic>
                            <topic><title>common/ - 编辑器通用功能</title></topic>
                            <topic><title>contrib/ - 编辑器功能贡献</title></topic>
                            <topic><title>standalone/ - 独立编辑器</title></topic>
                          </topics>
                        </children>
                      </topic>
                    </topics>
                  </children>
                </topic>

                <topic id="joycoder-structure">
                  <title>JoyCoder特有目录</title>
                  <children>
                    <topics type="attached">
                      <topic id="joycoder-platform">
                        <title>platform/JoyCoder/</title>
                        <children>
                          <topics type="attached">
                            <topic><title>common/ - 通用服务接口</title></topic>
                            <topic><title>browser/ - 浏览器实现</title></topic>
                            <topic><title>electron-main/ - 主进程实现</title></topic>
                          </topics>
                        </children>
                      </topic>
                      <topic id="joycoder-contrib">
                        <title>workbench/contrib/JoyCoder/</title>
                        <children>
                          <topics type="attached">
                            <topic><title>browser/ - 浏览器功能实现</title></topic>
                            <topic><title>react/ - React组件</title></topic>
                            <topic><title>service/ - 业务服务</title></topic>
                            <topic><title>commands/ - 命令定义</title></topic>
                          </topics>
                        </children>
                      </topic>
                    </topics>
                  </children>
                </topic>
              </topics>
            </children>
          </topic>

          <!-- 开发指南 -->
          <topic id="development-guide">
            <title>开发指南</title>
            <children>
              <topics type="attached">
                <topic id="dev-setup">
                  <title>开发环境设置</title>
                  <children>
                    <topics type="attached">
                      <topic><title>Node.js 18+ 环境</title></topic>
                      <topic><title>npm install - 依赖安装</title></topic>
                      <topic><title>npm run dev - 开发启动</title></topic>
                      <topic><title>npm run watch - 监听模式</title></topic>
                    </topics>
                  </children>
                </topic>

                <topic id="dev-workflow">
                  <title>开发工作流</title>
                  <children>
                    <topics type="attached">
                      <topic><title>1. 代码修改</title></topic>
                      <topic><title>2. React组件构建</title></topic>
                      <topic><title>3. TypeScript编译</title></topic>
                      <topic><title>4. 热重载测试</title></topic>
                      <topic><title>5. 单元测试</title></topic>
                    </topics>
                  </children>
                </topic>

                <topic id="testing">
                  <title>测试体系</title>
                  <children>
                    <topics type="attached">
                      <topic><title>单元测试 - Mocha</title></topic>
                      <topic><title>集成测试 - Playwright</title></topic>
                      <topic><title>端到端测试</title></topic>
                      <topic><title>性能测试</title></topic>
                    </topics>
                  </children>
                </topic>

                <topic id="debugging">
                  <title>调试工具</title>
                  <children>
                    <topics type="attached">
                      <topic><title>VS Code调试配置</title></topic>
                      <topic><title>Chrome DevTools</title></topic>
                      <topic><title>Electron调试</title></topic>
                      <topic><title>日志系统</title></topic>
                    </topics>
                  </children>
                </topic>
              </topics>
            </children>
          </topic>
        </topics>
      </children>
    </topic>
  </sheet>
</xmap-content>
