# JoyCode 无密钥环访问配置指南

## 📋 概述

为了解决用户在使用JoyCode时遇到的系统密钥环访问弹窗问题，我们已经在构建时默认配置了无密钥环访问模式。用户无需任何额外操作即可享受无弹窗的使用体验。

## 🎯 解决的问题

- ✅ 避免macOS Keychain访问弹窗
- ✅ 避免Linux gnome-keyring访问弹窗  
- ✅ 避免Windows凭据管理器访问弹窗
- ✅ 避免AI插件触发的密钥存储访问
- ✅ 提供更流畅的用户体验

## 🔧 技术实现

### 1. 默认启动参数配置

**文件**: `src/main.ts`

```typescript
function parseCLIArgs(): NativeParsedArgs {
    return minimist(process.argv, {
        // ... 其他配置
        default: {
            'sandbox': true,
            'use-inmemory-secretstorage': true,  // 🔑 默认使用内存密钥存储
            'password-store': 'basic',           // 🔑 默认使用基本密码存储
            'skip-release-notes': true           // 🔑 默认跳过发行说明
        }
    });
}
```

### 2. Product配置

**文件**: `product.json`

```json
{
    "defaultArgs": {
        "use-inmemory-secretstorage": true,
        "password-store": "basic", 
        "skip-release-notes": true
    }
}
```

### 3. 环境服务增强

**文件**: `src/vs/platform/environment/common/environmentService.ts`

增强了环境服务以支持从product.json读取默认参数，确保配置的一致性。

### 4. 加密服务优化

**文件**: `src/vs/platform/encryption/electron-main/encryptionMainService.ts`

优化了加密服务，当检测到`use-inmemory-secretstorage`参数时自动启用基本文本加密。

### 5. macOS应用包配置

**文件**: `JoyCoder-release-scripts/no-keychain-info.plist`

```xml
<key>LSEnvironment</key>
<dict>
    <key>VSCODE_USE_INMEMORY_SECRETSTORAGE</key>
    <string>1</string>
    <key>VSCODE_PASSWORD_STORE</key>
    <string>basic</string>
    <key>VSCODE_SKIP_RELEASE_NOTES</key>
    <string>1</string>
</dict>
```

## 🚀 用户体验

### 构建后的效果

1. **无弹窗启动**: 用户启动JoyCode时不会看到任何密钥环访问弹窗
2. **AI插件正常工作**: 新增的AI插件可以正常使用，不会触发密钥环访问
3. **设置持久化**: 用户设置仍然会被正确保存，只是使用内存存储而非系统密钥环
4. **跨平台一致**: Windows、macOS、Linux都有一致的无弹窗体验

### 对比

| 配置方式 | 用户操作 | 弹窗情况 | 维护成本 |
|---------|---------|---------|---------|
| **构建时配置** ✅ | 无需操作 | 无弹窗 | 低 |
| 脚本启动 | 需要使用特定脚本 | 无弹窗 | 中 |
| 手动配置 | 需要手动设置参数 | 无弹窗 | 高 |
| 默认配置 | 无操作 | 有弹窗 | 低 |

## 🔍 验证方法

运行测试脚本验证配置：

```bash
chmod +x test-no-keychain.sh
./test-no-keychain.sh
```

## 📝 注意事项

### 安全性

- 使用内存密钥存储意味着密钥不会持久化到系统密钥环
- 对于大多数用户场景，这不会影响安全性
- 敏感信息仍然会在应用内部进行适当的处理

### 兼容性

- 所有现有功能保持正常工作
- 扩展和插件不受影响
- 用户设置和配置正常保存

### 可选性

如果用户希望使用系统密钥环，可以通过以下方式覆盖默认配置：

```bash
# 启用系统密钥环
./JoyCode --no-use-inmemory-secretstorage --password-store=gnome-libsecret
```

## 🎉 总结

通过这些修改，JoyCode现在默认提供无密钥环访问的流畅体验，同时保持了所有功能的完整性。用户可以直接使用构建后的应用，无需任何额外配置或脚本。
