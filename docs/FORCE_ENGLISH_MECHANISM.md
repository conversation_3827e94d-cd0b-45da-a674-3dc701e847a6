# 强制英文语言机制

## 🎯 **策略调整**

根据您的要求，我已经重新调整了语言包处理策略：**当构建版本不一致时，强制删除所有语言包并使用默认的英文语言**。

## 🔧 **实现方案**

### 1. **核心策略变更**

#### 原来的策略
```typescript
// 检查用户是否之前使用中文
const wasUsingChinese = await this.wasUserUsingChinese(userDataDir);

// 如果用户之前使用中文，安装新版本的中文语言包
if (wasUsingChinese) {
    await this.installFreshChineseLanguagePack(userDataDir);
} else {
    // 保持英文配置
}
```

#### 新的策略
```typescript
// 构建版本不一致时，强制删除所有语言包并切换到英文
if (storedBuildVersion !== currentBuildVersion) {
    this.logService.info('构建版本不同，强制删除所有语言包并切换到英文...');
    await this.forceResetToEnglish();
    this.logService.info('已强制切换到英文语言，语言包清理完成');
}
```

### 2. **新增的核心方法**

#### `forceSetToEnglish()` 方法
```typescript
private async forceSetToEnglish(userDataDir: URI): Promise<void> {
    // 1. 创建空的语言包配置（删除所有语言包）
    const languagePacksJsonPath = URI.file(path.join(userDataDir.fsPath, 'languagepacks.json'));
    await this.fileService.writeFile(languagePacksJsonPath, VSBuffer.fromString('{}'));

    // 2. 删除 argv.json 文件，让应用使用默认英文
    const argvPath = URI.file(path.join(userDataDir.fsPath, 'argv.json'));
    if (await this.fileService.exists(argvPath)) {
        await this.fileService.del(argvPath);
    }

    // 3. 创建英文语言强制标记文件
    const englishMarkerPath = URI.file(path.join(userDataDir.fsPath, '.force-english-language'));
    await this.fileService.writeFile(englishMarkerPath, VSBuffer.fromString(JSON.stringify({
        timestamp: Date.now(),
        buildVersion: currentBuildVersion,
        reason: 'Build version changed - forced to English for stability',
        message: 'All language packs have been removed to ensure application stability'
    }, null, 2)));

    // 4. 设置环境变量强制使用英文
    process.env['VSCODE_FORCE_ENGLISH'] = 'true';
    process.env['VSCODE_NLS_CONFIG'] = JSON.stringify({
        locale: 'en',
        availableLanguages: {}
    });
}
```

### 3. **完整的清理流程**

#### 修改后的 `resetLanguagePacksForVersionChange()` 方法
```typescript
private async resetLanguagePacksForVersionChange(userDataDir: URI): Promise<void> {
    // 1. 完全清理所有旧的语言包相关文件和缓存
    await this.completelyCleanOldLanguagePackData(userDataDir);

    // 2. 强制设置为英文环境
    await this.forceSetToEnglish(userDataDir);

    // 3. 清理可能的损坏标记文件
    // 4. 设置强制重新加载标记
}
```

## 🚀 **机制特点**

### 1. **无条件强制英文**
- 不再检查用户之前是否使用中文
- 不再尝试重新安装中文语言包
- 直接强制切换到英文环境

### 2. **彻底的清理**
- 删除所有语言包配置（`languagepacks.json` 设为 `{}`）
- 删除 `argv.json` 文件，让应用使用默认语言
- 清理 `clp` 目录和所有缓存
- 清理扩展缓存中的语言包文件

### 3. **强制标记机制**
- 创建 `.force-english-language` 标记文件
- 记录强制切换的原因和时间
- 便于调试和问题追踪

### 4. **环境变量设置**
```typescript
process.env['VSCODE_FORCE_ENGLISH'] = 'true';
process.env['VSCODE_NLS_CONFIG'] = JSON.stringify({
    locale: 'en',
    availableLanguages: {}
});
```

## 📋 **执行条件**

### 触发条件
```typescript
// 检查构建版本是否需要进行黑屏检查
const currentBuildVersion = (this.productService as any).buildVersion || this.productService.version || '1.0.0';
const needsCheck = await this.shouldPerformBlackScreenCheck(currentBuildVersion);

if (!needsCheck) {
    this.logService.info(`构建版本一致 (${currentBuildVersion})，跳过黑屏检查`);
    return;
}

// 构建版本不一致时，强制重置为英文语言
this.logService.info(`构建版本不一致，直接重置语言包 (当前构建版本: ${currentBuildVersion})`);
await this.resetLanguagePacksForVersionChange(currentJoyCodeSupportDir);
```

### 执行时机
- 应用启动时自动检查
- 构建版本与存储版本不一致时触发
- 在 `handleBlackScreenIssues()` 方法中执行

## 🎯 **预期效果**

### 1. **应用稳定性**
- ✅ 避免语言包版本不兼容导致的问题
- ✅ 消除中文语言显示错乱
- ✅ 防止界面重复显示问题
- ✅ 减少启动黑屏风险

### 2. **用户体验**
- ✅ 确保应用能够正常启动和运行
- ✅ 提供一致的英文界面体验
- ✅ 避免语言包损坏导致的功能异常

### 3. **维护便利性**
- ✅ 简化语言包管理逻辑
- ✅ 减少语言相关的bug报告
- ✅ 提供清晰的问题追踪机制

## 📝 **使用说明**

### 自动触发
- 新的机制会在应用启动时自动检查
- 当检测到构建版本变化时自动执行
- 无需用户手动干预

### 手动触发
如果需要手动触发强制英文机制：
1. 删除 `blackscreen-check-version.json` 文件
2. 重启应用
3. 应用会检测到版本不一致并执行强制英文

### 验证效果
重启应用后检查：
- `argv.json` 文件应该被删除
- `languagepacks.json` 应该为空对象 `{}`
- 存在 `.force-english-language` 标记文件
- 应用界面显示为英文

## 🔍 **调试信息**

### 日志输出
```
构建版本不同，强制删除所有语言包并切换到英文...
强制设置为英文语言...
已清空语言包配置，删除所有语言包
已删除 argv.json，将使用默认英文语言
已创建英文语言强制标记
强制设置为英文语言完成
语言包重置完成（构建版本变化），已强制切换到英文语言
```

### 标记文件内容
```json
{
  "timestamp": 1640995200000,
  "buildVersion": "1.95.0-insider",
  "reason": "Build version changed - forced to English for stability",
  "message": "All language packs have been removed to ensure application stability"
}
```

## 🎉 **总结**

这个新的强制英文机制具有以下优势：

1. **简单可靠**：不再尝试重新安装语言包，直接使用英文
2. **彻底清理**：删除所有可能导致问题的语言包文件
3. **稳定性优先**：确保应用能够正常启动和运行
4. **易于调试**：提供清晰的日志和标记文件
5. **自动化**：无需用户干预，自动处理版本变化

这种策略特别适合解决您遇到的中文语言错乱问题，通过强制使用英文来确保应用的稳定性和一致性。
