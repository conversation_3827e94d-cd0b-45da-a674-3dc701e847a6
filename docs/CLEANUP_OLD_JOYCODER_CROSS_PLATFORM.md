# cleanupOldJoyCoderData 跨平台适配

## 🎯 **问题识别**

您发现了一个重要问题：**`cleanupOldJoyCoderData` 也需要适配 Windows**。

原有实现只支持 macOS 环境，使用了硬编码的 macOS 路径，无法在 Windows 和 Linux 环境下正确清理旧版数据。

## ❌ **原有问题**

### 1. **平台限制**
```typescript
// 只在 macOS 下执行
if (isMacintosh) {
    await this.cleanupOldJoyCoderData();
}
```

### 2. **硬编码路径**
```typescript
// 只支持 macOS 路径
const oldJoyCoderSupportDir = URI.file(path.join(userHome, 'Library', 'Application Support', 'JoyCoder'));
```

### 3. **单一命令行工具处理**
```typescript
// 只处理 Unix 系统的命令行工具
const joycoderCommandPath = URI.file('/usr/local/bin/joycoder');
```

## ✅ **跨平台适配方案**

### 1. **统一入口**

#### 修改前：
```typescript
if (isMacintosh) {
    await this.cleanupOldJoyCoderData();
}
```

#### 修改后：
```typescript
// --- 清理旧版JoyCoder相关目录和命令（跨平台） ---
await this.cleanupOldJoyCoderData();
```

### 2. **平台检测和路径适配**

#### 新增 `getOldJoyCoderSupportDirectories()` 方法：
```typescript
private getOldJoyCoderSupportDirectories(userHome: string): Array<{ uri: URI; description: string }> {
    const directories: Array<{ uri: URI; description: string }> = [];

    if (isMacintosh) {
        // macOS: ~/Library/Application Support/JoyCoder
        directories.push({
            uri: URI.file(path.join(userHome, 'Library', 'Application Support', 'JoyCoder')),
            description: 'macOS Application Support目录'
        });
    } else if (isWindows) {
        // Windows: %APPDATA%\JoyCoder
        directories.push({
            uri: URI.file(path.join(userHome, 'AppData', 'Roaming', 'JoyCoder')),
            description: 'Windows AppData目录'
        });
        // Windows: %LOCALAPPDATA%\JoyCoder
        directories.push({
            uri: URI.file(path.join(userHome, 'AppData', 'Local', 'JoyCoder')),
            description: 'Windows LocalAppData目录'
        });
    } else {
        // Linux: ~/.config/JoyCoder
        directories.push({
            uri: URI.file(path.join(userHome, '.config', 'JoyCoder')),
            description: 'Linux config目录'
        });
    }

    return directories;
}
```

### 3. **跨平台命令行工具清理**

#### 重构 `uninstallOldJoyCoderCommand()` 方法：
```typescript
private async uninstallOldJoyCoderCommand(): Promise<void> {
    try {
        if (isMacintosh || isLinux) {
            // macOS 和 Linux: 检查并删除 /usr/local/bin/joycoder 符号链接
            await this.uninstallUnixJoyCoderCommand();
        } else if (isWindows) {
            // Windows: 检查并删除可能的命令行工具
            await this.uninstallWindowsJoyCoderCommand();
        }
    } catch (error) {
        this.logService.error('卸载旧版JoyCoder命令时发生错误:', error);
    }
}
```

#### 新增 `uninstallWindowsJoyCoderCommand()` 方法：
```typescript
private async uninstallWindowsJoyCoderCommand(): Promise<void> {
    // Windows 可能的命令行工具位置
    const possiblePaths = [
        'C:\\Program Files\\JoyCoder\\bin\\joycoder.exe',
        'C:\\Program Files (x86)\\JoyCoder\\bin\\joycoder.exe',
        path.join(this.environmentMainService.userHome.fsPath, 'AppData', 'Local', 'Programs', 'JoyCoder', 'bin', 'joycoder.exe')
    ];

    for (const commandPath of possiblePaths) {
        const joycoderCommandUri = URI.file(commandPath);
        if (await this.fileService.exists(joycoderCommandUri)) {
            // 删除命令行工具
            await this.fileService.del(joycoderCommandUri);
        }
    }

    // 检查 PATH 环境变量
    const { stdout } = await execAsync('echo %PATH%');
    if (stdout.includes('JoyCoder')) {
        this.logService.info('检测到PATH中包含JoyCoder相关路径，建议手动清理');
    }
}
```

## 📊 **平台支持对比**

| 平台 | 应用支持目录 | 命令行工具位置 | 权限处理 |
|------|-------------|---------------|----------|
| **macOS** | `~/Library/Application Support/JoyCoder` | `/usr/local/bin/joycoder` | osascript + 管理员权限 |
| **Windows** | `%APPDATA%\JoyCoder`<br>`%LOCALAPPDATA%\JoyCoder` | `Program Files\JoyCoder\bin\joycoder.exe`<br>`AppData\Local\Programs\JoyCoder\bin\joycoder.exe` | 直接删除 + PATH 检查 |
| **Linux** | `~/.config/JoyCoder` | `/usr/local/bin/joycoder` | sudo + 管理员权限 |

## 🔧 **清理范围**

### 1. **平台特定目录**
- **macOS**: `~/Library/Application Support/JoyCoder`
- **Windows**: `%APPDATA%\JoyCoder` 和 `%LOCALAPPDATA%\JoyCoder`
- **Linux**: `~/.config/JoyCoder`

### 2. **通用配置目录**
- `~/.joycoder` (所有平台)
- `~/.joycoder-editor` (所有平台)

### 3. **命令行工具**
- **Unix系统**: `/usr/local/bin/joycoder`
- **Windows**: 多个可能位置的 `joycoder.exe`

### 4. **环境变量**
- **Windows**: 检查 PATH 中的 JoyCoder 相关路径

## 📝 **日志输出示例**

### macOS 环境：
```
[info] 开始清理旧版JoyCoder相关数据 (macOS)...
[info] 用户主目录: /Users/<USER>
[info] 发现旧版JoyCoder支持目录，正在删除: /Users/<USER>/Library/Application Support/JoyCoder
[info] 成功删除旧版JoyCoder支持目录: macOS Application Support目录
[info] 旧版JoyCoder数据清理完成 (macOS)
```

### Windows 环境：
```
[info] 开始清理旧版JoyCoder相关数据 (Windows)...
[info] 用户主目录: C:\Users\<USER>\Users\username\AppData\Roaming\JoyCoder
[info] 成功删除旧版JoyCoder支持目录: Windows AppData目录
[info] 发现旧版Windows joycoder命令，正在删除: C:\Program Files\JoyCoder\bin\joycoder.exe
[info] 成功删除旧版Windows joycoder命令: C:\Program Files\JoyCoder\bin\joycoder.exe
[info] 旧版JoyCoder数据清理完成 (Windows)
```

### Linux 环境：
```
[info] 开始清理旧版JoyCoder相关数据 (Linux)...
[info] 用户主目录: /home/<USER>
[info] 发现旧版JoyCoder支持目录，正在删除: /home/<USER>/.config/JoyCoder
[info] 成功删除旧版JoyCoder支持目录: Linux config目录
[info] 旧版JoyCoder数据清理完成 (Linux)
```

## 🛡️ **安全考虑**

### 1. **权限处理**
- **macOS**: 使用 osascript 请求管理员权限
- **Linux**: 使用 sudo 命令
- **Windows**: 直接删除，避免复杂的权限提升

### 2. **错误处理**
- 每个删除操作都有独立的错误处理
- 失败不会阻塞其他清理操作
- 详细的错误日志记录

### 3. **PATH 环境变量**
- Windows 下只检查不自动修改
- 避免破坏用户的环境配置

## 🎯 **适配效果**

### ✅ **功能完整性**
- 支持所有主流平台（macOS、Windows、Linux）
- 清理范围覆盖所有可能的旧版数据
- 命令行工具清理适配各平台特点

### ✅ **代码质量**
- 统一的处理流程
- 平台特定的优化
- 完善的错误处理和日志记录

### ✅ **用户体验**
- 自动检测平台并执行相应清理
- 详细的操作日志
- 安全的清理机制

## 🎉 **总结**

通过这次跨平台适配：

1. **扩展支持**：从只支持 macOS 扩展到支持 macOS、Windows、Linux
2. **路径适配**：使用平台特定的标准路径
3. **命令行工具**：适配各平台的命令行工具安装位置
4. **权限处理**：针对不同平台使用合适的权限处理方式
5. **安全性**：保持安全的清理机制，避免误删重要数据

现在 `cleanupOldJoyCoderData` 方法可以在所有平台上正确工作，为用户提供一致的旧版数据清理体验。
