# JoyCoder IDE和扩展之间的通信机制调研

本文档提供了一个示例，展示如何在扩展中接收和处理来自 JoyCoder IDE 的登录信息。

### 方案一 通过命令(commands)系统进行通信

IDE端注册命令：

````typescript
// IDE端注册命令
CommandsRegistry.registerCommand('workbench.action.joycoderIsLoggedIn', async (accessor: ServicesAccessor, message: IAuthMessage) => {
    const loginService = accessor.get(ILoginService);
    // 处理来自扩展的消息
    await loginService.handleExtensionMessage(message);
    return true;
});

// 扩展端示例
class ExtensionAuthService {
    // 同步到IDE
    private _syncToIDE(message: IAuthMessage): void {
        // 扩展调用IDE的命令
        vscode.commands.executeCommand('workbench.action.joycoderIsLoggedIn', message);
    }

    // 接收IDE的消息
    constructor(context: vscode.ExtensionContext) {
        // 注册命令来接收IDE的消息
        context.subscriptions.push(
            vscode.commands.registerCommand('joycoder.extension.joycoderIsLoggedIn',
                (message: IAuthMessage) => {
                    console.log('Received auth message from IDE:', message);
                    if (message.type === 'LOGIN') {
                        this._handleIDELogin(message.userInfo!);
                    } else if (message.type === 'LOGOUT') {
                        this._handleIDELogout();
                    }
                }
            )
            // 可以监听其他指令
        );
    }
}
````

### 方案二 通过通信接口进行通信

- IDE 主进程通过通道（Channel）暴露 API
- 扩展进程通过命令系统暴露 API
- 双向通信保持同步,双方都维护各自的状态，通过事件通知状态变化，确保状态一致性

优点
- 清晰的 API 边界
- 双向通信机制
- 良好的错误处理
- 状态同步可靠



- 首先在 IDE 主进程注册通道和命令：

````typescript
// 注册主进程 API 通道
class LoginMainApiChannel implements IServerChannel {
    constructor(private service: ILoginMainService) { }

    listen(_: unknown, event: string): Event<any> {
        switch (event) {
            case 'onDidChangeLoginStatus': return this.service.onDidChangeLoginStatus;
            default: throw new Error(`未知事件: ${event}`);
        }
    }

    call(_: unknown, command: string, arg?: any): Promise<any> {
        switch (command) {
            case 'handleExtensionLogin': return this.service.handleExtensionMessage(arg);
            case 'handleExtensionLogout': return this.service.handleExtensionMessage({ type: 'LOGOUT' });
            default: throw new Error(`未知命令: ${command}`);
        }
    }
}

// 注册通道
Registry.as<IMainProcessChannelRegistry>(MainProcessExtensions.MainProcessChannels)
    .registerChannel('loginMainApi', new LoginMainApiChannel(loginMainService));
````

- 在扩展进程注册 API 通道：

````typescript
// 扩展入口（在扩展项目根目录的 src/extension.ts）
export class ExtensionAuthApiChannel {
    private static instance: ExtensionAuthApiChannel;
    private authService: ExtensionAuthService;

    private constructor(context: vscode.ExtensionContext) {
        this.authService = new ExtensionAuthService(context);

        // 注册扩展 API 命令
        context.subscriptions.push(
            vscode.commands.registerCommand('joycoder.extension.api.handleIDELogin',
                async (userInfo: IUserInfo) => {
                    return this.authService.handleIDELogin(userInfo);
                }
            ),
            vscode.commands.registerCommand('joycoder.extension.api.handleIDELogout',
                async () => {
                    return this.authService.handleIDELogout();
                }
            )
        );
    }

    static initialize(context: vscode.ExtensionContext) {
        if (!ExtensionAuthApiChannel.instance) {
            ExtensionAuthApiChannel.instance = new ExtensionAuthApiChannel(context);
        }
        return ExtensionAuthApiChannel.instance;
    }
}

// 在扩展激活时初始化
export function activate(context: vscode.ExtensionContext) {
    ExtensionAuthApiChannel.initialize(context);
}

````
- 扩展服务实现：

````typescript
// 扩展认证服务（在扩展项目的 src/services/authService.ts）：
export class ExtensionAuthService {
    private _userInfo: IUserInfo | null = null;
    private _authState: AuthState = AuthState.LoggedOut;
    private readonly _onAuthStateChanged = new vscode.EventEmitter<AuthState>();

    constructor(private readonly context: vscode.ExtensionContext) {}

    // IDE 调用的 API
    async handleIDELogin(userInfo: IUserInfo): Promise<boolean> {
        try {
            this._userInfo = userInfo;
            this._authState = AuthState.LoggedIn;
            this._onAuthStateChanged.fire(this._authState);
            await this._updateExtensionState();
            return true;
        } catch (error) {
            console.error('处理 IDE 登录失败:', error);
            return false;
        }
    }

    async handleIDELogout(): Promise<boolean> {
        try {
            this._userInfo = null;
            this._authState = AuthState.LoggedOut;
            this._onAuthStateChanged.fire(this._authState);
            await this._updateExtensionState();
            return true;
        } catch (error) {
            console.error('处理 IDE 登出失败:', error);
            return false;
        }
    }

    // 扩展调用主进程 API
    async login(token: string): Promise<boolean> {
        try {
            const userInfo = await this._performTokenLogin(token);

            // 调用主进程 API
            const mainProcessApi = await this._getMainProcessApi();
            await mainProcessApi.handleExtensionLogin({
                type: 'LOGIN',
                userInfo,
                timestamp: Date.now()
            });

            return true;
        } catch (error) {
            console.error('扩展登录失败:', error);
            return false;
        }
    }

    async logout(): Promise<boolean> {
        try {
            // 调用主进程 API
            const mainProcessApi = await this._getMainProcessApi();
            await mainProcessApi.handleExtensionLogout();
            return true;
        } catch (error) {
            console.error('扩展登出失败:', error);
            return false;
        }
    }

    private async _getMainProcessApi() {
        // 获取主进程 API 客户端
        return await vscode.commands.executeCommand('workbench.getMainProcessApi', 'loginMainApi');
    }

    private async _updateExtensionState() {
        await vscode.commands.executeCommand('setContext', 'joycoder.isLoggedIn',
            this._authState === AuthState.LoggedIn);
    }
}
````

- IDE 主进程服务调用扩展 API

````typescript
// 主进程 API 通道定义（在  src/vs/platform/login/electron-main/ 目录下）：
export class LoginMainService implements ILoginMainService {

    // 。。。
    async syncToExtension(message: IAuthMessage): Promise<boolean> {
        try {
            if (message.type === 'LOGIN') {
                // 调用扩展 API
                await this.commandService.executeCommand(
                    'joycoder.extension.api.handleIDELogin',
                    message.userInfo
                );
            } else if (message.type === 'LOGOUT') {
                await this.commandService.executeCommand(
                    'joycoder.extension.api.handleIDELogout'
                );
            }

            this._onDidChangeLoginStatus.fire(message.type === 'LOGIN');
            return true;
        } catch (error) {
            this.logService.error('同步到扩展失败:', error);
            return false;
        }
    }

    async handleExtensionMessage(message: IAuthMessage): Promise<void> {
        try {
            if (message.type === 'LOGIN' && message.userInfo) {
                // 处理扩展登录
                await this.joyCoderUserService.writeMainUserInfo({
                    ...message.userInfo
                });
                this._onDidChangeLoginStatus.fire(true);
            } else if (message.type === 'LOGOUT') {
                // 处理扩展登出
                await this.joyCoderUserService.writeMainUserInfo(null);
                this._onDidChangeLoginStatus.fire(false);
            }
        } catch (error) {
            this.logService.error('处理扩展消息失败:', error);
            throw error;
        }
    }
}
````
