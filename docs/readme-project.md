# JoyCoder-IDE 源代码目录结构说明

## 启动相关文件
### 基础启动文件
- `bootstrap-cli.ts`: CLI模式启动引导文件
  - 处理命令行参数
  - 初始化CLI环境
  - 配置日志系统
  - 启动命令行界面

- `bootstrap-esm.ts`: ES模块加载启动引导文件
  - 配置ES模块加载器
  - 处理模块解析和导入
  - 支持TypeScript模块的动态导入

- `bootstrap-fork.ts`: 进程分叉启动引导文件
  - 处理子进程创建
  - 配置进程间通信
  - 管理进程生命周期

- `bootstrap-import.ts`: 模块导入启动引导文件
  - 处理动态模块导入
  - 配置模块缓存
  - 支持热重载

### 高级启动文件
- `bootstrap-meta.ts`: 元数据启动引导文件
  - 加载项目元数据
  - 处理配置信息
  - 初始化环境变量

- `bootstrap-node.ts`: Node环境启动引导文件
  - 配置Node.js运行环境
  - 初始化全局对象
  - 设置异常处理

- `bootstrap-server.ts`: 服务器启动引导文件
  - 初始化服务器实例
  - 配置中间件
  - 启动监听服务

- `bootstrap-window.ts`: 窗口进程启动引导文件
  - 创建主窗口实例
  - 配置窗口事件处理
  - 初始化UI组件

## 主要入口文件
- `main.ts`: 应用程序主入口文件
  - 协调各个模块的初始化
  - 管理应用生命周期
  - 处理全局事件

- `cli.ts`: 命令行界面入口文件
  - 定义命令行指令
  - 处理参数解析
  - 执行命令操作

- `server-main.ts`: 服务器主入口文件
  - 配置服务器路由
  - 处理请求响应
  - 管理服务器状态

- `server-cli.ts`: 服务器命令行入口文件
  - 提供服务器管理命令
  - 处理服务器配置
  - 控制服务器运行

## 配置文件
- `tsconfig.base.json`: TypeScript基础配置文件
  - 定义编译选项
  - 配置模块解析
  - 设置语言特性

- `tsconfig.json`: 主TypeScript配置文件
  - 继承基础配置
  - 定义项目结构
  - 配置特定选项

- `tsconfig.monaco.json`: Monaco编辑器相关TypeScript配置
  - 配置编辑器特性
  - 设置语言服务
  - 定义编辑器行为

- `tsconfig.tsec.json`: TypeScript安全检查配置
  - 设置安全规则
  - 配置代码分析
  - 定义检查级别

- `tsconfig.vscode-dts.json`: VSCode类型定义配置
  - 配置API类型
  - 设置类型生成
  - 管理类型导出

- `tsec.exemptions.json`: TypeScript安全检查豁免配置
  - 定义豁免规则
  - 配置特例处理
  - 管理安全策略

## 核心目录结构

### typings/
类型定义目录，包含各种全局类型声明：
- `base-common.d.ts`: 基础通用类型定义
  - 核心数据类型
  - 通用接口定义
  - 工具类型

- `crypto.d.ts`: 加密相关类型
  - 加密算法类型
  - 密钥管理接口
  - 安全协议定义

- `editContext.d.ts`: 编辑上下文类型
  - 编辑器状态
  - 选择范围
  - 编辑操作

- `require.d.ts`: 模块加载类型
  - 模块定义
  - 导入导出类型
  - 加载器接口

- `thenable.d.ts`: Promise类型
  - Promise链式调用
  - 异步操作类型
  - 错误处理

### vs/
核心源代码目录：

#### 基础设施
- `loader.js`: 模块加载器
  - 异步模块加载
  - 依赖管理
  - 资源优化

#### 编辑器核心 (editor/)
- `editor.all.ts`: 编辑器完整功能集成
  - 核心编辑功能
  - 插件系统
  - 界面组件

- `editor.main.ts`: 编辑器主要实现
  - 文档管理
  - 编辑操作
  - 视图渲染

##### 通用功能 (common/)
- `languageFeatureRegistry.ts`: 语言特性注册
  - 语法高亮
  - 代码补全
  - 错误诊断

- `standaloneStrings.ts`: 独立字符串资源
  - 本地化支持
  - 界面文本
  - 错误消息

##### 服务实现 (services/)
- `editorBaseApi.ts`: 编辑器基础API
  - 核心接口定义
  - 基础功能实现
  - 扩展点定义

- `editorSimpleWorker.ts`: 编辑器工作进程
  - 后台处理
  - 性能优化
  - 并发控制

- `languageFeatures.ts`: 语言特性实现
  - 智能提示
  - 代码格式化
  - 引用查找

- `modelService.ts`: 文档模型服务
  - 文档管理
  - 内容变更
  - 状态同步

- `semanticTokensStyling.ts`: 语义化标记样式
  - 代码着色
  - 主题支持
  - 样式定制

### vscode-dts/
VSCode API类型定义目录：

#### 核心API
- `vscode.d.ts`: 主要API定义
  - 扩展API
  - 工作区API
  - 调试API

#### 提议的API
##### 编辑器增强
- `vscode.proposed.editorInsets.d.ts`: 编辑器内嵌功能
- `vscode.proposed.inlineEdit.d.ts`: 内联编辑支持
- `vscode.proposed.languageStatus.d.ts`: 语言状态

##### 智能功能
- `vscode.proposed.aiRelatedInformation.d.ts`: AI相关信息
- `vscode.proposed.codeActionAI.d.ts`: AI代码操作
- `vscode.proposed.languageModelCapabilities.d.ts`: 语言模型能力

##### 协作功能
- `vscode.proposed.chatProvider.d.ts`: 聊天功能
- `vscode.proposed.commentReactor.d.ts`: 评论响应
- `vscode.proposed.shareProvider.d.ts`: 分享功能

##### 开发工具
- `vscode.proposed.debugVisualization.d.ts`: 调试可视化
- `vscode.proposed.testObserver.d.ts`: 测试观察器
- `vscode.proposed.profileContentHandlers.d.ts`: 性能分析

##### 终端相关
- `vscode.proposed.terminalQuickFixProvider.d.ts`: 终端快速修复
- `vscode.proposed.terminalShellType.d.ts`: 终端Shell类型
- `vscode.proposed.terminalSelection.d.ts`: 终端选择

## 文件关系说明

### 启动流程
1. 用户启动应用 → `main.ts`
2. 根据启动模式选择相应的bootstrap文件
3. bootstrap文件初始化环境
4. 加载配置文件
5. 启动相应的服务

### 编辑器初始化
1. `editor.main.ts` 作为入口
2. 通过 `modelService.ts` 创建文档模型
3. 使用 `languageFeatures.ts` 加载语言支持
4. 应用 `semanticTokensStyling.ts` 进行代码着色

### 类型系统
1. `typings/` 下的基础类型定义
2. `vscode-dts/` 中的API类型定义
3. 各个模块中的具体实现

### 扩展系统
1. 通过 `vscode.d.ts` 定义扩展API
2. 使用提议的API进行功能扩展
3. 通过服务实现进行具体功能支持
