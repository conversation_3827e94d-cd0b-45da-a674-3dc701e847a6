# Windows Shell Command Fix Test

## 问题描述
在Windows系统中卸载joycode命令之后，无法再重新安装，报错：
"Unable to find shell script in 'C:\Program Files\JoyCode\bin\joycode.cmd'"

## 修复内容

### 1. 改进了 `installWindowsShellCommand` 方法
- 添加了文件不存在时的自动创建逻辑
- 在安装前检查文件是否存在，如果不存在则调用 `createWindowsShellCommandFile` 方法创建

### 2. 新增了 `createWindowsShellCommandFile` 方法
- 支持生产环境和开发环境的不同处理逻辑
- 生产环境：基于模板创建 `joycode.cmd` 文件
- 开发环境：创建或使用现有的 `code-cli.bat` 文件

### 3. 改进了 `getActualCliPath` 方法
- 移除了对文件存在性的检查，直接返回应该存在的路径
- 简化了路径解析逻辑，提高了可靠性

## 修复逻辑

### 生产环境 (isBuilt = true)
1. 路径：`C:\Program Files\JoyCode\bin\joycode.cmd`
2. 如果文件不存在，创建包含以下内容的cmd文件：
```cmd
@echo off
setlocal
set VSCODE_DEV=
set ELECTRON_RUN_AS_NODE=1
"%~dp0..\JoyCode.exe" "%~dp0..\resources\app\out\cli.js" %*
IF %ERRORLEVEL% NEQ 0 EXIT /b %ERRORLEVEL%
endlocal
```

### 开发环境 (isBuilt = false)
1. 路径：`{appRoot}\scripts\code-cli.bat`
2. 如果文件不存在，创建开发环境版本的批处理文件

## 测试步骤

### 测试场景1：正常安装
1. 打开JoyCode IDE
2. 使用命令面板执行"Install 'joycode' command in PATH"
3. 验证命令是否成功安装

### 测试场景2：卸载后重新安装
1. 使用命令面板执行"Uninstall 'joycode' command from PATH"
2. 验证命令是否成功卸载
3. 再次使用命令面板执行"Install 'joycode' command in PATH"
4. 验证命令是否成功重新安装（这是之前失败的场景）

### 测试场景3：文件被手动删除后安装
1. 手动删除 `C:\Program Files\JoyCode\bin\joycode.cmd` 文件
2. 使用命令面板执行"Install 'joycode' command in PATH"
3. 验证命令是否能够自动创建文件并成功安装

## 预期结果
- 所有测试场景都应该成功
- 不再出现"Unable to find shell script"错误
- 命令行工具能够正常工作

## 日志输出
修复后的代码会输出详细的日志信息，包括：
- CLI路径解析过程
- 文件创建过程
- 安装/卸载过程的详细状态

可以通过开发者工具的控制台查看这些日志信息。
