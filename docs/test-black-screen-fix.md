# JoyCode 黑屏问题修复测试指南

## 概述

本文档描述了如何测试 JoyCode 启动时黑屏问题的修复功能。

## 修复内容

### 1. 自动检测和清理功能

- **自动检测用户数据损坏**：在启动时检查关键文件和目录
- **自动备份和清理**：发现问题时自动备份并清理用户数据目录
- **用户友好通知**：显示清理操作的详细信息

### 2. 命令行参数支持

新增两个命令行参数：

- `--force-cleanup`：强制清理用户数据目录
- `--reset-user-data`：重置用户数据目录（与 --force-cleanup 相同）

### 3. 检测逻辑

系统会检查以下关键路径：
- `Library/Application Support/JoyCode/User/globalStorage`
- `Library/Application Support/JoyCode/User/workspaceStorage`
- `Library/Application Support/JoyCode/logs`
- `Library/Application Support/JoyCode/CachedExtensions`

以及关键配置文件：
- `User/settings.json`
- `User/globalStorage/storage.json`

## 测试步骤

### 测试1：正常启动（无问题）

1. 确保 JoyCode 正常运行
2. 重启应用
3. 验证：应该正常启动，无额外弹窗

### 测试2：强制清理测试

1. 关闭 JoyCode
2. 使用命令行启动：
   ```bash
   /Applications/JoyCode.app/Contents/MacOS/JoyCode --force-cleanup
   ```
3. 验证：
   - 应该显示用户数据清理通知
   - 原数据应该被备份到 `Library/Application Support/JoyCode.backup-[timestamp]`
   - 应用应该正常启动

### 测试3：模拟损坏数据测试

1. 关闭 JoyCode
2. 手动损坏配置文件：
   ```bash
   echo "invalid json" > ~/Library/Application\ Support/JoyCode/User/settings.json
   ```
3. 启动 JoyCode
4. 验证：
   - 应该自动检测到损坏
   - 显示清理通知
   - 应用正常启动

### 测试4：模拟缺失目录测试

1. 关闭 JoyCode
2. 删除关键目录：
   ```bash
   rm -rf ~/Library/Application\ Support/JoyCode/User/globalStorage
   ```
3. 启动 JoyCode
4. 验证：
   - 应该检测到目录缺失
   - 自动清理并重新创建
   - 应用正常启动

## 日志检查

启动时检查控制台日志，应该看到类似信息：

```
[main] 开始检测可能导致黑屏的问题...
[main] 用户数据目录检查正常
```

或者在检测到问题时：

```
[main] 开始检测可能导致黑屏的问题...
[main] 检测到用户数据损坏，可能导致黑屏问题
[main] 开始清理当前版本用户数据，原因: 用户数据损坏
[main] 备份用户数据到: /Users/<USER>/Library/Application Support/JoyCode.backup-2024-01-01T12-00-00-000Z
[main] 成功备份用户数据目录
```

## 预期结果

1. **自动检测**：系统能够自动检测到可能导致黑屏的问题
2. **安全备份**：在清理前会尝试备份用户数据
3. **用户通知**：清理操作会通过对话框通知用户
4. **正常启动**：清理后应用能够正常启动，不再出现黑屏

## 故障排除

如果测试过程中遇到问题：

1. 检查控制台日志获取详细错误信息
2. 确认备份目录是否创建成功
3. 验证权限设置是否正确
4. 如果自动清理失败，可以手动删除 `~/Library/Application Support/JoyCode` 目录

## 注意事项

- 测试前建议备份重要的用户设置
- 强制清理会删除所有用户配置和扩展设置
- 备份目录包含时间戳，可以用于恢复数据
