# JoyCode 黑屏问题修复实现文档

## 问题描述

用户在启动 JoyCode 时偶尔会遇到黑屏问题，通常是由于用户数据目录 `Library/Application Support/JoyCode` 中的配置文件损坏导致的。目前的解决方案是手动删除该目录，但这对用户来说不够友好。

## 解决方案

实现了一套自动检测和修复机制，能够在启动时检测可能导致黑屏的问题，并自动进行清理和修复。

## 实现细节

### 1. 核心功能函数

#### `handleBlackScreenIssues()`
- 主要的黑屏问题检测和处理函数
- 支持命令行参数强制清理
- 自动检测用户数据损坏

#### `detectUserDataCorruption()`
- 检测用户数据目录中的关键文件和目录
- 验证 JSON 配置文件的完整性
- 检查关键路径的可访问性

#### `cleanupCurrentUserData()`
- 安全地清理用户数据目录
- 先尝试备份，失败时直接删除
- 生成带时间戳的备份目录

#### `showUserDataCleanupNotification()`
- 向用户显示清理操作的通知
- 提供备份路径信息

### 2. 检测逻辑

系统会检查以下关键组件：

**关键目录：**
- `User/globalStorage` - 全局存储目录
- `User/workspaceStorage` - 工作区存储目录
- `logs` - 日志目录
- `CachedExtensions` - 缓存扩展目录

**关键配置文件：**
- `User/settings.json` - 用户设置文件
- `User/globalStorage/storage.json` - 全局存储文件

### 3. 命令行参数支持

新增了两个命令行参数：

```typescript
'force-cleanup': { 
    type: 'boolean', 
    description: "Force cleanup of user data directory on startup", 
    cat: 't' 
},
'reset-user-data': { 
    type: 'boolean', 
    description: "Reset user data directory on startup", 
    cat: 't' 
}
```

### 4. 启动流程集成

在 `startup()` 方法中，在清理旧版 JoyCoder 数据之后添加了黑屏问题检测：

```typescript
// --- 清理旧版JoyCoder相关目录和命令 ---
if (isMacintosh) {
    await this.cleanupOldJoyCoderData();
}

// --- 检测并处理可能导致黑屏的问题 ---
if (isMacintosh) {
    await this.handleBlackScreenIssues();
}
```

## 文件修改清单

### 1. `src/vs/code/electron-main/app.ts`
- 添加了 `handleBlackScreenIssues()` 方法
- 添加了 `detectUserDataCorruption()` 方法
- 添加了 `cleanupCurrentUserData()` 方法
- 添加了 `showUserDataCleanupNotification()` 方法
- 在 `startup()` 方法中集成了黑屏检测逻辑

### 2. `src/main.ts`
- 在 `parseCLIArgs()` 中添加了新的布尔参数支持

### 3. `src/vs/platform/environment/common/argv.ts`
- 在 `NativeParsedArgs` 接口中添加了新参数类型定义

### 4. `src/vs/platform/environment/node/argv.ts`
- 在 `OPTIONS` 中添加了新参数的完整定义和描述

## 工作流程

1. **启动检测**：应用启动时自动调用 `handleBlackScreenIssues()`
2. **参数检查**：检查是否有强制清理的命令行参数
3. **数据验证**：检查用户数据目录的完整性
4. **问题处理**：如果检测到问题，执行清理操作
5. **用户通知**：显示清理结果和备份信息
6. **继续启动**：清理完成后继续正常启动流程

## 安全特性

1. **备份优先**：总是先尝试备份用户数据
2. **时间戳标识**：备份目录包含时间戳，避免冲突
3. **错误处理**：完善的错误处理和日志记录
4. **用户通知**：清理操作对用户透明，提供详细信息

## 使用方法

### 自动检测（推荐）
正常启动应用，系统会自动检测和处理问题。

### 手动强制清理
```bash
/Applications/JoyCode.app/Contents/MacOS/JoyCode --force-cleanup
```

或者：
```bash
/Applications/JoyCode.app/Contents/MacOS/JoyCode --reset-user-data
```

## 日志输出

系统会在控制台输出详细的日志信息：

```
[main] 开始检测可能导致黑屏的问题...
[main] 用户数据目录检查正常
```

或者在检测到问题时：

```
[main] 检测到用户数据损坏，可能导致黑屏问题
[main] 开始清理当前版本用户数据，原因: 用户数据损坏
[main] 备份用户数据到: /Users/<USER>/Library/Application Support/JoyCode.backup-2024-01-01T12-00-00-000Z
[main] 成功备份用户数据目录
```

## 兼容性

- 目前仅在 macOS 平台启用（通过 `isMacintosh` 检查）
- 可以轻松扩展到其他平台
- 不影响现有功能和用户体验

## 测试建议

1. 正常启动测试
2. 强制清理参数测试
3. 模拟数据损坏测试
4. 备份和恢复测试

详细测试步骤请参考 `test-black-screen-fix.md` 文档。
