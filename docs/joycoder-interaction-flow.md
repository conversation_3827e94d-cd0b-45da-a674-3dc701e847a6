# JoyCoder-IDE 功能交互流程图

本文档详细描述了 JoyCoder-IDE 中主要功能的交互流程，展示了用户与各个 AI 辅助功能交互时的代码执行路径。

## 快速编辑功能 (Ctrl+K) 流程图

```mermaid
sequenceDiagram
    participant User as 用户
    participant QEA as quickEditActions.ts
    participant IDS as inlineDiffsService.ts
    participant LLMS as llmMessageService.ts
    participant LLM as 大型语言模型 (LLM)
    
    User->>QEA: 按下 Ctrl+K 快捷键
    QEA->>IDS: 调用 addCtrlKZone()
    IDS->>IDS: 创建输入区域
    User->>IDS: 输入编辑指令
    IDS->>IDS: 调用 startApplying()
    IDS->>LLMS: 发送 LLM 请求
    LLMS->>LLM: 发送提示到 LLM
    LLM->>LLMS: 返回流式响应
    LLMS->>IDS: 传递 LLM 响应
    IDS->>IDS: 调用 _writeDiffZoneLLMText()
    IDS->>IDS: 更新编辑器内容
    IDS->>IDS: 显示接受/拒绝按钮
    User->>IDS: 点击接受/拒绝
    IDS->>IDS: 调用 acceptDiff()/rejectDiff()
    IDS->>IDS: 更新最终代码
```

## 侧边栏聊天功能 (Ctrl+L) 流程图

```mermaid
sequenceDiagram
    participant User as 用户
    participant SA as sidebarActions.ts
    participant SP as sidebarPane.ts
    participant SS as sidebarStateService.ts
    participant IDS as inlineDiffsService.ts
    participant LLMS as llmMessageService.ts
    participant LLM as 大型语言模型 (LLM)
    
    User->>SA: 按下 Ctrl+L 快捷键
    SA->>SS: 更新侧边栏状态
    SS->>SP: 显示侧边栏
    User->>SP: 输入聊天消息
    SP->>IDS: 调用 startApplying()
    IDS->>LLMS: 发送 LLM 请求
    LLMS->>LLM: 发送提示到 LLM
    LLM->>LLMS: 返回流式响应
    LLMS->>SP: 传递 LLM 响应
    SP->>SP: 更新聊天界面
```

## 自动完成功能流程图

```mermaid
sequenceDiagram
    participant User as 用户
    participant Editor as 代码编辑器
    participant ACS as autocompleteService.ts
    participant LLMS as llmMessageService.ts
    participant LLM as 大型语言模型 (LLM)
    
    User->>Editor: 编写代码
    Editor->>ACS: 触发自动完成
    ACS->>ACS: 获取上下文
    ACS->>LLMS: 发送 LLM 请求
    LLMS->>LLM: 发送提示到 LLM
    LLM->>LLMS: 返回补全建议
    LLMS->>ACS: 传递 LLM 响应
    ACS->>Editor: 显示补全建议
    User->>Editor: 选择/接受建议
    Editor->>Editor: 插入补全内容
```

## 登录流程图

```mermaid
sequenceDiagram
    participant User as 用户
    participant LA as loginAction.ts
    participant LSS as loginStatusService.ts
    participant LC as loginContribution.ts
    participant Browser as 浏览器
    participant Server as 认证服务器
    
    LC->>LSS: 检查登录状态
    LSS->>LC: 返回未登录状态
    LC->>LA: 显示登录按钮
    User->>LA: 点击登录按钮
    LA->>Browser: 打开登录页面
    User->>Browser: 输入凭据
    Browser->>Server: 发送登录请求
    Server->>Browser: 返回认证令牌
    Browser->>LSS: 保存认证令牌
    LSS->>LSS: 更新登录状态
    LSS->>LC: 通知登录成功
    LC->>LA: 隐藏登录按钮
```

## 欢迎页面流程图

```mermaid
flowchart TD
    A[应用启动] --> B{是否为开发环境?}
    B -->|是| C[welcome.contribution.ts]
    B -->|否| D{首次启动?}
    D -->|是| C
    D -->|否| E[跳过欢迎页面]
    
    C --> F[welcomeService.ts]
    F --> G[welcomePane.ts]
    G --> H[显示欢迎页面]
    H --> I[加载欢迎页面 React 组件]
    I --> J[用户与欢迎页面交互]
```

## 组件依赖关系图

```mermaid
flowchart TD
    subgraph "平台层"
        LLMS[llmMessageService.ts]
        JCSS[joyCoderSettingsService.ts]
        MTS[metricsService.ts]
    end
    
    subgraph "工作台贡献层"
        JC[joyCoder.contribution.ts]
        IDS[inlineDiffsService.ts]
        QEA[quickEditActions.ts]
        SA[sidebarActions.ts]
        SP[sidebarPane.ts]
        SS[sidebarStateService.ts]
        THS[threadHistoryService.ts]
        ACS[autocompleteService.ts]
        LA[loginAction.ts]
        LSS[loginStatusService.ts]
        LC[loginContribution.ts]
        WC[welcome.contribution.ts]
        WP[welcomePane.ts]
        WS[welcomeService.ts]
    end
    
    JC --> IDS
    JC --> QEA
    JC --> SA
    JC --> SP
    JC --> SS
    JC --> THS
    JC --> ACS
    JC --> LA
    JC --> LSS
    JC --> LC
    JC --> WC
    
    IDS --> LLMS
    QEA --> IDS
    SA --> SS
    SP --> SS
    ACS --> LLMS
    LA --> LSS
    LC --> LSS
    WC --> WS
    WS --> WP
    
    IDS --> MTS
    ACS --> MTS
    SA --> MTS
    
    IDS --> JCSS
    ACS --> JCSS
    
    classDef platform fill:#FFA500,stroke:#333,stroke-width:1px;
    classDef contrib fill:#6495ED,stroke:#333,stroke-width:1px;
    
    class LLMS,JCSS,MTS platform;
    class JC,IDS,QEA,SA,SP,SS,THS,ACS,LA,LSS,LC,WC,WP,WS contrib;
```

## 关键代码路径

### 平台层服务
- LLM 消息服务: `src/vs/platform/JoyCoder/common/llmMessageService.ts`
- JoyCoder 设置服务: `src/vs/platform/JoyCoder/common/joyCoderSettingsService.ts`
- 指标服务: `src/vs/platform/JoyCoder/common/metricsService.ts`

### 工作台贡献层
- JoyCoder 贡献: `src/vs/workbench/contrib/JoyCoder/browser/joyCoder.contribution.ts`
- 内联差异服务: `src/vs/workbench/contrib/JoyCoder/browser/inlineDiffsService.ts`
- 快速编辑操作: `src/vs/workbench/contrib/JoyCoder/browser/quickEditActions.ts`
- 侧边栏操作: `src/vs/workbench/contrib/JoyCoder/browser/sidebarActions.ts`
- 侧边栏面板: `src/vs/workbench/contrib/JoyCoder/browser/sidebarPane.ts`
- 侧边栏状态服务: `src/vs/workbench/contrib/JoyCoder/browser/sidebarStateService.ts`
- 线程历史服务: `src/vs/workbench/contrib/JoyCoder/browser/threadHistoryService.ts`
- 自动完成服务: `src/vs/workbench/contrib/JoyCoder/browser/autocompleteService.ts`
- 登录操作: `src/vs/workbench/contrib/JoyCoder/browser/loginAction.ts`
- 登录状态服务: `src/vs/workbench/contrib/JoyCoder/browser/loginStatusService.ts`
- 登录贡献: `src/vs/workbench/contrib/JoyCoder/browser/loginContribution.ts`
- 欢迎页面贡献: `src/vs/workbench/contrib/JoyCoder/browser/welcome.contribution.ts`
